import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  experimental: {
    // Increase body size limit for file uploads
    serverComponentsExternalPackages: [],
  },
  // Configure API routes for larger file uploads
  api: {
    bodyParser: {
      sizeLimit: '10mb', // Set to 10MB to be safe, but client-side is limited to 5MB
    },
  },
};

export default nextConfig;
