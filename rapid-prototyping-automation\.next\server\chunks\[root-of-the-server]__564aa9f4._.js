module.exports = {

"[project]/.next-internal/server/app/api/chat-architecture/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/chat-architecture/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@anthropic-ai/sdk/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Anthropic__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/@anthropic-ai/sdk/client.mjs [app-route] (ecmascript) <export Anthropic as default>");
;
;
// For development environments with SSL certificate issues
if ("TURBOPACK compile-time truthy", 1) {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}
const anthropic = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Anthropic__as__default$3e$__["default"]({
    apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development'
});
async function POST(request) {
    try {
        const { message, context, diagramCode } = await request.json();
        if (!message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No message provided'
            }, {
                status: 400
            });
        }
        // Check if API key is configured
        if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
            console.error('ANTHROPIC_API_KEY is not properly configured - returning demo response');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                response: "Thank you for your feedback! In demo mode, I can't actually modify the diagram, but I understand your request. Please configure your Claude API key to enable real-time diagram improvements.",
                updatedDiagramCode: diagramCode || null,
                message: 'Demo mode - Configure Claude API key for real chat functionality'
            });
        }
        console.log('Processing architecture chat message...');
        console.log(`User message: ${message}`);
        // Create a comprehensive prompt for architecture diagram improvement
        const fullPrompt = `You are a senior solutions architect helping to improve and refine architecture diagrams based on user feedback.

Context: The user is reviewing an architecture diagram that was generated from technical requirements. They may want to:
- Modify components or connections
- Add missing elements
- Improve the layout or organization
- Clarify relationships between components
- Add security layers or compliance elements
- Optimize for performance or scalability

Current diagram context:
${context || 'Architecture diagram for a cloud-native solution'}

Current diagram code (if available):
${diagramCode || 'No diagram code provided'}

User feedback/request:
${message}

Please provide helpful, specific advice and if the user requests changes to the diagram, provide updated Mermaid or PlantUML code that incorporates their feedback.

Be conversational, helpful, and focus on practical architecture improvements. If you provide updated diagram code, wrap it in appropriate code blocks (triple backticks for mermaid or @startuml/@enduml for PlantUML).`;
        let response;
        try {
            response = await anthropic.messages.create({
                model: 'claude-sonnet-4-20250514',
                max_tokens: 1500,
                messages: [
                    {
                        role: 'user',
                        content: fullPrompt
                    }
                ]
            });
            console.log('Claude chat API call successful');
        } catch (apiError) {
            console.error('Claude Chat API Error:', apiError);
            // Handle specific API errors
            if (apiError.status === 401) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid API key. Please check your Anthropic API key configuration.'
                }, {
                    status: 401
                });
            } else if (apiError.status === 429) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Rate limit exceeded. Please try again in a few minutes.'
                }, {
                    status: 429
                });
            } else {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: `Claude API error: ${apiError.message || 'Unknown error'}`
                }, {
                    status: 500
                });
            }
        }
        const chatResponse = response.content[0].type === 'text' ? response.content[0].text : '';
        // Check if the response contains updated diagram code
        let updatedDiagramCode = null;
        // Look for Mermaid code in the response
        const mermaidMatch = chatResponse.match(/```mermaid\s*([\s\S]*?)\s*```/i);
        if (mermaidMatch) {
            updatedDiagramCode = {
                type: 'mermaid',
                code: mermaidMatch[1].trim()
            };
        }
        // Look for PlantUML code in the response
        const plantUMLMatch = chatResponse.match(/@startuml([\s\S]*?)@enduml/i);
        if (plantUMLMatch) {
            updatedDiagramCode = {
                type: 'plantuml',
                code: `@startuml${plantUMLMatch[1]}@enduml`
            };
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            response: chatResponse,
            updatedDiagramCode: updatedDiagramCode,
            message: 'Chat response generated successfully'
        });
    } catch (error) {
        console.error('Error processing chat message:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to process chat message'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__564aa9f4._.js.map