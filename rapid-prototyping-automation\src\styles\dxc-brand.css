/* DXC Technology Brand System */

:root {
  /* DXC Core Color Palette */
  
  /* Greys */
  --dxc-grey-50: #f9fafb;
  --dxc-grey-100: #f3f4f6;
  --dxc-grey-200: #e5e7eb;
  --dxc-grey-300: #d1d5db;
  --dxc-grey-400: #9ca3af;
  --dxc-grey-500: #6b7280;
  --dxc-grey-600: #4b5563;
  --dxc-grey-700: #374151;
  --dxc-grey-800: #1f2937;
  --dxc-grey-900: #111827;
  
  /* Purple - DXC Primary Brand Color */
  --dxc-purple-50: #faf5ff;
  --dxc-purple-100: #f3e8ff;
  --dxc-purple-200: #e9d5ff;
  --dxc-purple-300: #d8b4fe;
  --dxc-purple-400: #c084fc;
  --dxc-purple-500: #a855f7;
  --dxc-purple-600: #7d2fd0;
  --dxc-purple-700: #6d28d9;
  --dxc-purple-800: #5b21b6;
  --dxc-purple-900: #4c1d95;
  
  /* Blue - DXC Secondary Brand Color */
  --dxc-blue-50: #eff6ff;
  --dxc-blue-100: #dbeafe;
  --dxc-blue-200: #bfdbfe;
  --dxc-blue-300: #93c5fd;
  --dxc-blue-400: #60a5fa;
  --dxc-blue-500: #3b82f6;
  --dxc-blue-600: #2563eb;
  --dxc-blue-700: #1d4ed8;
  --dxc-blue-800: #1e40af;
  --dxc-blue-900: #1e3a8a;
  
  /* Red - DXC Accent Color */
  --dxc-red-50: #fef2f2;
  --dxc-red-100: #fee2e2;
  --dxc-red-200: #fecaca;
  --dxc-red-300: #fca5a5;
  --dxc-red-400: #f87171;
  --dxc-red-500: #ef4444;
  --dxc-red-600: #dc2626;
  --dxc-red-700: #b91c1c;
  --dxc-red-800: #991b1b;
  --dxc-red-900: #7f1d1d;
  
  /* Black and White */
  --dxc-black: #000000;
  --dxc-white: #ffffff;
  
  /* DXC Typography Scale (8-point scale) */
  --dxc-text-xs: 0.75rem;    /* 12px */
  --dxc-text-sm: 0.875rem;   /* 14px */
  --dxc-text-base: 1rem;     /* 16px */
  --dxc-text-lg: 1.125rem;   /* 18px */
  --dxc-text-xl: 1.25rem;    /* 20px */
  --dxc-text-2xl: 1.5rem;    /* 24px */
  --dxc-text-3xl: 1.875rem;  /* 30px */
  --dxc-text-4xl: 2.25rem;   /* 36px */
  --dxc-text-5xl: 3rem;      /* 48px */
  --dxc-text-6xl: 3.75rem;   /* 60px */
  
  /* DXC Spacing System (8px grid) */
  --dxc-space-1: 0.25rem;    /* 4px - micro adjustments only */
  --dxc-space-2: 0.5rem;     /* 8px */
  --dxc-space-3: 0.75rem;    /* 12px */
  --dxc-space-4: 1rem;       /* 16px */
  --dxc-space-5: 1.25rem;    /* 20px */
  --dxc-space-6: 1.5rem;     /* 24px */
  --dxc-space-8: 2rem;       /* 32px */
  --dxc-space-10: 2.5rem;    /* 40px */
  --dxc-space-12: 3rem;      /* 48px */
  --dxc-space-16: 4rem;      /* 64px */
  --dxc-space-20: 5rem;      /* 80px */
  --dxc-space-24: 6rem;      /* 96px */
  
  /* DXC Grid System */
  --dxc-gutter-mobile: 1rem;     /* 16px gutters for mobile */
  --dxc-gutter-desktop: 1.5rem;  /* 24px gutters for desktop */
  --dxc-container-padding: var(--dxc-space-6); /* 24px horizontal padding */
  
  /* DXC Brand Accent Overlay */
  --dxc-brand-overlay: rgba(125, 47, 208, 0.15); /* Purple tint at 15% opacity */
}

/* DXC Typography System */
body {
  font-family: "Open Sans", sans-serif;
  font-size: var(--dxc-text-base);
  line-height: 1.6;
  color: var(--dxc-grey-800);
  background-color: var(--dxc-white);
}

/* DXC Heading Scale */
h1 {
  font-size: var(--dxc-text-5xl);
  font-weight: 700;
  line-height: 1.2;
  color: var(--dxc-grey-900);
  margin-bottom: var(--dxc-space-6);
}

h2 {
  font-size: var(--dxc-text-4xl);
  font-weight: 600;
  line-height: 1.3;
  color: var(--dxc-grey-900);
  margin-bottom: var(--dxc-space-5);
}

h3 {
  font-size: var(--dxc-text-3xl);
  font-weight: 600;
  line-height: 1.3;
  color: var(--dxc-grey-900);
  margin-bottom: var(--dxc-space-4);
}

h4 {
  font-size: var(--dxc-text-2xl);
  font-weight: 600;
  line-height: 1.4;
  color: var(--dxc-grey-800);
  margin-bottom: var(--dxc-space-4);
}

h5 {
  font-size: var(--dxc-text-xl);
  font-weight: 600;
  line-height: 1.4;
  color: var(--dxc-grey-800);
  margin-bottom: var(--dxc-space-3);
}

h6 {
  font-size: var(--dxc-text-lg);
  font-weight: 600;
  line-height: 1.4;
  color: var(--dxc-grey-700);
  margin-bottom: var(--dxc-space-3);
}

/* DXC Body Text */
p {
  font-size: var(--dxc-text-base);
  line-height: 1.6;
  color: var(--dxc-grey-700);
  margin-bottom: var(--dxc-space-4);
}

/* DXC Small Text */
.dxc-text-small {
  font-size: var(--dxc-text-sm);
  color: var(--dxc-grey-600);
}

.dxc-text-xs {
  font-size: var(--dxc-text-xs);
  color: var(--dxc-grey-500);
}

/* DXC Responsive Grid System */
.dxc-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--dxc-container-padding);
}

.dxc-grid {
  display: grid;
  gap: var(--dxc-gutter-mobile);
  grid-template-columns: repeat(4, 1fr); /* 4 columns on mobile */
}

/* Desktop Grid (1056px and up) */
@media (min-width: 66rem) {
  .dxc-grid {
    gap: var(--dxc-gutter-desktop);
    grid-template-columns: repeat(8, 1fr); /* 8 columns on desktop */
  }
}

/* DXC Button System */
.dxc-button {
  font-family: "Open Sans", sans-serif;
  font-size: var(--dxc-text-base);
  font-weight: 600;
  padding: var(--dxc-space-3) var(--dxc-space-6);
  border-radius: var(--dxc-space-2);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--dxc-space-2);
}

/* Primary Actions - DXC Purple */
.dxc-button-primary {
  background-color: var(--dxc-purple-600);
  color: var(--dxc-white);
}

.dxc-button-primary:hover {
  background-color: var(--dxc-purple-700);
}

.dxc-button-primary:focus {
  outline: 2px solid var(--dxc-purple-300);
  outline-offset: 2px;
}

/* Secondary Actions - DXC Blue */
.dxc-button-secondary {
  background-color: var(--dxc-blue-600);
  color: var(--dxc-white);
}

.dxc-button-secondary:hover {
  background-color: var(--dxc-blue-700);
}

.dxc-button-secondary:focus {
  outline: 2px solid var(--dxc-blue-300);
  outline-offset: 2px;
}

/* Neutral Actions */
.dxc-button-neutral {
  background-color: var(--dxc-grey-100);
  color: var(--dxc-grey-800);
  border: 1px solid var(--dxc-grey-300);
}

.dxc-button-neutral:hover {
  background-color: var(--dxc-grey-200);
  border-color: var(--dxc-grey-400);
}

/* DXC Header with Logo */
.dxc-header {
  background-color: var(--dxc-white);
  border-bottom: 1px solid var(--dxc-grey-200);
  padding: var(--dxc-space-4) 0;
}

.dxc-logo {
  height: 40px;
  width: auto;
}

.dxc-logo-link {
  display: inline-block;
  padding: var(--dxc-space-1) 0;
}

/* DXC Image Treatment */
.dxc-image {
  filter: saturate(0.2); /* Desaturate to 20% */
  position: relative;
}

.dxc-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--dxc-brand-overlay);
  pointer-events: none;
}

/* DXC Surface Colors */
.dxc-surface-primary {
  background-color: var(--dxc-white);
  border: 1px solid var(--dxc-grey-200);
}

.dxc-surface-secondary {
  background-color: var(--dxc-grey-50);
  border: 1px solid var(--dxc-grey-200);
}

.dxc-surface-accent {
  background-color: var(--dxc-purple-50);
  border: 1px solid var(--dxc-purple-200);
}

/* DXC Accessibility Enhancements */
.dxc-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for accessibility */
*:focus {
  outline: 2px solid var(--dxc-purple-600);
  outline-offset: 2px;
}

/* High contrast text for accessibility (WCAG AA 4.5:1) */
.dxc-text-high-contrast {
  color: var(--dxc-grey-900);
}

.dxc-text-medium-contrast {
  color: var(--dxc-grey-700);
}

.dxc-text-low-contrast {
  color: var(--dxc-grey-600);
}
