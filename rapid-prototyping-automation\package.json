{"name": "rapid-prototyping-automation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@anthropic-ai/sdk": "^0.55.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-toast": "^1.2.14", "clsx": "^2.1.1", "docx": "^9.5.1", "framer-motion": "^12.19.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "mermaid": "^10.9.1", "next": "15.3.4", "openai": "^5.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}