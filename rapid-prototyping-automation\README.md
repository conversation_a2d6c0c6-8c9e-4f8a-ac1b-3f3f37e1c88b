# Rapid Prototyping Automation

A modern web application that transforms conversation transcripts into comprehensive technical documentation and implementation guides. Built with Next.js, TypeScript, and Tailwind CSS with WCAG-compliant design.

## 🚀 Features

### Complete 5-Step Workflow
1. **Transcript Upload** - Drag-and-drop file upload with validation
2. **Problem Statement Generation** - **Claude AI-powered** analysis with professional Word document generation
3. **Technical Requirements** - Detailed technical specifications document
4. **Architecture Diagram** - Visual system architecture with chat-based feedback
5. **Implementation Prompts** - Copy-paste ready development prompts

### 🤖 AI-Powered Analysis
- **Claude 3.5 Sonnet Integration** - Latest Anthropic AI model for document generation
- **Professional Word Documents** - Automatically generated .docx files
- **Comprehensive Analysis** - Executive summaries, business challenges, and actionable recommendations
- **Smart Content Extraction** - Identifies participants, pain points, and solution requirements

### Modern UI/UX
- **WCAG Compliant** - Accessible design with proper contrast ratios
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile
- **Enticing Color Palette** - Beautiful gradients and modern aesthetics
- **Interactive Components** - Smooth animations and transitions
- **Toast Notifications** - Real-time user feedback

### Technical Highlights
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **React Hooks** for state management
- **File Upload** with drag-and-drop support
- **Chat Interface** for architecture feedback
- **Document Generation** and download functionality

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd rapid-prototyping-automation
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up Claude AI integration:
```bash
cp .env.example .env.local
```
Edit `.env.local` and add your Anthropic API key:
```env
ANTHROPIC_API_KEY=sk-ant-your-api-key-here
```
See [CLAUDE_SETUP.md](./CLAUDE_SETUP.md) for detailed setup instructions.

4. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles and design system
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/
│   ├── ui/                # Reusable UI components
│   │   ├── Button.tsx     # Button component
│   │   ├── Card.tsx       # Card components
│   │   ├── ChatInterface.tsx # Chat interface
│   │   ├── FileUpload.tsx # File upload component
│   │   ├── Progress.tsx   # Progress bar
│   │   └── Toast.tsx      # Toast notifications
│   └── RapidPrototypingApp.tsx # Main application
└── lib/
    └── utils.ts           # Utility functions
```

## 🎨 Design System

### Color Palette
- **Primary**: Blue gradient (#0ea5e9 to #0284c7)
- **Secondary**: Purple gradient (#d946ef to #c026d3)
- **Accent**: Orange (#f97316)
- **Success**: Green (#22c55e)
- **Error**: Red (#ef4444)

### Typography
- **Font**: Inter (Google Fonts)
- **Headings**: Semibold with tight letter spacing
- **Body**: Regular weight with 1.6 line height

### Components
- **Buttons**: Gradient backgrounds with hover effects
- **Cards**: Subtle shadows with gradient options
- **Inputs**: Focus states with ring effects
- **Progress**: Animated gradient fill

## 🔧 Customization

### Adding New Steps
To add additional steps to the workflow:

1. Update the `STEPS` array in `RapidPrototypingApp.tsx`
2. Add the new step case in the main render method
3. Update the `Step` type definition
4. Implement the step's UI and logic

### Modifying Colors
Update the CSS custom properties in `globals.css`:

```css
:root {
  --primary-500: #your-color;
  --secondary-500: #your-color;
  /* ... */
}
```

### Adding Components
Create new components in `src/components/ui/` following the existing patterns:

```tsx
import { cn } from '@/lib/utils';

interface YourComponentProps {
  // props
}

export const YourComponent: React.FC<YourComponentProps> = ({ ... }) => {
  return (
    <div className={cn('base-classes', className)}>
      {/* content */}
    </div>
  );
};
```

## 🧪 Testing

The application includes:
- **Type Safety** with TypeScript
- **ESLint** for code quality
- **Responsive Design** testing across devices
- **Accessibility** compliance with WCAG guidelines

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- Render

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Tailwind CSS for the utility-first CSS framework
- Lucide React for the beautiful icons
- Inter font family for typography
