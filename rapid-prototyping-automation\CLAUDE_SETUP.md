# Claude API Integration Setup

This guide will help you set up Claude AI integration for the complete rapid prototyping workflow including problem statements, technical requirements, architecture diagrams, and interactive chat.

## Prerequisites

1. **Anthropic Account**: You need an account with Anthropic to access Claude API
2. **API Key**: You'll need a valid Claude API key with sufficient credits

## Step 1: Get Your Claude API Key

1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Sign up or log in to your account
3. Navigate to the API Keys section
4. Create a new API key
5. Copy the API key (it starts with `sk-ant-`)

## Step 2: Configure Environment Variables

1. In your project root, create a `.env.local` file:
```bash
cp .env.example .env.local
```

2. Edit `.env.local` and add your API key:
```env
ANTHROPIC_API_KEY=sk-ant-your-actual-api-key-here
```

3. **Important**: Never commit your `.env.local` file to version control!

## Step 3: Verify Integration

1. Start your development server:
```bash
npm run dev
```

2. Upload a transcript file (TXT, DOCX, or PDF)
3. The system should:
   - Send the transcript to Claude AI
   - Generate a comprehensive problem statement
   - Return a downloadable Word document
   - Generate technical requirements from the problem statement
   - Create architecture diagrams with Mermaid and PlantUML
   - Provide interactive chat for diagram improvements

## API Usage & Costs

- **Model Used**: Claude Sonnet 4 (latest)
- **Typical Cost**: ~$0.10-0.50 per transcript analysis
- **Processing Time**: 30-60 seconds depending on transcript length
- **Token Limits**: Up to 4000 tokens for response

## Troubleshooting

### Common Issues

1. **"API Key not found" error**
   - Ensure `.env.local` file exists in project root
   - Verify the API key is correctly formatted
   - Restart your development server

2. **"Rate limit exceeded" error**
   - You've hit Anthropic's rate limits
   - Wait a few minutes before trying again
   - Consider upgrading your Anthropic plan

3. **"File processing failed" error**
   - Check if the uploaded file is readable
   - Ensure file size is under 10MB
   - Try converting to plain text format

4. **Document generation fails**
   - Check browser console for detailed errors
   - Verify the API response is valid
   - Try with a shorter transcript

### Debug Mode

To enable detailed logging, add to your `.env.local`:
```env
NODE_ENV=development
DEBUG=true
```

## File Format Support

The integration supports:
- **Plain Text** (.txt) - Best compatibility
- **Word Documents** (.docx) - Extracted to plain text
- **PDF Files** (.pdf) - Text extraction (may vary by PDF type)
- **Markdown** (.md) - Full support

## Security Notes

1. **API Key Security**:
   - Never expose your API key in client-side code
   - Use environment variables only
   - Rotate keys regularly

2. **Data Privacy**:
   - Transcripts are sent to Anthropic's servers
   - Review Anthropic's privacy policy
   - Consider data sensitivity before uploading

3. **Rate Limiting**:
   - Implement client-side rate limiting for production
   - Monitor API usage in Anthropic console
   - Set up billing alerts

## Production Deployment

For production deployment:

1. **Environment Variables**:
   - Set `ANTHROPIC_API_KEY` in your hosting platform
   - Use secure secret management

2. **Error Handling**:
   - Implement proper error logging
   - Add retry mechanisms for failed requests
   - Set up monitoring and alerts

3. **Performance**:
   - Consider caching for repeated requests
   - Implement request queuing for high volume
   - Monitor response times

## Support

If you encounter issues:

1. Check the [Anthropic Documentation](https://docs.anthropic.com/)
2. Review the API status page
3. Contact Anthropic support for API-related issues
4. Check this project's GitHub issues for common problems

## API Response Format

The Claude API returns a structured analysis that includes:

- **Title & Participants**: Extracted from transcript
- **Executive Summary**: 3-4 sentence overview
- **Background & Context**: Strategic context and objectives
- **Key Business Challenges**: Identified challenges with quotes
- **User Needs & Pain Points**: Structured analysis
- **Problem Statement**: "How Might We" formulation
- **Constraints & Success Criteria**: Implementation guidelines
- **Next Steps**: Actionable recommendations
- **Key Insights**: Critical quotes and insights

This analysis is then formatted into a professional Word document for download.
