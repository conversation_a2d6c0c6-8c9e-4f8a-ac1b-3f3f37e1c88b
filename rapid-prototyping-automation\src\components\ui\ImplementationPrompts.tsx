import React, { useState } from 'react';
import { But<PERSON> } from './Button';
import { Copy, Download, Code, Database, Server, Cloud, Monitor } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImplementationPromptsProps {
  prompts?: {
    frontend: string;
    backend: string;
    database: string;
    devops: string | any;
  };
  onCopyPrompt?: (prompt: string, type: string) => void;
  className?: string;
}

export const ImplementationPrompts: React.FC<ImplementationPromptsProps> = ({
  prompts,
  onCopyPrompt,
  className
}) => {
  const [activeTab, setActiveTab] = useState<'frontend' | 'backend' | 'database' | 'devops'>('frontend');

  const handleCopyPrompt = (prompt: string, type: string) => {
    navigator.clipboard.writeText(prompt);
    if (onCopyPrompt) {
      onCopyPrompt(prompt, type);
    }
  };

  const handleDownloadPrompt = (content: string, filename: string, type: string = 'text/plain') => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const element = document.createElement('a');
    element.href = url;
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    URL.revokeObjectURL(url);
  };

  const handleDownloadDevOps = () => {
    if (!prompts?.devops) return;
    
    let content = prompts.devops;
    let filename = 'devops-config.json';
    
    // If it's already an object, stringify it
    if (typeof content === 'object') {
      content = JSON.stringify(content, null, 2);
    } else {
      // Try to parse and reformat if it's a string
      try {
        const parsed = JSON.parse(content);
        content = JSON.stringify(parsed, null, 2);
      } catch (e) {
        // If not valid JSON, keep as is
        filename = 'devops-config.txt';
      }
    }
    
    handleDownloadPrompt(content, filename, 'application/json');
  };

  const tabs = [
    {
      id: 'frontend' as const,
      label: 'Frontend',
      icon: Monitor,
      color: 'blue',
      description: 'React/Vue UI Components'
    },
    {
      id: 'backend' as const,
      label: 'Backend API',
      icon: Server,
      color: 'green',
      description: 'REST API & Services'
    },
    {
      id: 'database' as const,
      label: 'Database',
      icon: Database,
      color: 'purple',
      description: 'Schema & Migrations'
    },
    {
      id: 'devops' as const,
      label: 'DevOps',
      icon: Cloud,
      color: 'orange',
      description: 'Infrastructure as Code'
    }
  ];

  const getPromptContent = (type: string) => {
    if (!prompts) return '';
    
    switch (type) {
      case 'frontend':
        return prompts.frontend;
      case 'backend':
        return prompts.backend;
      case 'database':
        return prompts.database;
      case 'devops':
        return typeof prompts.devops === 'object' 
          ? JSON.stringify(prompts.devops, null, 2)
          : prompts.devops;
      default:
        return '';
    }
  };

  if (!prompts) {
    return (
      <div className={cn('text-center py-8', className)}>
        <Code className="w-16 h-16 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">Implementation prompts will appear here</p>
        <p className="text-sm text-gray-400 mt-1">Generate from technical requirements to see the prompts</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm',
                  isActive
                    ? `border-${tab.color}-500 text-${tab.color}-600`
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className={cn(
                  'mr-2 h-5 w-5',
                  isActive ? `text-${tab.color}-500` : 'text-gray-400 group-hover:text-gray-500'
                )} />
                <div className="text-left">
                  <div>{tab.label}</div>
                  <div className="text-xs text-gray-400">{tab.description}</div>
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {tabs.find(t => t.id === activeTab)?.label} Implementation Prompt
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Copy this prompt and paste it into Loveable AI to generate the {activeTab} implementation
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handleCopyPrompt(getPromptContent(activeTab), activeTab)}
              >
                <Copy className="w-4 h-4 mr-1" />
                Copy Prompt
              </Button>
              
              {activeTab === 'devops' ? (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleDownloadDevOps}
                >
                  <Download className="w-4 h-4 mr-1" />
                  Download JSON
                </Button>
              ) : (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => handleDownloadPrompt(
                    getPromptContent(activeTab),
                    `${activeTab}-prompt.txt`
                  )}
                >
                  <Download className="w-4 h-4 mr-1" />
                  Download
                </Button>
              )}
            </div>
          </div>

          {/* Prompt Content */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-auto max-h-96 leading-relaxed">
              {getPromptContent(activeTab)}
            </pre>
          </div>

          {/* Special DevOps Info */}
          {activeTab === 'devops' && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start">
                <Cloud className="w-5 h-5 text-blue-500 mt-0.5 mr-2" />
                <div>
                  <h4 className="font-medium text-blue-900">Infrastructure as Code</h4>
                  <p className="text-blue-700 text-sm mt-1">
                    This JSON configuration can be used with Terraform or Ansible for automated deployment.
                    Download the file and customize it for your specific cloud provider and requirements.
                  </p>
                  <div className="mt-2 flex gap-4 text-xs text-blue-600">
                    <span>• Terraform: terraform apply</span>
                    <span>• Ansible: ansible-playbook</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Loveable AI Integration Info */}
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-start">
              <Code className="w-5 h-5 text-green-500 mt-0.5 mr-2" />
              <div>
                <h4 className="font-medium text-green-900">Loveable AI Integration</h4>
                <p className="text-green-700 text-sm mt-1">
                  Copy the prompt above and paste it directly into Loveable AI. The prompt includes all necessary
                  technical specifications, best practices, and implementation details for rapid prototyping.
                </p>
                <div className="mt-2 text-xs text-green-600">
                  💡 Tip: Each prompt is optimized for AI-powered development tools
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <div key={tab.id} className="bg-white border border-gray-200 rounded-lg p-4 text-center">
              <Icon className={cn('w-8 h-8 mx-auto mb-2', `text-${tab.color}-500`)} />
              <h4 className="font-medium text-gray-900 text-sm">{tab.label}</h4>
              <p className="text-xs text-gray-500 mt-1">{tab.description}</p>
              <Button
                variant="secondary"
                size="sm"
                className="mt-3 w-full"
                onClick={() => {
                  setActiveTab(tab.id);
                  handleCopyPrompt(getPromptContent(tab.id), tab.id);
                }}
              >
                <Copy className="w-3 h-3 mr-1" />
                Copy
              </Button>
            </div>
          );
        })}
      </div>
    </div>
  );
};
