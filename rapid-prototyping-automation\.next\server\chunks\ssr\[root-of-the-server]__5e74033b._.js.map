{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  gradient?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n}\n\nexport const Card: React.FC<CardProps> = ({\n  children,\n  className,\n  gradient = false,\n  padding = 'md'\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  return (\n    <div\n      className={cn(\n        'card',\n        gradient && 'card-gradient',\n        paddingClasses[padding],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardTitle: React.FC<CardTitleProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <h3 className={cn('text-xl font-semibold gradient-text', className)}>\n      {children}\n    </h3>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContent: React.FC<CardContentProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('text-gray-600', className)}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AACA;;;AASO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,IAAI,EACf;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,YAAY,iBACZ,cAAc,CAAC,QAAQ,EACvB;kBAGD;;;;;;AAGP;AAOO,MAAM,aAAwC,CAAC,EACpD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;AAOO,MAAM,YAAsC,CAAC,EAClD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBACtD;;;;;;AAGP;AAOO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBACjC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'success';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  disabled,\n  children,\n  ...props\n}) => {\n  return (\n    <button\n      className={cn(\n        'btn',\n        `btn-${variant}`,\n        `btn-${size}`,\n        loading && 'opacity-75 cursor-not-allowed',\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-3 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AASO,MAAM,SAAgC,CAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,CAAC,IAAI,EAAE,SAAS,EAChB,CAAC,IAAI,EAAE,MAAM,EACb,WAAW,iCACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ProgressProps {\n  value: number;\n  max?: number;\n  className?: string;\n  showLabel?: boolean;\n  label?: string;\n}\n\nexport const Progress: React.FC<ProgressProps> = ({\n  value,\n  max = 100,\n  className,\n  showLabel = false,\n  label\n}) => {\n  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);\n\n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between items-center mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">\n            {label || 'Progress'}\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            {Math.round(percentage)}%\n          </span>\n        </div>\n      )}\n      <div className=\"progress-bar\">\n        <div\n          className=\"progress-fill\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,MAAM,GAAG,EACT,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACN;IACC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,SAAS;;;;;;kCAEZ,8OAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAI9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/FileUpload.tsx"], "sourcesContent": ["import React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, File, X } from 'lucide-react';\nimport { cn, formatFileSize } from '@/lib/utils';\nimport { Button } from './Button';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  accept?: Record<string, string[]>;\n  maxSize?: number;\n  className?: string;\n}\n\nexport const FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  accept = {\n    'text/*': ['.txt', '.md', '.doc', '.docx'],\n    'application/pdf': ['.pdf']\n  },\n  maxSize = 10 * 1024 * 1024, // 10MB\n  className\n}) => {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [error, setError] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError('');\n    \n    if (rejectedFiles.length > 0) {\n      const rejection = rejectedFiles[0];\n      if (rejection.errors[0]?.code === 'file-too-large') {\n        setError(`File is too large. Maximum size is ${formatFileSize(maxSize)}`);\n      } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n        setError('Invalid file type. Please upload a text document or PDF.');\n      } else {\n        setError('File upload failed. Please try again.');\n      }\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      setSelectedFile(file);\n      onFileSelect(file);\n    }\n  }, [onFileSelect, maxSize]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept,\n    maxSize,\n    multiple: false\n  });\n\n  const removeFile = () => {\n    setSelectedFile(null);\n    setError('');\n  };\n\n  return (\n    <div className={cn('w-full', className)}>\n      {!selectedFile ? (\n        <div\n          {...getRootProps()}\n          className={cn(\n            'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200',\n            isDragActive\n              ? 'border-blue-400 bg-blue-50'\n              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n          )}\n        >\n          <input {...getInputProps()} />\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <p className=\"text-lg font-medium text-gray-700 mb-2\">\n            {isDragActive ? 'Drop the file here' : 'Upload transcript file'}\n          </p>\n          <p className=\"text-sm text-gray-500 mb-4\">\n            Drag and drop your file here, or click to browse\n          </p>\n          <p className=\"text-xs text-gray-400\">\n            Supports: TXT, MD, DOC, DOCX, PDF (max {formatFileSize(maxSize)})\n          </p>\n        </div>\n      ) : (\n        <div className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <File className=\"h-8 w-8 text-blue-500\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">{selectedFile.name}</p>\n                <p className=\"text-sm text-gray-500\">\n                  {formatFileSize(selectedFile.size)}\n                </p>\n              </div>\n            </div>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={removeFile}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      )}\n      \n      {error && (\n        <div className=\"mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2\">\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AASO,MAAM,aAAwC,CAAC,EACpD,YAAY,EACZ,SAAS;IACP,UAAU;QAAC;QAAQ;QAAO;QAAQ;KAAQ;IAC1C,mBAAmB;QAAC;KAAO;AAC7B,CAAC,EACD,UAAU,KAAK,OAAO,IAAI,EAC1B,SAAS,EACV;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,eAAuB;QACjD,SAAS;QAET,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,YAAY,aAAa,CAAC,EAAE;YAClC,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,kBAAkB;gBAClD,SAAS,CAAC,mCAAmC,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YAC1E,OAAO,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,qBAAqB;gBAC5D,SAAS;YACX,OAAO;gBACL,SAAS;YACX;YACA;QACF;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,gBAAgB;YAChB,aAAa;QACf;IACF,GAAG;QAAC;QAAc;KAAQ;IAE1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA;QACA;QACA,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,CAAC,6BACA,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,eACI,+BACA;;kCAGN,8OAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAE,WAAU;kCACV,eAAe,uBAAuB;;;;;;kCAEzC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAE,WAAU;;4BAAwB;4BACK,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;4BAAS;;;;;;;;;;;;qCAIpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAA6B,aAAa,IAAI;;;;;;sDAC3D,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;sCAIvC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ChatInterface.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Button } from './Button';\nimport { Send, Bot, User } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: 'user' | 'bot';\n  timestamp: Date;\n}\n\ninterface ChatInterfaceProps {\n  onFeedback: (feedback: string) => void;\n  className?: string;\n}\n\nexport const ChatInterface: React.FC<ChatInterfaceProps> = ({\n  onFeedback,\n  className\n}) => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: 'I\\'ve generated the architecture diagram based on your requirements. What do you think about the current design?',\n      sender: 'bot',\n      timestamp: new Date()\n    }\n  ]);\n  const [inputValue, setInputValue] = useState('');\n\n  const handleSendMessage = () => {\n    if (!inputValue.trim()) return;\n\n    const newMessage: Message = {\n      id: Date.now().toString(),\n      content: inputValue,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    onFeedback(inputValue);\n    setInputValue('');\n\n    // Simulate bot response\n    setTimeout(() => {\n      const botResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        content: 'Thank you for your feedback! I\\'ll incorporate these changes into the architecture design.',\n        sender: 'bot',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, botResponse]);\n    }, 1000);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className={cn('bg-white border border-gray-200 rounded-lg', className)}>\n      <div className=\"p-4 border-b border-gray-200\">\n        <h4 className=\"font-semibold text-gray-900\">Architecture Feedback</h4>\n        <p className=\"text-sm text-gray-600\">Share your thoughts on the generated architecture</p>\n      </div>\n      \n      <div className=\"h-64 overflow-y-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={cn(\n              'flex items-start space-x-3',\n              message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n            )}\n          >\n            <div className={cn(\n              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',\n              message.sender === 'user' \n                ? 'bg-blue-500 text-white' \n                : 'bg-gray-200 text-gray-600'\n            )}>\n              {message.sender === 'user' ? (\n                <User className=\"w-4 h-4\" />\n              ) : (\n                <Bot className=\"w-4 h-4\" />\n              )}\n            </div>\n            <div className={cn(\n              'flex-1 max-w-xs lg:max-w-md px-4 py-2 rounded-lg',\n              message.sender === 'user'\n                ? 'bg-blue-500 text-white ml-auto'\n                : 'bg-gray-100 text-gray-900'\n            )}>\n              <p className=\"text-sm\">{message.content}</p>\n              <p className={cn(\n                'text-xs mt-1',\n                message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'\n              )}>\n                {message.timestamp.toLocaleTimeString([], { \n                  hour: '2-digit', \n                  minute: '2-digit' \n                })}\n              </p>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex space-x-2\">\n          <textarea\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Share your feedback on the architecture...\"\n            className=\"flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows={2}\n          />\n          <Button\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim()}\n            size=\"sm\"\n            className=\"self-end\"\n          >\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAcO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,EACV,SAAS,EACV;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;QACzC,WAAW;QACX,cAAc;QAEd,wBAAwB;QACxB,WAAW;YACT,MAAM,cAAuB;gBAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;QAC5C,GAAG;IACL;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;;0BAC/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,QAAQ,MAAM,KAAK,SAAS,qCAAqC;;0CAGnE,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uEACA,QAAQ,MAAM,KAAK,SACf,2BACA;0CAEH,QAAQ,MAAM,KAAK,uBAClB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAGnB,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,oDACA,QAAQ,MAAM,KAAK,SACf,mCACA;;kDAEJ,8OAAC;wCAAE,WAAU;kDAAW,QAAQ,OAAO;;;;;;kDACvC,8OAAC;wCAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,gBACA,QAAQ,MAAM,KAAK,SAAS,kBAAkB;kDAE7C,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4CACxC,MAAM;4CACN,QAAQ;wCACV;;;;;;;;;;;;;uBAhCC,QAAQ,EAAE;;;;;;;;;;0BAuCrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,YAAY;4BACZ,aAAY;4BACZ,WAAU;4BACV,MAAM;;;;;;sCAER,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI;4BAC1B,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Toast.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { CheckCircle, X, AlertCircle, Info } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport type ToastType = 'success' | 'error' | 'info' | 'warning';\n\ninterface Toast {\n  id: string;\n  message: string;\n  type: ToastType;\n  duration?: number;\n}\n\ninterface ToastProps {\n  toast: Toast;\n  onRemove: (id: string) => void;\n}\n\nconst ToastComponent: React.FC<ToastProps> = ({ toast, onRemove }) => {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id);\n    }, toast.duration || 3000);\n\n    return () => clearTimeout(timer);\n  }, [toast.id, toast.duration, onRemove]);\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5\" />;\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'warning':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5\" />;\n      default:\n        return <Info className=\"h-5 w-5\" />;\n    }\n  };\n\n  const getStyles = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800';\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800';\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800';\n    }\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex items-center justify-between p-4 rounded-lg border shadow-lg animate-slide-up',\n        getStyles()\n      )}\n    >\n      <div className=\"flex items-center space-x-3\">\n        {getIcon()}\n        <span className=\"text-sm font-medium\">{toast.message}</span>\n      </div>\n      <button\n        onClick={() => onRemove(toast.id)}\n        className=\"ml-4 text-current hover:opacity-70 transition-opacity\"\n      >\n        <X className=\"h-4 w-4\" />\n      </button>\n    </div>\n  );\n};\n\ninterface ToastContainerProps {\n  toasts: Toast[];\n  onRemove: (id: string) => void;\n}\n\nexport const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  );\n};\n\n// Hook for managing toasts\nexport const useToast = () => {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n\n  const addToast = (message: string, type: ToastType = 'info', duration?: number) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const newToast: Toast = { id, message, type, duration };\n    setToasts(prev => [...prev, newToast]);\n  };\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success: (message: string, duration?: number) => addToast(message, 'success', duration),\n    error: (message: string, duration?: number) => addToast(message, 'error', duration),\n    info: (message: string, duration?: number) => addToast(message, 'info', duration),\n    warning: (message: string, duration?: number) => addToast(message, 'warning', duration),\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAgBA,MAAM,iBAAuC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,SAAS,MAAM,EAAE;QACnB,GAAG,MAAM,QAAQ,IAAI;QAErB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,YAAY;QAChB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA;;0BAGF,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBAAK,WAAU;kCAAuB,MAAM,OAAO;;;;;;;;;;;;0BAEtD,8OAAC;gBACC,SAAS,IAAM,SAAS,MAAM,EAAE;gBAChC,WAAU;0BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;AAOO,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChF,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;AAGO,MAAM,WAAW;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC,SAAiB,OAAkB,MAAM,EAAE;QAC3D,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAkB;YAAE;YAAI;YAAS;YAAM;QAAS;QACtD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;QAC9E,OAAO,CAAC,SAAiB,WAAsB,SAAS,SAAS,SAAS;QAC1E,MAAM,CAAC,SAAiB,WAAsB,SAAS,SAAS,QAAQ;QACxE,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;IAChF;AACF", "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/RapidPrototypingApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { Progress } from '@/components/ui/Progress';\nimport { FileUpload } from '@/components/ui/FileUpload';\nimport { ChatInterface } from '@/components/ui/ChatInterface';\nimport { ToastContainer, useToast } from '@/components/ui/Toast';\nimport { ThinkingIndicator } from '@/components/ui/ThinkingIndicator';\nimport { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';\n\ntype Step = 1 | 2 | 3 | 4 | 5;\n\ninterface AppState {\n  currentStep: Step;\n  uploadedFile: File | null;\n  isProcessing: boolean;\n  processingStep: string;\n  error: string | null;\n  documents: {\n    problemStatement: string | null;\n    technicalRequirements: string | null;\n    architectureDiagram: string | null;\n    prompts: string[] | null;\n  };\n  validations: {\n    problemStatement: boolean | null;\n    technicalRequirements: boolean | null;\n    architectureDiagram: boolean | null;\n  };\n}\n\nconst STEPS = [\n  { id: 1, title: 'Upload Transcript', icon: FileText, description: 'Upload your conversation transcript' },\n  { id: 2, title: 'Problem Statement', icon: CheckCircle, description: 'Review and validate problem statement' },\n  { id: 3, title: 'Technical Requirements', icon: Settings, description: 'Review technical requirements document' },\n  { id: 4, title: 'Architecture Diagram', icon: Image, description: 'Review system architecture' },\n  { id: 5, title: 'Generated Prompts', icon: Code, description: 'Copy implementation prompts' }\n];\n\nexport const RapidPrototypingApp: React.FC = () => {\n  const { toasts, removeToast, success, error, info } = useToast();\n  const [state, setState] = useState<AppState>({\n    currentStep: 1,\n    uploadedFile: null,\n    isProcessing: false,\n    processingStep: '',\n    error: null,\n    documents: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null,\n      prompts: null\n    },\n    validations: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null\n    }\n  });\n\n  const handleFileUpload = async (file: File) => {\n    setState(prev => ({\n      ...prev,\n      uploadedFile: file,\n      isProcessing: true,\n      processingStep: 'Uploading transcript...',\n      error: null\n    }));\n\n    info('Processing your transcript with Claude AI...', 5000);\n\n    try {\n      setState(prev => ({ ...prev, processingStep: 'Analyzing transcript with Claude AI...' }));\n\n      // Create FormData to send file to API\n      const formData = new FormData();\n      formData.append('file', file);\n\n      setState(prev => ({ ...prev, processingStep: 'Generating problem statement document...' }));\n\n      // Call the API to generate problem statement\n      const response = await fetch('/api/generate-problem-statement', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate problem statement`;\n        throw new Error(errorMessage);\n      }\n\n      setState(prev => ({ ...prev, processingStep: 'Finalizing document...' }));\n\n      // Get the generated document as blob\n      const blob = await response.blob();\n      const documentUrl = URL.createObjectURL(blob);\n\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        currentStep: 2,\n        error: null,\n        documents: {\n          ...prev.documents,\n          problemStatement: documentUrl\n        }\n      }));\n\n      success('Problem statement document generated successfully with Claude AI!');\n    } catch (err) {\n      console.error('Error generating problem statement:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate problem statement. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        error: errorMessage\n      }));\n    }\n  };\n\n  const handleValidation = async (documentType: keyof AppState['validations'], isValid: boolean) => {\n    setState(prev => ({\n      ...prev,\n      validations: {\n        ...prev.validations,\n        [documentType]: isValid\n      }\n    }));\n\n    if (isValid) {\n      const nextStep = state.currentStep + 1;\n\n      if (nextStep === 3 && documentType === 'problemStatement') {\n        // Generate technical requirements from problem statement\n        await generateTechnicalRequirements();\n      } else if (nextStep <= 5) {\n        // Simulate generating other documents\n        setState(prev => ({ ...prev, isProcessing: true, processingStep: 'Generating next document...' }));\n        await new Promise(resolve => setTimeout(resolve, 1500));\n\n        let newDocuments = { ...state.documents };\n\n        if (nextStep === 4) {\n          newDocuments.architectureDiagram = 'Generated architecture diagram...';\n        } else if (nextStep === 5) {\n          newDocuments.prompts = ['Prompt 1...', 'Prompt 2...', 'Prompt 3...'];\n        }\n\n        setState(prev => ({\n          ...prev,\n          currentStep: nextStep as Step,\n          documents: newDocuments,\n          isProcessing: false,\n          processingStep: ''\n        }));\n      }\n    }\n  };\n\n  const generateTechnicalRequirements = async () => {\n    setState(prev => ({\n      ...prev,\n      isProcessing: true,\n      processingStep: 'Extracting problem statement content...',\n      error: null\n    }));\n\n    try {\n      // Extract content from the problem statement document\n      setState(prev => ({ ...prev, processingStep: 'Analyzing problem statement with Claude AI...' }));\n\n      let problemStatementContent = '';\n\n      if (state.documents.problemStatement) {\n        // For now, we'll use a placeholder since we can't easily extract from the blob\n        // In a real implementation, you might store the text content separately\n        problemStatementContent = `Problem Statement Document Analysis:\n\nThis document contains the comprehensive problem statement analysis including:\n- Executive Summary of the business challenges\n- Background and context of the workshop\n- Key business challenges identified\n- Core user needs and pain points\n- How Might We problem statement\n- Constraints and success criteria\n- Next steps and recommendations\n- Key insights and quotes from stakeholders\n\nThe technical solution should address the scalability, performance, and integration challenges identified in the problem statement while providing a modern, cloud-native architecture that can handle the specified requirements.`;\n      }\n\n      setState(prev => ({ ...prev, processingStep: 'Generating technical requirements document...' }));\n\n      // Call the technical requirements API\n      const response = await fetch('/api/generate-technical-requirements', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          problemStatementContent: problemStatementContent\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate technical requirements`;\n        throw new Error(errorMessage);\n      }\n\n      setState(prev => ({ ...prev, processingStep: 'Finalizing technical document...' }));\n\n      // Get the generated document as blob\n      const blob = await response.blob();\n      const documentUrl = URL.createObjectURL(blob);\n\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        currentStep: 3,\n        error: null,\n        documents: {\n          ...prev.documents,\n          technicalRequirements: documentUrl\n        }\n      }));\n\n      success('Technical requirements document generated successfully with Claude AI!');\n    } catch (err) {\n      console.error('Error generating technical requirements:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate technical requirements. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        error: errorMessage\n      }));\n    }\n  };\n\n  const currentStepData = STEPS.find(step => step.id === state.currentStep);\n  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <ToastContainer toasts={toasts} onRemove={removeToast} />\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold gradient-text mb-4\">\n            Rapid Prototyping Automation\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Transform your conversation transcripts into comprehensive technical documentation and implementation guides\n          </p>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <Progress \n            value={progress} \n            showLabel \n            label={`Step ${state.currentStep} of ${STEPS.length}: ${currentStepData?.title}`}\n            className=\"max-w-2xl mx-auto\"\n          />\n        </div>\n\n        {/* Steps Navigation */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"flex space-x-4 overflow-x-auto pb-2\">\n            {STEPS.map((step) => {\n              const Icon = step.icon;\n              const isActive = step.id === state.currentStep;\n              const isCompleted = step.id < state.currentStep;\n              \n              return (\n                <div\n                  key={step.id}\n                  className={`flex flex-col items-center min-w-[120px] p-3 rounded-lg transition-all ${\n                    isActive \n                      ? 'bg-blue-100 border-2 border-blue-300' \n                      : isCompleted \n                        ? 'bg-green-100 border-2 border-green-300'\n                        : 'bg-gray-100 border-2 border-gray-200'\n                  }`}\n                >\n                  <Icon \n                    className={`h-6 w-6 mb-2 ${\n                      isActive \n                        ? 'text-blue-600' \n                        : isCompleted \n                          ? 'text-green-600'\n                          : 'text-gray-400'\n                    }`} \n                  />\n                  <span className={`text-sm font-medium text-center ${\n                    isActive \n                      ? 'text-blue-800' \n                      : isCompleted \n                        ? 'text-green-800'\n                        : 'text-gray-600'\n                  }`}>\n                    {step.title}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-4xl mx-auto\">\n          {state.currentStep === 1 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Upload Your Conversation Transcript</CardTitle>\n              </CardHeader>\n              <CardContent>\n                {!state.isProcessing ? (\n                  <FileUpload onFileSelect={handleFileUpload} />\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <div className=\"animate-pulse-slow mb-4\">\n                      <FileText className=\"h-16 w-16 text-blue-500 mx-auto\" />\n                    </div>\n                    <h3 className=\"text-xl font-semibold mb-2\">Processing Your Transcript</h3>\n                    <p className=\"text-gray-600 mb-4\">\n                      {state.processingStep || 'Analyzing your conversation with Claude AI...'}\n                    </p>\n                    <div className=\"max-w-md mx-auto\">\n                      <Progress value={50} showLabel={false} />\n                    </div>\n                    <p className=\"text-sm text-gray-500 mt-3\">\n                      This may take 30-60 seconds depending on transcript length\n                    </p>\n                    {state.error && (\n                      <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n                        <p className=\"text-red-700 text-sm\">{state.error}</p>\n                        <Button\n                          variant=\"secondary\"\n                          size=\"sm\"\n                          className=\"mt-2\"\n                          onClick={() => setState(prev => ({ ...prev, error: null, isProcessing: false }))}\n                        >\n                          Try Again\n                        </Button>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 2 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Problem Statement Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">AI-Generated Problem Statement</h4>\n                    <div className=\"prose max-w-none\">\n                      <p className=\"text-gray-700 leading-relaxed\">\n                        Claude AI has analyzed your conversation transcript and generated a comprehensive problem statement document.\n                        This professional document includes executive summary, business challenges, user needs, and actionable recommendations.\n                      </p>\n                      <div className=\"mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\">\n                        <h5 className=\"font-medium text-blue-900 mb-2\">Document Includes:</h5>\n                        <ul className=\"list-disc list-inside text-blue-800 space-y-1\">\n                          <li>Executive Summary & Background Context</li>\n                          <li>Key Business Challenges with Supporting Quotes</li>\n                          <li>Core User Needs & Pain Points Analysis</li>\n                          <li>\"How Might We\" Problem Statement</li>\n                          <li>Constraints & Success Criteria</li>\n                          <li>Next Steps & Recommendations</li>\n                          <li>Key Insights & Critical Quotes</li>\n                        </ul>\n                      </div>\n                      <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                        <p className=\"text-green-800 text-sm\">\n                          ✅ Generated using Claude Sonnet 4 - Latest AI model for professional document creation\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\n                    <Button\n                      variant=\"success\"\n                      size=\"lg\"\n                      onClick={() => {\n                        if (state.documents.problemStatement) {\n                          // Download the actual generated document\n                          const element = document.createElement('a');\n                          element.href = state.documents.problemStatement;\n                          element.download = 'Problem_Statement.docx';\n                          document.body.appendChild(element);\n                          element.click();\n                          document.body.removeChild(element);\n                          success('Problem Statement document downloaded!');\n                        } else {\n                          error('Document not available. Please try uploading the transcript again.');\n                        }\n                      }}\n                      className=\"flex-1\"\n                      disabled={!state.documents.problemStatement}\n                    >\n                      Download Problem Statement (.docx)\n                    </Button>\n                  </div>\n\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                    <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                    <p className=\"text-yellow-700 mb-4\">\n                      Please review the problem statement document and confirm if it accurately captures your requirements.\n                    </p>\n                    <div className=\"flex gap-3\">\n                      <Button\n                        variant=\"success\"\n                        onClick={() => handleValidation('problemStatement', true)}\n                      >\n                        YES - Continue\n                      </Button>\n                      <Button\n                        variant=\"secondary\"\n                        onClick={() => handleValidation('problemStatement', false)}\n                      >\n                        NO - Needs Feedback\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 3 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Technical Requirements Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">AI-Generated Technical Solution Document</h4>\n                    <div className=\"prose max-w-none\">\n                      <p className=\"text-gray-700 leading-relaxed mb-4\">\n                        Claude AI has analyzed your problem statement and generated a comprehensive technical solution document.\n                        This professional document includes solution architecture, component design, and implementation roadmap.\n                      </p>\n                      <div className=\"grid md:grid-cols-2 gap-4\">\n                        <div className=\"p-4 bg-blue-50 rounded-lg\">\n                          <h5 className=\"font-medium text-blue-900 mb-2\">Architecture & Design</h5>\n                          <ul className=\"list-disc list-inside text-blue-800 space-y-1 text-sm\">\n                            <li>Solution Architecture Overview</li>\n                            <li>Component Design & Technology Choices</li>\n                            <li>Data Flow & Integration Patterns</li>\n                            <li>Cloud-Native Implementation Strategy</li>\n                          </ul>\n                        </div>\n                        <div className=\"p-4 bg-purple-50 rounded-lg\">\n                          <h5 className=\"font-medium text-purple-900 mb-2\">Requirements & Implementation</h5>\n                          <ul className=\"list-disc list-inside text-purple-800 space-y-1 text-sm\">\n                            <li>Security & Compliance Framework</li>\n                            <li>Non-Functional Requirements</li>\n                            <li>Prototype Scope & MVP Features</li>\n                            <li>Implementation Roadmap</li>\n                          </ul>\n                        </div>\n                      </div>\n                      <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                        <p className=\"text-green-800 text-sm\">\n                          ✅ Generated using Claude Sonnet 4 - Based on your problem statement analysis\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\n                    <Button\n                      variant=\"success\"\n                      size=\"lg\"\n                      onClick={() => {\n                        if (state.documents.technicalRequirements) {\n                          // Download the actual generated document\n                          const element = document.createElement('a');\n                          element.href = state.documents.technicalRequirements;\n                          element.download = 'Technical_Solution_Document.docx';\n                          document.body.appendChild(element);\n                          element.click();\n                          document.body.removeChild(element);\n                          success('Technical Solution Document downloaded!');\n                        } else {\n                          error('Document not available. Please try regenerating from the problem statement.');\n                        }\n                      }}\n                      className=\"flex-1\"\n                      disabled={!state.documents.technicalRequirements}\n                    >\n                      Download Technical Solution Document (.docx)\n                    </Button>\n                  </div>\n\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                    <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                    <p className=\"text-yellow-700 mb-4\">\n                      Please review the technical requirements document and confirm if it meets your technical specifications.\n                    </p>\n                    <div className=\"flex gap-3\">\n                      <Button\n                        variant=\"success\"\n                        onClick={() => handleValidation('technicalRequirements', true)}\n                      >\n                        YES - Continue\n                      </Button>\n                      <Button\n                        variant=\"secondary\"\n                        onClick={() => handleValidation('technicalRequirements', false)}\n                      >\n                        NO - Needs Feedback\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 4 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>System Architecture Diagram</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Generated Architecture Diagram</h4>\n                    <div className=\"bg-gray-100 rounded-lg p-8 text-center\">\n                      <Image className=\"h-24 w-24 text-gray-400 mx-auto mb-4\" />\n                      <p className=\"text-gray-600 mb-2\">Complex System Architecture</p>\n                      <p className=\"text-sm text-gray-500\">\n                        A comprehensive diagram showing system components, data flow, and integration points\n                      </p>\n                    </div>\n                    <div className=\"mt-4 grid md:grid-cols-3 gap-4\">\n                      <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\n                        <h5 className=\"font-medium text-blue-900 mb-1\">Frontend Layer</h5>\n                        <p className=\"text-sm text-blue-700\">React, TypeScript, Tailwind</p>\n                      </div>\n                      <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                        <h5 className=\"font-medium text-green-900 mb-1\">Backend Services</h5>\n                        <p className=\"text-sm text-green-700\">Node.js, Express, PostgreSQL</p>\n                      </div>\n                      <div className=\"text-center p-3 bg-purple-50 rounded-lg\">\n                        <h5 className=\"font-medium text-purple-900 mb-1\">Infrastructure</h5>\n                        <p className=\"text-sm text-purple-700\">AWS, Docker, Kubernetes</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-blue-800 mb-3\">Quick Review</h4>\n                      <p className=\"text-blue-700 mb-4\">\n                        Does the architecture diagram accurately represent your system design requirements?\n                      </p>\n                      <div className=\"flex gap-3\">\n                        <Button\n                          variant=\"success\"\n                          onClick={() => handleValidation('architectureDiagram', true)}\n                        >\n                          YES - Looks Good\n                        </Button>\n                        <Button\n                          variant=\"secondary\"\n                          onClick={() => handleValidation('architectureDiagram', false)}\n                        >\n                          NO - Needs Changes\n                        </Button>\n                      </div>\n                    </div>\n\n                    <ChatInterface\n                      onFeedback={(feedback) => {\n                        console.log('Architecture feedback:', feedback);\n                        // Handle feedback processing here\n                      }}\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 5 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Implementation Prompts</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Copy-Paste Ready Prompts</h4>\n                    <p className=\"text-gray-600 mb-6\">\n                      Here are the generated prompts for implementing your solution. Click to copy each prompt.\n                    </p>\n\n                    <div className=\"space-y-4\">\n                      {[\n                        {\n                          title: \"Frontend Development Prompt\",\n                          content: \"Create a React TypeScript application with the following components: user authentication, dashboard interface, real-time data visualization, and responsive design using Tailwind CSS...\"\n                        },\n                        {\n                          title: \"Backend API Development Prompt\",\n                          content: \"Build a Node.js Express API with the following endpoints: user management, data processing, real-time WebSocket connections, and PostgreSQL database integration...\"\n                        },\n                        {\n                          title: \"Database Schema Prompt\",\n                          content: \"Design a PostgreSQL database schema with the following tables: users, projects, data_points, and audit_logs. Include proper indexing and relationships...\"\n                        },\n                        {\n                          title: \"DevOps & Deployment Prompt\",\n                          content: \"Create Docker containers and Kubernetes deployment configurations for a scalable application with load balancing, auto-scaling, and monitoring...\"\n                        }\n                      ].map((prompt, index) => (\n                        <div key={index} className=\"border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors\">\n                          <div className=\"flex justify-between items-start mb-2\">\n                            <h5 className=\"font-medium text-gray-900\">{prompt.title}</h5>\n                            <Button\n                              size=\"sm\"\n                              variant=\"secondary\"\n                              onClick={() => {\n                                navigator.clipboard.writeText(prompt.content);\n                                success('Prompt copied to clipboard!');\n                              }}\n                            >\n                              Copy\n                            </Button>\n                          </div>\n                          <p className=\"text-sm text-gray-600 line-clamp-3\">\n                            {prompt.content}\n                          </p>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 text-center\">\n                    <CheckCircle className=\"h-12 w-12 text-green-500 mx-auto mb-3\" />\n                    <h4 className=\"font-semibold text-green-800 mb-2\">Process Complete!</h4>\n                    <p className=\"text-green-700\">\n                      Your rapid prototyping automation is complete. Use the prompts above to implement your solution.\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAiCA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,8MAAA,CAAA,WAAQ;QAAE,aAAa;IAAsC;IACxG;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,2NAAA,CAAA,cAAW;QAAE,aAAa;IAAwC;IAC7G;QAAE,IAAI;QAAG,OAAO;QAA0B,MAAM,0MAAA,CAAA,WAAQ;QAAE,aAAa;IAAyC;IAChH;QAAE,IAAI;QAAG,OAAO;QAAwB,MAAM,oMAAA,CAAA,QAAK;QAAE,aAAa;IAA6B;IAC/F;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,kMAAA,CAAA,OAAI;QAAE,aAAa;IAA8B;CAC7F;AAEM,MAAM,sBAAgC;IAC3C,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAC3C,aAAa;QACb,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,OAAO;QACP,WAAW;YACT,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;YACrB,SAAS;QACX;QACA,aAAa;YACX,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;YACT,CAAC;QAED,KAAK,gDAAgD;QAErD,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAyC,CAAC;YAEvF,sCAAsC;YACtC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAA2C,CAAC;YAEzF,6CAA6C;YAC7C,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,uCAAuC,CAAC;gBACjH,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAyB,CAAC;YAEvE,qCAAqC;YACrC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,cAAc,IAAI,eAAe,CAAC;YAExC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,kBAAkB;oBACpB;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,OAAO,cAA6C;QAC3E,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;oBACX,GAAG,KAAK,WAAW;oBACnB,CAAC,aAAa,EAAE;gBAClB;YACF,CAAC;QAED,IAAI,SAAS;YACX,MAAM,WAAW,MAAM,WAAW,GAAG;YAErC,IAAI,aAAa,KAAK,iBAAiB,oBAAoB;gBACzD,yDAAyD;gBACzD,MAAM;YACR,OAAO,IAAI,YAAY,GAAG;gBACxB,sCAAsC;gBACtC,SAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;wBAAM,gBAAgB;oBAA8B,CAAC;gBAChG,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,IAAI,eAAe;oBAAE,GAAG,MAAM,SAAS;gBAAC;gBAExC,IAAI,aAAa,GAAG;oBAClB,aAAa,mBAAmB,GAAG;gBACrC,OAAO,IAAI,aAAa,GAAG;oBACzB,aAAa,OAAO,GAAG;wBAAC;wBAAe;wBAAe;qBAAc;gBACtE;gBAEA,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,aAAa;wBACb,WAAW;wBACX,cAAc;wBACd,gBAAgB;oBAClB,CAAC;YACH;QACF;IACF;IAEA,MAAM,gCAAgC;QACpC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,gBAAgB;gBAChB,OAAO;YACT,CAAC;QAED,IAAI;YACF,sDAAsD;YACtD,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAgD,CAAC;YAE9F,IAAI,0BAA0B;YAE9B,IAAI,MAAM,SAAS,CAAC,gBAAgB,EAAE;gBACpC,+EAA+E;gBAC/E,wEAAwE;gBACxE,0BAA0B,CAAC;;;;;;;;;;;;kOAY+L,CAAC;YAC7N;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAgD,CAAC;YAE9F,sCAAsC;YACtC,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,yBAAyB;gBAC3B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,4CAA4C,CAAC;gBACtH,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAmC,CAAC;YAEjF,qCAAqC;YACrC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,cAAc,IAAI,eAAe,CAAC;YAExC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,uBAAuB;oBACzB;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,kBAAkB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,WAAW;IACxE,MAAM,WAAW,AAAC,CAAC,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK;IAElE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,UAAU;;;;;;0BAC1C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,SAAS;4BACT,OAAO,CAAC,KAAK,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,iBAAiB,OAAO;4BAChF,WAAU;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC;gCACV,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,KAAK,EAAE,KAAK,MAAM,WAAW;gCAC9C,MAAM,cAAc,KAAK,EAAE,GAAG,MAAM,WAAW;gCAE/C,qBACE,8OAAC;oCAEC,WAAW,CAAC,uEAAuE,EACjF,WACI,yCACA,cACE,2CACA,wCACN;;sDAEF,8OAAC;4CACC,WAAW,CAAC,aAAa,EACvB,WACI,kBACA,cACE,mBACA,iBACN;;;;;;sDAEJ,8OAAC;4CAAK,WAAW,CAAC,gCAAgC,EAChD,WACI,kBACA,cACE,mBACA,iBACN;sDACC,KAAK,KAAK;;;;;;;mCAzBR,KAAK,EAAE;;;;;4BA6BlB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,WAAW,KAAK,mBACrB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACT,CAAC,MAAM,YAAY,iBAClB,8OAAC,sIAAA,CAAA,aAAU;4CAAC,cAAc;;;;;iEAE1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DACV,MAAM,cAAc,IAAI;;;;;;8DAE3B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO;wDAAI,WAAW;;;;;;;;;;;8DAElC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;gDAGzC,MAAM,KAAK,kBACV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB,MAAM,KAAK;;;;;;sEAChD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,SAAS,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO;wEAAM,cAAc;oEAAM,CAAC;sEAC/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAWd,MAAM,WAAW,KAAK,mBACrB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAI7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;;;;;;;;;;;;;8EAGR,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;8DAO5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,IAAI,MAAM,SAAS,CAAC,gBAAgB,EAAE;gEACpC,yCAAyC;gEACzC,MAAM,UAAU,SAAS,aAAa,CAAC;gEACvC,QAAQ,IAAI,GAAG,MAAM,SAAS,CAAC,gBAAgB;gEAC/C,QAAQ,QAAQ,GAAG;gEACnB,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ,KAAK;gEACb,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ;4DACV,OAAO;gEACL,MAAM;4DACR;wDACF;wDACA,WAAU;wDACV,UAAU,CAAC,MAAM,SAAS,CAAC,gBAAgB;kEAC5C;;;;;;;;;;;8DAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;8EAGD,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUZ,MAAM,WAAW,KAAK,mBACrB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAqC;;;;;;8EAIlD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAAiC;;;;;;8FAC/C,8OAAC;oFAAG,WAAU;;sGACZ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;;;;;;;;;;;;;sFAGR,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAAmC;;;;;;8FACjD,8OAAC;oFAAG,WAAU;;sGACZ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;;;;;;;;;;;;;;;;;;;8EAIV,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;8DAO5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,IAAI,MAAM,SAAS,CAAC,qBAAqB,EAAE;gEACzC,yCAAyC;gEACzC,MAAM,UAAU,SAAS,aAAa,CAAC;gEACvC,QAAQ,IAAI,GAAG,MAAM,SAAS,CAAC,qBAAqB;gEACpD,QAAQ,QAAQ,GAAG;gEACnB,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ,KAAK;gEACb,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ;4DACV,OAAO;gEACL,MAAM;4DACR;wDACF;wDACA,WAAU;wDACV,UAAU,CAAC,MAAM,SAAS,CAAC,qBAAqB;kEACjD;;;;;;;;;;;8DAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;8EAGD,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUZ,MAAM,WAAW,KAAK,mBACrB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAClC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAIvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAkC;;;;;;sFAChD,8OAAC;4EAAE,WAAU;sFAAyB;;;;;;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAmC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAA0B;;;;;;;;;;;;;;;;;;;;;;;;8DAK7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAGlC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;sFAGD,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;;;;;;;;;;;;;sEAML,8OAAC,yIAAA,CAAA,gBAAa;4DACZ,YAAY,CAAC;gEACX,QAAQ,GAAG,CAAC,0BAA0B;4DACtC,kCAAkC;4DACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQX,MAAM,WAAW,KAAK,mBACrB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAE,WAAU;sEAAqB;;;;;;sEAIlC,8OAAC;4DAAI,WAAU;sEACZ;gEACC;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;6DACD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAA6B,OAAO,KAAK;;;;;;8FACvD,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS;wFACP,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;wFAC5C,QAAQ;oFACV;8FACD;;;;;;;;;;;;sFAIH,8OAAC;4EAAE,WAAU;sFACV,OAAO,OAAO;;;;;;;mEAfT;;;;;;;;;;;;;;;;8DAsBhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlD", "debugId": null}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { RapidPrototypingApp } from '@/components/RapidPrototypingApp';\n\nexport default function Home() {\n  return <RapidPrototypingApp />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,yIAAA,CAAA,sBAAmB;;;;;AAC7B", "debugId": null}}]}