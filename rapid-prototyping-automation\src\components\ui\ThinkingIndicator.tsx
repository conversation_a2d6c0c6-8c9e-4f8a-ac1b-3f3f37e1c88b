import React, { useState, useEffect } from 'react';
import { Brain, Lightbulb, Zap, Cpu, Sparkles } from 'lucide-react';

interface ThinkingIndicatorProps {
  step: 'transcript' | 'technical' | 'architecture' | 'prompts';
  className?: string;
}

const THINKING_MESSAGES = {
  transcript: [
    "🧠 Reading your conversation transcript...",
    "🔍 Identifying key stakeholders and pain points...",
    "💡 Extracting business challenges...",
    "📝 Crafting executive summary...",
    "🎯 Formulating 'How Might We' statements...",
    "✨ Polishing the problem statement...",
    "🚀 Almost done with the analysis..."
  ],
  technical: [
    "🏗️ Analyzing problem statement architecture needs...",
    "⚡ Designing cloud-native solution components...",
    "🔧 Selecting optimal technology stack...",
    "🌐 Mapping data flow and integration patterns...",
    "🔒 Defining security and compliance framework...",
    "📊 Calculating scalability requirements...",
    "🎯 Scoping MVP features and timeline...",
    "📋 Generating implementation roadmap..."
  ],
  architecture: [
    "🎨 Sketching system architecture diagrams...",
    "🔗 Connecting microservices and APIs...",
    "☁️ Optimizing cloud infrastructure layout...",
    "🔄 Designing data pipelines...",
    "🛡️ Adding security layers...",
    "📈 Planning for scale and performance..."
  ],
  prompts: [
    "💻 Crafting development prompts...",
    "🔨 Preparing implementation guides...",
    "📚 Organizing code templates...",
    "🎯 Finalizing copy-paste instructions..."
  ]
};

const THINKING_ICONS = [Brain, Lightbulb, Zap, Cpu, Sparkles];

export const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ 
  step, 
  className = '' 
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [currentIconIndex, setCurrentIconIndex] = useState(0);
  const [dots, setDots] = useState('');

  const messages = THINKING_MESSAGES[step];
  const CurrentIcon = THINKING_ICONS[currentIconIndex];

  useEffect(() => {
    // Rotate messages every 2.5 seconds
    const messageInterval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % messages.length);
    }, 2500);

    // Rotate icons every 1.5 seconds
    const iconInterval = setInterval(() => {
      setCurrentIconIndex((prev) => (prev + 1) % THINKING_ICONS.length);
    }, 1500);

    // Animate dots every 500ms
    const dotsInterval = setInterval(() => {
      setDots((prev) => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => {
      clearInterval(messageInterval);
      clearInterval(iconInterval);
      clearInterval(dotsInterval);
    };
  }, [messages.length]);

  return (
    <div className={`text-center py-8 ${className}`}>
      {/* Animated Icon */}
      <div className="relative mb-6">
        <div className="animate-pulse-slow mb-4">
          <CurrentIcon className="h-16 w-16 text-blue-500 mx-auto animate-bounce" />
        </div>
        
        {/* Thinking bubbles animation */}
        <div className="absolute -top-2 -right-2">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-ping" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-ping" style={{ animationDelay: '200ms' }}></div>
            <div className="w-2 h-2 bg-pink-400 rounded-full animate-ping" style={{ animationDelay: '400ms' }}></div>
          </div>
        </div>
      </div>

      {/* Main thinking message */}
      <h3 className="text-xl font-semibold mb-3 text-gray-800">
        Claude is thinking{dots}
      </h3>

      {/* Dynamic message */}
      <div className="min-h-[60px] flex items-center justify-center">
        <p className="text-gray-600 text-lg animate-fade-in max-w-md mx-auto">
          {messages[currentMessageIndex]}
        </p>
      </div>

      {/* Progress indicator */}
      <div className="mt-6 max-w-md mx-auto">
        <div className="flex justify-between text-xs text-gray-500 mb-2">
          <span>Processing...</span>
          <span>{Math.round(((currentMessageIndex + 1) / messages.length) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000 ease-out"
            style={{ width: `${((currentMessageIndex + 1) / messages.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Fun fact */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-lg mx-auto">
        <p className="text-blue-800 text-sm">
          <Sparkles className="inline h-4 w-4 mr-1" />
          <strong>Did you know?</strong> Claude processes thousands of tokens per second to understand context and generate human-like responses!
        </p>
      </div>

      {/* Estimated time */}
      <p className="text-sm text-gray-500 mt-4">
        ⏱️ This usually takes 30-60 seconds depending on content complexity
      </p>
    </div>
  );
};
