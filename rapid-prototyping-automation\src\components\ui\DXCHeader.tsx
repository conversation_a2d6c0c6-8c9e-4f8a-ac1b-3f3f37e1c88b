import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface DXCHeaderProps {
  className?: string;
}

export const DXCHeader: React.FC<DXCHeaderProps> = ({ className }) => {
  return (
    <header className={cn('dxc-header', className)}>
      <div className="dxc-container">
        <div className="flex items-center justify-between">
          <Link 
            href="/" 
            className="dxc-logo-link"
            aria-label="DXC Technology - Go to homepage"
          >
            {/* DXC Logo SVG - Inline for better control */}
            <svg 
              className="dxc-logo" 
              width="120" 
              height="40" 
              viewBox="0 0 120 40" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
              role="img"
              aria-labelledby="dxc-logo-title"
            >
              <title id="dxc-logo-title">DXC Technology</title>
              
              {/* DXC Logo - Simplified version */}
              <g fill="var(--dxc-grey-900)">
                {/* D */}
                <path d="M0 8h8.5c6.9 0 12.5 5.6 12.5 12.5S15.4 33 8.5 33H0V8zm6 20h2.5c3.6 0 6.5-2.9 6.5-6.5S12.1 15 8.5 15H6v13z"/>
                
                {/* X */}
                <path d="M35 8l6 8.5L47 8h7l-9 12.5L54 33h-7l-6-8.5L35 33h-7l9-12.5L28 8h7z"/>
                
                {/* C */}
                <path d="M70 15c-1.7-1.7-4-2.7-6.5-2.7-5.2 0-9.5 4.3-9.5 9.5s4.3 9.5 9.5 9.5c2.5 0 4.8-1 6.5-2.7l4.2 4.2c-2.8 2.8-6.7 4.5-10.7 4.5-8.3 0-15-6.7-15-15s6.7-15 15-15c4 0 7.9 1.7 10.7 4.5L70 15z"/>
              </g>
              
              {/* Technology text */}
              <g fill="var(--dxc-grey-700)" fontSize="8" fontFamily="Open Sans, sans-serif">
                <text x="0" y="40" fontSize="6">Technology</text>
              </g>
            </svg>
          </Link>
          
          {/* Navigation could go here */}
          <nav className="hidden md:flex items-center space-x-8" role="navigation" aria-label="Main navigation">
            {/* Navigation items would go here */}
          </nav>
        </div>
      </div>
    </header>
  );
};

// Alternative component for when you have the actual DXC logo file
interface DXCLogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export const DXCLogo: React.FC<DXCLogoProps> = ({ 
  className, 
  width = 120, 
  height = 40 
}) => {
  return (
    <Link 
      href="/" 
      className={cn('dxc-logo-link', className)}
      aria-label="DXC Technology - Go to homepage"
    >
      {/* When you have the actual dxc-logo-black.svg file, use this: */}
      {/* <img 
        src="/images/dxc-logo-black.svg" 
        alt="DXC Technology" 
        className="dxc-logo"
        width={width}
        height={height}
      /> */}
      
      {/* Fallback SVG logo for now */}
      <svg 
        className="dxc-logo" 
        width={width} 
        height={height} 
        viewBox="0 0 120 40" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        role="img"
        aria-labelledby="dxc-logo-title-2"
      >
        <title id="dxc-logo-title-2">DXC Technology</title>
        
        {/* DXC Logo */}
        <g fill="var(--dxc-grey-900)">
          {/* D */}
          <path d="M0 8h8.5c6.9 0 12.5 5.6 12.5 12.5S15.4 33 8.5 33H0V8zm6 20h2.5c3.6 0 6.5-2.9 6.5-6.5S12.1 15 8.5 15H6v13z"/>
          
          {/* X */}
          <path d="M35 8l6 8.5L47 8h7l-9 12.5L54 33h-7l-6-8.5L35 33h-7l9-12.5L28 8h7z"/>
          
          {/* C */}
          <path d="M70 15c-1.7-1.7-4-2.7-6.5-2.7-5.2 0-9.5 4.3-9.5 9.5s4.3 9.5 9.5 9.5c2.5 0 4.8-1 6.5-2.7l4.2 4.2c-2.8 2.8-6.7 4.5-10.7 4.5-8.3 0-15-6.7-15-15s6.7-15 15-15c4 0 7.9 1.7 10.7 4.5L70 15z"/>
        </g>
        
        {/* Technology text */}
        <g fill="var(--dxc-grey-700)">
          <text x="0" y="40" fontSize="6" fontFamily="Open Sans, sans-serif">Technology</text>
        </g>
      </svg>
    </Link>
  );
};
