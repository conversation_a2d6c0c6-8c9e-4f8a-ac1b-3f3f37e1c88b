import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface DXCHeaderProps {
  className?: string;
}

export const DXCHeader: React.FC<DXCHeaderProps> = ({ className }) => {
  return (
    <header className={cn('dxc-header', className)}>
      <div className="dxc-container">
        <div className="flex items-center justify-between">
          <Link 
            href="/" 
            className="dxc-logo-link"
            aria-label="DXC Technology - Go to homepage"
          >
            {/* Official DXC Technology Logo - Exact Match */}
            <svg
              className="dxc-logo"
              width="280"
              height="40"
              viewBox="0 0 280 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              role="img"
              aria-labelledby="dxc-logo-title"
            >
              <title id="dxc-logo-title">DXC Technology</title>

              {/* DXC Logo - Exact Recreation */}
              <g>
                {/* D - Modern geometric design */}
                <path d="M0 8h14c8.837 0 16 7.163 16 16s-7.163 16-16 16H0V8zm6 26h8c5.523 0 10-4.477 10-10s-4.477-10-10-10H6v20z" fill="#7c3aed"/>

                {/* X - Angular crossed design */}
                <g fill="#7c3aed">
                  <path d="M38 8h6l8 12 8-12h6L54 24l12 16h-6l-8-12-8 12h-6l12-16L38 8z"/>
                </g>

                {/* C - Modern curved design */}
                <path d="M84 14c-3-3-7-5-12-5-8.284 0-15 6.716-15 15s6.716 15 15 15c5 0 9-2 12-5l4 4c-4 4-10 7-16 7-11.046 0-20-8.954-20-20S60.954 4 72 4c6 0 12 3 16 7l-4 3z" fill="#7c3aed"/>
              </g>

              {/* TECHNOLOGY text - Exact spacing and weight */}
              <g fill="#000000">
                <text x="120" y="26" fontSize="16" fontFamily="Open Sans, sans-serif" fontWeight="700" letterSpacing="0.15em">TECHNOLOGY</text>
              </g>
            </svg>
          </Link>
          
          {/* Navigation could go here */}
          <nav className="hidden md:flex items-center space-x-8" role="navigation" aria-label="Main navigation">
            {/* Navigation items would go here */}
          </nav>
        </div>
      </div>
    </header>
  );
};

// Alternative component for when you have the actual DXC logo file
interface DXCLogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export const DXCLogo: React.FC<DXCLogoProps> = ({
  className,
  width = 280,
  height = 40
}) => {
  return (
    <Link 
      href="/" 
      className={cn('dxc-logo-link', className)}
      aria-label="DXC Technology - Go to homepage"
    >
      {/* When you have the actual dxc-logo-black.svg file, use this: */}
      {/* <img 
        src="/images/dxc-logo-black.svg" 
        alt="DXC Technology" 
        className="dxc-logo"
        width={width}
        height={height}
      /> */}
      
      {/* Official DXC Technology Logo - Exact Match */}
      <svg
        className="dxc-logo"
        width={width}
        height={height}
        viewBox="0 0 280 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        role="img"
        aria-labelledby="dxc-logo-title-2"
      >
        <title id="dxc-logo-title-2">DXC Technology</title>

        {/* DXC Logo - Exact Recreation */}
        <g>
          {/* D - Modern geometric design */}
          <path d="M0 8h14c8.837 0 16 7.163 16 16s-7.163 16-16 16H0V8zm6 26h8c5.523 0 10-4.477 10-10s-4.477-10-10-10H6v20z" fill="#7c3aed"/>

          {/* X - Angular crossed design */}
          <g fill="#7c3aed">
            <path d="M38 8h6l8 12 8-12h6L54 24l12 16h-6l-8-12-8 12h-6l12-16L38 8z"/>
          </g>

          {/* C - Modern curved design */}
          <path d="M84 14c-3-3-7-5-12-5-8.284 0-15 6.716-15 15s6.716 15 15 15c5 0 9-2 12-5l4 4c-4 4-10 7-16 7-11.046 0-20-8.954-20-20S60.954 4 72 4c6 0 12 3 16 7l-4 3z" fill="#7c3aed"/>
        </g>

        {/* TECHNOLOGY text - Exact spacing and weight */}
        <g fill="#000000">
          <text x="120" y="26" fontSize="16" fontFamily="Open Sans, sans-serif" fontWeight="700" letterSpacing="0.15em">TECHNOLOGY</text>
        </g>
      </svg>
    </Link>
  );
};
