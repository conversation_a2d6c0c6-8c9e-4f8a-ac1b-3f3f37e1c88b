import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface DXCHeaderProps {
  className?: string;
}

export const DXCHeader: React.FC<DXCHeaderProps> = ({ className }) => {
  return (
    <header className={cn('dxc-header', className)}>
      <div className="dxc-container">
        <div className="flex items-center justify-between">
          <Link 
            href="/" 
            className="dxc-logo-link"
            aria-label="DXC Technology - Go to homepage"
          >
            {/* Official DXC Technology Logo */}
            <svg
              className="dxc-logo"
              width="200"
              height="40"
              viewBox="0 0 200 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              role="img"
              aria-labelledby="dxc-logo-title"
            >
              <title id="dxc-logo-title">DXC Technology</title>

              {/* DXC Logo - Official Design */}
              <g>
                {/* D */}
                <path d="M0 5h12c8.284 0 15 6.716 15 15s-6.716 15-15 15H0V5zm8 22h4c4.418 0 8-3.582 8-8s-3.582-8-8-8H8v16z" fill="#7d2fd0"/>

                {/* X */}
                <path d="M35 5l8 12L51 5h9l-12 15L60 35h-9l-8-12L35 35h-9l12-15L26 5h9z" fill="#7d2fd0"/>

                {/* C */}
                <path d="M75 12c-2.5-2.5-6-4-10-4-7.732 0-14 6.268-14 14s6.268 14 14 14c4 0 7.5-1.5 10-4l5 5c-4 4-9.5 6-15 6-11.046 0-20-8.954-20-20S54.954 3 66 3c5.5 0 11 2 15 6l-6 3z" fill="#7d2fd0"/>
              </g>

              {/* TECHNOLOGY text */}
              <g fill="#1f2937">
                <text x="100" y="15" fontSize="14" fontFamily="Open Sans, sans-serif" fontWeight="700" letterSpacing="0.1em">TECHNOLOGY</text>
              </g>
            </svg>
          </Link>
          
          {/* Navigation could go here */}
          <nav className="hidden md:flex items-center space-x-8" role="navigation" aria-label="Main navigation">
            {/* Navigation items would go here */}
          </nav>
        </div>
      </div>
    </header>
  );
};

// Alternative component for when you have the actual DXC logo file
interface DXCLogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export const DXCLogo: React.FC<DXCLogoProps> = ({
  className,
  width = 200,
  height = 40
}) => {
  return (
    <Link 
      href="/" 
      className={cn('dxc-logo-link', className)}
      aria-label="DXC Technology - Go to homepage"
    >
      {/* When you have the actual dxc-logo-black.svg file, use this: */}
      {/* <img 
        src="/images/dxc-logo-black.svg" 
        alt="DXC Technology" 
        className="dxc-logo"
        width={width}
        height={height}
      /> */}
      
      {/* Official DXC Technology Logo */}
      <svg
        className="dxc-logo"
        width={width}
        height={height}
        viewBox="0 0 200 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        role="img"
        aria-labelledby="dxc-logo-title-2"
      >
        <title id="dxc-logo-title-2">DXC Technology</title>

        {/* DXC Logo - Official Design */}
        <g>
          {/* D */}
          <path d="M0 5h12c8.284 0 15 6.716 15 15s-6.716 15-15 15H0V5zm8 22h4c4.418 0 8-3.582 8-8s-3.582-8-8-8H8v16z" fill="#7d2fd0"/>

          {/* X */}
          <path d="M35 5l8 12L51 5h9l-12 15L60 35h-9l-8-12L35 35h-9l12-15L26 5h9z" fill="#7d2fd0"/>

          {/* C */}
          <path d="M75 12c-2.5-2.5-6-4-10-4-7.732 0-14 6.268-14 14s6.268 14 14 14c4 0 7.5-1.5 10-4l5 5c-4 4-9.5 6-15 6-11.046 0-20-8.954-20-20S54.954 3 66 3c5.5 0 11 2 15 6l-6 3z" fill="#7d2fd0"/>
        </g>

        {/* TECHNOLOGY text */}
        <g fill="#1f2937">
          <text x="100" y="15" fontSize="14" fontFamily="Open Sans, sans-serif" fontWeight="700" letterSpacing="0.1em">TECHNOLOGY</text>
        </g>
      </svg>
    </Link>
  );
};
