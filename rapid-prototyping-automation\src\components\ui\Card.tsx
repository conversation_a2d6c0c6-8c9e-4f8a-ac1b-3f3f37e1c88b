import React from 'react';
import { cn } from '@/lib/utils';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  gradient?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  gradient = false,
  padding = 'md'
}) => {
  const getDXCPadding = () => {
    switch (padding) {
      case 'sm':
        return 'var(--dxc-space-4)';
      case 'lg':
        return 'var(--dxc-space-8)';
      default:
        return 'var(--dxc-space-6)';
    }
  };

  return (
    <div
      className={cn('dxc-surface-primary', className)}
      style={{
        padding: getDXCPadding(),
        borderRadius: 'var(--dxc-space-2)',
        backgroundColor: gradient ? 'var(--dxc-purple-50)' : 'var(--dxc-white)',
        border: gradient ? '1px solid var(--dxc-purple-200)' : '1px solid var(--dxc-grey-200)',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
      }}
    >
      {children}
    </div>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className
}) => {
  return (
    <div
      className={cn(className)}
      style={{
        marginBottom: 'var(--dxc-space-6)',
        paddingBottom: 'var(--dxc-space-4)',
        borderBottom: '1px solid var(--dxc-grey-200)'
      }}
    >
      {children}
    </div>
  );
};

interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
}

export const CardTitle: React.FC<CardTitleProps> = ({
  children,
  className
}) => {
  return (
    <h3
      className={cn(className)}
      style={{
        fontSize: 'var(--dxc-text-2xl)',
        fontWeight: '600',
        color: 'var(--dxc-grey-900)',
        lineHeight: '1.3',
        margin: '0'
      }}
    >
      {children}
    </h3>
  );
};

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  className
}) => {
  return (
    <div className={cn('text-gray-600', className)}>
      {children}
    </div>
  );
};
