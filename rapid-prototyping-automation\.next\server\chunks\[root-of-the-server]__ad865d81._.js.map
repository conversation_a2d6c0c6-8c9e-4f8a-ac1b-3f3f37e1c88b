{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/generate-problem-statement/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\nimport { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';\nimport mammoth from 'mammoth';\n\n// For development environments with SSL certificate issues\nif (process.env.NODE_ENV === 'development') {\n  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n}\n\n// Check if API key is configured\nif (!process.env.ANTHROPIC_API_KEY) {\n  console.error('ANTHROPIC_API_KEY is not configured in environment variables');\n}\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',\n});\n\nconst PROBLEM_STATEMENT_PROMPT = `You are a top 0.1% senior innovation consultant and professional technical writer. \n\n1. Load & Analyze  \n   - Open and read the entire transcript file attached.  \n   - Strip out all timestamps; focus only on speaker content, context, and meaning.\n\n2. Deep Content Extraction\n   - Identify every meeting attendee from the transcript  \n   - Identify every business challenge the customer articulates.  \n   - Capture direct quotes (paraphrased or exact) that illustrate those pain points.  \n   - List all attendees from DXC proposed solution ideas, including any technical requirements mentioned.  \n   - Note CTO's facilitation questions, concerns, and prioritization recommendations.\n\n3. Craft a Polished Analysis\n   - Build a structured analysis with these sections:\n     1. Title and Participants\n        - Infer the title from the intended solution outlined in the transcript and propose a catchy title  \n        - List all attendees and their roles\n     2. Executive Summary (1 paragraph)  \n        - Summarize the workshop goal and outcome in 3–4 sentences—no timestamps.  \n     3. Background & Context (½ page)  \n        - Why this session was convened, the user's strategic imperatives, and desired outcomes.  \n     4. Key Business Challenges  \n        - For each challenge, include:  \n          - Name of Challenge  \n          - Description (2–3 sentences) with a representative quote.  \n     5. Core User Needs & Pain Points  \n        - List of needs and pain points with supporting details or quotes\n     6. How Might We… Problem Statement  \n        - Craft one compelling HMW sentence that unifies all the challenges and needs.  \n     7. Constraints & Success Criteria  \n        - List constraints and corresponding success criteria\n     8. Next Steps & Recommendations (1 page)  \n        - Three to five actionable recommendations to guide prototype/MVP design, each with rationale.  \n     9. Key Insights & Quotes  \n        - A list of the 5–7 most critical quotes or insights—no timestamps, just speaker name and content.\n\n4. Output Format\n   - Provide the response in structured JSON format with clear sections\n   - No timestamps or file metadata in the response\n   - Focus on actionable insights and clear problem articulation\n\nPlease analyze the transcript and provide a comprehensive problem statement analysis.`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    // Check if API key is configured\n    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo document');\n\n      // For development, create a mock document\n      const mockDoc = await createMockDocument(file.name);\n      const buffer = await Packer.toBuffer(mockDoc);\n\n      return new NextResponse(buffer, {\n        headers: {\n          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n          'Content-Disposition': 'attachment; filename=\"Problem_Statement_Demo.docx\"',\n        },\n      });\n    }\n\n    // Read file content\n    let transcriptContent = '';\n\n    console.log(`Processing file: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);\n\n    try {\n      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n        console.log('Reading as plain text file');\n        transcriptContent = await file.text();\n      } else if (file.name.endsWith('.docx')) {\n        console.log('Reading as DOCX file');\n        const arrayBuffer = await file.arrayBuffer();\n        const result = await mammoth.extractRawText({ buffer: Buffer.from(arrayBuffer) });\n        transcriptContent = result.value;\n      } else {\n        console.log('Reading as generic text file');\n        // For other formats, try to read as text\n        transcriptContent = await file.text();\n      }\n    } catch (fileError) {\n      console.error('Error reading file content:', fileError);\n      return NextResponse.json({ error: 'Failed to read file content' }, { status: 400 });\n    }\n\n    console.log(`Extracted content length: ${transcriptContent.length} characters`);\n    console.log(`Content preview: ${transcriptContent.substring(0, 200)}...`);\n\n    if (!transcriptContent.trim()) {\n      console.error('File content is empty after extraction');\n      return NextResponse.json({ error: 'File appears to be empty or unreadable' }, { status: 400 });\n    }\n\n    // Call Claude API\n    console.log('Preparing to call Claude API...');\n    const fullPrompt = `${PROBLEM_STATEMENT_PROMPT}\\n\\nTranscript Content:\\n${transcriptContent}`;\n    console.log(`Full prompt length: ${fullPrompt.length} characters`);\n\n    let response;\n    try {\n      console.log('Calling Claude API with Sonnet 3.5 (fallback)...');\n      response = await anthropic.messages.create({\n        model: 'claude-3-5-sonnet-20241022',\n        max_tokens: 4000,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n      console.log('Claude API call successful');\n    } catch (apiError: any) {\n      console.error('Claude API Error:', apiError);\n\n      // Handle specific API errors\n      if (apiError.status === 401) {\n        return NextResponse.json({\n          error: 'Invalid API key. Please check your Anthropic API key configuration.'\n        }, { status: 401 });\n      } else if (apiError.status === 429) {\n        return NextResponse.json({\n          error: 'Rate limit exceeded. Please try again in a few minutes.'\n        }, { status: 429 });\n      } else if (apiError.message?.includes('model')) {\n        return NextResponse.json({\n          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.'\n        }, { status: 400 });\n      } else if (apiError.code === 'UNABLE_TO_GET_ISSUER_CERT_LOCALLY') {\n        return NextResponse.json({\n          error: 'SSL certificate issue. This is a development environment issue.'\n        }, { status: 500 });\n      } else {\n        return NextResponse.json({\n          error: `Claude API error: ${apiError.message || 'Unknown error'}`\n        }, { status: 500 });\n      }\n    }\n\n    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    // Parse the analysis and create Word document\n    const doc = await createWordDocument(analysisText, file.name);\n    \n    // Generate the document buffer\n    const buffer = await Packer.toBuffer(doc);\n    \n    // Return the document as a downloadable file\n    return new NextResponse(buffer, {\n      headers: {\n        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'Content-Disposition': 'attachment; filename=\"Problem_Statement.docx\"',\n      },\n    });\n\n  } catch (error) {\n    console.error('Error generating problem statement:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate problem statement' }, \n      { status: 500 }\n    );\n  }\n}\n\nasync function createWordDocument(analysisText: string, originalFileName: string): Promise<Document> {\n  // Parse the analysis text to extract structured information\n  const sections = parseAnalysisText(analysisText);\n  \n  const doc = new Document({\n    sections: [{\n      properties: {},\n      children: [\n        // Title Page\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.title || \"Problem Statement Analysis\",\n              bold: true,\n              size: 32,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.TITLE,\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 400 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: `Analysis Date: ${new Date().toLocaleDateString()}`,\n              size: 24\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: `Source: ${originalFileName}`,\n              size: 20,\n              italics: true\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 800 }\n        }),\n\n        // Executive Summary\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Executive Summary\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.executiveSummary || \"Executive summary will be generated based on transcript analysis.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Background & Context\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Background & Context\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.background || \"Background and context will be extracted from the transcript analysis.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Key Business Challenges\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Key Business Challenges\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.challenges || \"Business challenges will be identified and detailed from the transcript.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Problem Statement\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"How Might We… Problem Statement\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.problemStatement || \"How might we address the core challenges identified in this analysis?\",\n              size: 22,\n              bold: true,\n              color: \"DC2626\"\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Next Steps\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Next Steps & Recommendations\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.nextSteps || \"Actionable recommendations will be provided based on the analysis.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Raw Analysis\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Detailed Analysis\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: analysisText,\n              size: 20\n            })\n          ],\n          spacing: { after: 400 }\n        })\n      ]\n    }]\n  });\n\n  return doc;\n}\n\nfunction parseAnalysisText(text: string) {\n  // Simple parsing logic - in a real implementation, you'd want more sophisticated parsing\n  const sections: any = {};\n  \n  // Try to extract key sections from the Claude response\n  const titleMatch = text.match(/title[:\\s]*([^\\n]+)/i);\n  sections.title = titleMatch ? titleMatch[1].trim() : null;\n  \n  const summaryMatch = text.match(/executive summary[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.executiveSummary = summaryMatch ? summaryMatch[1].trim() : null;\n  \n  const backgroundMatch = text.match(/background[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.background = backgroundMatch ? backgroundMatch[1].trim() : null;\n  \n  const challengesMatch = text.match(/challenges[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.challenges = challengesMatch ? challengesMatch[1].trim() : null;\n  \n  const problemMatch = text.match(/how might we[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.problemStatement = problemMatch ? problemMatch[1].trim() : null;\n  \n  const stepsMatch = text.match(/next steps[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.nextSteps = stepsMatch ? stepsMatch[1].trim() : null;\n  \n  return sections;\n}\n\nasync function createMockDocument(originalFileName: string): Promise<Document> {\n  const doc = new Document({\n    sections: [{\n      properties: {},\n      children: [\n        // Title Page\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Problem Statement Analysis - Demo\",\n              bold: true,\n              size: 32,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.TITLE,\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 400 }\n        }),\n\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: `Analysis Date: ${new Date().toLocaleDateString()}`,\n              size: 24\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 200 }\n        }),\n\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: `Source: ${originalFileName}`,\n              size: 20,\n              italics: true\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 800 }\n        }),\n\n        // Notice\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"⚠️ Demo Mode - Claude API Not Configured\",\n              bold: true,\n              size: 24,\n              color: \"DC2626\"\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 400 }\n        }),\n\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"This is a demonstration document. To generate real AI-powered problem statements, please configure your Anthropic API key in the .env.local file.\",\n              size: 22,\n              color: \"DC2626\"\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 600 }\n        }),\n\n        // Sample Content\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Executive Summary\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"This is a sample problem statement document. When Claude AI is properly configured, this section will contain a comprehensive analysis of your conversation transcript, including identified challenges, stakeholder needs, and strategic recommendations.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Setup Instructions\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Setup Instructions\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"1. Get your Anthropic API key from https://console.anthropic.com/\\n2. Create a .env.local file in your project root\\n3. Add: ANTHROPIC_API_KEY=your-api-key-here\\n4. Restart your development server\\n5. Upload a transcript to generate real AI analysis\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        })\n      ]\n    }]\n  });\n\n  return doc;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,2DAA2D;AAC3D,wCAA4C;IAC1C,QAAQ,GAAG,CAAC,4BAA4B,GAAG;AAC7C;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC3C;AAEA,MAAM,2BAA2B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qFA0CmD,CAAC;AAE/E,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,iCAAiC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;YACrG,QAAQ,KAAK,CAAC;YAEd,0CAA0C;YAC1C,MAAM,UAAU,MAAM,mBAAmB,KAAK,IAAI;YAClD,MAAM,SAAS,MAAM,wIAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;YAErC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,QAAQ;gBAC9B,SAAS;oBACP,gBAAgB;oBAChB,uBAAuB;gBACzB;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,oBAAoB;QAExB,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC;QAEzF,IAAI;YACF,IAAI,KAAK,IAAI,KAAK,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAC5D,QAAQ,GAAG,CAAC;gBACZ,oBAAoB,MAAM,KAAK,IAAI;YACrC,OAAO,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU;gBACtC,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc,MAAM,KAAK,WAAW;gBAC1C,MAAM,SAAS,MAAM,yIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;oBAAE,QAAQ,OAAO,IAAI,CAAC;gBAAa;gBAC/E,oBAAoB,OAAO,KAAK;YAClC,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,yCAAyC;gBACzC,oBAAoB,MAAM,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,WAAW;YAClB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8B,GAAG;gBAAE,QAAQ;YAAI;QACnF;QAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,kBAAkB,MAAM,CAAC,WAAW,CAAC;QAC9E,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,kBAAkB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAExE,IAAI,CAAC,kBAAkB,IAAI,IAAI;YAC7B,QAAQ,KAAK,CAAC;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAyC,GAAG;gBAAE,QAAQ;YAAI;QAC9F;QAEA,kBAAkB;QAClB,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,GAAG,yBAAyB,yBAAyB,EAAE,mBAAmB;QAC7F,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,MAAM,CAAC,WAAW,CAAC;QAEjE,IAAI;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,6BAA6B;YAC7B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,OAAO,EAAE,SAAS,UAAU;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,IAAI,KAAK,qCAAqC;gBAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,CAAC,kBAAkB,EAAE,SAAS,OAAO,IAAI,iBAAiB;gBACnE,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,8CAA8C;QAC9C,MAAM,MAAM,MAAM,mBAAmB,cAAc,KAAK,IAAI;QAE5D,+BAA+B;QAC/B,MAAM,SAAS,MAAM,wIAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;QAErC,6CAA6C;QAC7C,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,QAAQ;YAC9B,SAAS;gBACP,gBAAgB;gBAChB,uBAAuB;YACzB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,mBAAmB,YAAoB,EAAE,gBAAwB;IAC9E,4DAA4D;IAC5D,MAAM,WAAW,kBAAkB;IAEnC,MAAM,MAAM,IAAI,wIAAA,CAAA,WAAQ,CAAC;QACvB,UAAU;YAAC;gBACT,YAAY,CAAC;gBACb,UAAU;oBACR,aAAa;oBACb,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,KAAK,IAAI;gCACxB,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,KAAK;wBAC3B,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,CAAC,eAAe,EAAE,IAAI,OAAO,kBAAkB,IAAI;gCACzD,MAAM;4BACR;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,CAAC,QAAQ,EAAE,kBAAkB;gCACnC,MAAM;gCACN,SAAS;4BACX;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,oBAAoB;oBACpB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,gBAAgB,IAAI;gCACnC,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,uBAAuB;oBACvB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,UAAU,IAAI;gCAC7B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,0BAA0B;oBAC1B,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,UAAU,IAAI;gCAC7B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,oBAAoB;oBACpB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,gBAAgB,IAAI;gCACnC,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,aAAa;oBACb,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,SAAS,IAAI;gCAC5B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,eAAe;oBACf,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;iBACD;YACH;SAAE;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB,IAAY;IACrC,yFAAyF;IACzF,MAAM,WAAgB,CAAC;IAEvB,uDAAuD;IACvD,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,SAAS,KAAK,GAAG,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK;IAErD,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,SAAS,gBAAgB,GAAG,eAAe,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,SAAS,UAAU,GAAG,kBAAkB,eAAe,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,SAAS,UAAU,GAAG,kBAAkB,eAAe,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,SAAS,gBAAgB,GAAG,eAAe,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,SAAS,SAAS,GAAG,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK;IAEzD,OAAO;AACT;AAEA,eAAe,mBAAmB,gBAAwB;IACxD,MAAM,MAAM,IAAI,wIAAA,CAAA,WAAQ,CAAC;QACvB,UAAU;YAAC;gBACT,YAAY,CAAC;gBACb,UAAU;oBACR,aAAa;oBACb,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,KAAK;wBAC3B,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,CAAC,eAAe,EAAE,IAAI,OAAO,kBAAkB,IAAI;gCACzD,MAAM;4BACR;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,CAAC,QAAQ,EAAE,kBAAkB;gCACnC,MAAM;gCACN,SAAS;4BACX;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,SAAS;oBACT,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,iBAAiB;oBACjB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,qBAAqB;oBACrB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;iBACD;YACH;SAAE;IACJ;IAEA,OAAO;AACT", "debugId": null}}]}