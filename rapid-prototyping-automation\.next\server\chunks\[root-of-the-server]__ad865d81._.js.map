{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/generate-problem-statement/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\nimport { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } from 'docx';\nimport mammoth from 'mammoth';\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY,\n});\n\nconst PROBLEM_STATEMENT_PROMPT = `You are a top 0.1% senior innovation consultant and professional technical writer. \n\n1. Load & Analyze  \n   - Open and read the entire transcript file attached.  \n   - Strip out all timestamps; focus only on speaker content, context, and meaning.\n\n2. Deep Content Extraction\n   - Identify every meeting attendee from the transcript  \n   - Identify every business challenge the customer articulates.  \n   - Capture direct quotes (paraphrased or exact) that illustrate those pain points.  \n   - List all attendees from DXC proposed solution ideas, including any technical requirements mentioned.  \n   - Note CTO's facilitation questions, concerns, and prioritization recommendations.\n\n3. Craft a Polished Analysis\n   - Build a structured analysis with these sections:\n     1. Title and Participants\n        - Infer the title from the intended solution outlined in the transcript and propose a catchy title  \n        - List all attendees and their roles\n     2. Executive Summary (1 paragraph)  \n        - Summarize the workshop goal and outcome in 3–4 sentences—no timestamps.  \n     3. Background & Context (½ page)  \n        - Why this session was convened, the user's strategic imperatives, and desired outcomes.  \n     4. Key Business Challenges  \n        - For each challenge, include:  \n          - Name of Challenge  \n          - Description (2–3 sentences) with a representative quote.  \n     5. Core User Needs & Pain Points  \n        - List of needs and pain points with supporting details or quotes\n     6. How Might We… Problem Statement  \n        - Craft one compelling HMW sentence that unifies all the challenges and needs.  \n     7. Constraints & Success Criteria  \n        - List constraints and corresponding success criteria\n     8. Next Steps & Recommendations (1 page)  \n        - Three to five actionable recommendations to guide prototype/MVP design, each with rationale.  \n     9. Key Insights & Quotes  \n        - A list of the 5–7 most critical quotes or insights—no timestamps, just speaker name and content.\n\n4. Output Format\n   - Provide the response in structured JSON format with clear sections\n   - No timestamps or file metadata in the response\n   - Focus on actionable insights and clear problem articulation\n\nPlease analyze the transcript and provide a comprehensive problem statement analysis.`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n    \n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    // Read file content\n    let transcriptContent = '';\n    \n    if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n      transcriptContent = await file.text();\n    } else if (file.name.endsWith('.docx')) {\n      const arrayBuffer = await file.arrayBuffer();\n      const result = await mammoth.extractRawText({ buffer: Buffer.from(arrayBuffer) });\n      transcriptContent = result.value;\n    } else {\n      // For other formats, try to read as text\n      transcriptContent = await file.text();\n    }\n\n    if (!transcriptContent.trim()) {\n      return NextResponse.json({ error: 'File appears to be empty or unreadable' }, { status: 400 });\n    }\n\n    // Call Claude API\n    const response = await anthropic.messages.create({\n      model: 'claude-sonnet-4-20250514',\n      max_tokens: 4000,\n      messages: [\n        {\n          role: 'user',\n          content: `${PROBLEM_STATEMENT_PROMPT}\\n\\nTranscript Content:\\n${transcriptContent}`\n        }\n      ]\n    });\n\n    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    // Parse the analysis and create Word document\n    const doc = await createWordDocument(analysisText, file.name);\n    \n    // Generate the document buffer\n    const buffer = await Packer.toBuffer(doc);\n    \n    // Return the document as a downloadable file\n    return new NextResponse(buffer, {\n      headers: {\n        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'Content-Disposition': 'attachment; filename=\"Problem_Statement.docx\"',\n      },\n    });\n\n  } catch (error) {\n    console.error('Error generating problem statement:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate problem statement' }, \n      { status: 500 }\n    );\n  }\n}\n\nasync function createWordDocument(analysisText: string, originalFileName: string): Promise<Document> {\n  // Parse the analysis text to extract structured information\n  const sections = parseAnalysisText(analysisText);\n  \n  const doc = new Document({\n    sections: [{\n      properties: {},\n      children: [\n        // Title Page\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.title || \"Problem Statement Analysis\",\n              bold: true,\n              size: 32,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.TITLE,\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 400 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: `Analysis Date: ${new Date().toLocaleDateString()}`,\n              size: 24\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: `Source: ${originalFileName}`,\n              size: 20,\n              italics: true\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 800 }\n        }),\n\n        // Executive Summary\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Executive Summary\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.executiveSummary || \"Executive summary will be generated based on transcript analysis.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Background & Context\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Background & Context\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.background || \"Background and context will be extracted from the transcript analysis.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Key Business Challenges\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Key Business Challenges\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.challenges || \"Business challenges will be identified and detailed from the transcript.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Problem Statement\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"How Might We… Problem Statement\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.problemStatement || \"How might we address the core challenges identified in this analysis?\",\n              size: 22,\n              bold: true,\n              color: \"DC2626\"\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Next Steps\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Next Steps & Recommendations\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.nextSteps || \"Actionable recommendations will be provided based on the analysis.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Raw Analysis\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Detailed Analysis\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: analysisText,\n              size: 20\n            })\n          ],\n          spacing: { after: 400 }\n        })\n      ]\n    }]\n  });\n\n  return doc;\n}\n\nfunction parseAnalysisText(text: string) {\n  // Simple parsing logic - in a real implementation, you'd want more sophisticated parsing\n  const sections: any = {};\n  \n  // Try to extract key sections from the Claude response\n  const titleMatch = text.match(/title[:\\s]*([^\\n]+)/i);\n  sections.title = titleMatch ? titleMatch[1].trim() : null;\n  \n  const summaryMatch = text.match(/executive summary[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.executiveSummary = summaryMatch ? summaryMatch[1].trim() : null;\n  \n  const backgroundMatch = text.match(/background[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.background = backgroundMatch ? backgroundMatch[1].trim() : null;\n  \n  const challengesMatch = text.match(/challenges[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.challenges = challengesMatch ? challengesMatch[1].trim() : null;\n  \n  const problemMatch = text.match(/how might we[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.problemStatement = problemMatch ? problemMatch[1].trim() : null;\n  \n  const stepsMatch = text.match(/next steps[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.nextSteps = stepsMatch ? stepsMatch[1].trim() : null;\n  \n  return sections;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;AACvC;AAEA,MAAM,2BAA2B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qFA0CmD,CAAC;AAE/E,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,oBAAoB;QACpB,IAAI,oBAAoB;QAExB,IAAI,KAAK,IAAI,KAAK,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;YAC5D,oBAAoB,MAAM,KAAK,IAAI;QACrC,OAAO,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtC,MAAM,cAAc,MAAM,KAAK,WAAW;YAC1C,MAAM,SAAS,MAAM,yIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAAE,QAAQ,OAAO,IAAI,CAAC;YAAa;YAC/E,oBAAoB,OAAO,KAAK;QAClC,OAAO;YACL,yCAAyC;YACzC,oBAAoB,MAAM,KAAK,IAAI;QACrC;QAEA,IAAI,CAAC,kBAAkB,IAAI,IAAI;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAyC,GAAG;gBAAE,QAAQ;YAAI;QAC9F;QAEA,kBAAkB;QAClB,MAAM,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;YAC/C,OAAO;YACP,YAAY;YACZ,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,GAAG,yBAAyB,yBAAyB,EAAE,mBAAmB;gBACrF;aACD;QACH;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,8CAA8C;QAC9C,MAAM,MAAM,MAAM,mBAAmB,cAAc,KAAK,IAAI;QAE5D,+BAA+B;QAC/B,MAAM,SAAS,MAAM,wIAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;QAErC,6CAA6C;QAC7C,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,QAAQ;YAC9B,SAAS;gBACP,gBAAgB;gBAChB,uBAAuB;YACzB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,mBAAmB,YAAoB,EAAE,gBAAwB;IAC9E,4DAA4D;IAC5D,MAAM,WAAW,kBAAkB;IAEnC,MAAM,MAAM,IAAI,wIAAA,CAAA,WAAQ,CAAC;QACvB,UAAU;YAAC;gBACT,YAAY,CAAC;gBACb,UAAU;oBACR,aAAa;oBACb,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,KAAK,IAAI;gCACxB,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,KAAK;wBAC3B,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,CAAC,eAAe,EAAE,IAAI,OAAO,kBAAkB,IAAI;gCACzD,MAAM;4BACR;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,CAAC,QAAQ,EAAE,kBAAkB;gCACnC,MAAM;gCACN,SAAS;4BACX;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,oBAAoB;oBACpB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,gBAAgB,IAAI;gCACnC,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,uBAAuB;oBACvB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,UAAU,IAAI;gCAC7B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,0BAA0B;oBAC1B,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,UAAU,IAAI;gCAC7B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,oBAAoB;oBACpB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,gBAAgB,IAAI;gCACnC,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,aAAa;oBACb,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,SAAS,IAAI;gCAC5B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,eAAe;oBACf,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;iBACD;YACH;SAAE;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB,IAAY;IACrC,yFAAyF;IACzF,MAAM,WAAgB,CAAC;IAEvB,uDAAuD;IACvD,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,SAAS,KAAK,GAAG,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK;IAErD,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,SAAS,gBAAgB,GAAG,eAAe,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,SAAS,UAAU,GAAG,kBAAkB,eAAe,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,SAAS,UAAU,GAAG,kBAAkB,eAAe,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,SAAS,gBAAgB,GAAG,eAAe,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,SAAS,SAAS,GAAG,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK;IAEzD,OAAO;AACT", "debugId": null}}]}