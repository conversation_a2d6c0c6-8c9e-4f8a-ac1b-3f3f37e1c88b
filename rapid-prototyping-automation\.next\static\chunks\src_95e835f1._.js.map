{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  gradient?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n}\n\nexport const Card: React.FC<CardProps> = ({\n  children,\n  className,\n  gradient = false,\n  padding = 'md'\n}) => {\n  const getDXCPadding = () => {\n    switch (padding) {\n      case 'sm':\n        return 'var(--dxc-space-4)';\n      case 'lg':\n        return 'var(--dxc-space-8)';\n      default:\n        return 'var(--dxc-space-6)';\n    }\n  };\n\n  return (\n    <div\n      className={cn('dxc-surface-primary', className)}\n      style={{\n        padding: getDXCPadding(),\n        borderRadius: 'var(--dxc-space-2)',\n        backgroundColor: gradient ? 'var(--dxc-purple-50)' : 'var(--dxc-white)',\n        border: gradient ? '1px solid var(--dxc-purple-200)' : '1px solid var(--dxc-grey-200)',\n        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'\n      }}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div\n      className={cn(className)}\n      style={{\n        marginBottom: 'var(--dxc-space-6)',\n        paddingBottom: 'var(--dxc-space-4)',\n        borderBottom: '1px solid var(--dxc-grey-200)'\n      }}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardTitle: React.FC<CardTitleProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <h3\n      className={cn(className)}\n      style={{\n        fontSize: 'var(--dxc-text-2xl)',\n        fontWeight: '600',\n        color: 'var(--dxc-grey-900)',\n        lineHeight: '1.3',\n        margin: '0'\n      }}\n    >\n      {children}\n    </h3>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContent: React.FC<CardContentProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('text-gray-600', className)}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AACA;;;AASO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,IAAI,EACf;IACC,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,cAAc;YACd,iBAAiB,WAAW,yBAAyB;YACrD,QAAQ,WAAW,oCAAoC;YACvD,WAAW;QACb;kBAEC;;;;;;AAGP;KA/Ba;AAsCN,MAAM,aAAwC,CAAC,EACpD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,OAAO;YACL,cAAc;YACd,eAAe;YACf,cAAc;QAChB;kBAEC;;;;;;AAGP;MAhBa;AAuBN,MAAM,YAAsC,CAAC,EAClD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,OAAO;YACL,UAAU;YACV,YAAY;YACZ,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;kBAEC;;;;;;AAGP;MAlBa;AAyBN,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBACjC;;;;;;AAGP;MATa", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'neutral' | 'success' | 'warning' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  disabled,\n  children,\n  ...props\n}) => {\n  // Map variants to DXC classes\n  const getVariantClass = () => {\n    switch (variant) {\n      case 'primary':\n        return 'dxc-button-primary';\n      case 'secondary':\n        return 'dxc-button-secondary';\n      case 'neutral':\n        return 'dxc-button-neutral';\n      case 'success':\n        return 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-300';\n      case 'warning':\n        return 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-300';\n      case 'destructive':\n        return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-300';\n      default:\n        return 'dxc-button-primary';\n    }\n  };\n\n  // Map sizes to DXC spacing\n  const getSizeClass = () => {\n    switch (size) {\n      case 'sm':\n        return 'h-8 px-4 py-2 text-sm';\n      case 'lg':\n        return 'h-12 px-8 py-4 text-lg';\n      default:\n        return 'h-10 px-6 py-3';\n    }\n  };\n\n  return (\n    <button\n      className={cn(\n        'dxc-button',\n        getVariantClass(),\n        getSizeClass(),\n        loading && 'opacity-75 cursor-not-allowed',\n        className\n      )}\n      disabled={disabled || loading}\n      aria-label={props['aria-label'] || (typeof children === 'string' ? children : undefined)}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-3 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AASO,MAAM,SAAgC,CAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cACA,mBACA,gBACA,WAAW,iCACX;QAEF,UAAU,YAAY;QACtB,cAAY,KAAK,CAAC,aAAa,IAAI,CAAC,OAAO,aAAa,WAAW,WAAW,SAAS;QACtF,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA/Ea", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ProgressProps {\n  value: number;\n  max?: number;\n  className?: string;\n  showLabel?: boolean;\n  label?: string;\n}\n\nexport const Progress: React.FC<ProgressProps> = ({\n  value,\n  max = 100,\n  className,\n  showLabel = false,\n  label\n}) => {\n  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);\n\n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between items-center mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">\n            {label || 'Progress'}\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            {Math.round(percentage)}%\n          </span>\n        </div>\n      )}\n      <div className=\"progress-bar\">\n        <div\n          className=\"progress-fill\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,MAAM,GAAG,EACT,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACN;IACC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,SAAS;;;;;;kCAEZ,6LAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAI9B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C;KA7Ba", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/FileUpload.tsx"], "sourcesContent": ["import React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, File, X } from 'lucide-react';\nimport { cn, formatFileSize } from '@/lib/utils';\nimport { Button } from './Button';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  accept?: Record<string, string[]>;\n  maxSize?: number;\n  className?: string;\n}\n\nexport const FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  accept = {\n    'text/*': ['.txt', '.md', '.doc', '.docx'],\n    'application/pdf': ['.pdf']\n  },\n  maxSize = 10 * 1024 * 1024, // 10MB\n  className\n}) => {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [error, setError] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError('');\n    \n    if (rejectedFiles.length > 0) {\n      const rejection = rejectedFiles[0];\n      if (rejection.errors[0]?.code === 'file-too-large') {\n        setError(`File is too large. Maximum size is ${formatFileSize(maxSize)}`);\n      } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n        setError('Invalid file type. Please upload a text document or PDF.');\n      } else {\n        setError('File upload failed. Please try again.');\n      }\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      setSelectedFile(file);\n      onFileSelect(file);\n    }\n  }, [onFileSelect, maxSize]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept,\n    maxSize,\n    multiple: false\n  });\n\n  const removeFile = () => {\n    setSelectedFile(null);\n    setError('');\n  };\n\n  return (\n    <div className={cn('w-full', className)}>\n      {!selectedFile ? (\n        <div\n          {...getRootProps()}\n          className={cn(\n            'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200',\n            isDragActive\n              ? 'border-blue-400 bg-blue-50'\n              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n          )}\n        >\n          <input {...getInputProps()} />\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <p className=\"text-lg font-medium text-gray-700 mb-2\">\n            {isDragActive ? 'Drop the file here' : 'Upload transcript file'}\n          </p>\n          <p className=\"text-sm text-gray-500 mb-4\">\n            Drag and drop your file here, or click to browse\n          </p>\n          <p className=\"text-xs text-gray-400\">\n            Supports: TXT, MD, DOC, DOCX, PDF (max {formatFileSize(maxSize)})\n          </p>\n        </div>\n      ) : (\n        <div className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <File className=\"h-8 w-8 text-blue-500\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">{selectedFile.name}</p>\n                <p className=\"text-sm text-gray-500\">\n                  {formatFileSize(selectedFile.size)}\n                </p>\n              </div>\n            </div>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={removeFile}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      )}\n      \n      {error && (\n        <div className=\"mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2\">\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;;AASO,MAAM,aAAwC,CAAC,EACpD,YAAY,EACZ,SAAS;IACP,UAAU;QAAC;QAAQ;QAAO;QAAQ;KAAQ;IAC1C,mBAAmB;QAAC;KAAO;AAC7B,CAAC,EACD,UAAU,KAAK,OAAO,IAAI,EAC1B,SAAS,EACV;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC,eAAuB;YACjD,SAAS;YAET,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,YAAY,aAAa,CAAC,EAAE;gBAClC,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,kBAAkB;oBAClD,SAAS,CAAC,mCAAmC,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAC1E,OAAO,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,qBAAqB;oBAC5D,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;gBACA;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,gBAAgB;gBAChB,aAAa;YACf;QACF;yCAAG;QAAC;QAAc;KAAQ;IAE1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA;QACA;QACA,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,CAAC,6BACA,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,eACI,+BACA;;kCAGN,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAE,WAAU;kCACV,eAAe,uBAAuB;;;;;;kCAEzC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAE,WAAU;;4BAAwB;4BACK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;4BAAS;;;;;;;;;;;;qCAIpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA6B,aAAa,IAAI;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;GArGa;;QAkC2C,2KAAA,CAAA,cAAW;;;KAlCtD", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Toast.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { CheckCircle, X, AlertCircle, Info } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport type ToastType = 'success' | 'error' | 'info' | 'warning';\n\ninterface Toast {\n  id: string;\n  message: string;\n  type: ToastType;\n  duration?: number;\n}\n\ninterface ToastProps {\n  toast: Toast;\n  onRemove: (id: string) => void;\n}\n\nconst ToastComponent: React.FC<ToastProps> = ({ toast, onRemove }) => {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id);\n    }, toast.duration || 3000);\n\n    return () => clearTimeout(timer);\n  }, [toast.id, toast.duration, onRemove]);\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5\" />;\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'warning':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5\" />;\n      default:\n        return <Info className=\"h-5 w-5\" />;\n    }\n  };\n\n  const getStyles = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800';\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800';\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800';\n    }\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex items-center justify-between p-4 rounded-lg border shadow-lg animate-slide-up',\n        getStyles()\n      )}\n    >\n      <div className=\"flex items-center space-x-3\">\n        {getIcon()}\n        <span className=\"text-sm font-medium\">{toast.message}</span>\n      </div>\n      <button\n        onClick={() => onRemove(toast.id)}\n        className=\"ml-4 text-current hover:opacity-70 transition-opacity\"\n      >\n        <X className=\"h-4 w-4\" />\n      </button>\n    </div>\n  );\n};\n\ninterface ToastContainerProps {\n  toasts: Toast[];\n  onRemove: (id: string) => void;\n}\n\nexport const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  );\n};\n\n// Hook for managing toasts\nexport const useToast = () => {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n\n  const addToast = (message: string, type: ToastType = 'info', duration?: number) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const newToast: Toast = { id, message, type, duration };\n    setToasts(prev => [...prev, newToast]);\n  };\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success: (message: string, duration?: number) => addToast(message, 'success', duration),\n    error: (message: string, duration?: number) => addToast(message, 'error', duration),\n    info: (message: string, duration?: number) => addToast(message, 'info', duration),\n    warning: (message: string, duration?: number) => addToast(message, 'warning', duration),\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAgBA,MAAM,iBAAuC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,SAAS,MAAM,EAAE;gBACnB;iDAAG,MAAM,QAAQ,IAAI;YAErB;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,YAAY;QAChB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA;;0BAGF,6LAAC;gBAAI,WAAU;;oBACZ;kCACD,6LAAC;wBAAK,WAAU;kCAAuB,MAAM,OAAO;;;;;;;;;;;;0BAEtD,6LAAC;gBACC,SAAS,IAAM,SAAS,MAAM,EAAE;gBAChC,WAAU;0BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GA1DM;KAAA;AAiEC,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChF,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;MARa;AAWN,MAAM,WAAW;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC,SAAiB,OAAkB,MAAM,EAAE;QAC3D,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAkB;YAAE;YAAI;YAAS;YAAM;QAAS;QACtD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;QAC9E,OAAO,CAAC,SAAiB,WAAsB,SAAS,SAAS,SAAS;QAC1E,MAAM,CAAC,SAAiB,WAAsB,SAAS,SAAS,QAAQ;QACxE,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;IAChF;AACF;IAtBa", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ThinkingIndicator.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Brain, Lightbulb, Zap, C<PERSON>, Sparkles } from 'lucide-react';\n\ninterface ThinkingIndicatorProps {\n  step: 'transcript' | 'technical' | 'architecture' | 'prompts';\n  className?: string;\n}\n\nconst THINKING_MESSAGES = {\n  transcript: [\n    \"🧠 Reading your conversation transcript...\",\n    \"🔍 Identifying key stakeholders and pain points...\",\n    \"💡 Extracting business challenges...\",\n    \"📝 Crafting executive summary...\",\n    \"🎯 Formulating 'How Might We' statements...\",\n    \"✨ Polishing the problem statement...\",\n    \"🚀 Almost done with the analysis...\"\n  ],\n  technical: [\n    \"🏗️ Analyzing problem statement architecture needs...\",\n    \"⚡ Designing cloud-native solution components...\",\n    \"🔧 Selecting optimal technology stack...\",\n    \"🌐 Mapping data flow and integration patterns...\",\n    \"🔒 Defining security and compliance framework...\",\n    \"📊 Calculating scalability requirements...\",\n    \"🎯 Scoping MVP features and timeline...\",\n    \"📋 Generating implementation roadmap...\"\n  ],\n  architecture: [\n    \"🎨 Analyzing technical requirements for architecture...\",\n    \"🏗️ Sketching system architecture diagrams...\",\n    \"🔗 Connecting microservices and APIs...\",\n    \"☁️ Optimizing cloud infrastructure layout...\",\n    \"🔄 Designing data pipelines and flows...\",\n    \"🛡️ Adding security layers and compliance...\",\n    \"📈 Planning for scale and performance...\",\n    \"🎯 Generating Mermaid and PlantUML code...\",\n    \"✨ Finalizing architecture visualization...\"\n  ],\n  prompts: [\n    \"🎯 Analyzing technical requirements for implementation...\",\n    \"💻 Crafting frontend development prompts for Loveable AI...\",\n    \"🔧 Building backend API specifications and endpoints...\",\n    \"🗄️ Designing database schemas with relationships...\",\n    \"☁️ Creating DevOps deployment configs in JSON format...\",\n    \"📝 Optimizing prompts for AI development tools...\",\n    \"🚀 Generating copy-paste ready implementation guides...\",\n    \"✨ Finalizing Loveable AI integration prompts...\"\n  ]\n};\n\nconst THINKING_ICONS = [Brain, Lightbulb, Zap, Cpu, Sparkles];\n\nexport const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ \n  step, \n  className = '' \n}) => {\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\n  const [currentIconIndex, setCurrentIconIndex] = useState(0);\n  const [dots, setDots] = useState('');\n\n  const messages = THINKING_MESSAGES[step];\n  const CurrentIcon = THINKING_ICONS[currentIconIndex];\n\n  useEffect(() => {\n    // Rotate messages every 2.5 seconds\n    const messageInterval = setInterval(() => {\n      setCurrentMessageIndex((prev) => (prev + 1) % messages.length);\n    }, 2500);\n\n    // Rotate icons every 1.5 seconds\n    const iconInterval = setInterval(() => {\n      setCurrentIconIndex((prev) => (prev + 1) % THINKING_ICONS.length);\n    }, 1500);\n\n    // Animate dots every 500ms\n    const dotsInterval = setInterval(() => {\n      setDots((prev) => {\n        if (prev === '...') return '';\n        return prev + '.';\n      });\n    }, 500);\n\n    return () => {\n      clearInterval(messageInterval);\n      clearInterval(iconInterval);\n      clearInterval(dotsInterval);\n    };\n  }, [messages.length]);\n\n  return (\n    <div className={`text-center py-8 ${className}`}>\n      {/* Animated Icon */}\n      <div className=\"relative mb-6\">\n        <div className=\"animate-pulse-slow mb-4\">\n          <CurrentIcon className=\"h-16 w-16 text-blue-500 mx-auto animate-bounce\" />\n        </div>\n        \n        {/* Thinking bubbles animation */}\n        <div className=\"absolute -top-2 -right-2\">\n          <div className=\"flex space-x-1\">\n            <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-ping\" style={{ animationDelay: '0ms' }}></div>\n            <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-ping\" style={{ animationDelay: '200ms' }}></div>\n            <div className=\"w-2 h-2 bg-pink-400 rounded-full animate-ping\" style={{ animationDelay: '400ms' }}></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main thinking message */}\n      <h3 className=\"text-xl font-semibold mb-3 text-gray-800\">\n        Claude is thinking{dots}\n      </h3>\n\n      {/* Dynamic message */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        <p className=\"text-gray-600 text-lg animate-fade-in max-w-md mx-auto\">\n          {messages[currentMessageIndex]}\n        </p>\n      </div>\n\n      {/* Progress indicator */}\n      <div className=\"mt-6 max-w-md mx-auto\">\n        <div className=\"flex justify-between text-xs text-gray-500 mb-2\">\n          <span>Processing...</span>\n          <span>{Math.round(((currentMessageIndex + 1) / messages.length) * 100)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div \n            className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000 ease-out\"\n            style={{ width: `${((currentMessageIndex + 1) / messages.length) * 100}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* Fun fact */}\n      <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-lg mx-auto\">\n        <p className=\"text-blue-800 text-sm\">\n          <Sparkles className=\"inline h-4 w-4 mr-1\" />\n          <strong>Did you know?</strong> Claude processes thousands of tokens per second to understand context and generate human-like responses!\n        </p>\n      </div>\n\n      {/* Estimated time */}\n      <p className=\"text-sm text-gray-500 mt-4\">\n        ⏱️ This usually takes 30-60 seconds depending on content complexity\n      </p>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAOA,MAAM,oBAAoB;IACxB,YAAY;QACV;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,iBAAiB;IAAC,uMAAA,CAAA,QAAK;IAAE,+MAAA,CAAA,YAAS;IAAE,mMAAA,CAAA,MAAG;IAAE,mMAAA,CAAA,MAAG;IAAE,6MAAA,CAAA,WAAQ;CAAC;AAEtD,MAAM,oBAAsD,CAAC,EAClE,IAAI,EACJ,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,WAAW,iBAAiB,CAAC,KAAK;IACxC,MAAM,cAAc,cAAc,CAAC,iBAAiB;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,oCAAoC;YACpC,MAAM,kBAAkB;+DAAY;oBAClC;uEAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,SAAS,MAAM;;gBAC/D;8DAAG;YAEH,iCAAiC;YACjC,MAAM,eAAe;4DAAY;oBAC/B;oEAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,eAAe,MAAM;;gBAClE;2DAAG;YAEH,2BAA2B;YAC3B,MAAM,eAAe;4DAAY;oBAC/B;oEAAQ,CAAC;4BACP,IAAI,SAAS,OAAO,OAAO;4BAC3B,OAAO,OAAO;wBAChB;;gBACF;2DAAG;YAEH;+CAAO;oBACL,cAAc;oBACd,cAAc;oBACd,cAAc;gBAChB;;QACF;sCAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,qBACE,6LAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;;0BAE7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAY,WAAU;;;;;;;;;;;kCAIzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAgD,OAAO;wCAAE,gBAAgB;oCAAM;;;;;;8CAC9F,6LAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;8CAClG,6LAAC;oCAAI,WAAU;oCAAgD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAMtG,6LAAC;gBAAG,WAAU;;oBAA2C;oBACpC;;;;;;;0BAIrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,QAAQ,CAAC,oBAAoB;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM,KAAK,KAAK,CAAC,AAAC,CAAC,sBAAsB,CAAC,IAAI,SAAS,MAAM,GAAI;oCAAK;;;;;;;;;;;;;kCAEzE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,AAAC,CAAC,sBAAsB,CAAC,IAAI,SAAS,MAAM,GAAI,IAAI,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAMhF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;sCAAO;;;;;;wBAAsB;;;;;;;;;;;;0BAKlC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAKhD;GA/Fa;KAAA", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/DXCHeader.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\n\ninterface DXCHeaderProps {\n  className?: string;\n}\n\nexport const DXCHeader: React.FC<DXCHeaderProps> = ({ className }) => {\n  return (\n    <header className={cn('dxc-header', className)}>\n      <div className=\"dxc-container\">\n        <div className=\"flex items-center justify-between\">\n          <Link \n            href=\"/\" \n            className=\"dxc-logo-link\"\n            aria-label=\"DXC Technology - Go to homepage\"\n          >\n            {/* Official DXC Technology Logo */}\n            <svg\n              className=\"dxc-logo\"\n              width=\"200\"\n              height=\"40\"\n              viewBox=\"0 0 200 40\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              role=\"img\"\n              aria-labelledby=\"dxc-logo-title\"\n            >\n              <title id=\"dxc-logo-title\">DXC Technology</title>\n\n              {/* DXC Logo - Official Design */}\n              <g>\n                {/* D */}\n                <path d=\"M0 5h12c8.284 0 15 6.716 15 15s-6.716 15-15 15H0V5zm8 22h4c4.418 0 8-3.582 8-8s-3.582-8-8-8H8v16z\" fill=\"#7d2fd0\"/>\n\n                {/* X */}\n                <path d=\"M35 5l8 12L51 5h9l-12 15L60 35h-9l-8-12L35 35h-9l12-15L26 5h9z\" fill=\"#7d2fd0\"/>\n\n                {/* C */}\n                <path d=\"M75 12c-2.5-2.5-6-4-10-4-7.732 0-14 6.268-14 14s6.268 14 14 14c4 0 7.5-1.5 10-4l5 5c-4 4-9.5 6-15 6-11.046 0-20-8.954-20-20S54.954 3 66 3c5.5 0 11 2 15 6l-6 3z\" fill=\"#7d2fd0\"/>\n              </g>\n\n              {/* TECHNOLOGY text */}\n              <g fill=\"#1f2937\">\n                <text x=\"100\" y=\"15\" fontSize=\"14\" fontFamily=\"Open Sans, sans-serif\" fontWeight=\"700\" letterSpacing=\"0.1em\">TECHNOLOGY</text>\n              </g>\n            </svg>\n          </Link>\n          \n          {/* Navigation could go here */}\n          <nav className=\"hidden md:flex items-center space-x-8\" role=\"navigation\" aria-label=\"Main navigation\">\n            {/* Navigation items would go here */}\n          </nav>\n        </div>\n      </div>\n    </header>\n  );\n};\n\n// Alternative component for when you have the actual DXC logo file\ninterface DXCLogoProps {\n  className?: string;\n  width?: number;\n  height?: number;\n}\n\nexport const DXCLogo: React.FC<DXCLogoProps> = ({\n  className,\n  width = 200,\n  height = 40\n}) => {\n  return (\n    <Link \n      href=\"/\" \n      className={cn('dxc-logo-link', className)}\n      aria-label=\"DXC Technology - Go to homepage\"\n    >\n      {/* When you have the actual dxc-logo-black.svg file, use this: */}\n      {/* <img \n        src=\"/images/dxc-logo-black.svg\" \n        alt=\"DXC Technology\" \n        className=\"dxc-logo\"\n        width={width}\n        height={height}\n      /> */}\n      \n      {/* Official DXC Technology Logo */}\n      <svg\n        className=\"dxc-logo\"\n        width={width}\n        height={height}\n        viewBox=\"0 0 200 40\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        role=\"img\"\n        aria-labelledby=\"dxc-logo-title-2\"\n      >\n        <title id=\"dxc-logo-title-2\">DXC Technology</title>\n\n        {/* DXC Logo - Official Design */}\n        <g>\n          {/* D */}\n          <path d=\"M0 5h12c8.284 0 15 6.716 15 15s-6.716 15-15 15H0V5zm8 22h4c4.418 0 8-3.582 8-8s-3.582-8-8-8H8v16z\" fill=\"#7d2fd0\"/>\n\n          {/* X */}\n          <path d=\"M35 5l8 12L51 5h9l-12 15L60 35h-9l-8-12L35 35h-9l12-15L26 5h9z\" fill=\"#7d2fd0\"/>\n\n          {/* C */}\n          <path d=\"M75 12c-2.5-2.5-6-4-10-4-7.732 0-14 6.268-14 14s6.268 14 14 14c4 0 7.5-1.5 10-4l5 5c-4 4-9.5 6-15 6-11.046 0-20-8.954-20-20S54.954 3 66 3c5.5 0 11 2 15 6l-6 3z\" fill=\"#7d2fd0\"/>\n        </g>\n\n        {/* TECHNOLOGY text */}\n        <g fill=\"#1f2937\">\n          <text x=\"100\" y=\"15\" fontSize=\"14\" fontFamily=\"Open Sans, sans-serif\" fontWeight=\"700\" letterSpacing=\"0.1em\">TECHNOLOGY</text>\n        </g>\n      </svg>\n    </Link>\n  );\n};\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAMO,MAAM,YAAsC,CAAC,EAAE,SAAS,EAAE;IAC/D,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAClC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;wBACV,cAAW;kCAGX,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,OAAM;4BACN,MAAK;4BACL,mBAAgB;;8CAEhB,6LAAC;oCAAM,IAAG;8CAAiB;;;;;;8CAG3B,6LAAC;;sDAEC,6LAAC;4CAAK,GAAE;4CAAoG,MAAK;;;;;;sDAGjH,6LAAC;4CAAK,GAAE;4CAAiE,MAAK;;;;;;sDAG9E,6LAAC;4CAAK,GAAE;4CAAkK,MAAK;;;;;;;;;;;;8CAIjL,6LAAC;oCAAE,MAAK;8CACN,cAAA,6LAAC;wCAAK,GAAE;wCAAM,GAAE;wCAAK,UAAS;wCAAK,YAAW;wCAAwB,YAAW;wCAAM,eAAc;kDAAQ;;;;;;;;;;;;;;;;;;;;;;kCAMnH,6LAAC;wBAAI,WAAU;wBAAwC,MAAK;wBAAa,cAAW;;;;;;;;;;;;;;;;;;;;;;AAO9F;KAlDa;AA2DN,MAAM,UAAkC,CAAC,EAC9C,SAAS,EACT,QAAQ,GAAG,EACX,SAAS,EAAE,EACZ;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,cAAW;kBAYX,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAQ;YACR,MAAK;YACL,OAAM;YACN,MAAK;YACL,mBAAgB;;8BAEhB,6LAAC;oBAAM,IAAG;8BAAmB;;;;;;8BAG7B,6LAAC;;sCAEC,6LAAC;4BAAK,GAAE;4BAAoG,MAAK;;;;;;sCAGjH,6LAAC;4BAAK,GAAE;4BAAiE,MAAK;;;;;;sCAG9E,6LAAC;4BAAK,GAAE;4BAAkK,MAAK;;;;;;;;;;;;8BAIjL,6LAAC;oBAAE,MAAK;8BACN,cAAA,6LAAC;wBAAK,GAAE;wBAAM,GAAE;wBAAK,UAAS;wBAAK,YAAW;wBAAwB,YAAW;wBAAM,eAAc;kCAAQ;;;;;;;;;;;;;;;;;;;;;;AAKvH;MApDa", "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/RapidPrototypingApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, CardH<PERSON>er, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { Progress } from '@/components/ui/Progress';\nimport { FileUpload } from '@/components/ui/FileUpload';\nimport { ToastContainer, useToast } from '@/components/ui/Toast';\nimport { ThinkingIndicator } from '@/components/ui/ThinkingIndicator';\nimport { DXCHeader } from '@/components/ui/DXCHeader';\nimport { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\n// Types\ntype Step = 1 | 2 | 3 | 4 | 5;\n\ninterface AppState {\n  currentStep: Step;\n  uploadedFile: File | null;\n  isProcessing: boolean;\n  processingStep: string;\n  isTransitioning: boolean;\n  transitionType: 'problem' | 'technical' | 'architecture' | 'prompts' | null;\n  error: string | null;\n  documents: {\n    problemStatement: string | null;\n    technicalRequirements: string | null;\n    architectureDiagram: {\n      mermaidCode?: string;\n      plantUMLCode?: string;\n      legend?: string[];\n      fullResponse?: string;\n      debug?: any;\n    } | null;\n    prompts: {\n      frontend: string;\n      backend: string;\n      database: string;\n      devops: string | any;\n    } | null;\n  };\n}\n\nconst STEPS = [\n  { id: 1 as Step, title: 'Upload', icon: FileText },\n  { id: 2 as Step, title: 'Problem Statement', icon: Settings },\n  { id: 3 as Step, title: 'Technical Requirements', icon: Settings },\n  { id: 4 as Step, title: 'Architecture Diagram', icon: Image },\n  { id: 5 as Step, title: 'Implementation Prompts', icon: Code },\n];\n\nexport const RapidPrototypingApp: React.FC = () => {\n  const { toasts, removeToast, success, error, info } = useToast();\n  const [state, setState] = useState<AppState>({\n    currentStep: 1,\n    uploadedFile: null,\n    isProcessing: false,\n    processingStep: '',\n    isTransitioning: false,\n    transitionType: null,\n    error: null,\n    documents: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null,\n      prompts: null,\n    },\n  });\n\n  const currentStepData = STEPS.find(step => step.id === state.currentStep);\n  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;\n\n  const handleFileUpload = async (file: File) => {\n    setState(prev => ({ ...prev, uploadedFile: file }));\n    success('File uploaded successfully!');\n  };\n\n  const handleValidation = async (documentType: string, isValid: boolean) => {\n    if (isValid) {\n      const nextStep = state.currentStep + 1;\n      if (nextStep <= 5) {\n        setState(prev => ({ \n          ...prev, \n          currentStep: nextStep as Step,\n          error: null \n        }));\n        success(`Proceeding to Step ${nextStep}`);\n      }\n    } else {\n      info('Please review and make necessary changes before proceeding.');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <DXCHeader />\n      <ToastContainer toasts={toasts} onRemove={removeToast} />\n      \n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-6\">\n            Rapid Prototyping Automation\n          </h1>\n          <p className=\"text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed\">\n            Transform your conversation transcripts into comprehensive technical documentation and implementation guides powered by DXC Technology's AI solutions\n          </p>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <Progress \n            value={progress} \n            showLabel \n            label={`Step ${state.currentStep} of ${STEPS.length}: ${currentStepData?.title}`}\n            className=\"max-w-2xl mx-auto\"\n          />\n        </div>\n\n        {/* Steps Navigation */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"flex space-x-4 overflow-x-auto pb-2\">\n            {STEPS.map((step) => {\n              const Icon = step.icon;\n              const isActive = step.id === state.currentStep;\n              const isCompleted = step.id < state.currentStep;\n              \n              return (\n                <div\n                  key={step.id}\n                  className={cn(\n                    \"flex flex-col items-center min-w-[120px] p-3 rounded-lg transition-all\",\n                    isActive \n                      ? 'bg-purple-100 border-2 border-purple-300' \n                      : isCompleted \n                        ? 'bg-blue-100 border-2 border-blue-300'\n                        : 'bg-gray-100 border-2 border-gray-200'\n                  )}\n                >\n                  <Icon \n                    className={cn(\n                      \"h-6 w-6 mb-2\",\n                      isActive \n                        ? 'text-purple-600' \n                        : isCompleted \n                          ? 'text-blue-600'\n                          : 'text-gray-400'\n                    )} \n                  />\n                  <span className={cn(\n                    \"text-sm font-medium text-center\",\n                    isActive \n                      ? 'text-purple-800' \n                      : isCompleted \n                        ? 'text-blue-800'\n                        : 'text-gray-600'\n                  )}>\n                    {step.title}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Error Display */}\n        {state.error && (\n          <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800\">{state.error}</p>\n          </div>\n        )}\n\n        {/* Thinking Indicator */}\n        {state.isTransitioning && state.transitionType && (\n          <div className=\"mb-8\">\n            <ThinkingIndicator type={state.transitionType} />\n          </div>\n        )}\n\n        {/* Step Content */}\n        <div className=\"space-y-6\">\n          {/* Step 1: File Upload */}\n          {!state.isTransitioning && state.currentStep === 1 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Upload Conversation Transcript</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <FileUpload\n                  onFileUpload={handleFileUpload}\n                  acceptedTypes={['.txt', '.docx', '.pdf']}\n                  maxSize={10}\n                />\n                {state.uploadedFile && (\n                  <div className=\"mt-6 flex justify-center\">\n                    <Button \n                      variant=\"primary\" \n                      onClick={() => handleValidation('transcript', true)}\n                    >\n                      Continue to Problem Statement Generation\n                    </Button>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Step 2: Problem Statement */}\n          {!state.isTransitioning && state.currentStep === 2 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Problem Statement Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-8\">\n                  <h3 className=\"text-xl font-semibold mb-4\">Generate Problem Statement</h3>\n                  <p className=\"text-gray-600 mb-6\">\n                    Claude will analyze your transcript and generate a comprehensive problem statement document.\n                  </p>\n                  <Button \n                    variant=\"primary\" \n                    onClick={() => handleValidation('problemStatement', true)}\n                  >\n                    Generate Problem Statement\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Step 3: Technical Requirements */}\n          {!state.isTransitioning && state.currentStep === 3 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Technical Requirements Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-8\">\n                  <h3 className=\"text-xl font-semibold mb-4\">Generate Technical Requirements</h3>\n                  <p className=\"text-gray-600 mb-6\">\n                    Claude will create detailed technical requirements based on your problem statement.\n                  </p>\n                  <Button \n                    variant=\"primary\" \n                    onClick={() => handleValidation('technicalRequirements', true)}\n                  >\n                    Generate Technical Requirements\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Step 4: Architecture Diagram */}\n          {!state.isTransitioning && state.currentStep === 4 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>System Architecture Diagram</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-8\">\n                  <h3 className=\"text-xl font-semibold mb-4\">Generate Architecture Diagram</h3>\n                  <p className=\"text-gray-600 mb-6\">\n                    Claude will create visual architecture diagrams from your technical requirements.\n                  </p>\n                  <Button \n                    variant=\"primary\" \n                    onClick={() => handleValidation('architectureDiagram', true)}\n                  >\n                    Generate Architecture Diagram\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Step 5: Implementation Prompts */}\n          {!state.isTransitioning && state.currentStep === 5 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Implementation Prompts</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 text-center\">\n                  <CheckCircle className=\"h-12 w-12 text-green-500 mx-auto mb-3\" />\n                  <h4 className=\"font-semibold text-green-800 mb-2\">Process Complete!</h4>\n                  <p className=\"text-green-700\">\n                    Your rapid prototyping automation is complete. Implementation prompts are ready for Loveable AI.\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAXA;;;;;;;;;;;AA2CA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAW,OAAO;QAAU,MAAM,iNAAA,CAAA,WAAQ;IAAC;IACjD;QAAE,IAAI;QAAW,OAAO;QAAqB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAC5D;QAAE,IAAI;QAAW,OAAO;QAA0B,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACjE;QAAE,IAAI;QAAW,OAAO;QAAwB,MAAM,uMAAA,CAAA,QAAK;IAAC;IAC5D;QAAE,IAAI;QAAW,OAAO;QAA0B,MAAM,qMAAA,CAAA,OAAI;IAAC;CAC9D;AAEM,MAAM,sBAAgC;;IAC3C,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC3C,aAAa;QACb,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,WAAW;YACT,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;YACrB,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,WAAW;IACxE,MAAM,WAAW,AAAC,CAAC,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK;IAElE,MAAM,mBAAmB,OAAO;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;YAAK,CAAC;QACjD,QAAQ;IACV;IAEA,MAAM,mBAAmB,OAAO,cAAsB;QACpD,IAAI,SAAS;YACX,MAAM,WAAW,MAAM,WAAW,GAAG;YACrC,IAAI,YAAY,GAAG;gBACjB,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,aAAa;wBACb,OAAO;oBACT,CAAC;gBACD,QAAQ,CAAC,mBAAmB,EAAE,UAAU;YAC1C;QACF,OAAO;YACL,KAAK;QACP;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,YAAS;;;;;0BACV,6LAAC,oIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,UAAU;;;;;;0BAE1C,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,SAAS;4BACT,OAAO,CAAC,KAAK,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,iBAAiB,OAAO;4BAChF,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC;gCACV,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,KAAK,EAAE,KAAK,MAAM,WAAW;gCAC9C,MAAM,cAAc,KAAK,EAAE,GAAG,MAAM,WAAW;gCAE/C,qBACE,6LAAC;oCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,WACI,6CACA,cACE,yCACA;;sDAGR,6LAAC;4CACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,WACI,oBACA,cACE,kBACA;;;;;;sDAGV,6LAAC;4CAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,mCACA,WACI,oBACA,cACE,kBACA;sDAEL,KAAK,KAAK;;;;;;;mCA5BR,KAAK,EAAE;;;;;4BAgClB;;;;;;;;;;;oBAKH,MAAM,KAAK,kBACV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB,MAAM,KAAK;;;;;;;;;;;oBAK3C,MAAM,eAAe,IAAI,MAAM,cAAc,kBAC5C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gJAAA,CAAA,oBAAiB;4BAAC,MAAM,MAAM,cAAc;;;;;;;;;;;kCAKjD,6LAAC;wBAAI,WAAU;;4BAEZ,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC,yIAAA,CAAA,aAAU;gDACT,cAAc;gDACd,eAAe;oDAAC;oDAAQ;oDAAS;iDAAO;gDACxC,SAAS;;;;;;4CAEV,MAAM,YAAY,kBACjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,iBAAiB,cAAc;8DAC/C;;;;;;;;;;;;;;;;;;;;;;;4BAUV,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,iBAAiB,oBAAoB;8DACrD;;;;;;;;;;;;;;;;;;;;;;;4BASR,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,iBAAiB,yBAAyB;8DAC1D;;;;;;;;;;;;;;;;;;;;;;;4BASR,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,iBAAiB,uBAAuB;8DACxD;;;;;;;;;;;;;;;;;;;;;;;4BASR,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhD;GAtPa;;QAC2C,oIAAA,CAAA,WAAQ;;;KADnD", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { RapidPrototypingApp } from '@/components/RapidPrototypingApp';\n\nexport default function Home() {\n  return <RapidPrototypingApp />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,6LAAC,4IAAA,CAAA,sBAAmB;;;;;AAC7B;KAFwB", "debugId": null}}]}