'use client';

import React from 'react';
import { Button } from './Button';
import { Download, Copy } from 'lucide-react';

interface DocumentViewerProps {
  content?: string;
  title?: string;
  className?: string;
  downloadFileName?: string;
  onCopy?: () => void;
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  content,
  title = "Document",
  className = "",
  downloadFileName,
  onCopy
}) => {
  if (!content) {
    return (
      <div className={`p-6 bg-gray-50 rounded-lg border border-gray-200 ${className}`}>
        <p className="text-gray-500 text-center">No document content available</p>
      </div>
    );
  }

  const handleDownload = () => {
    if (!content || !downloadFileName) return;
    
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const element = document.createElement('a');
    element.href = url;
    element.download = downloadFileName;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    URL.revokeObjectURL(url);
  };

  const handleCopy = () => {
    if (!content) return;
    
    navigator.clipboard.writeText(content).then(() => {
      onCopy?.();
    }).catch(err => {
      console.error('Failed to copy content:', err);
    });
  };

  // Simple markdown rendering for display
  const renderMarkdown = (text: string) => {
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mb-4 text-gray-900">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mb-3 text-gray-800">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-medium mb-2 text-gray-700">$1</h3>')
      .replace(/^\* (.*$)/gim, '<li class="ml-4 mb-1">$1</li>')
      .replace(/^\- (.*$)/gim, '<li class="ml-4 mb-1">$1</li>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/\n/g, '<br/>');
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleCopy}
            className="flex items-center gap-2"
          >
            <Copy className="h-4 w-4" />
            Copy
          </Button>
          {downloadFileName && (
            <Button
              variant="primary"
              size="sm"
              onClick={handleDownload}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
          )}
        </div>
      </div>
      <div className="p-6 max-h-96 overflow-y-auto">
        <div 
          className="prose max-w-none text-gray-700 leading-relaxed"
          dangerouslySetInnerHTML={{ 
            __html: `<p class="mb-4">${renderMarkdown(content)}</p>` 
          }}
        />
      </div>
    </div>
  );
};
