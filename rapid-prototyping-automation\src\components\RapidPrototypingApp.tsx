'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Progress } from '@/components/ui/Progress';
import { FileUpload } from '@/components/ui/FileUpload';
import { ChatInterface } from '@/components/ui/ChatInterface';
import { ToastContainer, useToast } from '@/components/ui/Toast';
import { ThinkingIndicator } from '@/components/ui/ThinkingIndicator';
import { ArchitectureDiagram } from '@/components/ui/ArchitectureDiagram';
import { ImplementationPrompts } from '@/components/ui/ImplementationPrompts';
import { DXCHeader } from '@/components/ui/DXCHeader';
import { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';

type Step = 1 | 2 | 3 | 4 | 5;

interface AppState {
  currentStep: Step;
  uploadedFile: File | null;
  isProcessing: boolean;
  processingStep: string;
  error: string | null;
  isTransitioning: boolean;
  transitionType: 'transcript' | 'technical' | 'architecture' | 'prompts' | null;
  documents: {
    problemStatement: string | null;
    technicalRequirements: string | null;
    architectureDiagram: {
      mermaidCode?: string;
      plantUMLCode?: string;
      legend?: string[];
      fullResponse?: string;
      debug?: any;
    } | null;
    prompts: {
      frontend: string;
      backend: string;
      database: string;
      devops: string | any;
    } | null;
  };
  validations: {
    problemStatement: boolean | null;
    technicalRequirements: boolean | null;
    architectureDiagram: boolean | null;
  };
}

const STEPS = [
  { id: 1, title: 'Upload Transcript', icon: FileText, description: 'Upload your conversation transcript' },
  { id: 2, title: 'Problem Statement', icon: CheckCircle, description: 'Review and validate problem statement' },
  { id: 3, title: 'Technical Requirements', icon: Settings, description: 'Review technical requirements document' },
  { id: 4, title: 'Architecture Diagram', icon: Image, description: 'Review system architecture' },
  { id: 5, title: 'Generated Prompts', icon: Code, description: 'Copy implementation prompts' }
];

export const RapidPrototypingApp: React.FC = () => {
  const { toasts, removeToast, success, error, info } = useToast();
  const [state, setState] = useState<AppState>({
    currentStep: 1,
    uploadedFile: null,
    isProcessing: false,
    processingStep: '',
    error: null,
    isTransitioning: false,
    transitionType: null,
    documents: {
      problemStatement: null,
      technicalRequirements: null,
      architectureDiagram: null,
      prompts: null
    },
    validations: {
      problemStatement: null,
      technicalRequirements: null,
      architectureDiagram: null
    }
  });

  const handleFileUpload = async (file: File) => {
    setState(prev => ({
      ...prev,
      uploadedFile: file,
      isProcessing: true,
      processingStep: 'Uploading transcript...',
      error: null
    }));

    info('Processing your transcript with Claude AI...', 5000);

    try {
      setState(prev => ({ ...prev, processingStep: 'Analyzing transcript with Claude AI...' }));

      // Create FormData to send file to API
      const formData = new FormData();
      formData.append('file', file);

      setState(prev => ({ ...prev, processingStep: 'Generating problem statement document...' }));

      // Call the API to generate problem statement
      const response = await fetch('/api/generate-problem-statement', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate problem statement`;
        throw new Error(errorMessage);
      }

      setState(prev => ({ ...prev, processingStep: 'Finalizing document...' }));

      // Get the generated document as blob
      const blob = await response.blob();
      const documentUrl = URL.createObjectURL(blob);

      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingStep: '',
        currentStep: 2,
        error: null,
        documents: {
          ...prev.documents,
          problemStatement: documentUrl
        }
      }));

      success('Problem statement document generated successfully with Claude AI!');
    } catch (err) {
      console.error('Error generating problem statement:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate problem statement. Please try again.';
      error(errorMessage);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingStep: '',
        error: errorMessage
      }));
    }
  };

  const handleValidation = async (documentType: keyof AppState['validations'], isValid: boolean) => {
    setState(prev => ({
      ...prev,
      validations: {
        ...prev.validations,
        [documentType]: isValid
      }
    }));

    if (isValid) {
      const nextStep = state.currentStep + 1;

      if (nextStep === 3 && documentType === 'problemStatement') {
        // Show thinking indicator for technical requirements generation
        setState(prev => ({
          ...prev,
          isTransitioning: true,
          transitionType: 'technical',
          error: null
        }));

        // Small delay to show the thinking indicator
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Generate technical requirements from problem statement
        await generateTechnicalRequirements();
      } else if (nextStep === 4 && documentType === 'technicalRequirements') {
        // Generate architecture diagram from technical requirements
        setState(prev => ({
          ...prev,
          isTransitioning: true,
          transitionType: 'architecture',
          error: null
        }));

        // Small delay to show the thinking indicator
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Generate architecture diagram
        await generateArchitectureDiagram();
      } else if (nextStep === 5 && documentType === 'architectureDiagram') {
        // Generate implementation prompts from technical requirements
        setState(prev => ({
          ...prev,
          isTransitioning: true,
          transitionType: 'prompts',
          error: null
        }));

        // Small delay to show the thinking indicator
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Generate implementation prompts
        await generateImplementationPrompts();
      }
    }
  };

  const generateTechnicalRequirements = async () => {
    try {
      // Extract content from the problem statement document
      let problemStatementContent = '';

      if (state.documents.problemStatement) {
        // For now, we'll use a placeholder since we can't easily extract from the blob
        // In a real implementation, you might store the text content separately
        problemStatementContent = `Problem Statement Document Analysis:

This document contains the comprehensive problem statement analysis including:
- Executive Summary of the business challenges
- Background and context of the workshop
- Key business challenges identified
- Core user needs and pain points
- How Might We problem statement
- Constraints and success criteria
- Next steps and recommendations
- Key insights and quotes from stakeholders

The technical solution should address the scalability, performance, and integration challenges identified in the problem statement while providing a modern, cloud-native architecture that can handle the specified requirements.`;
      }

      // Call the technical requirements API
      const response = await fetch('/api/generate-technical-requirements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          problemStatementContent: problemStatementContent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate technical requirements`;
        throw new Error(errorMessage);
      }

      // Get the generated document as blob
      const blob = await response.blob();
      const documentUrl = URL.createObjectURL(blob);

      setState(prev => ({
        ...prev,
        isTransitioning: false,
        transitionType: null,
        currentStep: 3,
        error: null,
        documents: {
          ...prev.documents,
          technicalRequirements: documentUrl
        }
      }));

      success('Technical requirements document generated successfully with Claude AI!');
    } catch (err) {
      console.error('Error generating technical requirements:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate technical requirements. Please try again.';
      error(errorMessage);
      setState(prev => ({
        ...prev,
        isTransitioning: false,
        transitionType: null,
        error: errorMessage
      }));
    }
  };

  const generateArchitectureDiagram = async () => {
    try {
      // Extract content from the technical requirements document
      let technicalRequirementsContent = '';

      if (state.documents.technicalRequirements) {
        // For now, we'll use a placeholder since we can't easily extract from the blob
        // In a real implementation, you might store the text content separately
        technicalRequirementsContent = `Technical Solution Document Analysis:

This document contains the comprehensive technical solution including:
- Solution Architecture Overview with cloud-native design
- Component Design with technology choices and interfaces
- Data Flow & Integration patterns and real-time processing
- Security & Compliance framework and requirements
- Non-Functional Requirements for scalability and performance
- Prototype Scope & MVP Features for validation
- Implementation Roadmap with phases and milestones
- Next Steps & Recommendations for moving to prototype

The architecture diagram should visualize the cloud-native solution with microservices, data flows, security layers, and integration points as specified in the technical requirements.`;
      }

      // Call the architecture diagram API
      const response = await fetch('/api/generate-architecture-diagram', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          technicalRequirementsContent: technicalRequirementsContent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate architecture diagram`;
        throw new Error(errorMessage);
      }

      const data = await response.json();

      setState(prev => ({
        ...prev,
        isTransitioning: false,
        transitionType: null,
        currentStep: 4,
        error: null,
        documents: {
          ...prev.documents,
          architectureDiagram: {
            mermaidCode: data.mermaid,
            plantUMLCode: data.plantUML,
            legend: data.legend,
            fullResponse: data.fullResponse,
            debug: data.debug
          }
        }
      }));

      success('Architecture diagram generated successfully with Claude Sonnet 4!');
    } catch (err) {
      console.error('Error generating architecture diagram:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate architecture diagram. Please try again.';
      error(errorMessage);
      setState(prev => ({
        ...prev,
        isTransitioning: false,
        transitionType: null,
        error: errorMessage
      }));
    }
  };

  const generateImplementationPrompts = async () => {
    try {
      // Extract content from the technical requirements document
      let technicalRequirementsContent = '';

      if (state.documents.technicalRequirements) {
        // For now, we'll use a placeholder since we can't easily extract from the blob
        // In a real implementation, you might store the text content separately
        technicalRequirementsContent = `Technical Solution Document Analysis:

This document contains the comprehensive technical solution including:
- Solution Architecture Overview with cloud-native design
- Component Design with technology choices and interfaces
- Data Flow & Integration patterns and real-time processing
- Security & Compliance framework and requirements
- Non-Functional Requirements for scalability and performance
- Prototype Scope & MVP Features for validation
- Implementation Roadmap with phases and milestones
- Next Steps & Recommendations for moving to prototype

The implementation prompts should provide detailed, actionable instructions for building:
1. Frontend application with modern UI framework
2. Backend API with proper architecture
3. Database schema with relationships and constraints
4. DevOps deployment configuration in JSON format

Each prompt should be ready to copy-paste into Loveable AI for rapid prototyping.`;
      }

      // Call the implementation prompts API
      const response = await fetch('/api/generate-implementation-prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          technicalRequirementsContent: technicalRequirementsContent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate implementation prompts`;
        throw new Error(errorMessage);
      }

      const data = await response.json();

      setState(prev => ({
        ...prev,
        isTransitioning: false,
        transitionType: null,
        currentStep: 5,
        error: null,
        documents: {
          ...prev.documents,
          prompts: data.prompts
        }
      }));

      success('Implementation prompts generated successfully with Claude Sonnet 4!');
    } catch (err) {
      console.error('Error generating implementation prompts:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate implementation prompts. Please try again.';
      error(errorMessage);
      setState(prev => ({
        ...prev,
        isTransitioning: false,
        transitionType: null,
        error: errorMessage
      }));
    }
  };

  const currentStepData = STEPS.find(step => step.id === state.currentStep);
  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--dxc-grey-50)' }}>
      {/* DXC Header */}
      <DXCHeader />

      <ToastContainer toasts={toasts} onRemove={removeToast} />

      <main className="dxc-container" style={{ paddingTop: 'var(--dxc-space-8)', paddingBottom: 'var(--dxc-space-8)' }}>
        {/* Header */}
        <div className="text-center" style={{ marginBottom: 'var(--dxc-space-8)' }}>
          <h1 style={{
            fontSize: 'var(--dxc-text-5xl)',
            fontWeight: '700',
            color: 'var(--dxc-grey-900)',
            marginBottom: 'var(--dxc-space-6)'
          }}>
            Rapid Prototyping Automation
          </h1>
          <p style={{
            fontSize: 'var(--dxc-text-lg)',
            color: 'var(--dxc-grey-700)',
            maxWidth: '48rem',
            margin: '0 auto',
            lineHeight: '1.6'
          }}>
            Transform your conversation transcripts into comprehensive technical documentation and implementation guides powered by DXC Technology's AI solutions
          </p>
        </div>

        {/* Progress Bar */}
        <div style={{ marginBottom: 'var(--dxc-space-8)' }}>
          <div className="max-w-2xl mx-auto">
            <div style={{ marginBottom: 'var(--dxc-space-4)' }}>
              <p style={{
                fontSize: 'var(--dxc-text-sm)',
                color: 'var(--dxc-grey-600)',
                textAlign: 'center'
              }}>
                Step {state.currentStep} of {STEPS.length}: {currentStepData?.title}
              </p>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: 'var(--dxc-grey-200)',
              borderRadius: 'var(--dxc-space-1)',
              overflow: 'hidden'
            }}>
              <div
                style={{
                  width: `${progress}%`,
                  height: '100%',
                  backgroundColor: 'var(--dxc-purple-600)',
                  transition: 'width 0.3s ease-in-out'
                }}
              />
            </div>
          </div>
        </div>

        {/* Steps Navigation */}
        <div className="flex justify-center" style={{ marginBottom: 'var(--dxc-space-8)' }}>
          <div className="dxc-grid" style={{ maxWidth: '800px', gridTemplateColumns: 'repeat(5, 1fr)' }}>
            {STEPS.map((step) => {
              const Icon = step.icon;
              const isActive = step.id === state.currentStep;
              const isCompleted = step.id < state.currentStep;

              return (
                <div
                  key={step.id}
                  className="dxc-surface-primary"
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    padding: 'var(--dxc-space-4)',
                    borderRadius: 'var(--dxc-space-2)',
                    border: `2px solid ${
                      isActive ? 'var(--dxc-purple-600)' :
                      isCompleted ? 'var(--dxc-blue-600)' :
                      'var(--dxc-grey-300)'
                    }`,
                    backgroundColor: isActive ? 'var(--dxc-purple-50)' :
                                   isCompleted ? 'var(--dxc-blue-50)' :
                                   'var(--dxc-white)',
                    transition: 'all 0.2s ease-in-out',
                    minWidth: '120px'
                  }}
                >
                  <div style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 'var(--dxc-space-2)',
                    backgroundColor: isActive ? 'var(--dxc-purple-600)' :
                                   isCompleted ? 'var(--dxc-blue-600)' :
                                   'var(--dxc-grey-300)',
                    color: isActive || isCompleted ? 'var(--dxc-white)' : 'var(--dxc-grey-600)'
                  }}>
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                  </div>
                  <span style={{
                    fontSize: 'var(--dxc-text-sm)',
                    fontWeight: '600',
                    textAlign: 'center',
                    color: isActive ? 'var(--dxc-purple-700)' :
                           isCompleted ? 'var(--dxc-blue-700)' :
                           'var(--dxc-grey-600)'
                  }}>
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {/* Show thinking indicator when transitioning between steps */}
          {state.isTransitioning && state.transitionType && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardContent>
                <ThinkingIndicator step={state.transitionType} />
                {state.error && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg max-w-md mx-auto">
                    <p className="text-red-700 text-sm">{state.error}</p>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="mt-2"
                      onClick={() => setState(prev => ({
                        ...prev,
                        error: null,
                        isTransitioning: false,
                        transitionType: null
                      }))}
                    >
                      Try Again
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {!state.isTransitioning && state.currentStep === 1 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Upload Your Conversation Transcript</CardTitle>
              </CardHeader>
              <CardContent>
                {!state.isProcessing ? (
                  <FileUpload onFileSelect={handleFileUpload} />
                ) : (
                  <div>
                    <ThinkingIndicator step="transcript" />
                    {state.error && (
                      <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg max-w-md mx-auto">
                        <p className="text-red-700 text-sm">{state.error}</p>
                        <Button
                          variant="secondary"
                          size="sm"
                          className="mt-2"
                          onClick={() => setState(prev => ({ ...prev, error: null, isProcessing: false }))}
                        >
                          Try Again
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {!state.isTransitioning && state.currentStep === 2 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Problem Statement Document</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">AI-Generated Problem Statement</h4>
                    <div className="prose max-w-none">
                      <p className="text-gray-700 leading-relaxed">
                        Claude AI has analyzed your conversation transcript and generated a comprehensive problem statement document.
                        This professional document includes executive summary, business challenges, user needs, and actionable recommendations.
                      </p>
                      <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                        <h5 className="font-medium text-blue-900 mb-2">Document Includes:</h5>
                        <ul className="list-disc list-inside text-blue-800 space-y-1">
                          <li>Executive Summary & Background Context</li>
                          <li>Key Business Challenges with Supporting Quotes</li>
                          <li>Core User Needs & Pain Points Analysis</li>
                          <li>"How Might We" Problem Statement</li>
                          <li>Constraints & Success Criteria</li>
                          <li>Next Steps & Recommendations</li>
                          <li>Key Insights & Critical Quotes</li>
                        </ul>
                      </div>
                      <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                        <p className="text-green-800 text-sm">
                          ✅ Generated using Claude Sonnet 4 - Latest AI model for professional document creation
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      variant="success"
                      size="lg"
                      onClick={() => {
                        if (state.documents.problemStatement) {
                          // Download the actual generated document
                          const element = document.createElement('a');
                          element.href = state.documents.problemStatement;
                          element.download = 'Problem_Statement.docx';
                          document.body.appendChild(element);
                          element.click();
                          document.body.removeChild(element);
                          success('Problem Statement document downloaded!');
                        } else {
                          error('Document not available. Please try uploading the transcript again.');
                        }
                      }}
                      className="flex-1"
                      disabled={!state.documents.problemStatement}
                    >
                      Download Problem Statement (.docx)
                    </Button>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-800 mb-3">Validation Required</h4>
                    <p className="text-yellow-700 mb-4">
                      Please review the problem statement document and confirm if it accurately captures your requirements.
                    </p>
                    <div className="flex gap-3">
                      <Button
                        variant="success"
                        onClick={() => handleValidation('problemStatement', true)}
                      >
                        YES - Continue
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={() => handleValidation('problemStatement', false)}
                      >
                        NO - Needs Feedback
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {!state.isTransitioning && state.currentStep === 3 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Technical Requirements Document</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">AI-Generated Technical Solution Document</h4>
                    <div className="prose max-w-none">
                      <p className="text-gray-700 leading-relaxed mb-4">
                        Claude AI has analyzed your problem statement and generated a comprehensive technical solution document.
                        This professional document includes solution architecture, component design, and implementation roadmap.
                      </p>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="p-4 bg-blue-50 rounded-lg">
                          <h5 className="font-medium text-blue-900 mb-2">Architecture & Design</h5>
                          <ul className="list-disc list-inside text-blue-800 space-y-1 text-sm">
                            <li>Solution Architecture Overview</li>
                            <li>Component Design & Technology Choices</li>
                            <li>Data Flow & Integration Patterns</li>
                            <li>Cloud-Native Implementation Strategy</li>
                          </ul>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-lg">
                          <h5 className="font-medium text-purple-900 mb-2">Requirements & Implementation</h5>
                          <ul className="list-disc list-inside text-purple-800 space-y-1 text-sm">
                            <li>Security & Compliance Framework</li>
                            <li>Non-Functional Requirements</li>
                            <li>Prototype Scope & MVP Features</li>
                            <li>Implementation Roadmap</li>
                          </ul>
                        </div>
                      </div>
                      <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                        <p className="text-green-800 text-sm">
                          ✅ Generated using Claude Sonnet 4 - Based on your problem statement analysis
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      variant="success"
                      size="lg"
                      onClick={() => {
                        if (state.documents.technicalRequirements) {
                          // Download the actual generated document
                          const element = document.createElement('a');
                          element.href = state.documents.technicalRequirements;
                          element.download = 'Technical_Solution_Document.docx';
                          document.body.appendChild(element);
                          element.click();
                          document.body.removeChild(element);
                          success('Technical Solution Document downloaded!');
                        } else {
                          error('Document not available. Please try regenerating from the problem statement.');
                        }
                      }}
                      className="flex-1"
                      disabled={!state.documents.technicalRequirements}
                    >
                      Download Technical Solution Document (.docx)
                    </Button>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-800 mb-3">Validation Required</h4>
                    <p className="text-yellow-700 mb-4">
                      Please review the technical requirements document and confirm if it meets your technical specifications.
                    </p>
                    <div className="flex gap-3">
                      <Button
                        variant="success"
                        onClick={() => handleValidation('technicalRequirements', true)}
                      >
                        YES - Continue
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={() => handleValidation('technicalRequirements', false)}
                      >
                        NO - Needs Feedback
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {!state.isTransitioning && state.currentStep === 4 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>AI-Generated System Architecture</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">Claude Sonnet 4 Generated Architecture</h4>
                    <div className="prose max-w-none mb-4">
                      <p className="text-gray-700 leading-relaxed">
                        Claude Sonnet 4 has analyzed your technical requirements and generated a comprehensive system architecture diagram.
                        This includes component relationships, data flows, and integration patterns.
                      </p>
                      <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                        <p className="text-green-800 text-sm">
                          ✅ Generated using Claude Sonnet 4 - Based on your technical solution document
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Architecture Diagram Component */}
                  <ArchitectureDiagram
                    mermaidCode={state.documents.architectureDiagram?.mermaidCode}
                    plantUMLCode={state.documents.architectureDiagram?.plantUMLCode}
                    legend={state.documents.architectureDiagram?.legend}
                    fullResponse={state.documents.architectureDiagram?.fullResponse}
                    debug={state.documents.architectureDiagram?.debug}
                    onCopyCode={(code, type) => {
                      success(`${type === 'mermaid' ? 'Mermaid' : 'PlantUML'} code copied to clipboard!`);
                    }}
                  />

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 mb-3">Quick Review</h4>
                      <p className="text-blue-700 mb-4">
                        Does the architecture diagram accurately represent your system design requirements?
                      </p>
                      <div className="flex gap-3">
                        <Button
                          variant="success"
                          onClick={() => handleValidation('architectureDiagram', true)}
                        >
                          YES - Looks Good
                        </Button>
                        <Button
                          variant="secondary"
                          onClick={() => handleValidation('architectureDiagram', false)}
                        >
                          NO - Needs Changes
                        </Button>
                      </div>
                    </div>

                    <ChatInterface
                      context="System architecture diagram generated from technical requirements"
                      diagramCode={state.documents.architectureDiagram?.mermaidCode || ''}
                      onFeedback={(feedback) => {
                        console.log('Architecture feedback:', feedback);
                      }}
                      onDiagramUpdate={(updatedCode) => {
                        console.log('Diagram update:', updatedCode);
                        // Update the diagram with new code
                        setState(prev => ({
                          ...prev,
                          documents: {
                            ...prev.documents,
                            architectureDiagram: {
                              ...prev.documents.architectureDiagram,
                              [updatedCode.type === 'mermaid' ? 'mermaidCode' : 'plantUMLCode']: updatedCode.code
                            }
                          }
                        }));
                        success('Architecture diagram updated based on your feedback!');
                      }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {!state.isTransitioning && state.currentStep === 5 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Loveable AI Implementation Prompts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">Claude Sonnet 4 Generated Prompts</h4>
                    <div className="prose max-w-none mb-4">
                      <p className="text-gray-700 leading-relaxed">
                        Claude Sonnet 4 has analyzed your technical requirements and generated four comprehensive implementation prompts.
                        Each prompt is optimized for Loveable AI and includes detailed specifications, best practices, and ready-to-use code instructions.
                      </p>
                      <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                        <p className="text-green-800 text-sm">
                          ✅ Generated using Claude Sonnet 4 - Ready for Loveable AI copy-paste implementation
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Implementation Prompts Component */}
                  <ImplementationPrompts
                    prompts={state.documents.prompts}
                    onCopyPrompt={(prompt, type) => {
                      success(`${type.charAt(0).toUpperCase() + type.slice(1)} prompt copied to clipboard!`);
                    }}
                  />

                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
                    <h4 className="font-semibold text-green-800 mb-2">Rapid Prototyping Complete!</h4>
                    <p className="text-green-700 mb-3">
                      Your complete AI-powered prototyping workflow is finished. You now have:
                    </p>
                    <div className="grid md:grid-cols-2 gap-4 text-left">
                      <div className="bg-white rounded-lg p-3">
                        <h5 className="font-medium text-green-800 mb-1">📋 Documents Generated</h5>
                        <ul className="text-sm text-green-700 space-y-1">
                          <li>• Problem Statement (Word)</li>
                          <li>• Technical Requirements (Word)</li>
                          <li>• Architecture Diagrams (SVG)</li>
                        </ul>
                      </div>
                      <div className="bg-white rounded-lg p-3">
                        <h5 className="font-medium text-green-800 mb-1">🚀 Implementation Ready</h5>
                        <ul className="text-sm text-green-700 space-y-1">
                          <li>• Frontend Prompt (Loveable AI)</li>
                          <li>• Backend API Prompt (Loveable AI)</li>
                          <li>• Database Schema (Loveable AI)</li>
                          <li>• DevOps Config (JSON Download)</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
