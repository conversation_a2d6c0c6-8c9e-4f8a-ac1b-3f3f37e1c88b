'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Progress } from '@/components/ui/Progress';
import { FileUpload } from '@/components/ui/FileUpload';
import { ChatInterface } from '@/components/ui/ChatInterface';
import { ToastContainer, useToast } from '@/components/ui/Toast';
import { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';

type Step = 1 | 2 | 3 | 4 | 5;

interface AppState {
  currentStep: Step;
  uploadedFile: File | null;
  isProcessing: boolean;
  processingStep: string;
  error: string | null;
  documents: {
    problemStatement: string | null;
    technicalRequirements: string | null;
    architectureDiagram: string | null;
    prompts: string[] | null;
  };
  validations: {
    problemStatement: boolean | null;
    technicalRequirements: boolean | null;
    architectureDiagram: boolean | null;
  };
}

const STEPS = [
  { id: 1, title: 'Upload Transcript', icon: FileText, description: 'Upload your conversation transcript' },
  { id: 2, title: 'Problem Statement', icon: CheckCircle, description: 'Review and validate problem statement' },
  { id: 3, title: 'Technical Requirements', icon: Settings, description: 'Review technical requirements document' },
  { id: 4, title: 'Architecture Diagram', icon: Image, description: 'Review system architecture' },
  { id: 5, title: 'Generated Prompts', icon: Code, description: 'Copy implementation prompts' }
];

export const RapidPrototypingApp: React.FC = () => {
  const { toasts, removeToast, success, error, info } = useToast();
  const [state, setState] = useState<AppState>({
    currentStep: 1,
    uploadedFile: null,
    isProcessing: false,
    processingStep: '',
    error: null,
    documents: {
      problemStatement: null,
      technicalRequirements: null,
      architectureDiagram: null,
      prompts: null
    },
    validations: {
      problemStatement: null,
      technicalRequirements: null,
      architectureDiagram: null
    }
  });

  const handleFileUpload = async (file: File) => {
    setState(prev => ({
      ...prev,
      uploadedFile: file,
      isProcessing: true,
      processingStep: 'Uploading transcript...',
      error: null
    }));

    info('Processing your transcript with Claude AI...', 5000);

    try {
      setState(prev => ({ ...prev, processingStep: 'Analyzing transcript with Claude AI...' }));

      // Create FormData to send file to API
      const formData = new FormData();
      formData.append('file', file);

      setState(prev => ({ ...prev, processingStep: 'Generating problem statement document...' }));

      // Call the API to generate problem statement
      const response = await fetch('/api/generate-problem-statement', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate problem statement`;
        throw new Error(errorMessage);
      }

      setState(prev => ({ ...prev, processingStep: 'Finalizing document...' }));

      // Get the generated document as blob
      const blob = await response.blob();
      const documentUrl = URL.createObjectURL(blob);

      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingStep: '',
        currentStep: 2,
        error: null,
        documents: {
          ...prev.documents,
          problemStatement: documentUrl
        }
      }));

      success('Problem statement document generated successfully with Claude AI!');
    } catch (err) {
      console.error('Error generating problem statement:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate problem statement. Please try again.';
      error(errorMessage);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingStep: '',
        error: errorMessage
      }));
    }
  };

  const handleValidation = async (documentType: keyof AppState['validations'], isValid: boolean) => {
    setState(prev => ({
      ...prev,
      validations: {
        ...prev.validations,
        [documentType]: isValid
      }
    }));

    if (isValid) {
      const nextStep = state.currentStep + 1;

      if (nextStep === 3 && documentType === 'problemStatement') {
        // Generate technical requirements from problem statement
        await generateTechnicalRequirements();
      } else if (nextStep <= 5) {
        // Simulate generating other documents
        setState(prev => ({ ...prev, isProcessing: true, processingStep: 'Generating next document...' }));
        await new Promise(resolve => setTimeout(resolve, 1500));

        let newDocuments = { ...state.documents };

        if (nextStep === 4) {
          newDocuments.architectureDiagram = 'Generated architecture diagram...';
        } else if (nextStep === 5) {
          newDocuments.prompts = ['Prompt 1...', 'Prompt 2...', 'Prompt 3...'];
        }

        setState(prev => ({
          ...prev,
          currentStep: nextStep as Step,
          documents: newDocuments,
          isProcessing: false,
          processingStep: ''
        }));
      }
    }
  };

  const generateTechnicalRequirements = async () => {
    setState(prev => ({
      ...prev,
      isProcessing: true,
      processingStep: 'Extracting problem statement content...',
      error: null
    }));

    try {
      // Extract content from the problem statement document
      setState(prev => ({ ...prev, processingStep: 'Analyzing problem statement with Claude AI...' }));

      let problemStatementContent = '';

      if (state.documents.problemStatement) {
        // For now, we'll use a placeholder since we can't easily extract from the blob
        // In a real implementation, you might store the text content separately
        problemStatementContent = `Problem Statement Document Analysis:

This document contains the comprehensive problem statement analysis including:
- Executive Summary of the business challenges
- Background and context of the workshop
- Key business challenges identified
- Core user needs and pain points
- How Might We problem statement
- Constraints and success criteria
- Next steps and recommendations
- Key insights and quotes from stakeholders

The technical solution should address the scalability, performance, and integration challenges identified in the problem statement while providing a modern, cloud-native architecture that can handle the specified requirements.`;
      }

      setState(prev => ({ ...prev, processingStep: 'Generating technical requirements document...' }));

      // Call the technical requirements API
      const response = await fetch('/api/generate-technical-requirements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          problemStatementContent: problemStatementContent
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate technical requirements`;
        throw new Error(errorMessage);
      }

      setState(prev => ({ ...prev, processingStep: 'Finalizing technical document...' }));

      // Get the generated document as blob
      const blob = await response.blob();
      const documentUrl = URL.createObjectURL(blob);

      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingStep: '',
        currentStep: 3,
        error: null,
        documents: {
          ...prev.documents,
          technicalRequirements: documentUrl
        }
      }));

      success('Technical requirements document generated successfully with Claude AI!');
    } catch (err) {
      console.error('Error generating technical requirements:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate technical requirements. Please try again.';
      error(errorMessage);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingStep: '',
        error: errorMessage
      }));
    }
  };

  const currentStepData = STEPS.find(step => step.id === state.currentStep);
  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <ToastContainer toasts={toasts} onRemove={removeToast} />
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold gradient-text mb-4">
            Rapid Prototyping Automation
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Transform your conversation transcripts into comprehensive technical documentation and implementation guides
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress 
            value={progress} 
            showLabel 
            label={`Step ${state.currentStep} of ${STEPS.length}: ${currentStepData?.title}`}
            className="max-w-2xl mx-auto"
          />
        </div>

        {/* Steps Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-4 overflow-x-auto pb-2">
            {STEPS.map((step) => {
              const Icon = step.icon;
              const isActive = step.id === state.currentStep;
              const isCompleted = step.id < state.currentStep;
              
              return (
                <div
                  key={step.id}
                  className={`flex flex-col items-center min-w-[120px] p-3 rounded-lg transition-all ${
                    isActive 
                      ? 'bg-blue-100 border-2 border-blue-300' 
                      : isCompleted 
                        ? 'bg-green-100 border-2 border-green-300'
                        : 'bg-gray-100 border-2 border-gray-200'
                  }`}
                >
                  <Icon 
                    className={`h-6 w-6 mb-2 ${
                      isActive 
                        ? 'text-blue-600' 
                        : isCompleted 
                          ? 'text-green-600'
                          : 'text-gray-400'
                    }`} 
                  />
                  <span className={`text-sm font-medium text-center ${
                    isActive 
                      ? 'text-blue-800' 
                      : isCompleted 
                        ? 'text-green-800'
                        : 'text-gray-600'
                  }`}>
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {state.currentStep === 1 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Upload Your Conversation Transcript</CardTitle>
              </CardHeader>
              <CardContent>
                {!state.isProcessing ? (
                  <FileUpload onFileSelect={handleFileUpload} />
                ) : (
                  <div className="text-center py-8">
                    <div className="animate-pulse-slow mb-4">
                      <FileText className="h-16 w-16 text-blue-500 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">Processing Your Transcript</h3>
                    <p className="text-gray-600 mb-4">
                      {state.processingStep || 'Analyzing your conversation with Claude AI...'}
                    </p>
                    <div className="max-w-md mx-auto">
                      <Progress value={50} showLabel={false} />
                    </div>
                    <p className="text-sm text-gray-500 mt-3">
                      This may take 30-60 seconds depending on transcript length
                    </p>
                    {state.error && (
                      <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-700 text-sm">{state.error}</p>
                        <Button
                          variant="secondary"
                          size="sm"
                          className="mt-2"
                          onClick={() => setState(prev => ({ ...prev, error: null, isProcessing: false }))}
                        >
                          Try Again
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {state.currentStep === 2 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Problem Statement Document</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">AI-Generated Problem Statement</h4>
                    <div className="prose max-w-none">
                      <p className="text-gray-700 leading-relaxed">
                        Claude AI has analyzed your conversation transcript and generated a comprehensive problem statement document.
                        This professional document includes executive summary, business challenges, user needs, and actionable recommendations.
                      </p>
                      <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                        <h5 className="font-medium text-blue-900 mb-2">Document Includes:</h5>
                        <ul className="list-disc list-inside text-blue-800 space-y-1">
                          <li>Executive Summary & Background Context</li>
                          <li>Key Business Challenges with Supporting Quotes</li>
                          <li>Core User Needs & Pain Points Analysis</li>
                          <li>"How Might We" Problem Statement</li>
                          <li>Constraints & Success Criteria</li>
                          <li>Next Steps & Recommendations</li>
                          <li>Key Insights & Critical Quotes</li>
                        </ul>
                      </div>
                      <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                        <p className="text-green-800 text-sm">
                          ✅ Generated using Claude Sonnet 4 - Latest AI model for professional document creation
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      variant="success"
                      size="lg"
                      onClick={() => {
                        if (state.documents.problemStatement) {
                          // Download the actual generated document
                          const element = document.createElement('a');
                          element.href = state.documents.problemStatement;
                          element.download = 'Problem_Statement.docx';
                          document.body.appendChild(element);
                          element.click();
                          document.body.removeChild(element);
                          success('Problem Statement document downloaded!');
                        } else {
                          error('Document not available. Please try uploading the transcript again.');
                        }
                      }}
                      className="flex-1"
                      disabled={!state.documents.problemStatement}
                    >
                      Download Problem Statement (.docx)
                    </Button>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-800 mb-3">Validation Required</h4>
                    <p className="text-yellow-700 mb-4">
                      Please review the problem statement document and confirm if it accurately captures your requirements.
                    </p>
                    <div className="flex gap-3">
                      <Button
                        variant="success"
                        onClick={() => handleValidation('problemStatement', true)}
                      >
                        YES - Continue
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={() => handleValidation('problemStatement', false)}
                      >
                        NO - Needs Feedback
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {state.currentStep === 3 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Technical Requirements Document</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">AI-Generated Technical Solution Document</h4>
                    <div className="prose max-w-none">
                      <p className="text-gray-700 leading-relaxed mb-4">
                        Claude AI has analyzed your problem statement and generated a comprehensive technical solution document.
                        This professional document includes solution architecture, component design, and implementation roadmap.
                      </p>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="p-4 bg-blue-50 rounded-lg">
                          <h5 className="font-medium text-blue-900 mb-2">Architecture & Design</h5>
                          <ul className="list-disc list-inside text-blue-800 space-y-1 text-sm">
                            <li>Solution Architecture Overview</li>
                            <li>Component Design & Technology Choices</li>
                            <li>Data Flow & Integration Patterns</li>
                            <li>Cloud-Native Implementation Strategy</li>
                          </ul>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-lg">
                          <h5 className="font-medium text-purple-900 mb-2">Requirements & Implementation</h5>
                          <ul className="list-disc list-inside text-purple-800 space-y-1 text-sm">
                            <li>Security & Compliance Framework</li>
                            <li>Non-Functional Requirements</li>
                            <li>Prototype Scope & MVP Features</li>
                            <li>Implementation Roadmap</li>
                          </ul>
                        </div>
                      </div>
                      <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                        <p className="text-green-800 text-sm">
                          ✅ Generated using Claude Sonnet 4 - Based on your problem statement analysis
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button
                      variant="success"
                      size="lg"
                      onClick={() => {
                        if (state.documents.technicalRequirements) {
                          // Download the actual generated document
                          const element = document.createElement('a');
                          element.href = state.documents.technicalRequirements;
                          element.download = 'Technical_Solution_Document.docx';
                          document.body.appendChild(element);
                          element.click();
                          document.body.removeChild(element);
                          success('Technical Solution Document downloaded!');
                        } else {
                          error('Document not available. Please try regenerating from the problem statement.');
                        }
                      }}
                      className="flex-1"
                      disabled={!state.documents.technicalRequirements}
                    >
                      Download Technical Solution Document (.docx)
                    </Button>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-800 mb-3">Validation Required</h4>
                    <p className="text-yellow-700 mb-4">
                      Please review the technical requirements document and confirm if it meets your technical specifications.
                    </p>
                    <div className="flex gap-3">
                      <Button
                        variant="success"
                        onClick={() => handleValidation('technicalRequirements', true)}
                      >
                        YES - Continue
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={() => handleValidation('technicalRequirements', false)}
                      >
                        NO - Needs Feedback
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {state.currentStep === 4 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>System Architecture Diagram</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">Generated Architecture Diagram</h4>
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                      <Image className="h-24 w-24 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-2">Complex System Architecture</p>
                      <p className="text-sm text-gray-500">
                        A comprehensive diagram showing system components, data flow, and integration points
                      </p>
                    </div>
                    <div className="mt-4 grid md:grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <h5 className="font-medium text-blue-900 mb-1">Frontend Layer</h5>
                        <p className="text-sm text-blue-700">React, TypeScript, Tailwind</p>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <h5 className="font-medium text-green-900 mb-1">Backend Services</h5>
                        <p className="text-sm text-green-700">Node.js, Express, PostgreSQL</p>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <h5 className="font-medium text-purple-900 mb-1">Infrastructure</h5>
                        <p className="text-sm text-purple-700">AWS, Docker, Kubernetes</p>
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 mb-3">Quick Review</h4>
                      <p className="text-blue-700 mb-4">
                        Does the architecture diagram accurately represent your system design requirements?
                      </p>
                      <div className="flex gap-3">
                        <Button
                          variant="success"
                          onClick={() => handleValidation('architectureDiagram', true)}
                        >
                          YES - Looks Good
                        </Button>
                        <Button
                          variant="secondary"
                          onClick={() => handleValidation('architectureDiagram', false)}
                        >
                          NO - Needs Changes
                        </Button>
                      </div>
                    </div>

                    <ChatInterface
                      onFeedback={(feedback) => {
                        console.log('Architecture feedback:', feedback);
                        // Handle feedback processing here
                      }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {state.currentStep === 5 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Implementation Prompts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h4 className="font-semibold text-lg mb-4">Copy-Paste Ready Prompts</h4>
                    <p className="text-gray-600 mb-6">
                      Here are the generated prompts for implementing your solution. Click to copy each prompt.
                    </p>

                    <div className="space-y-4">
                      {[
                        {
                          title: "Frontend Development Prompt",
                          content: "Create a React TypeScript application with the following components: user authentication, dashboard interface, real-time data visualization, and responsive design using Tailwind CSS..."
                        },
                        {
                          title: "Backend API Development Prompt",
                          content: "Build a Node.js Express API with the following endpoints: user management, data processing, real-time WebSocket connections, and PostgreSQL database integration..."
                        },
                        {
                          title: "Database Schema Prompt",
                          content: "Design a PostgreSQL database schema with the following tables: users, projects, data_points, and audit_logs. Include proper indexing and relationships..."
                        },
                        {
                          title: "DevOps & Deployment Prompt",
                          content: "Create Docker containers and Kubernetes deployment configurations for a scalable application with load balancing, auto-scaling, and monitoring..."
                        }
                      ].map((prompt, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                          <div className="flex justify-between items-start mb-2">
                            <h5 className="font-medium text-gray-900">{prompt.title}</h5>
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => {
                                navigator.clipboard.writeText(prompt.content);
                                success('Prompt copied to clipboard!');
                              }}
                            >
                              Copy
                            </Button>
                          </div>
                          <p className="text-sm text-gray-600 line-clamp-3">
                            {prompt.content}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
                    <h4 className="font-semibold text-green-800 mb-2">Process Complete!</h4>
                    <p className="text-green-700">
                      Your rapid prototyping automation is complete. Use the prompts above to implement your solution.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
