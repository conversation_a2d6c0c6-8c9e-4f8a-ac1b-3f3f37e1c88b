'use client';

import React, { useState } from 'react';
import { <PERSON>, CardH<PERSON>er, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Progress } from '@/components/ui/Progress';
import { FileUpload } from '@/components/ui/FileUpload';
import { ToastContainer, useToast } from '@/components/ui/Toast';
import { ThinkingIndicator } from '@/components/ui/ThinkingIndicator';
import { DXCHeader } from '@/components/ui/DXCHeader';
import { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';
import { cn } from '@/lib/utils';

// Types
type Step = 1 | 2 | 3 | 4 | 5;

interface AppState {
  currentStep: Step;
  uploadedFile: File | null;
  isProcessing: boolean;
  processingStep: string;
  isTransitioning: boolean;
  transitionType: 'problem' | 'technical' | 'architecture' | 'prompts' | null;
  error: string | null;
  documents: {
    problemStatement: string | null;
    technicalRequirements: string | null;
    architectureDiagram: {
      mermaidCode?: string;
      plantUMLCode?: string;
      legend?: string[];
      fullResponse?: string;
      debug?: any;
    } | null;
    prompts: {
      frontend: string;
      backend: string;
      database: string;
      devops: string | any;
    } | null;
  };
}

const STEPS = [
  { id: 1 as Step, title: 'Upload', icon: FileText },
  { id: 2 as Step, title: 'Problem Statement', icon: Settings },
  { id: 3 as Step, title: 'Technical Requirements', icon: Settings },
  { id: 4 as Step, title: 'Architecture Diagram', icon: Image },
  { id: 5 as Step, title: 'Implementation Prompts', icon: Code },
];

export const RapidPrototypingApp: React.FC = () => {
  const { toasts, removeToast, success, error, info } = useToast();
  const [state, setState] = useState<AppState>({
    currentStep: 1,
    uploadedFile: null,
    isProcessing: false,
    processingStep: '',
    isTransitioning: false,
    transitionType: null,
    error: null,
    documents: {
      problemStatement: null,
      technicalRequirements: null,
      architectureDiagram: null,
      prompts: null,
    },
  });

  const currentStepData = STEPS.find(step => step.id === state.currentStep);
  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;

  const handleFileUpload = async (file: File) => {
    setState(prev => ({ ...prev, uploadedFile: file }));
    success('File uploaded successfully!');
  };

  const handleValidation = async (documentType: string, isValid: boolean) => {
    if (isValid) {
      const nextStep = state.currentStep + 1;
      if (nextStep <= 5) {
        setState(prev => ({ 
          ...prev, 
          currentStep: nextStep as Step,
          error: null 
        }));
        success(`Proceeding to Step ${nextStep}`);
      }
    } else {
      info('Please review and make necessary changes before proceeding.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DXCHeader />
      <ToastContainer toasts={toasts} onRemove={removeToast} />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Rapid Prototyping Automation
          </h1>
          <p className="text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed">
            Transform your conversation transcripts into comprehensive technical documentation and implementation guides powered by DXC Technology's AI solutions
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress 
            value={progress} 
            showLabel 
            label={`Step ${state.currentStep} of ${STEPS.length}: ${currentStepData?.title}`}
            className="max-w-2xl mx-auto"
          />
        </div>

        {/* Steps Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-4 overflow-x-auto pb-2">
            {STEPS.map((step) => {
              const Icon = step.icon;
              const isActive = step.id === state.currentStep;
              const isCompleted = step.id < state.currentStep;
              
              return (
                <div
                  key={step.id}
                  className={cn(
                    "flex flex-col items-center min-w-[120px] p-3 rounded-lg transition-all",
                    isActive 
                      ? 'bg-purple-100 border-2 border-purple-300' 
                      : isCompleted 
                        ? 'bg-blue-100 border-2 border-blue-300'
                        : 'bg-gray-100 border-2 border-gray-200'
                  )}
                >
                  <Icon 
                    className={cn(
                      "h-6 w-6 mb-2",
                      isActive 
                        ? 'text-purple-600' 
                        : isCompleted 
                          ? 'text-blue-600'
                          : 'text-gray-400'
                    )} 
                  />
                  <span className={cn(
                    "text-sm font-medium text-center",
                    isActive 
                      ? 'text-purple-800' 
                      : isCompleted 
                        ? 'text-blue-800'
                        : 'text-gray-600'
                  )}>
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Error Display */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{state.error}</p>
          </div>
        )}

        {/* Thinking Indicator */}
        {state.isTransitioning && state.transitionType && (
          <div className="mb-8">
            <ThinkingIndicator type={state.transitionType} />
          </div>
        )}

        {/* Step Content */}
        <div className="space-y-6">
          {/* Step 1: File Upload */}
          {!state.isTransitioning && state.currentStep === 1 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Upload Conversation Transcript</CardTitle>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onFileSelect={handleFileUpload}
                  maxSize={5 * 1024 * 1024}
                />
                {state.uploadedFile && (
                  <div className="mt-6 flex justify-center">
                    <Button 
                      variant="primary" 
                      onClick={() => handleValidation('transcript', true)}
                    >
                      Continue to Problem Statement Generation
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Step 2: Problem Statement */}
          {!state.isTransitioning && state.currentStep === 2 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Problem Statement Document</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-xl font-semibold mb-4">Generate Problem Statement</h3>
                  <p className="text-gray-600 mb-6">
                    Claude will analyze your transcript and generate a comprehensive problem statement document.
                  </p>
                  <Button 
                    variant="primary" 
                    onClick={() => handleValidation('problemStatement', true)}
                  >
                    Generate Problem Statement
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Technical Requirements */}
          {!state.isTransitioning && state.currentStep === 3 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Technical Requirements Document</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-xl font-semibold mb-4">Generate Technical Requirements</h3>
                  <p className="text-gray-600 mb-6">
                    Claude will create detailed technical requirements based on your problem statement.
                  </p>
                  <Button 
                    variant="primary" 
                    onClick={() => handleValidation('technicalRequirements', true)}
                  >
                    Generate Technical Requirements
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 4: Architecture Diagram */}
          {!state.isTransitioning && state.currentStep === 4 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>System Architecture Diagram</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <h3 className="text-xl font-semibold mb-4">Generate Architecture Diagram</h3>
                  <p className="text-gray-600 mb-6">
                    Claude will create visual architecture diagrams from your technical requirements.
                  </p>
                  <Button 
                    variant="primary" 
                    onClick={() => handleValidation('architectureDiagram', true)}
                  >
                    Generate Architecture Diagram
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 5: Implementation Prompts */}
          {!state.isTransitioning && state.currentStep === 5 && (
            <Card gradient padding="lg" className="animate-fade-in">
              <CardHeader>
                <CardTitle>Implementation Prompts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
                  <h4 className="font-semibold text-green-800 mb-2">Process Complete!</h4>
                  <p className="text-green-700">
                    Your rapid prototyping automation is complete. Implementation prompts are ready for Loveable AI.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  );
};
