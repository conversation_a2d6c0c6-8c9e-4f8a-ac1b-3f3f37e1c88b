{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/generate-architecture-diagram/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\n\n// For development environments with SSL certificate issues\nif (process.env.NODE_ENV === 'development') {\n  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n}\n\n// Check if API key is configured\nif (!process.env.ANTHROPIC_API_KEY) {\n  console.error('ANTHROPIC_API_KEY is not configured in environment variables');\n}\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',\n});\n\nconst ARCHITECTURE_DIAGRAM_PROMPT = `1. ROLE AND POINT-OF-<PERSON><PERSON><PERSON>\n<PERSON> are a senior solutions architect and technical illustrator trusted to convert written requirements into visual architecture assets.\n\n2. OBJECTIVE\nParse the supplied technical requirements document, identify every relevant component, and deliver a single, professionally formatted solution diagram that would satisfy an enterprise design review.\n\n3. INPUT (VERBATIM)\n<<<\n[This is the content from the technical requirements document]\n>>>\n\nPROCESS (FOLLOW THESE STEPS IN ORDER)\n   1. Inventory – list all actors, systems, services, data stores, queues, APIs, protocols, environments, and user roles.\n   2. Classify & Group – organise items into logical layers (Presentation, Application, Data, Network, Security, External).\n   3. Map Flows – define directional connections, interaction types (REST, gRPC, Message Bus, SFTP, etc.), and cardinalities.\n   4. Validate Coverage – ensure every listed requirement from the source appears in the visual model; flag anything missing with \"TBD\".\n   5. Render – produce the diagram in a style equivalent to Microsoft Visio: clean, minimalist, consistent iconography, landscape orientation.\n   6. Export Code – output an editable text representation (PlantUML AND Mermaid) so the diagram can be tweaked later.\n\n5. OUTPUT SPECIFICATION\n   - Primary: high-resolution SVG (preferred) or PNG graphic, transparent background, max-width 1600 px.\n   - Secondary: plain-text blocks containing:\n       * PlantUML code wrapped in @startuml / @enduml\n       * Mermaid code wrapped in \\`\\`\\`mermaid blocks\n   - Legend & Notes: 5-10 bullet points explaining symbols, color codes, and any assumptions.\n\n6. STYLISTIC AND QUALITY GUIDELINES\n   - Use standard architecture icons (database cylinder, user silhouette, cloud edge, queue stack, etc.).\n   - Label layers with subtle background shading; use sans-serif font 10-12 pt.\n   - Arrowheads denote direction; dashed lines are asynchronous; solid lines are synchronous.\n   - Follow WCAG AA color-contrast guidelines.\n\n7. CONSTRAINTS AND ERROR HANDLING\n   - If critical details are absent, insert a red dashed placeholder box and note it in the legend.\n   - Do not invent business logic beyond the document; only infer necessary connections for coherence.\n   - Output only the diagram image, the two code blocks, and the legend.\n\n8. DELIVERY FORMAT TEMPLATE\n[SVG or PNG IMAGE]\n\n--- begin plantuml ---\n@startuml\n(generated code)\n@enduml\n--- end plantuml ---\n\n--- begin mermaid ---\ngraph LR\n(generated code)\n--- end mermaid ---\n\nLEGEND AND ASSUMPTIONS\n- (list here)`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { technicalRequirementsContent } = await request.json();\n    \n    if (!technicalRequirementsContent) {\n      return NextResponse.json({ error: 'No technical requirements content provided' }, { status: 400 });\n    }\n\n    // Check if API key is configured\n    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo response');\n      \n      // For development, return a mock response\n      return NextResponse.json({\n        success: true,\n        diagramUrl: null,\n        plantUML: `@startuml\n!theme plain\ntitle System Architecture Diagram - Demo Mode\n\npackage \"Presentation Layer\" {\n  [Web Application] as webapp\n  [Mobile App] as mobile\n}\n\npackage \"Application Layer\" {\n  [API Gateway] as gateway\n  [Microservice A] as msA\n  [Microservice B] as msB\n}\n\npackage \"Data Layer\" {\n  database \"Primary DB\" as db1\n  database \"Cache\" as cache\n}\n\nwebapp --> gateway\nmobile --> gateway\ngateway --> msA\ngateway --> msB\nmsA --> db1\nmsB --> cache\n\nnote right of gateway : Demo Mode - Configure OpenAI API key\n@enduml`,\n        mermaid: `graph TB\n    subgraph \"Presentation Layer\"\n        WA[Web Application]\n        MA[Mobile App]\n    end\n    \n    subgraph \"Application Layer\"\n        AG[API Gateway]\n        MSA[Microservice A]\n        MSB[Microservice B]\n    end\n    \n    subgraph \"Data Layer\"\n        DB[(Primary Database)]\n        CACHE[(Cache)]\n    end\n    \n    WA --> AG\n    MA --> AG\n    AG --> MSA\n    AG --> MSB\n    MSA --> DB\n    MSB --> CACHE`,\n        legend: [\n          \"Demo Mode - Claude API not configured\",\n          \"Configure ANTHROPIC_API_KEY to generate real diagrams\",\n          \"This is a sample architecture diagram\",\n          \"Real diagrams will be generated from your technical requirements\"\n        ],\n        message: 'Demo architecture diagram generated. Configure Claude API key for real diagram generation.'\n      });\n    }\n\n    console.log('Generating architecture diagram from technical requirements...');\n    console.log(`Technical requirements content length: ${technicalRequirementsContent.length} characters`);\n\n    // Call Claude API with Sonnet 4\n    console.log('Calling Claude API with Sonnet 4 for architecture diagram...');\n    const fullPrompt = `${ARCHITECTURE_DIAGRAM_PROMPT}\\n\\nTechnical Requirements Document Content:\\n${technicalRequirementsContent}`;\n    console.log(`Full prompt length: ${fullPrompt.length} characters`);\n\n    let response;\n    try {\n      response = await anthropic.messages.create({\n        model: 'claude-sonnet-4-20250514',\n        max_tokens: 4000,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n      console.log('Claude API call successful for architecture diagram');\n    } catch (apiError: any) {\n      console.error('Claude API Error:', apiError);\n\n      // Handle specific API errors\n      if (apiError.status === 401) {\n        return NextResponse.json({\n          error: 'Invalid API key. Please check your Anthropic API key configuration.'\n        }, { status: 401 });\n      } else if (apiError.status === 429) {\n        return NextResponse.json({\n          error: 'Rate limit exceeded. Please try again in a few minutes.'\n        }, { status: 429 });\n      } else if (apiError.message?.includes('model')) {\n        return NextResponse.json({\n          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.'\n        }, { status: 400 });\n      } else {\n        return NextResponse.json({\n          error: `Claude API error: ${apiError.message || 'Unknown error'}`\n        }, { status: 500 });\n      }\n    }\n\n    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    // Parse the response to extract PlantUML, Mermaid, and legend\n    const parsedResponse = parseArchitectureResponse(analysisText);\n\n    console.log('Parsed Mermaid code:', parsedResponse.mermaid);\n    console.log('Parsed PlantUML code:', parsedResponse.plantUML);\n    console.log('Parsed legend:', parsedResponse.legend);\n\n    // For now, we'll return the text-based diagram codes\n    // In a full implementation, you might want to render these to actual images\n    return NextResponse.json({\n      success: true,\n      diagramUrl: null, // Would contain actual image URL in full implementation\n      plantUML: parsedResponse.plantUML,\n      mermaid: parsedResponse.mermaid,\n      legend: parsedResponse.legend,\n      fullResponse: analysisText,\n      message: 'Architecture diagram generated successfully with Claude Sonnet 4!'\n    });\n\n  } catch (error) {\n    console.error('Error generating architecture diagram:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate architecture diagram' }, \n      { status: 500 }\n    );\n  }\n}\n\nfunction parseArchitectureResponse(text: string) {\n  const result = {\n    plantUML: '',\n    mermaid: '',\n    legend: [] as string[]\n  };\n\n  console.log('Parsing architecture response, length:', text.length);\n\n  // Extract PlantUML code - try multiple formats\n  let plantUMLMatch = text.match(/--- begin plantuml ---\\s*([\\s\\S]*?)\\s*--- end plantuml ---/i);\n  if (plantUMLMatch) {\n    result.plantUML = plantUMLMatch[1].trim();\n  } else {\n    // Try alternative format with @startuml/@enduml\n    plantUMLMatch = text.match(/@startuml([\\s\\S]*?)@enduml/i);\n    if (plantUMLMatch) {\n      result.plantUML = `@startuml${plantUMLMatch[1]}@enduml`;\n    } else {\n      // Try looking for PlantUML in code blocks\n      plantUMLMatch = text.match(/```plantuml\\s*([\\s\\S]*?)\\s*```/i);\n      if (plantUMLMatch) {\n        result.plantUML = plantUMLMatch[1].trim();\n      }\n    }\n  }\n\n  // Extract Mermaid code - try multiple formats\n  let mermaidMatch = text.match(/--- begin mermaid ---\\s*([\\s\\S]*?)\\s*--- end mermaid ---/i);\n  if (mermaidMatch) {\n    result.mermaid = mermaidMatch[1].trim();\n  } else {\n    // Try alternative format with ```mermaid\n    mermaidMatch = text.match(/```mermaid\\s*([\\s\\S]*?)\\s*```/i);\n    if (mermaidMatch) {\n      result.mermaid = mermaidMatch[1].trim();\n    } else {\n      // Try looking for graph/flowchart patterns\n      mermaidMatch = text.match(/graph\\s+(?:TB|TD|BT|RL|LR)\\s*([\\s\\S]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n      if (mermaidMatch) {\n        result.mermaid = `graph TB\\n${mermaidMatch[1].trim()}`;\n      } else {\n        // Try flowchart pattern\n        mermaidMatch = text.match(/flowchart\\s+(?:TB|TD|BT|RL|LR)\\s*([\\s\\S]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n        if (mermaidMatch) {\n          result.mermaid = `flowchart TB\\n${mermaidMatch[1].trim()}`;\n        }\n      }\n    }\n  }\n\n  // Extract legend\n  const legendMatch = text.match(/LEGEND AND ASSUMPTIONS\\s*([\\s\\S]*?)(?=\\n\\n|$)/i);\n  if (legendMatch) {\n    const legendText = legendMatch[1].trim();\n    result.legend = legendText.split('\\n')\n      .map(line => line.trim())\n      .filter(line => line.length > 0 && (line.startsWith('-') || line.startsWith('•')))\n      .map(line => line.replace(/^[-•]\\s*/, '').trim())\n      .filter(line => line.length > 0);\n  }\n\n  // If no structured formats found, try to extract any diagram-like content\n  if (!result.mermaid && !result.plantUML) {\n    console.log('No structured diagram found, looking for any diagram content...');\n\n    // Look for any graph-like content\n    const graphMatch = text.match(/(graph|flowchart|sequenceDiagram|classDiagram)[\\s\\S]*?(?=\\n\\n|$)/i);\n    if (graphMatch) {\n      result.mermaid = graphMatch[0].trim();\n    }\n  }\n\n  console.log('Parsed result:', {\n    plantUMLLength: result.plantUML.length,\n    mermaidLength: result.mermaid.length,\n    legendCount: result.legend.length\n  });\n\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,2DAA2D;AAC3D,wCAA4C;IAC1C,QAAQ,GAAG,CAAC,4BAA4B,GAAG;AAC7C;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC3C;AAEA,MAAM,8BAA8B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aAoDxB,CAAC;AAEP,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,4BAA4B,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3D,IAAI,CAAC,8BAA8B;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6C,GAAG;gBAAE,QAAQ;YAAI;QAClG;QAEA,iCAAiC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;YACrG,QAAQ,KAAK,CAAC;YAEd,0CAA0C;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,YAAY;gBACZ,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BZ,CAAC;gBACA,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;iBAsBD,CAAC;gBACV,QAAQ;oBACN;oBACA;oBACA;oBACA;iBACD;gBACD,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,6BAA6B,MAAM,CAAC,WAAW,CAAC;QAEtG,gCAAgC;QAChC,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,GAAG,4BAA4B,8CAA8C,EAAE,8BAA8B;QAChI,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,MAAM,CAAC,WAAW,CAAC;QAEjE,IAAI;QACJ,IAAI;YACF,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,6BAA6B;YAC7B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,OAAO,EAAE,SAAS,UAAU;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,CAAC,kBAAkB,EAAE,SAAS,OAAO,IAAI,iBAAiB;gBACnE,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,8DAA8D;QAC9D,MAAM,iBAAiB,0BAA0B;QAEjD,QAAQ,GAAG,CAAC,wBAAwB,eAAe,OAAO;QAC1D,QAAQ,GAAG,CAAC,yBAAyB,eAAe,QAAQ;QAC5D,QAAQ,GAAG,CAAC,kBAAkB,eAAe,MAAM;QAEnD,qDAAqD;QACrD,4EAA4E;QAC5E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,YAAY;YACZ,UAAU,eAAe,QAAQ;YACjC,SAAS,eAAe,OAAO;YAC/B,QAAQ,eAAe,MAAM;YAC7B,cAAc;YACd,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0C,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,0BAA0B,IAAY;IAC7C,MAAM,SAAS;QACb,UAAU;QACV,SAAS;QACT,QAAQ,EAAE;IACZ;IAEA,QAAQ,GAAG,CAAC,0CAA0C,KAAK,MAAM;IAEjE,+CAA+C;IAC/C,IAAI,gBAAgB,KAAK,KAAK,CAAC;IAC/B,IAAI,eAAe;QACjB,OAAO,QAAQ,GAAG,aAAa,CAAC,EAAE,CAAC,IAAI;IACzC,OAAO;QACL,gDAAgD;QAChD,gBAAgB,KAAK,KAAK,CAAC;QAC3B,IAAI,eAAe;YACjB,OAAO,QAAQ,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC;QACzD,OAAO;YACL,0CAA0C;YAC1C,gBAAgB,KAAK,KAAK,CAAC;YAC3B,IAAI,eAAe;gBACjB,OAAO,QAAQ,GAAG,aAAa,CAAC,EAAE,CAAC,IAAI;YACzC;QACF;IACF;IAEA,8CAA8C;IAC9C,IAAI,eAAe,KAAK,KAAK,CAAC;IAC9B,IAAI,cAAc;QAChB,OAAO,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC,IAAI;IACvC,OAAO;QACL,yCAAyC;QACzC,eAAe,KAAK,KAAK,CAAC;QAC1B,IAAI,cAAc;YAChB,OAAO,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC,IAAI;QACvC,OAAO;YACL,2CAA2C;YAC3C,eAAe,KAAK,KAAK,CAAC;YAC1B,IAAI,cAAc;gBAChB,OAAO,OAAO,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,CAAC,IAAI,IAAI;YACxD,OAAO;gBACL,wBAAwB;gBACxB,eAAe,KAAK,KAAK,CAAC;gBAC1B,IAAI,cAAc;oBAChB,OAAO,OAAO,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,EAAE,CAAC,IAAI,IAAI;gBAC5D;YACF;QACF;IACF;IAEA,iBAAiB;IACjB,MAAM,cAAc,KAAK,KAAK,CAAC;IAC/B,IAAI,aAAa;QACf,MAAM,aAAa,WAAW,CAAC,EAAE,CAAC,IAAI;QACtC,OAAO,MAAM,GAAG,WAAW,KAAK,CAAC,MAC9B,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC,IAAI,GAC/E,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,YAAY,IAAI,IAAI,IAC7C,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAClC;IAEA,0EAA0E;IAC1E,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,QAAQ,EAAE;QACvC,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,aAAa,KAAK,KAAK,CAAC;QAC9B,IAAI,YAAY;YACd,OAAO,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI;QACrC;IACF;IAEA,QAAQ,GAAG,CAAC,kBAAkB;QAC5B,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QACtC,eAAe,OAAO,OAAO,CAAC,MAAM;QACpC,aAAa,OAAO,MAAM,CAAC,MAAM;IACnC;IAEA,OAAO;AACT", "debugId": null}}]}