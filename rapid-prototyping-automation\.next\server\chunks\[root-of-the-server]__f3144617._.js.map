{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/generate-architecture-diagram/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\n\n// For development environments with SSL certificate issues\nif (process.env.NODE_ENV === 'development') {\n  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n}\n\n// Check if API key is configured\nif (!process.env.ANTHROPIC_API_KEY) {\n  console.error('ANTHROPIC_API_KEY is not configured in environment variables');\n}\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',\n});\n\nconst MERMAID_DIAGRAM_PROMPT = `Generate a Mermaid architecture diagram from the technical requirements.\n\nSTRICT SYNTAX RULES:\n1. Start with exactly: graph TD\n2. Node IDs must be simple: A, B, Frontend, API, DB (no spaces, no hyphens, no special chars)\n3. Labels in quotes: A[\"Frontend Application\"]\n4. Connections: A --> B\n5. Subgraphs: subgraph \"Layer Name\"\n\nVALID EXAMPLE:\ngraph TD\n    A[\"Frontend Application\"] --> B[\"API Gateway\"]\n    B --> C[\"Authentication Service\"]\n    B --> D[\"Business Logic API\"]\n    D --> E[\"Primary Database\"]\n    D --> F[\"Redis Cache\"]\n\n    subgraph \"Presentation\"\n        A\n    end\n\n    subgraph \"Application\"\n        B\n        C\n        D\n    end\n\n    subgraph \"Data\"\n        E\n        F\n    end\n\nOUTPUT ONLY THE MERMAID CODE - NO EXPLANATIONS:`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { technicalRequirements } = await request.json();\n    \n    if (!technicalRequirements) {\n      return NextResponse.json({ error: 'Technical requirements are required' }, { status: 400 });\n    }\n\n    console.log(`Processing technical requirements: ${technicalRequirements.length} characters`);\n\n    // Check if API key is configured\n    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo diagram');\n      \n      // Return demo Mermaid diagram\n      const demoMermaid = `graph TD\n    A[\"Frontend Application\"] --> B[\"API Gateway\"]\n    B --> C[\"Authentication Service\"]\n    B --> D[\"Business Logic API\"]\n    D --> E[\"Primary Database\"]\n    D --> F[\"Redis Cache\"]\n    B --> G[\"External Services\"]\n\n    subgraph \"Presentation Layer\"\n        A\n    end\n\n    subgraph \"Application Layer\"\n        B\n        C\n        D\n    end\n\n    subgraph \"Data Layer\"\n        E\n        F\n    end\n\n    subgraph \"External\"\n        G\n    end`;\n\n      return NextResponse.json({\n        success: true,\n        mermaidCode: demoMermaid,\n        message: 'Demo architecture diagram generated. Configure Claude API key for real diagram generation.'\n      });\n    }\n\n    console.log('Generating architecture diagram with Claude Sonnet 4...');\n    \n    // Call Claude API with simplified prompt\n    const fullPrompt = `${MERMAID_DIAGRAM_PROMPT}\\n\\nTechnical Requirements:\\n${technicalRequirements}`;\n    \n    let response;\n    try {\n      response = await anthropic.messages.create({\n        model: 'claude-sonnet-4-20250514',\n        max_tokens: 20000,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n      console.log('Claude API call successful for architecture diagram');\n    } catch (apiError: any) {\n      console.error('Claude API Error:', apiError);\n      \n      if (apiError.status === 401) {\n        return NextResponse.json({ \n          error: 'Invalid API key. Please check your Anthropic API key configuration.' \n        }, { status: 401 });\n      } else if (apiError.status === 429) {\n        return NextResponse.json({ \n          error: 'Rate limit exceeded. Please try again in a few minutes.' \n        }, { status: 429 });\n      } else {\n        return NextResponse.json({ \n          error: `Claude API error: ${apiError.message || 'Unknown error'}` \n        }, { status: 500 });\n      }\n    }\n\n    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    // Clean and validate the Mermaid code\n    const mermaidCode = cleanMermaidCode(analysisText);\n    \n    console.log('Generated Mermaid code:', mermaidCode.substring(0, 200));\n\n    return NextResponse.json({\n      success: true,\n      mermaidCode: mermaidCode,\n      message: 'Architecture diagram generated successfully with Claude Sonnet 4!'\n    });\n\n  } catch (error) {\n    console.error('Error generating architecture diagram:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate architecture diagram' }, \n      { status: 500 }\n    );\n  }\n}\n\nfunction cleanMermaidCode(text: string): string {\n  // Remove any markdown code block markers\n  let cleaned = text\n    .replace(/^```mermaid\\s*/i, '')\n    .replace(/\\s*```$/i, '')\n    .replace(/^```\\s*/i, '')\n    .trim();\n\n  // Remove any extra whitespace and normalize line endings\n  cleaned = cleaned.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n\n  // Ensure it starts with a valid Mermaid diagram type\n  if (!cleaned.match(/^(graph|flowchart|sequenceDiagram|classDiagram)/i)) {\n    // If it doesn't start with a diagram type, try to detect and add one\n    if (cleaned.includes('-->') || cleaned.includes('---')) {\n      cleaned = `graph TD\\n${cleaned}`;\n    }\n  }\n\n  // Fix common Mermaid syntax issues\n  cleaned = cleaned\n    // Fix node labels - ensure they're properly quoted\n    .replace(/(\\w+)\\[([^\\]]+)\\]/g, (match, nodeId, label) => {\n      // If label contains spaces or special chars, wrap in quotes\n      if (label.includes(' ') || /[^a-zA-Z0-9]/.test(label)) {\n        return `${nodeId}[\"${label.replace(/\"/g, '')}\"]`;\n      }\n      return match;\n    })\n    // Fix arrows with proper spacing\n    .replace(/\\s*-->\\s*/g, ' --> ')\n    // Remove extra spaces but preserve line structure\n    .replace(/[ \\t]+/g, ' ')\n    // Clean up line endings\n    .replace(/\\n\\s+/g, '\\n')\n    .replace(/\\n+/g, '\\n')\n    .trim();\n\n  return cleaned;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,2DAA2D;AAC3D,wCAA4C;IAC1C,QAAQ,GAAG,CAAC,4BAA4B,GAAG;AAC7C;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC3C;AAEA,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAgCe,CAAC;AAEzC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,qBAAqB,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpD,IAAI,CAAC,uBAAuB;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAsC,GAAG;gBAAE,QAAQ;YAAI;QAC3F;QAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,sBAAsB,MAAM,CAAC,WAAW,CAAC;QAE3F,iCAAiC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;YACrG,QAAQ,KAAK,CAAC;YAEd,8BAA8B;YAC9B,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;OAyBpB,CAAC;YAEF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,aAAa;gBACb,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC;QAEZ,yCAAyC;QACzC,MAAM,aAAa,GAAG,uBAAuB,6BAA6B,EAAE,uBAAuB;QAEnG,IAAI;QACJ,IAAI;YACF,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,CAAC,kBAAkB,EAAE,SAAS,OAAO,IAAI,iBAAiB;gBACnE,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,sCAAsC;QACtC,MAAM,cAAc,iBAAiB;QAErC,QAAQ,GAAG,CAAC,2BAA2B,YAAY,SAAS,CAAC,GAAG;QAEhE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,aAAa;YACb,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0C,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,iBAAiB,IAAY;IACpC,yCAAyC;IACzC,IAAI,UAAU,KACX,OAAO,CAAC,mBAAmB,IAC3B,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,YAAY,IACpB,IAAI;IAEP,yDAAyD;IACzD,UAAU,QAAQ,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,OAAO;IAExD,qDAAqD;IACrD,IAAI,CAAC,QAAQ,KAAK,CAAC,qDAAqD;QACtE,qEAAqE;QACrE,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,QAAQ;YACtD,UAAU,CAAC,UAAU,EAAE,SAAS;QAClC;IACF;IAEA,mCAAmC;IACnC,UAAU,OACR,mDAAmD;KAClD,OAAO,CAAC,sBAAsB,CAAC,OAAO,QAAQ;QAC7C,4DAA4D;QAC5D,IAAI,MAAM,QAAQ,CAAC,QAAQ,eAAe,IAAI,CAAC,QAAQ;YACrD,OAAO,GAAG,OAAO,EAAE,EAAE,MAAM,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;QAClD;QACA,OAAO;IACT,EACA,iCAAiC;KAChC,OAAO,CAAC,cAAc,QACvB,kDAAkD;KACjD,OAAO,CAAC,WAAW,IACpB,wBAAwB;KACvB,OAAO,CAAC,UAAU,MAClB,OAAO,CAAC,QAAQ,MAChB,IAAI;IAEP,OAAO;AACT", "debugId": null}}]}