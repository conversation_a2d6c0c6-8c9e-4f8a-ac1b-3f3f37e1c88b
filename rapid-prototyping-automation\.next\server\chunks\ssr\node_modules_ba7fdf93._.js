module.exports = {

"[project]/node_modules/d3-shape/src/constant.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function __TURBOPACK__default__export__(x) {
    return function constant() {
        return x;
    };
}
}}),
"[project]/node_modules/d3-path/src/path.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Path": (()=>Path),
    "path": (()=>path),
    "pathRound": (()=>pathRound)
});
const pi = Math.PI, tau = 2 * pi, epsilon = 1e-6, tauEpsilon = tau - epsilon;
function append(strings) {
    this._ += strings[0];
    for(let i = 1, n = strings.length; i < n; ++i){
        this._ += arguments[i] + strings[i];
    }
}
function appendRound(digits) {
    let d = Math.floor(digits);
    if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);
    if (d > 15) return append;
    const k = 10 ** d;
    return function(strings) {
        this._ += strings[0];
        for(let i = 1, n = strings.length; i < n; ++i){
            this._ += Math.round(arguments[i] * k) / k + strings[i];
        }
    };
}
class Path {
    constructor(digits){
        this._x0 = this._y0 = this._x1 = this._y1 = null; // end of current subpath
        this._ = "";
        this._append = digits == null ? append : appendRound(digits);
    }
    moveTo(x, y) {
        this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;
    }
    closePath() {
        if (this._x1 !== null) {
            this._x1 = this._x0, this._y1 = this._y0;
            this._append`Z`;
        }
    }
    lineTo(x, y) {
        this._append`L${this._x1 = +x},${this._y1 = +y}`;
    }
    quadraticCurveTo(x1, y1, x, y) {
        this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;
    }
    bezierCurveTo(x1, y1, x2, y2, x, y) {
        this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;
    }
    arcTo(x1, y1, x2, y2, r) {
        x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;
        // Is the radius negative? Error.
        if (r < 0) throw new Error(`negative radius: ${r}`);
        let x0 = this._x1, y0 = this._y1, x21 = x2 - x1, y21 = y2 - y1, x01 = x0 - x1, y01 = y0 - y1, l01_2 = x01 * x01 + y01 * y01;
        // Is this path empty? Move to (x1,y1).
        if (this._x1 === null) {
            this._append`M${this._x1 = x1},${this._y1 = y1}`;
        } else if (!(l01_2 > epsilon)) ;
        else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {
            this._append`L${this._x1 = x1},${this._y1 = y1}`;
        } else {
            let x20 = x2 - x0, y20 = y2 - y0, l21_2 = x21 * x21 + y21 * y21, l20_2 = x20 * x20 + y20 * y20, l21 = Math.sqrt(l21_2), l01 = Math.sqrt(l01_2), l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2), t01 = l / l01, t21 = l / l21;
            // If the start tangent is not coincident with (x0,y0), line to.
            if (Math.abs(t01 - 1) > epsilon) {
                this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;
            }
            this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;
        }
    }
    arc(x, y, r, a0, a1, ccw) {
        x = +x, y = +y, r = +r, ccw = !!ccw;
        // Is the radius negative? Error.
        if (r < 0) throw new Error(`negative radius: ${r}`);
        let dx = r * Math.cos(a0), dy = r * Math.sin(a0), x0 = x + dx, y0 = y + dy, cw = 1 ^ ccw, da = ccw ? a0 - a1 : a1 - a0;
        // Is this path empty? Move to (x0,y0).
        if (this._x1 === null) {
            this._append`M${x0},${y0}`;
        } else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {
            this._append`L${x0},${y0}`;
        }
        // Is this arc empty? We’re done.
        if (!r) return;
        // Does the angle go the wrong way? Flip the direction.
        if (da < 0) da = da % tau + tau;
        // Is this a complete circle? Draw two arcs to complete the circle.
        if (da > tauEpsilon) {
            this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;
        } else if (da > epsilon) {
            this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;
        }
    }
    rect(x, y, w, h) {
        this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;
    }
    toString() {
        return this._;
    }
}
function path() {
    return new Path;
}
// Allow instanceof d3.path
path.prototype = Path.prototype;
function pathRound(digits = 3) {
    return new Path(+digits);
}
}}),
"[project]/node_modules/d3-shape/src/path.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "withPath": (()=>withPath)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$path$2f$src$2f$path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-path/src/path.js [app-ssr] (ecmascript)");
;
function withPath(shape) {
    let digits = 3;
    shape.digits = function(_) {
        if (!arguments.length) return digits;
        if (_ == null) {
            digits = null;
        } else {
            const d = Math.floor(_);
            if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);
            digits = d;
        }
        return shape;
    };
    return ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$path$2f$src$2f$path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Path"](digits);
}
}}),
"[project]/node_modules/d3-shape/src/arc.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/constant.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/math.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/path.js [app-ssr] (ecmascript)");
;
;
;
function arcInnerRadius(d) {
    return d.innerRadius;
}
function arcOuterRadius(d) {
    return d.outerRadius;
}
function arcStartAngle(d) {
    return d.startAngle;
}
function arcEndAngle(d) {
    return d.endAngle;
}
function arcPadAngle(d) {
    return d && d.padAngle; // Note: optional!
}
function intersect(x0, y0, x1, y1, x2, y2, x3, y3) {
    var x10 = x1 - x0, y10 = y1 - y0, x32 = x3 - x2, y32 = y3 - y2, t = y32 * x10 - x32 * y10;
    if (t * t < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) return;
    t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;
    return [
        x0 + t * x10,
        y0 + t * y10
    ];
}
// Compute perpendicular offset line of length rc.
// http://mathworld.wolfram.com/Circle-LineIntersection.html
function cornerTangents(x0, y0, x1, y1, r1, rc, cw) {
    var x01 = x0 - x1, y01 = y0 - y1, lo = (cw ? rc : -rc) / (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sqrt"])(x01 * x01 + y01 * y01), ox = lo * y01, oy = -lo * x01, x11 = x0 + ox, y11 = y0 + oy, x10 = x1 + ox, y10 = y1 + oy, x00 = (x11 + x10) / 2, y00 = (y11 + y10) / 2, dx = x10 - x11, dy = y10 - y11, d2 = dx * dx + dy * dy, r = r1 - rc, D = x11 * y10 - x10 * y11, d = (dy < 0 ? -1 : 1) * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sqrt"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["max"])(0, r * r * d2 - D * D)), cx0 = (D * dy - dx * d) / d2, cy0 = (-D * dx - dy * d) / d2, cx1 = (D * dy + dx * d) / d2, cy1 = (-D * dx + dy * d) / d2, dx0 = cx0 - x00, dy0 = cy0 - y00, dx1 = cx1 - x00, dy1 = cy1 - y00;
    // Pick the closer of the two intersection points.
    // TODO Is there a faster way to determine which intersection to use?
    if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;
    return {
        cx: cx0,
        cy: cy0,
        x01: -ox,
        y01: -oy,
        x11: cx0 * (r1 / r - 1),
        y11: cy0 * (r1 / r - 1)
    };
}
function __TURBOPACK__default__export__() {
    var innerRadius = arcInnerRadius, outerRadius = arcOuterRadius, cornerRadius = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(0), padRadius = null, startAngle = arcStartAngle, endAngle = arcEndAngle, padAngle = arcPadAngle, context = null, path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withPath"])(arc);
    function arc() {
        var buffer, r, r0 = +innerRadius.apply(this, arguments), r1 = +outerRadius.apply(this, arguments), a0 = startAngle.apply(this, arguments) - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["halfPi"], a1 = endAngle.apply(this, arguments) - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["halfPi"], da = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["abs"])(a1 - a0), cw = a1 > a0;
        if (!context) context = buffer = path();
        // Ensure that the outer radius is always larger than the inner radius.
        if (r1 < r0) r = r1, r1 = r0, r0 = r;
        // Is it a point?
        if (!(r1 > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"])) context.moveTo(0, 0);
        else if (da > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tau"] - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) {
            context.moveTo(r1 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cos"])(a0), r1 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(a0));
            context.arc(0, 0, r1, a0, a1, !cw);
            if (r0 > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) {
                context.moveTo(r0 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cos"])(a1), r0 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(a1));
                context.arc(0, 0, r0, a1, a0, cw);
            }
        } else {
            var a01 = a0, a11 = a1, a00 = a0, a10 = a1, da0 = da, da1 = da, ap = padAngle.apply(this, arguments) / 2, rp = ap > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"] && (padRadius ? +padRadius.apply(this, arguments) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sqrt"])(r0 * r0 + r1 * r1)), rc = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["min"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["abs"])(r1 - r0) / 2, +cornerRadius.apply(this, arguments)), rc0 = rc, rc1 = rc, t0, t1;
            // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.
            if (rp > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) {
                var p0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["asin"])(rp / r0 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(ap)), p1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["asin"])(rp / r1 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(ap));
                if ((da0 -= p0 * 2) > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) p0 *= cw ? 1 : -1, a00 += p0, a10 -= p0;
                else da0 = 0, a00 = a10 = (a0 + a1) / 2;
                if ((da1 -= p1 * 2) > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) p1 *= cw ? 1 : -1, a01 += p1, a11 -= p1;
                else da1 = 0, a01 = a11 = (a0 + a1) / 2;
            }
            var x01 = r1 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cos"])(a01), y01 = r1 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(a01), x10 = r0 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cos"])(a10), y10 = r0 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(a10);
            // Apply rounded corners?
            if (rc > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) {
                var x11 = r1 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cos"])(a11), y11 = r1 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(a11), x00 = r0 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cos"])(a00), y00 = r0 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(a00), oc;
                // Restrict the corner radius according to the sector angle. If this
                // intersection fails, it’s probably because the arc is too small, so
                // disable the corner radius entirely.
                if (da < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pi"]) {
                    if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {
                        var ax = x01 - oc[0], ay = y01 - oc[1], bx = x11 - oc[0], by = y11 - oc[1], kc = 1 / (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["acos"])((ax * bx + ay * by) / ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sqrt"])(ax * ax + ay * ay) * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sqrt"])(bx * bx + by * by))) / 2), lc = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sqrt"])(oc[0] * oc[0] + oc[1] * oc[1]);
                        rc0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["min"])(rc, (r0 - lc) / (kc - 1));
                        rc1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["min"])(rc, (r1 - lc) / (kc + 1));
                    } else {
                        rc0 = rc1 = 0;
                    }
                }
            }
            // Is the sector collapsed to a line?
            if (!(da1 > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"])) context.moveTo(x01, y01);
            else if (rc1 > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) {
                t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);
                t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);
                context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);
                // Have the corners merged?
                if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.y01, t0.x01), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.y01, t1.x01), !cw);
                else {
                    context.arc(t0.cx, t0.cy, rc1, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.y01, t0.x01), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.y11, t0.x11), !cw);
                    context.arc(0, 0, r1, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.cy + t0.y11, t0.cx + t0.x11), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.cy + t1.y11, t1.cx + t1.x11), !cw);
                    context.arc(t1.cx, t1.cy, rc1, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.y11, t1.x11), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.y01, t1.x01), !cw);
                }
            } else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);
            // Is there no inner ring, and it’s a circular sector?
            // Or perhaps it’s an annular sector collapsed due to padding?
            if (!(r0 > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) || !(da0 > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"])) context.lineTo(x10, y10);
            else if (rc0 > __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["epsilon"]) {
                t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);
                t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);
                context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);
                // Have the corners merged?
                if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.y01, t0.x01), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.y01, t1.x01), !cw);
                else {
                    context.arc(t0.cx, t0.cy, rc0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.y01, t0.x01), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.y11, t0.x11), !cw);
                    context.arc(0, 0, r0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t0.cy + t0.y11, t0.cx + t0.x11), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.cy + t1.y11, t1.cx + t1.x11), cw);
                    context.arc(t1.cx, t1.cy, rc0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.y11, t1.x11), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["atan2"])(t1.y01, t1.x01), !cw);
                }
            } else context.arc(0, 0, r0, a10, a00, cw);
        }
        context.closePath();
        if (buffer) return context = null, buffer + "" || null;
    }
    arc.centroid = function() {
        var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2, a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pi"] / 2;
        return [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cos"])(a) * r,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sin"])(a) * r
        ];
    };
    arc.innerRadius = function(_) {
        return arguments.length ? (innerRadius = typeof _ === "function" ? _ : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(+_), arc) : innerRadius;
    };
    arc.outerRadius = function(_) {
        return arguments.length ? (outerRadius = typeof _ === "function" ? _ : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(+_), arc) : outerRadius;
    };
    arc.cornerRadius = function(_) {
        return arguments.length ? (cornerRadius = typeof _ === "function" ? _ : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(+_), arc) : cornerRadius;
    };
    arc.padRadius = function(_) {
        return arguments.length ? (padRadius = _ == null ? null : typeof _ === "function" ? _ : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(+_), arc) : padRadius;
    };
    arc.startAngle = function(_) {
        return arguments.length ? (startAngle = typeof _ === "function" ? _ : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(+_), arc) : startAngle;
    };
    arc.endAngle = function(_) {
        return arguments.length ? (endAngle = typeof _ === "function" ? _ : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(+_), arc) : endAngle;
    };
    arc.padAngle = function(_) {
        return arguments.length ? (padAngle = typeof _ === "function" ? _ : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(+_), arc) : padAngle;
    };
    arc.context = function(_) {
        return arguments.length ? (context = _ == null ? null : _, arc) : context;
    };
    return arc;
}
}}),
"[project]/node_modules/d3-shape/src/arc.js [app-ssr] (ecmascript) <export default as arc>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "arc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$arc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$arc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/arc.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/mermaid/dist/svgDrawCommon-5e1cfd1d.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "a": (()=>drawBackgroundRect),
    "b": (()=>drawEmbeddedImage),
    "c": (()=>drawImage),
    "d": (()=>drawRect),
    "e": (()=>getTextObj),
    "f": (()=>drawText),
    "g": (()=>getNoteRect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@braintree/sanitize-url/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-ssr] (ecmascript)");
;
;
const drawRect = (element, rectData)=>{
    const rectElement = element.append("rect");
    rectElement.attr("x", rectData.x);
    rectElement.attr("y", rectData.y);
    rectElement.attr("fill", rectData.fill);
    rectElement.attr("stroke", rectData.stroke);
    rectElement.attr("width", rectData.width);
    rectElement.attr("height", rectData.height);
    if (rectData.name) {
        rectElement.attr("name", rectData.name);
    }
    rectData.rx !== void 0 && rectElement.attr("rx", rectData.rx);
    rectData.ry !== void 0 && rectElement.attr("ry", rectData.ry);
    if (rectData.attrs !== void 0) {
        for(const attrKey in rectData.attrs){
            rectElement.attr(attrKey, rectData.attrs[attrKey]);
        }
    }
    rectData.class !== void 0 && rectElement.attr("class", rectData.class);
    return rectElement;
};
const drawBackgroundRect = (element, bounds)=>{
    const rectData = {
        x: bounds.startx,
        y: bounds.starty,
        width: bounds.stopx - bounds.startx,
        height: bounds.stopy - bounds.starty,
        fill: bounds.fill,
        stroke: bounds.stroke,
        class: "rect"
    };
    const rectElement = drawRect(element, rectData);
    rectElement.lower();
};
const drawText = (element, textData)=>{
    const nText = textData.text.replace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["J"], " ");
    const textElem = element.append("text");
    textElem.attr("x", textData.x);
    textElem.attr("y", textData.y);
    textElem.attr("class", "legend");
    textElem.style("text-anchor", textData.anchor);
    textData.class !== void 0 && textElem.attr("class", textData.class);
    const tspan = textElem.append("tspan");
    tspan.attr("x", textData.x + textData.textMargin * 2);
    tspan.text(nText);
    return textElem;
};
const drawImage = (elem, x, y, link)=>{
    const imageElement = elem.append("image");
    imageElement.attr("x", x);
    imageElement.attr("y", y);
    const sanitizedLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sanitizeUrl"])(link);
    imageElement.attr("xlink:href", sanitizedLink);
};
const drawEmbeddedImage = (element, x, y, link)=>{
    const imageElement = element.append("use");
    imageElement.attr("x", x);
    imageElement.attr("y", y);
    const sanitizedLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sanitizeUrl"])(link);
    imageElement.attr("xlink:href", `#${sanitizedLink}`);
};
const getNoteRect = ()=>{
    const noteRectData = {
        x: 0,
        y: 0,
        width: 100,
        height: 100,
        fill: "#EDF2AE",
        stroke: "#666",
        anchor: "start",
        rx: 0,
        ry: 0
    };
    return noteRectData;
};
const getTextObj = ()=>{
    const testObject = {
        x: 0,
        y: 0,
        width: 100,
        height: 100,
        "text-anchor": "start",
        style: "#666",
        textMargin: 0,
        rx: 0,
        ry: 0,
        tspan: true
    };
    return testObject;
};
;
}}),
"[project]/node_modules/mermaid/dist/journeyDiagram-6625b456.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "diagram": (()=>diagram)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$arc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__arc$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/arc.js [app-ssr] (ecmascript) <export default as arc>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-ssr] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$svgDrawCommon$2d$5e1cfd1d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/svgDrawCommon-5e1cfd1d.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ts$2d$dedent$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ts-dedent/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@braintree/sanitize-url/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dompurify/dist/purify.es.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
var parser = function() {
    var o = function(k, v, o2, l) {
        for(o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);
        return o2;
    }, $V0 = [
        6,
        8,
        10,
        11,
        12,
        14,
        16,
        17,
        18
    ], $V1 = [
        1,
        9
    ], $V2 = [
        1,
        10
    ], $V3 = [
        1,
        11
    ], $V4 = [
        1,
        12
    ], $V5 = [
        1,
        13
    ], $V6 = [
        1,
        14
    ];
    var parser2 = {
        trace: function trace() {},
        yy: {},
        symbols_: {
            "error": 2,
            "start": 3,
            "journey": 4,
            "document": 5,
            "EOF": 6,
            "line": 7,
            "SPACE": 8,
            "statement": 9,
            "NEWLINE": 10,
            "title": 11,
            "acc_title": 12,
            "acc_title_value": 13,
            "acc_descr": 14,
            "acc_descr_value": 15,
            "acc_descr_multiline_value": 16,
            "section": 17,
            "taskName": 18,
            "taskData": 19,
            "$accept": 0,
            "$end": 1
        },
        terminals_: {
            2: "error",
            4: "journey",
            6: "EOF",
            8: "SPACE",
            10: "NEWLINE",
            11: "title",
            12: "acc_title",
            13: "acc_title_value",
            14: "acc_descr",
            15: "acc_descr_value",
            16: "acc_descr_multiline_value",
            17: "section",
            18: "taskName",
            19: "taskData"
        },
        productions_: [
            0,
            [
                3,
                3
            ],
            [
                5,
                0
            ],
            [
                5,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                1
            ],
            [
                7,
                1
            ],
            [
                7,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                2
            ]
        ],
        performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {
            var $0 = $$.length - 1;
            switch(yystate){
                case 1:
                    return $$[$0 - 1];
                case 2:
                    this.$ = [];
                    break;
                case 3:
                    $$[$0 - 1].push($$[$0]);
                    this.$ = $$[$0 - 1];
                    break;
                case 4:
                case 5:
                    this.$ = $$[$0];
                    break;
                case 6:
                case 7:
                    this.$ = [];
                    break;
                case 8:
                    yy.setDiagramTitle($$[$0].substr(6));
                    this.$ = $$[$0].substr(6);
                    break;
                case 9:
                    this.$ = $$[$0].trim();
                    yy.setAccTitle(this.$);
                    break;
                case 10:
                case 11:
                    this.$ = $$[$0].trim();
                    yy.setAccDescription(this.$);
                    break;
                case 12:
                    yy.addSection($$[$0].substr(8));
                    this.$ = $$[$0].substr(8);
                    break;
                case 13:
                    yy.addTask($$[$0 - 1], $$[$0]);
                    this.$ = "task";
                    break;
            }
        },
        table: [
            {
                3: 1,
                4: [
                    1,
                    2
                ]
            },
            {
                1: [
                    3
                ]
            },
            o($V0, [
                2,
                2
            ], {
                5: 3
            }),
            {
                6: [
                    1,
                    4
                ],
                7: 5,
                8: [
                    1,
                    6
                ],
                9: 7,
                10: [
                    1,
                    8
                ],
                11: $V1,
                12: $V2,
                14: $V3,
                16: $V4,
                17: $V5,
                18: $V6
            },
            o($V0, [
                2,
                7
            ], {
                1: [
                    2,
                    1
                ]
            }),
            o($V0, [
                2,
                3
            ]),
            {
                9: 15,
                11: $V1,
                12: $V2,
                14: $V3,
                16: $V4,
                17: $V5,
                18: $V6
            },
            o($V0, [
                2,
                5
            ]),
            o($V0, [
                2,
                6
            ]),
            o($V0, [
                2,
                8
            ]),
            {
                13: [
                    1,
                    16
                ]
            },
            {
                15: [
                    1,
                    17
                ]
            },
            o($V0, [
                2,
                11
            ]),
            o($V0, [
                2,
                12
            ]),
            {
                19: [
                    1,
                    18
                ]
            },
            o($V0, [
                2,
                4
            ]),
            o($V0, [
                2,
                9
            ]),
            o($V0, [
                2,
                10
            ]),
            o($V0, [
                2,
                13
            ])
        ],
        defaultActions: {},
        parseError: function parseError(str, hash) {
            if (hash.recoverable) {
                this.trace(str);
            } else {
                var error = new Error(str);
                error.hash = hash;
                throw error;
            }
        },
        parse: function parse(input) {
            var self = this, stack = [
                0
            ], tstack = [], vstack = [
                null
            ], lstack = [], table = this.table, yytext = "", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;
            var args = lstack.slice.call(arguments, 1);
            var lexer2 = Object.create(this.lexer);
            var sharedState = {
                yy: {}
            };
            for(var k in this.yy){
                if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
                    sharedState.yy[k] = this.yy[k];
                }
            }
            lexer2.setInput(input, sharedState.yy);
            sharedState.yy.lexer = lexer2;
            sharedState.yy.parser = this;
            if (typeof lexer2.yylloc == "undefined") {
                lexer2.yylloc = {};
            }
            var yyloc = lexer2.yylloc;
            lstack.push(yyloc);
            var ranges = lexer2.options && lexer2.options.ranges;
            if (typeof sharedState.yy.parseError === "function") {
                this.parseError = sharedState.yy.parseError;
            } else {
                this.parseError = Object.getPrototypeOf(this).parseError;
            }
            function lex() {
                var token;
                token = tstack.pop() || lexer2.lex() || EOF;
                if (typeof token !== "number") {
                    if (token instanceof Array) {
                        tstack = token;
                        token = tstack.pop();
                    }
                    token = self.symbols_[token] || token;
                }
                return token;
            }
            var symbol, state, action, r, yyval = {}, p, len, newState, expected;
            while(true){
                state = stack[stack.length - 1];
                if (this.defaultActions[state]) {
                    action = this.defaultActions[state];
                } else {
                    if (symbol === null || typeof symbol == "undefined") {
                        symbol = lex();
                    }
                    action = table[state] && table[state][symbol];
                }
                if (typeof action === "undefined" || !action.length || !action[0]) {
                    var errStr = "";
                    expected = [];
                    for(p in table[state]){
                        if (this.terminals_[p] && p > TERROR) {
                            expected.push("'" + this.terminals_[p] + "'");
                        }
                    }
                    if (lexer2.showPosition) {
                        errStr = "Parse error on line " + (yylineno + 1) + ":\n" + lexer2.showPosition() + "\nExpecting " + expected.join(", ") + ", got '" + (this.terminals_[symbol] || symbol) + "'";
                    } else {
                        errStr = "Parse error on line " + (yylineno + 1) + ": Unexpected " + (symbol == EOF ? "end of input" : "'" + (this.terminals_[symbol] || symbol) + "'");
                    }
                    this.parseError(errStr, {
                        text: lexer2.match,
                        token: this.terminals_[symbol] || symbol,
                        line: lexer2.yylineno,
                        loc: yyloc,
                        expected
                    });
                }
                if (action[0] instanceof Array && action.length > 1) {
                    throw new Error("Parse Error: multiple actions possible at state: " + state + ", token: " + symbol);
                }
                switch(action[0]){
                    case 1:
                        stack.push(symbol);
                        vstack.push(lexer2.yytext);
                        lstack.push(lexer2.yylloc);
                        stack.push(action[1]);
                        symbol = null;
                        {
                            yyleng = lexer2.yyleng;
                            yytext = lexer2.yytext;
                            yylineno = lexer2.yylineno;
                            yyloc = lexer2.yylloc;
                        }
                        break;
                    case 2:
                        len = this.productions_[action[1]][1];
                        yyval.$ = vstack[vstack.length - len];
                        yyval._$ = {
                            first_line: lstack[lstack.length - (len || 1)].first_line,
                            last_line: lstack[lstack.length - 1].last_line,
                            first_column: lstack[lstack.length - (len || 1)].first_column,
                            last_column: lstack[lstack.length - 1].last_column
                        };
                        if (ranges) {
                            yyval._$.range = [
                                lstack[lstack.length - (len || 1)].range[0],
                                lstack[lstack.length - 1].range[1]
                            ];
                        }
                        r = this.performAction.apply(yyval, [
                            yytext,
                            yyleng,
                            yylineno,
                            sharedState.yy,
                            action[1],
                            vstack,
                            lstack
                        ].concat(args));
                        if (typeof r !== "undefined") {
                            return r;
                        }
                        if (len) {
                            stack = stack.slice(0, -1 * len * 2);
                            vstack = vstack.slice(0, -1 * len);
                            lstack = lstack.slice(0, -1 * len);
                        }
                        stack.push(this.productions_[action[1]][0]);
                        vstack.push(yyval.$);
                        lstack.push(yyval._$);
                        newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
                        stack.push(newState);
                        break;
                    case 3:
                        return true;
                }
            }
            return true;
        }
    };
    var lexer = function() {
        var lexer2 = {
            EOF: 1,
            parseError: function parseError(str, hash) {
                if (this.yy.parser) {
                    this.yy.parser.parseError(str, hash);
                } else {
                    throw new Error(str);
                }
            },
            // resets the lexer, sets new input
            setInput: function(input, yy) {
                this.yy = yy || this.yy || {};
                this._input = input;
                this._more = this._backtrack = this.done = false;
                this.yylineno = this.yyleng = 0;
                this.yytext = this.matched = this.match = "";
                this.conditionStack = [
                    "INITIAL"
                ];
                this.yylloc = {
                    first_line: 1,
                    first_column: 0,
                    last_line: 1,
                    last_column: 0
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        0,
                        0
                    ];
                }
                this.offset = 0;
                return this;
            },
            // consumes and returns one char from the input
            input: function() {
                var ch = this._input[0];
                this.yytext += ch;
                this.yyleng++;
                this.offset++;
                this.match += ch;
                this.matched += ch;
                var lines = ch.match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno++;
                    this.yylloc.last_line++;
                } else {
                    this.yylloc.last_column++;
                }
                if (this.options.ranges) {
                    this.yylloc.range[1]++;
                }
                this._input = this._input.slice(1);
                return ch;
            },
            // unshifts one char (or a string) into the input
            unput: function(ch) {
                var len = ch.length;
                var lines = ch.split(/(?:\r\n?|\n)/g);
                this._input = ch + this._input;
                this.yytext = this.yytext.substr(0, this.yytext.length - len);
                this.offset -= len;
                var oldLines = this.match.split(/(?:\r\n?|\n)/g);
                this.match = this.match.substr(0, this.match.length - 1);
                this.matched = this.matched.substr(0, this.matched.length - 1);
                if (lines.length - 1) {
                    this.yylineno -= lines.length - 1;
                }
                var r = this.yylloc.range;
                this.yylloc = {
                    first_line: this.yylloc.first_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.first_column,
                    last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        r[0],
                        r[0] + this.yyleng - len
                    ];
                }
                this.yyleng = this.yytext.length;
                return this;
            },
            // When called from action, caches matched text and appends it on next action
            more: function() {
                this._more = true;
                return this;
            },
            // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
            reject: function() {
                if (this.options.backtrack_lexer) {
                    this._backtrack = true;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
                return this;
            },
            // retain first n characters of the match
            less: function(n) {
                this.unput(this.match.slice(n));
            },
            // displays already matched input, i.e. for error messages
            pastInput: function() {
                var past = this.matched.substr(0, this.matched.length - this.match.length);
                return (past.length > 20 ? "..." : "") + past.substr(-20).replace(/\n/g, "");
            },
            // displays upcoming input, i.e. for error messages
            upcomingInput: function() {
                var next = this.match;
                if (next.length < 20) {
                    next += this._input.substr(0, 20 - next.length);
                }
                return (next.substr(0, 20) + (next.length > 20 ? "..." : "")).replace(/\n/g, "");
            },
            // displays the character position where the lexing error occurred, i.e. for error messages
            showPosition: function() {
                var pre = this.pastInput();
                var c = new Array(pre.length + 1).join("-");
                return pre + this.upcomingInput() + "\n" + c + "^";
            },
            // test the lexed token: return FALSE when not a match, otherwise return token
            test_match: function(match, indexed_rule) {
                var token, lines, backup;
                if (this.options.backtrack_lexer) {
                    backup = {
                        yylineno: this.yylineno,
                        yylloc: {
                            first_line: this.yylloc.first_line,
                            last_line: this.last_line,
                            first_column: this.yylloc.first_column,
                            last_column: this.yylloc.last_column
                        },
                        yytext: this.yytext,
                        match: this.match,
                        matches: this.matches,
                        matched: this.matched,
                        yyleng: this.yyleng,
                        offset: this.offset,
                        _more: this._more,
                        _input: this._input,
                        yy: this.yy,
                        conditionStack: this.conditionStack.slice(0),
                        done: this.done
                    };
                    if (this.options.ranges) {
                        backup.yylloc.range = this.yylloc.range.slice(0);
                    }
                }
                lines = match[0].match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno += lines.length;
                }
                this.yylloc = {
                    first_line: this.yylloc.last_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.last_column,
                    last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + match[0].length
                };
                this.yytext += match[0];
                this.match += match[0];
                this.matches = match;
                this.yyleng = this.yytext.length;
                if (this.options.ranges) {
                    this.yylloc.range = [
                        this.offset,
                        this.offset += this.yyleng
                    ];
                }
                this._more = false;
                this._backtrack = false;
                this._input = this._input.slice(match[0].length);
                this.matched += match[0];
                token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
                if (this.done && this._input) {
                    this.done = false;
                }
                if (token) {
                    return token;
                } else if (this._backtrack) {
                    for(var k in backup){
                        this[k] = backup[k];
                    }
                    return false;
                }
                return false;
            },
            // return next match in input
            next: function() {
                if (this.done) {
                    return this.EOF;
                }
                if (!this._input) {
                    this.done = true;
                }
                var token, match, tempMatch, index;
                if (!this._more) {
                    this.yytext = "";
                    this.match = "";
                }
                var rules = this._currentRules();
                for(var i = 0; i < rules.length; i++){
                    tempMatch = this._input.match(this.rules[rules[i]]);
                    if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                        match = tempMatch;
                        index = i;
                        if (this.options.backtrack_lexer) {
                            token = this.test_match(tempMatch, rules[i]);
                            if (token !== false) {
                                return token;
                            } else if (this._backtrack) {
                                match = false;
                                continue;
                            } else {
                                return false;
                            }
                        } else if (!this.options.flex) {
                            break;
                        }
                    }
                }
                if (match) {
                    token = this.test_match(match, rules[index]);
                    if (token !== false) {
                        return token;
                    }
                    return false;
                }
                if (this._input === "") {
                    return this.EOF;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". Unrecognized text.\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
            },
            // return next match that has a token
            lex: function lex() {
                var r = this.next();
                if (r) {
                    return r;
                } else {
                    return this.lex();
                }
            },
            // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
            begin: function begin(condition) {
                this.conditionStack.push(condition);
            },
            // pop the previously active lexer condition state off the condition stack
            popState: function popState() {
                var n = this.conditionStack.length - 1;
                if (n > 0) {
                    return this.conditionStack.pop();
                } else {
                    return this.conditionStack[0];
                }
            },
            // produce the lexer rule set which is active for the currently active lexer condition state
            _currentRules: function _currentRules() {
                if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
                    return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
                } else {
                    return this.conditions["INITIAL"].rules;
                }
            },
            // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
            topState: function topState(n) {
                n = this.conditionStack.length - 1 - Math.abs(n || 0);
                if (n >= 0) {
                    return this.conditionStack[n];
                } else {
                    return "INITIAL";
                }
            },
            // alias for begin(condition)
            pushState: function pushState(condition) {
                this.begin(condition);
            },
            // return the number of states currently on the stack
            stateStackSize: function stateStackSize() {
                return this.conditionStack.length;
            },
            options: {
                "case-insensitive": true
            },
            performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {
                switch($avoiding_name_collisions){
                    case 0:
                        break;
                    case 1:
                        break;
                    case 2:
                        return 10;
                    case 3:
                        break;
                    case 4:
                        break;
                    case 5:
                        return 4;
                    case 6:
                        return 11;
                    case 7:
                        this.begin("acc_title");
                        return 12;
                    case 8:
                        this.popState();
                        return "acc_title_value";
                    case 9:
                        this.begin("acc_descr");
                        return 14;
                    case 10:
                        this.popState();
                        return "acc_descr_value";
                    case 11:
                        this.begin("acc_descr_multiline");
                        break;
                    case 12:
                        this.popState();
                        break;
                    case 13:
                        return "acc_descr_multiline_value";
                    case 14:
                        return 17;
                    case 15:
                        return 18;
                    case 16:
                        return 19;
                    case 17:
                        return ":";
                    case 18:
                        return 6;
                    case 19:
                        return "INVALID";
                }
            },
            rules: [
                /^(?:%(?!\{)[^\n]*)/i,
                /^(?:[^\}]%%[^\n]*)/i,
                /^(?:[\n]+)/i,
                /^(?:\s+)/i,
                /^(?:#[^\n]*)/i,
                /^(?:journey\b)/i,
                /^(?:title\s[^#\n;]+)/i,
                /^(?:accTitle\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*\{\s*)/i,
                /^(?:[\}])/i,
                /^(?:[^\}]*)/i,
                /^(?:section\s[^#:\n;]+)/i,
                /^(?:[^#:\n;]+)/i,
                /^(?::[^#\n;]+)/i,
                /^(?::)/i,
                /^(?:$)/i,
                /^(?:.)/i
            ],
            conditions: {
                "acc_descr_multiline": {
                    "rules": [
                        12,
                        13
                    ],
                    "inclusive": false
                },
                "acc_descr": {
                    "rules": [
                        10
                    ],
                    "inclusive": false
                },
                "acc_title": {
                    "rules": [
                        8
                    ],
                    "inclusive": false
                },
                "INITIAL": {
                    "rules": [
                        0,
                        1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7,
                        9,
                        11,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19
                    ],
                    "inclusive": true
                }
            }
        };
        return lexer2;
    }();
    parser2.lexer = lexer;
    function Parser() {
        this.yy = {};
    }
    Parser.prototype = parser2;
    parser2.Parser = Parser;
    return new Parser();
}();
parser.parser = parser;
const parser$1 = parser;
let currentSection = "";
const sections = [];
const tasks = [];
const rawTasks = [];
const clear = function() {
    sections.length = 0;
    tasks.length = 0;
    currentSection = "";
    rawTasks.length = 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["v"])();
};
const addSection = function(txt) {
    currentSection = txt;
    sections.push(txt);
};
const getSections = function() {
    return sections;
};
const getTasks = function() {
    let allItemsProcessed = compileTasks();
    const maxDepth = 100;
    let iterationCount = 0;
    while(!allItemsProcessed && iterationCount < maxDepth){
        allItemsProcessed = compileTasks();
        iterationCount++;
    }
    tasks.push(...rawTasks);
    return tasks;
};
const updateActors = function() {
    const tempActors = [];
    tasks.forEach((task)=>{
        if (task.people) {
            tempActors.push(...task.people);
        }
    });
    const unique = new Set(tempActors);
    return [
        ...unique
    ].sort();
};
const addTask = function(descr, taskData) {
    const pieces = taskData.substr(1).split(":");
    let score = 0;
    let peeps = [];
    if (pieces.length === 1) {
        score = Number(pieces[0]);
        peeps = [];
    } else {
        score = Number(pieces[0]);
        peeps = pieces[1].split(",");
    }
    const peopleList = peeps.map((s)=>s.trim());
    const rawTask = {
        section: currentSection,
        type: currentSection,
        people: peopleList,
        task: descr,
        score
    };
    rawTasks.push(rawTask);
};
const addTaskOrg = function(descr) {
    const newTask = {
        section: currentSection,
        type: currentSection,
        description: descr,
        task: descr,
        classes: []
    };
    tasks.push(newTask);
};
const compileTasks = function() {
    const compileTask = function(pos) {
        return rawTasks[pos].processed;
    };
    let allProcessed = true;
    for (const [i, rawTask] of rawTasks.entries()){
        compileTask(i);
        allProcessed = allProcessed && rawTask.processed;
    }
    return allProcessed;
};
const getActors = function() {
    return updateActors();
};
const db = {
    getConfig: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])().journey,
    clear,
    setDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["q"],
    getDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["t"],
    setAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["s"],
    getAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["g"],
    setAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["b"],
    getAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["a"],
    addSection,
    getSections,
    getTasks,
    addTask,
    addTaskOrg,
    getActors
};
const getStyles = (options)=>`.label {
    font-family: 'trebuchet ms', verdana, arial, sans-serif;
    font-family: var(--mermaid-font-family);
    color: ${options.textColor};
  }
  .mouth {
    stroke: #666;
  }

  line {
    stroke: ${options.textColor}
  }

  .legend {
    fill: ${options.textColor};
  }

  .label text {
    fill: #333;
  }
  .label {
    color: ${options.textColor}
  }

  .face {
    ${options.faceColor ? `fill: ${options.faceColor}` : "fill: #FFF8DC"};
    stroke: #999;
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${options.mainBkg};
    stroke: ${options.nodeBorder};
    stroke-width: 1px;
  }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${options.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${options.lineColor};
    stroke-width: 1.5px;
  }

  .flowchart-link {
    stroke: ${options.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${options.edgeLabelBackground};
    rect {
      opacity: 0.5;
    }
    text-align: center;
  }

  .cluster rect {
  }

  .cluster text {
    fill: ${options.titleColor};
  }

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: 'trebuchet ms', verdana, arial, sans-serif;
    font-family: var(--mermaid-font-family);
    font-size: 12px;
    background: ${options.tertiaryColor};
    border: 1px solid ${options.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .task-type-0, .section-type-0  {
    ${options.fillType0 ? `fill: ${options.fillType0}` : ""};
  }
  .task-type-1, .section-type-1  {
    ${options.fillType0 ? `fill: ${options.fillType1}` : ""};
  }
  .task-type-2, .section-type-2  {
    ${options.fillType0 ? `fill: ${options.fillType2}` : ""};
  }
  .task-type-3, .section-type-3  {
    ${options.fillType0 ? `fill: ${options.fillType3}` : ""};
  }
  .task-type-4, .section-type-4  {
    ${options.fillType0 ? `fill: ${options.fillType4}` : ""};
  }
  .task-type-5, .section-type-5  {
    ${options.fillType0 ? `fill: ${options.fillType5}` : ""};
  }
  .task-type-6, .section-type-6  {
    ${options.fillType0 ? `fill: ${options.fillType6}` : ""};
  }
  .task-type-7, .section-type-7  {
    ${options.fillType0 ? `fill: ${options.fillType7}` : ""};
  }

  .actor-0 {
    ${options.actor0 ? `fill: ${options.actor0}` : ""};
  }
  .actor-1 {
    ${options.actor1 ? `fill: ${options.actor1}` : ""};
  }
  .actor-2 {
    ${options.actor2 ? `fill: ${options.actor2}` : ""};
  }
  .actor-3 {
    ${options.actor3 ? `fill: ${options.actor3}` : ""};
  }
  .actor-4 {
    ${options.actor4 ? `fill: ${options.actor4}` : ""};
  }
  .actor-5 {
    ${options.actor5 ? `fill: ${options.actor5}` : ""};
  }
`;
const styles = getStyles;
const drawRect = function(elem, rectData) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$svgDrawCommon$2d$5e1cfd1d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["d"])(elem, rectData);
};
const drawFace = function(element, faceData) {
    const radius = 15;
    const circleElement = element.append("circle").attr("cx", faceData.cx).attr("cy", faceData.cy).attr("class", "face").attr("r", radius).attr("stroke-width", 2).attr("overflow", "visible");
    const face = element.append("g");
    face.append("circle").attr("cx", faceData.cx - radius / 3).attr("cy", faceData.cy - radius / 3).attr("r", 1.5).attr("stroke-width", 2).attr("fill", "#666").attr("stroke", "#666");
    face.append("circle").attr("cx", faceData.cx + radius / 3).attr("cy", faceData.cy - radius / 3).attr("r", 1.5).attr("stroke-width", 2).attr("fill", "#666").attr("stroke", "#666");
    function smile(face2) {
        const arc$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$arc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__arc$3e$__["arc"])().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);
        face2.append("path").attr("class", "mouth").attr("d", arc$1).attr("transform", "translate(" + faceData.cx + "," + (faceData.cy + 2) + ")");
    }
    function sad(face2) {
        const arc$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$arc$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__arc$3e$__["arc"])().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);
        face2.append("path").attr("class", "mouth").attr("d", arc$1).attr("transform", "translate(" + faceData.cx + "," + (faceData.cy + 7) + ")");
    }
    function ambivalent(face2) {
        face2.append("line").attr("class", "mouth").attr("stroke", 2).attr("x1", faceData.cx - 5).attr("y1", faceData.cy + 7).attr("x2", faceData.cx + 5).attr("y2", faceData.cy + 7).attr("class", "mouth").attr("stroke-width", "1px").attr("stroke", "#666");
    }
    if (faceData.score > 3) {
        smile(face);
    } else if (faceData.score < 3) {
        sad(face);
    } else {
        ambivalent(face);
    }
    return circleElement;
};
const drawCircle = function(element, circleData) {
    const circleElement = element.append("circle");
    circleElement.attr("cx", circleData.cx);
    circleElement.attr("cy", circleData.cy);
    circleElement.attr("class", "actor-" + circleData.pos);
    circleElement.attr("fill", circleData.fill);
    circleElement.attr("stroke", circleData.stroke);
    circleElement.attr("r", circleData.r);
    if (circleElement.class !== void 0) {
        circleElement.attr("class", circleElement.class);
    }
    if (circleData.title !== void 0) {
        circleElement.append("title").text(circleData.title);
    }
    return circleElement;
};
const drawText = function(elem, textData) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$svgDrawCommon$2d$5e1cfd1d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["f"])(elem, textData);
};
const drawLabel = function(elem, txtObject) {
    function genPoints(x, y, width, height, cut) {
        return x + "," + y + " " + (x + width) + "," + y + " " + (x + width) + "," + (y + height - cut) + " " + (x + width - cut * 1.2) + "," + (y + height) + " " + x + "," + (y + height);
    }
    const polygon = elem.append("polygon");
    polygon.attr("points", genPoints(txtObject.x, txtObject.y, 50, 20, 7));
    polygon.attr("class", "labelBox");
    txtObject.y = txtObject.y + txtObject.labelMargin;
    txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;
    drawText(elem, txtObject);
};
const drawSection = function(elem, section, conf2) {
    const g = elem.append("g");
    const rect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$svgDrawCommon$2d$5e1cfd1d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["g"])();
    rect.x = section.x;
    rect.y = section.y;
    rect.fill = section.fill;
    rect.width = conf2.width * section.taskCount + // width of the tasks
    conf2.diagramMarginX * (section.taskCount - 1);
    rect.height = conf2.height;
    rect.class = "journey-section section-type-" + section.num;
    rect.rx = 3;
    rect.ry = 3;
    drawRect(g, rect);
    _drawTextCandidateFunc(conf2)(section.text, g, rect.x, rect.y, rect.width, rect.height, {
        class: "journey-section section-type-" + section.num
    }, conf2, section.colour);
};
let taskCount = -1;
const drawTask = function(elem, task, conf2) {
    const center = task.x + conf2.width / 2;
    const g = elem.append("g");
    taskCount++;
    const maxHeight = 300 + 5 * 30;
    g.append("line").attr("id", "task" + taskCount).attr("x1", center).attr("y1", task.y).attr("x2", center).attr("y2", maxHeight).attr("class", "task-line").attr("stroke-width", "1px").attr("stroke-dasharray", "4 2").attr("stroke", "#666");
    drawFace(g, {
        cx: center,
        cy: 300 + (5 - task.score) * 30,
        score: task.score
    });
    const rect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$svgDrawCommon$2d$5e1cfd1d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["g"])();
    rect.x = task.x;
    rect.y = task.y;
    rect.fill = task.fill;
    rect.width = conf2.width;
    rect.height = conf2.height;
    rect.class = "task task-type-" + task.num;
    rect.rx = 3;
    rect.ry = 3;
    drawRect(g, rect);
    let xPos = task.x + 14;
    task.people.forEach((person)=>{
        const colour = task.actors[person].color;
        const circle = {
            cx: xPos,
            cy: task.y,
            r: 7,
            fill: colour,
            stroke: "#000",
            title: person,
            pos: task.actors[person].position
        };
        drawCircle(g, circle);
        xPos += 10;
    });
    _drawTextCandidateFunc(conf2)(task.task, g, rect.x, rect.y, rect.width, rect.height, {
        class: "task"
    }, conf2, task.colour);
};
const drawBackgroundRect = function(elem, bounds2) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$svgDrawCommon$2d$5e1cfd1d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["a"])(elem, bounds2);
};
const _drawTextCandidateFunc = function() {
    function byText(content, g, x, y, width, height, textAttrs, colour) {
        const text = g.append("text").attr("x", x + width / 2).attr("y", y + height / 2 + 5).style("font-color", colour).style("text-anchor", "middle").text(content);
        _setTextAttrs(text, textAttrs);
    }
    function byTspan(content, g, x, y, width, height, textAttrs, conf2, colour) {
        const { taskFontSize, taskFontFamily } = conf2;
        const lines = content.split(/<br\s*\/?>/gi);
        for(let i = 0; i < lines.length; i++){
            const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;
            const text = g.append("text").attr("x", x + width / 2).attr("y", y).attr("fill", colour).style("text-anchor", "middle").style("font-size", taskFontSize).style("font-family", taskFontFamily);
            text.append("tspan").attr("x", x + width / 2).attr("dy", dy).text(lines[i]);
            text.attr("y", y + height / 2).attr("dominant-baseline", "central").attr("alignment-baseline", "central");
            _setTextAttrs(text, textAttrs);
        }
    }
    function byFo(content, g, x, y, width, height, textAttrs, conf2) {
        const body = g.append("switch");
        const f = body.append("foreignObject").attr("x", x).attr("y", y).attr("width", width).attr("height", height).attr("position", "fixed");
        const text = f.append("xhtml:div").style("display", "table").style("height", "100%").style("width", "100%");
        text.append("div").attr("class", "label").style("display", "table-cell").style("text-align", "center").style("vertical-align", "middle").text(content);
        byTspan(content, body, x, y, width, height, textAttrs, conf2);
        _setTextAttrs(text, textAttrs);
    }
    function _setTextAttrs(toText, fromTextAttrsDict) {
        for(const key in fromTextAttrsDict){
            if (key in fromTextAttrsDict) {
                toText.attr(key, fromTextAttrsDict[key]);
            }
        }
    }
    return function(conf2) {
        return conf2.textPlacement === "fo" ? byFo : conf2.textPlacement === "old" ? byText : byTspan;
    };
}();
const initGraphics = function(graphics) {
    graphics.append("defs").append("marker").attr("id", "arrowhead").attr("refX", 5).attr("refY", 2).attr("markerWidth", 6).attr("markerHeight", 4).attr("orient", "auto").append("path").attr("d", "M 0,0 V 4 L6,2 Z");
};
const svgDraw = {
    drawRect,
    drawCircle,
    drawSection,
    drawText,
    drawLabel,
    drawTask,
    drawBackgroundRect,
    initGraphics
};
const setConf = function(cnf) {
    const keys = Object.keys(cnf);
    keys.forEach(function(key) {
        conf[key] = cnf[key];
    });
};
const actors = {};
function drawActorLegend(diagram2) {
    const conf2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])().journey;
    let yPos = 60;
    Object.keys(actors).forEach((person)=>{
        const colour = actors[person].color;
        const circleData = {
            cx: 20,
            cy: yPos,
            r: 7,
            fill: colour,
            stroke: "#000",
            pos: actors[person].position
        };
        svgDraw.drawCircle(diagram2, circleData);
        const labelData = {
            x: 40,
            y: yPos + 7,
            fill: "#666",
            text: person,
            textMargin: conf2.boxTextMargin | 5
        };
        svgDraw.drawText(diagram2, labelData);
        yPos += 20;
    });
}
const conf = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])().journey;
const LEFT_MARGIN = conf.leftMargin;
const draw = function(text, id, version, diagObj) {
    const conf2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])().journey;
    const securityLevel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])().securityLevel;
    let sandboxElement;
    if (securityLevel === "sandbox") {
        sandboxElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("#i" + id);
    }
    const root = securityLevel === "sandbox" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(sandboxElement.nodes()[0].contentDocument.body) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("body");
    bounds.init();
    const diagram2 = root.select("#" + id);
    svgDraw.initGraphics(diagram2);
    const tasks2 = diagObj.db.getTasks();
    const title = diagObj.db.getDiagramTitle();
    const actorNames = diagObj.db.getActors();
    for(const member in actors){
        delete actors[member];
    }
    let actorPos = 0;
    actorNames.forEach((actorName)=>{
        actors[actorName] = {
            color: conf2.actorColours[actorPos % conf2.actorColours.length],
            position: actorPos
        };
        actorPos++;
    });
    drawActorLegend(diagram2);
    bounds.insert(0, 0, LEFT_MARGIN, Object.keys(actors).length * 50);
    drawTasks(diagram2, tasks2, 0);
    const box = bounds.getBounds();
    if (title) {
        diagram2.append("text").text(title).attr("x", LEFT_MARGIN).attr("font-size", "4ex").attr("font-weight", "bold").attr("y", 25);
    }
    const height = box.stopy - box.starty + 2 * conf2.diagramMarginY;
    const width = LEFT_MARGIN + box.stopx + 2 * conf2.diagramMarginX;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["i"])(diagram2, height, width, conf2.useMaxWidth);
    diagram2.append("line").attr("x1", LEFT_MARGIN).attr("y1", conf2.height * 4).attr("x2", width - LEFT_MARGIN - 4).attr("y2", conf2.height * 4).attr("stroke-width", 4).attr("stroke", "black").attr("marker-end", "url(#arrowhead)");
    const extraVertForTitle = title ? 70 : 0;
    diagram2.attr("viewBox", `${box.startx} -25 ${width} ${height + extraVertForTitle}`);
    diagram2.attr("preserveAspectRatio", "xMinYMin meet");
    diagram2.attr("height", height + extraVertForTitle + 25);
};
const bounds = {
    data: {
        startx: void 0,
        stopx: void 0,
        starty: void 0,
        stopy: void 0
    },
    verticalPos: 0,
    sequenceItems: [],
    init: function() {
        this.sequenceItems = [];
        this.data = {
            startx: void 0,
            stopx: void 0,
            starty: void 0,
            stopy: void 0
        };
        this.verticalPos = 0;
    },
    updateVal: function(obj, key, val, fun) {
        if (obj[key] === void 0) {
            obj[key] = val;
        } else {
            obj[key] = fun(val, obj[key]);
        }
    },
    updateBounds: function(startx, starty, stopx, stopy) {
        const conf2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])().journey;
        const _self = this;
        let cnt = 0;
        function updateFn(type) {
            return function updateItemBounds(item) {
                cnt++;
                const n = _self.sequenceItems.length - cnt + 1;
                _self.updateVal(item, "starty", starty - n * conf2.boxMargin, Math.min);
                _self.updateVal(item, "stopy", stopy + n * conf2.boxMargin, Math.max);
                _self.updateVal(bounds.data, "startx", startx - n * conf2.boxMargin, Math.min);
                _self.updateVal(bounds.data, "stopx", stopx + n * conf2.boxMargin, Math.max);
                if (!(type === "activation")) {
                    _self.updateVal(item, "startx", startx - n * conf2.boxMargin, Math.min);
                    _self.updateVal(item, "stopx", stopx + n * conf2.boxMargin, Math.max);
                    _self.updateVal(bounds.data, "starty", starty - n * conf2.boxMargin, Math.min);
                    _self.updateVal(bounds.data, "stopy", stopy + n * conf2.boxMargin, Math.max);
                }
            };
        }
        this.sequenceItems.forEach(updateFn());
    },
    insert: function(startx, starty, stopx, stopy) {
        const _startx = Math.min(startx, stopx);
        const _stopx = Math.max(startx, stopx);
        const _starty = Math.min(starty, stopy);
        const _stopy = Math.max(starty, stopy);
        this.updateVal(bounds.data, "startx", _startx, Math.min);
        this.updateVal(bounds.data, "starty", _starty, Math.min);
        this.updateVal(bounds.data, "stopx", _stopx, Math.max);
        this.updateVal(bounds.data, "stopy", _stopy, Math.max);
        this.updateBounds(_startx, _starty, _stopx, _stopy);
    },
    bumpVerticalPos: function(bump) {
        this.verticalPos = this.verticalPos + bump;
        this.data.stopy = this.verticalPos;
    },
    getVerticalPos: function() {
        return this.verticalPos;
    },
    getBounds: function() {
        return this.data;
    }
};
const fills = conf.sectionFills;
const textColours = conf.sectionColours;
const drawTasks = function(diagram2, tasks2, verticalPos) {
    const conf2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])().journey;
    let lastSection = "";
    const sectionVHeight = conf2.height * 2 + conf2.diagramMarginY;
    const taskPos = verticalPos + sectionVHeight;
    let sectionNumber = 0;
    let fill = "#CCC";
    let colour = "black";
    let num = 0;
    for (const [i, task] of tasks2.entries()){
        if (lastSection !== task.section) {
            fill = fills[sectionNumber % fills.length];
            num = sectionNumber % fills.length;
            colour = textColours[sectionNumber % textColours.length];
            let taskInSectionCount = 0;
            const currentSection2 = task.section;
            for(let taskIndex = i; taskIndex < tasks2.length; taskIndex++){
                if (tasks2[taskIndex].section == currentSection2) {
                    taskInSectionCount = taskInSectionCount + 1;
                } else {
                    break;
                }
            }
            const section = {
                x: i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN,
                y: 50,
                text: task.section,
                fill,
                num,
                colour,
                taskCount: taskInSectionCount
            };
            svgDraw.drawSection(diagram2, section, conf2);
            lastSection = task.section;
            sectionNumber++;
        }
        const taskActors = task.people.reduce((acc, actorName)=>{
            if (actors[actorName]) {
                acc[actorName] = actors[actorName];
            }
            return acc;
        }, {});
        task.x = i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN;
        task.y = taskPos;
        task.width = conf2.diagramMarginX;
        task.height = conf2.diagramMarginY;
        task.colour = colour;
        task.fill = fill;
        task.num = num;
        task.actors = taskActors;
        svgDraw.drawTask(diagram2, task, conf2);
        bounds.insert(task.x, task.y, task.x + task.width + conf2.taskMargin, 300 + 5 * 30);
    }
};
const renderer = {
    setConf,
    draw
};
const diagram = {
    parser: parser$1,
    db,
    renderer,
    styles,
    init: (cnf)=>{
        renderer.setConf(cnf.journey);
        db.clear();
    }
};
;
}}),

};

//# sourceMappingURL=node_modules_ba7fdf93._.js.map