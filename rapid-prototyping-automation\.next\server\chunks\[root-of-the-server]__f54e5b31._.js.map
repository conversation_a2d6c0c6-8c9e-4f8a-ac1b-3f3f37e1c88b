{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/generate-technical-requirements/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\nimport { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';\n\n// For development environments with SSL certificate issues\nif (process.env.NODE_ENV === 'development') {\n  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n}\n\n// Check if API key is configured\nif (!process.env.ANTHROPIC_API_KEY) {\n  console.error('ANTHROPIC_API_KEY is not configured in environment variables');\n}\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',\n});\n\nconst TECHNICAL_REQUIREMENTS_PROMPT = `Role:\nYou are a seasoned Solution Architect and technical writer.\n\nAction:\n1. Load and parse the content attached.   \n2. Extract its sections (Executive Summary, Background & Context, Key Challenges, Core Needs, HMW Problem Statement, Constraints & Success Criteria, Next Steps).  \n3. Translate those insights into a Technical Solution Document that describes what needs to be built—and how—to address the challenges and deliver a prototype.\n\nContext:\nThe source document captures a Design Thinking workshop defining a problem statement. Your solution must align to that business context and success criteria.\n\nExpectation:\nProduce a polished technical analysis with these sections:\n\n1. Title and Overview\n   - Infer the title from the solution being proposed and write a catchy title that encompasses the solution.  \n   - Executive Summary: Briefly restate the problem and summarize the proposed technical approach.\n\n2. Solution Architecture Overview  \n   - High-level system components and interactions\n   - Cloud-native architecture approach\n   - Integration strategy\n\n3. Component Design  \n   - For each major component:  \n     - Purpose and functionality\n     - Technology choices and rationale\n     - Key interfaces and protocols  \n     - Data schemas and formats\n     - Scalability considerations\n\n4. Data Flow & Integration  \n   - Step-by-step flow from source systems → ingestion → processing → storage → visualization\n   - Integration points with data sources based on the industry and client context\n   - Real-time processing requirements\n\n5. Security & Compliance  \n   - Authentication and authorization model \n   - Data encryption at rest and in transit  \n   - Audit logging and compliance requirements\n   - Privacy and data protection measures\n\n6. Non-Functional Requirements  \n   - Scalability targets and auto-scaling strategy\n   - Availability and resilience (SLA targets)\n   - Performance benchmarks and optimization\n   - Monitoring, logging, and supportability\n\n7. Prototype Scope & MVP Features  \n   - Minimum viable product capabilities to validate the concept\n   - Core features for initial release\n   - Success metrics and validation criteria\n   - Timeline and development milestones\n\n8. Implementation Roadmap\n   - Phase 1: Foundation and core infrastructure\n   - Phase 2: Core functionality and integrations\n   - Phase 3: Advanced features and optimization\n   - Risk mitigation strategies\n\n9. Next Steps & Recommendations  \n   - 3–5 action items to move from design → prototype build\n   - Technology spike tasks or POC validations\n   - Resource requirements and team structure\n\nPlease provide a comprehensive technical solution that addresses all the challenges identified in the problem statement while being practical and implementable.`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { problemStatementContent } = await request.json();\n    \n    if (!problemStatementContent) {\n      return NextResponse.json({ error: 'No problem statement content provided' }, { status: 400 });\n    }\n\n    // Check if API key is configured\n    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo document');\n      \n      // For development, create demo markdown content\n      const demoContent = createDemoTechnicalMarkdown();\n\n      return NextResponse.json({\n        success: true,\n        content: demoContent,\n        message: 'Demo technical requirements document generated. Configure Claude API key for real document generation.'\n      });\n    }\n\n    console.log('Generating technical requirements from problem statement...');\n    console.log(`Problem statement content length: ${problemStatementContent.length} characters`);\n\n    // Call Claude API\n    console.log('Calling Claude API with Sonnet 4 for technical requirements...');\n    const fullPrompt = `${TECHNICAL_REQUIREMENTS_PROMPT}\\n\\nProblem Statement Document Content:\\n${problemStatementContent}`;\n    console.log(`Full prompt length: ${fullPrompt.length} characters`);\n    \n    let response;\n    try {\n      response = await anthropic.messages.create({\n        model: 'claude-sonnet-4-20250514',\n        max_tokens: 10000,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n      console.log('Claude API call successful for technical requirements');\n    } catch (apiError: any) {\n      console.error('Claude API Error:', apiError);\n      \n      // Handle specific API errors\n      if (apiError.status === 401) {\n        return NextResponse.json({ \n          error: 'Invalid API key. Please check your Anthropic API key configuration.' \n        }, { status: 401 });\n      } else if (apiError.status === 429) {\n        return NextResponse.json({ \n          error: 'Rate limit exceeded. Please try again in a few minutes.' \n        }, { status: 429 });\n      } else if (apiError.message?.includes('model')) {\n        return NextResponse.json({ \n          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.' \n        }, { status: 400 });\n      } else {\n        return NextResponse.json({ \n          error: `Claude API error: ${apiError.message || 'Unknown error'}` \n        }, { status: 500 });\n      }\n    }\n\n    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    // Format the analysis as Markdown document\n    const markdownContent = formatAsTechnicalMarkdown(analysisText);\n\n    return NextResponse.json({\n      success: true,\n      content: markdownContent, // Return formatted markdown content\n      message: 'Technical requirements document generated successfully with Claude Sonnet 4!'\n    });\n\n  } catch (error) {\n    console.error('Error generating technical requirements:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate technical requirements document' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction formatAsTechnicalMarkdown(analysisText: string): string {\n  const currentDate = new Date().toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n\n  return `# Technical Solution Document\n\n**Date:** ${currentDate}\n**Generated by:** Claude Sonnet 4 AI Analysis\n\n---\n\n${analysisText}\n\n---\n\n*This technical solution document was automatically generated using AI analysis. Please review and validate the technical specifications before proceeding with implementation.*`;\n}\n\nfunction createDemoTechnicalMarkdown(): string {\n  const currentDate = new Date().toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n\n  return `# Technical Solution Document\n\n**Date:** ${currentDate}\n**Generated by:** Demo Mode (Configure Claude API key for real analysis)\n\n---\n\n## Solution Architecture Overview\n\nThis is a demo technical solution document. To generate real technical requirements from your problem statement, please configure your Claude API key in the environment variables.\n\n## Component Design\n\n### Frontend Components\n- **User Interface**: Modern React-based web application\n- **State Management**: Redux or Context API for application state\n- **Routing**: React Router for navigation\n- **Styling**: Tailwind CSS for responsive design\n\n### Backend Components\n- **API Gateway**: RESTful API with Express.js\n- **Authentication**: JWT-based authentication system\n- **Database**: PostgreSQL for data persistence\n- **Caching**: Redis for performance optimization\n\n## Data Flow & Integration\n\n1. **User Interaction**: Frontend captures user input\n2. **API Processing**: Backend validates and processes requests\n3. **Data Storage**: Information persisted to database\n4. **Response**: Results returned to frontend for display\n\n## Security & Compliance\n\n- **Authentication**: Multi-factor authentication support\n- **Authorization**: Role-based access control\n- **Data Encryption**: TLS 1.3 for data in transit\n- **Compliance**: GDPR and SOC 2 compliance framework\n\n## Non-Functional Requirements\n\n- **Performance**: Sub-200ms API response times\n- **Scalability**: Support for 10,000+ concurrent users\n- **Availability**: 99.9% uptime SLA\n- **Security**: Regular security audits and penetration testing\n\n## Next Steps\n\n1. Configure Claude API key for real document generation\n2. Review and validate technical specifications\n3. Proceed to architecture diagram generation\n4. Generate implementation prompts for development\n\n---\n\n*This is a demo document. Configure your Claude API key to generate real technical requirements from problem statements.*`;\n}\n\nasync function createTechnicalRequirementsDocument(analysisText: string): Promise<Document> {\n  // Parse the analysis text to extract structured information\n  const sections = parseTechnicalAnalysisText(analysisText);\n  \n  const doc = new Document({\n    sections: [{\n      properties: {},\n      children: [\n        // Title Page\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.title || \"Technical Solution Document\",\n              bold: true,\n              size: 32,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.TITLE,\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 400 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: `Date: ${new Date().toLocaleDateString()}`,\n              size: 24\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Prepared by: Solution Architect\",\n              size: 20,\n              italics: true\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 800 }\n        }),\n\n        // Executive Summary\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Executive Summary\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.executiveSummary || \"Technical solution summary will be generated based on problem statement analysis.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Solution Architecture Overview\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Solution Architecture Overview\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.architecture || \"Cloud-native architecture overview and system components will be detailed here.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Component Design\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Component Design\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.components || \"Detailed component design including technology choices and interfaces.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Data Flow & Integration\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Data Flow & Integration\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.dataFlow || \"Data flow and integration patterns for the solution.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Security & Compliance\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Security & Compliance\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.security || \"Security architecture and compliance requirements.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Non-Functional Requirements\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Non-Functional Requirements\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.nonFunctional || \"Performance, scalability, and reliability requirements.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Prototype Scope & MVP Features\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Prototype Scope & MVP Features\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.mvp || \"Minimum viable product scope and key features for validation.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Next Steps & Recommendations\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Next Steps & Recommendations\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: sections.nextSteps || \"Implementation roadmap and recommended next steps.\",\n              size: 22\n            })\n          ],\n          spacing: { after: 400 }\n        }),\n\n        // Full Analysis\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Detailed Technical Analysis\",\n              bold: true,\n              size: 28,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.HEADING_1,\n          spacing: { before: 400, after: 200 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: analysisText,\n              size: 20\n            })\n          ],\n          spacing: { after: 400 }\n        })\n      ]\n    }]\n  });\n\n  return doc;\n}\n\nfunction parseTechnicalAnalysisText(text: string) {\n  const sections: any = {};\n  \n  // Try to extract key sections from the Claude response\n  const titleMatch = text.match(/title[:\\s]*([^\\n]+)/i);\n  sections.title = titleMatch ? titleMatch[1].trim() : null;\n  \n  const summaryMatch = text.match(/executive summary[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.executiveSummary = summaryMatch ? summaryMatch[1].trim() : null;\n  \n  const architectureMatch = text.match(/architecture[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.architecture = architectureMatch ? architectureMatch[1].trim() : null;\n  \n  const componentsMatch = text.match(/component[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.components = componentsMatch ? componentsMatch[1].trim() : null;\n  \n  const dataFlowMatch = text.match(/data flow[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.dataFlow = dataFlowMatch ? dataFlowMatch[1].trim() : null;\n  \n  const securityMatch = text.match(/security[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.security = securityMatch ? securityMatch[1].trim() : null;\n  \n  const nonFunctionalMatch = text.match(/non-functional[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.nonFunctional = nonFunctionalMatch ? nonFunctionalMatch[1].trim() : null;\n  \n  const mvpMatch = text.match(/mvp|prototype[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.mvp = mvpMatch ? mvpMatch[1].trim() : null;\n  \n  const stepsMatch = text.match(/next steps[:\\s]*([^]*?)(?=\\n\\n|\\n[A-Z]|$)/i);\n  sections.nextSteps = stepsMatch ? stepsMatch[1].trim() : null;\n  \n  return sections;\n}\n\nasync function createMockTechnicalDocument(): Promise<Document> {\n  const doc = new Document({\n    sections: [{\n      properties: {},\n      children: [\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"Technical Solution Document - Demo\",\n              bold: true,\n              size: 32,\n              color: \"2563EB\"\n            })\n          ],\n          heading: HeadingLevel.TITLE,\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 400 }\n        }),\n        \n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"⚠️ Demo Mode - Claude API Not Configured\",\n              bold: true,\n              size: 24,\n              color: \"DC2626\"\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 400 }\n        }),\n\n        new Paragraph({\n          children: [\n            new TextRun({\n              text: \"This is a demonstration document. Configure your Anthropic API key to generate real technical requirements.\",\n              size: 22,\n              color: \"DC2626\"\n            })\n          ],\n          alignment: AlignmentType.CENTER,\n          spacing: { after: 600 }\n        })\n      ]\n    }]\n  });\n\n  return doc;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEA,2DAA2D;AAC3D,wCAA4C;IAC1C,QAAQ,GAAG,CAAC,4BAA4B,GAAG;AAC7C;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC3C;AAEA,MAAM,gCAAgC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gKAiEyH,CAAC;AAE1J,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,uBAAuB,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEtD,IAAI,CAAC,yBAAyB;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAwC,GAAG;gBAAE,QAAQ;YAAI;QAC7F;QAEA,iCAAiC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;YACrG,QAAQ,KAAK,CAAC;YAEd,gDAAgD;YAChD,MAAM,cAAc;YAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,wBAAwB,MAAM,CAAC,WAAW,CAAC;QAE5F,kBAAkB;QAClB,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,GAAG,8BAA8B,yCAAyC,EAAE,yBAAyB;QACxH,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,MAAM,CAAC,WAAW,CAAC;QAEjE,IAAI;QACJ,IAAI;YACF,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,6BAA6B;YAC7B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,OAAO,EAAE,SAAS,UAAU;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,CAAC,kBAAkB,EAAE,SAAS,OAAO,IAAI,iBAAiB;gBACnE,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,2CAA2C;QAC3C,MAAM,kBAAkB,0BAA0B;QAElD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqD,GAC9D;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,0BAA0B,YAAoB;IACrD,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,OAAO,CAAC;;UAEA,EAAE,YAAY;;;;;AAKxB,EAAE,aAAa;;;;gLAIiK,CAAC;AACjL;AAEA,SAAS;IACP,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,OAAO,CAAC;;UAEA,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yHAqDiG,CAAC;AAC1H;AAEA,eAAe,oCAAoC,YAAoB;IACrE,4DAA4D;IAC5D,MAAM,WAAW,2BAA2B;IAE5C,MAAM,MAAM,IAAI,wIAAA,CAAA,WAAQ,CAAC;QACvB,UAAU;YAAC;gBACT,YAAY,CAAC;gBACb,UAAU;oBACR,aAAa;oBACb,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,KAAK,IAAI;gCACxB,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,KAAK;wBAC3B,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,CAAC,MAAM,EAAE,IAAI,OAAO,kBAAkB,IAAI;gCAChD,MAAM;4BACR;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,SAAS;4BACX;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,oBAAoB;oBACpB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,gBAAgB,IAAI;gCACnC,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,iCAAiC;oBACjC,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,YAAY,IAAI;gCAC/B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,mBAAmB;oBACnB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,UAAU,IAAI;gCAC7B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,0BAA0B;oBAC1B,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,QAAQ,IAAI;gCAC3B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,wBAAwB;oBACxB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,QAAQ,IAAI;gCAC3B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,8BAA8B;oBAC9B,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,aAAa,IAAI;gCAChC,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,iCAAiC;oBACjC,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,GAAG,IAAI;gCACtB,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,+BAA+B;oBAC/B,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM,SAAS,SAAS,IAAI;gCAC5B,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,gBAAgB;oBAChB,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,SAAS;wBAC/B,SAAS;4BAAE,QAAQ;4BAAK,OAAO;wBAAI;oBACrC;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;4BACR;yBACD;wBACD,SAAS;4BAAE,OAAO;wBAAI;oBACxB;iBACD;YACH;SAAE;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,2BAA2B,IAAY;IAC9C,MAAM,WAAgB,CAAC;IAEvB,uDAAuD;IACvD,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,SAAS,KAAK,GAAG,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK;IAErD,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,SAAS,gBAAgB,GAAG,eAAe,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,oBAAoB,KAAK,KAAK,CAAC;IACrC,SAAS,YAAY,GAAG,oBAAoB,iBAAiB,CAAC,EAAE,CAAC,IAAI,KAAK;IAE1E,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,SAAS,UAAU,GAAG,kBAAkB,eAAe,CAAC,EAAE,CAAC,IAAI,KAAK;IAEpE,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,SAAS,QAAQ,GAAG,gBAAgB,aAAa,CAAC,EAAE,CAAC,IAAI,KAAK;IAE9D,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,SAAS,QAAQ,GAAG,gBAAgB,aAAa,CAAC,EAAE,CAAC,IAAI,KAAK;IAE9D,MAAM,qBAAqB,KAAK,KAAK,CAAC;IACtC,SAAS,aAAa,GAAG,qBAAqB,kBAAkB,CAAC,EAAE,CAAC,IAAI,KAAK;IAE7E,MAAM,WAAW,KAAK,KAAK,CAAC;IAC5B,SAAS,GAAG,GAAG,WAAW,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK;IAE/C,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,SAAS,SAAS,GAAG,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK;IAEzD,OAAO;AACT;AAEA,eAAe;IACb,MAAM,MAAM,IAAI,wIAAA,CAAA,WAAQ,CAAC;QACvB,UAAU;YAAC;gBACT,YAAY,CAAC;gBACb,UAAU;oBACR,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,SAAS,wIAAA,CAAA,eAAY,CAAC,KAAK;wBAC3B,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;oBAEA,IAAI,wIAAA,CAAA,YAAS,CAAC;wBACZ,UAAU;4BACR,IAAI,wIAAA,CAAA,UAAO,CAAC;gCACV,MAAM;gCACN,MAAM;gCACN,OAAO;4BACT;yBACD;wBACD,WAAW,wIAAA,CAAA,gBAAa,CAAC,MAAM;wBAC/B,SAAS;4BAAE,OAAO;wBAAI;oBACxB;iBACD;YACH;SAAE;IACJ;IAEA,OAAO;AACT", "debugId": null}}]}