(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/mermaid/dist/flowDb-c1833063.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "d": (()=>db),
    "f": (()=>flowDb),
    "p": (()=>parser$1)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-client] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-client] (ecmascript)");
;
;
var parser = function() {
    var o = function(k, v, o2, l) {
        for(o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);
        return o2;
    }, $V0 = [
        1,
        4
    ], $V1 = [
        1,
        3
    ], $V2 = [
        1,
        5
    ], $V3 = [
        1,
        8,
        9,
        10,
        11,
        27,
        34,
        36,
        38,
        42,
        58,
        81,
        82,
        83,
        84,
        85,
        86,
        99,
        102,
        103,
        106,
        108,
        111,
        112,
        113,
        118,
        119,
        120,
        121
    ], $V4 = [
        2,
        2
    ], $V5 = [
        1,
        13
    ], $V6 = [
        1,
        14
    ], $V7 = [
        1,
        15
    ], $V8 = [
        1,
        16
    ], $V9 = [
        1,
        23
    ], $Va = [
        1,
        25
    ], $Vb = [
        1,
        26
    ], $Vc = [
        1,
        27
    ], $Vd = [
        1,
        49
    ], $Ve = [
        1,
        48
    ], $Vf = [
        1,
        29
    ], $Vg = [
        1,
        30
    ], $Vh = [
        1,
        31
    ], $Vi = [
        1,
        32
    ], $Vj = [
        1,
        33
    ], $Vk = [
        1,
        44
    ], $Vl = [
        1,
        46
    ], $Vm = [
        1,
        42
    ], $Vn = [
        1,
        47
    ], $Vo = [
        1,
        43
    ], $Vp = [
        1,
        50
    ], $Vq = [
        1,
        45
    ], $Vr = [
        1,
        51
    ], $Vs = [
        1,
        52
    ], $Vt = [
        1,
        34
    ], $Vu = [
        1,
        35
    ], $Vv = [
        1,
        36
    ], $Vw = [
        1,
        37
    ], $Vx = [
        1,
        57
    ], $Vy = [
        1,
        8,
        9,
        10,
        11,
        27,
        32,
        34,
        36,
        38,
        42,
        58,
        81,
        82,
        83,
        84,
        85,
        86,
        99,
        102,
        103,
        106,
        108,
        111,
        112,
        113,
        118,
        119,
        120,
        121
    ], $Vz = [
        1,
        61
    ], $VA = [
        1,
        60
    ], $VB = [
        1,
        62
    ], $VC = [
        8,
        9,
        11,
        73,
        75
    ], $VD = [
        1,
        88
    ], $VE = [
        1,
        93
    ], $VF = [
        1,
        92
    ], $VG = [
        1,
        89
    ], $VH = [
        1,
        85
    ], $VI = [
        1,
        91
    ], $VJ = [
        1,
        87
    ], $VK = [
        1,
        94
    ], $VL = [
        1,
        90
    ], $VM = [
        1,
        95
    ], $VN = [
        1,
        86
    ], $VO = [
        8,
        9,
        10,
        11,
        73,
        75
    ], $VP = [
        8,
        9,
        10,
        11,
        44,
        73,
        75
    ], $VQ = [
        8,
        9,
        10,
        11,
        29,
        42,
        44,
        46,
        48,
        50,
        52,
        54,
        56,
        58,
        61,
        63,
        65,
        66,
        68,
        73,
        75,
        86,
        99,
        102,
        103,
        106,
        108,
        111,
        112,
        113
    ], $VR = [
        8,
        9,
        11,
        42,
        58,
        73,
        75,
        86,
        99,
        102,
        103,
        106,
        108,
        111,
        112,
        113
    ], $VS = [
        42,
        58,
        86,
        99,
        102,
        103,
        106,
        108,
        111,
        112,
        113
    ], $VT = [
        1,
        121
    ], $VU = [
        1,
        120
    ], $VV = [
        1,
        128
    ], $VW = [
        1,
        142
    ], $VX = [
        1,
        143
    ], $VY = [
        1,
        144
    ], $VZ = [
        1,
        145
    ], $V_ = [
        1,
        130
    ], $V$ = [
        1,
        132
    ], $V01 = [
        1,
        136
    ], $V11 = [
        1,
        137
    ], $V21 = [
        1,
        138
    ], $V31 = [
        1,
        139
    ], $V41 = [
        1,
        140
    ], $V51 = [
        1,
        141
    ], $V61 = [
        1,
        146
    ], $V71 = [
        1,
        147
    ], $V81 = [
        1,
        126
    ], $V91 = [
        1,
        127
    ], $Va1 = [
        1,
        134
    ], $Vb1 = [
        1,
        129
    ], $Vc1 = [
        1,
        133
    ], $Vd1 = [
        1,
        131
    ], $Ve1 = [
        8,
        9,
        10,
        11,
        27,
        32,
        34,
        36,
        38,
        42,
        58,
        81,
        82,
        83,
        84,
        85,
        86,
        99,
        102,
        103,
        106,
        108,
        111,
        112,
        113,
        118,
        119,
        120,
        121
    ], $Vf1 = [
        1,
        149
    ], $Vg1 = [
        8,
        9,
        11
    ], $Vh1 = [
        8,
        9,
        10,
        11,
        14,
        42,
        58,
        86,
        102,
        103,
        106,
        108,
        111,
        112,
        113
    ], $Vi1 = [
        1,
        169
    ], $Vj1 = [
        1,
        165
    ], $Vk1 = [
        1,
        166
    ], $Vl1 = [
        1,
        170
    ], $Vm1 = [
        1,
        167
    ], $Vn1 = [
        1,
        168
    ], $Vo1 = [
        75,
        113,
        116
    ], $Vp1 = [
        8,
        9,
        10,
        11,
        12,
        14,
        27,
        29,
        32,
        42,
        58,
        73,
        81,
        82,
        83,
        84,
        85,
        86,
        87,
        102,
        106,
        108,
        111,
        112,
        113
    ], $Vq1 = [
        10,
        103
    ], $Vr1 = [
        31,
        47,
        49,
        51,
        53,
        55,
        60,
        62,
        64,
        65,
        67,
        69,
        113,
        114,
        115
    ], $Vs1 = [
        1,
        235
    ], $Vt1 = [
        1,
        233
    ], $Vu1 = [
        1,
        237
    ], $Vv1 = [
        1,
        231
    ], $Vw1 = [
        1,
        232
    ], $Vx1 = [
        1,
        234
    ], $Vy1 = [
        1,
        236
    ], $Vz1 = [
        1,
        238
    ], $VA1 = [
        1,
        255
    ], $VB1 = [
        8,
        9,
        11,
        103
    ], $VC1 = [
        8,
        9,
        10,
        11,
        58,
        81,
        102,
        103,
        106,
        107,
        108,
        109
    ];
    var parser2 = {
        trace: function trace() {},
        yy: {},
        symbols_: {
            "error": 2,
            "start": 3,
            "graphConfig": 4,
            "document": 5,
            "line": 6,
            "statement": 7,
            "SEMI": 8,
            "NEWLINE": 9,
            "SPACE": 10,
            "EOF": 11,
            "GRAPH": 12,
            "NODIR": 13,
            "DIR": 14,
            "FirstStmtSeparator": 15,
            "ending": 16,
            "endToken": 17,
            "spaceList": 18,
            "spaceListNewline": 19,
            "vertexStatement": 20,
            "separator": 21,
            "styleStatement": 22,
            "linkStyleStatement": 23,
            "classDefStatement": 24,
            "classStatement": 25,
            "clickStatement": 26,
            "subgraph": 27,
            "textNoTags": 28,
            "SQS": 29,
            "text": 30,
            "SQE": 31,
            "end": 32,
            "direction": 33,
            "acc_title": 34,
            "acc_title_value": 35,
            "acc_descr": 36,
            "acc_descr_value": 37,
            "acc_descr_multiline_value": 38,
            "link": 39,
            "node": 40,
            "styledVertex": 41,
            "AMP": 42,
            "vertex": 43,
            "STYLE_SEPARATOR": 44,
            "idString": 45,
            "DOUBLECIRCLESTART": 46,
            "DOUBLECIRCLEEND": 47,
            "PS": 48,
            "PE": 49,
            "(-": 50,
            "-)": 51,
            "STADIUMSTART": 52,
            "STADIUMEND": 53,
            "SUBROUTINESTART": 54,
            "SUBROUTINEEND": 55,
            "VERTEX_WITH_PROPS_START": 56,
            "NODE_STRING[field]": 57,
            "COLON": 58,
            "NODE_STRING[value]": 59,
            "PIPE": 60,
            "CYLINDERSTART": 61,
            "CYLINDEREND": 62,
            "DIAMOND_START": 63,
            "DIAMOND_STOP": 64,
            "TAGEND": 65,
            "TRAPSTART": 66,
            "TRAPEND": 67,
            "INVTRAPSTART": 68,
            "INVTRAPEND": 69,
            "linkStatement": 70,
            "arrowText": 71,
            "TESTSTR": 72,
            "START_LINK": 73,
            "edgeText": 74,
            "LINK": 75,
            "edgeTextToken": 76,
            "STR": 77,
            "MD_STR": 78,
            "textToken": 79,
            "keywords": 80,
            "STYLE": 81,
            "LINKSTYLE": 82,
            "CLASSDEF": 83,
            "CLASS": 84,
            "CLICK": 85,
            "DOWN": 86,
            "UP": 87,
            "textNoTagsToken": 88,
            "stylesOpt": 89,
            "idString[vertex]": 90,
            "idString[class]": 91,
            "CALLBACKNAME": 92,
            "CALLBACKARGS": 93,
            "HREF": 94,
            "LINK_TARGET": 95,
            "STR[link]": 96,
            "STR[tooltip]": 97,
            "alphaNum": 98,
            "DEFAULT": 99,
            "numList": 100,
            "INTERPOLATE": 101,
            "NUM": 102,
            "COMMA": 103,
            "style": 104,
            "styleComponent": 105,
            "NODE_STRING": 106,
            "UNIT": 107,
            "BRKT": 108,
            "PCT": 109,
            "idStringToken": 110,
            "MINUS": 111,
            "MULT": 112,
            "UNICODE_TEXT": 113,
            "TEXT": 114,
            "TAGSTART": 115,
            "EDGE_TEXT": 116,
            "alphaNumToken": 117,
            "direction_tb": 118,
            "direction_bt": 119,
            "direction_rl": 120,
            "direction_lr": 121,
            "$accept": 0,
            "$end": 1
        },
        terminals_: {
            2: "error",
            8: "SEMI",
            9: "NEWLINE",
            10: "SPACE",
            11: "EOF",
            12: "GRAPH",
            13: "NODIR",
            14: "DIR",
            27: "subgraph",
            29: "SQS",
            31: "SQE",
            32: "end",
            34: "acc_title",
            35: "acc_title_value",
            36: "acc_descr",
            37: "acc_descr_value",
            38: "acc_descr_multiline_value",
            42: "AMP",
            44: "STYLE_SEPARATOR",
            46: "DOUBLECIRCLESTART",
            47: "DOUBLECIRCLEEND",
            48: "PS",
            49: "PE",
            50: "(-",
            51: "-)",
            52: "STADIUMSTART",
            53: "STADIUMEND",
            54: "SUBROUTINESTART",
            55: "SUBROUTINEEND",
            56: "VERTEX_WITH_PROPS_START",
            57: "NODE_STRING[field]",
            58: "COLON",
            59: "NODE_STRING[value]",
            60: "PIPE",
            61: "CYLINDERSTART",
            62: "CYLINDEREND",
            63: "DIAMOND_START",
            64: "DIAMOND_STOP",
            65: "TAGEND",
            66: "TRAPSTART",
            67: "TRAPEND",
            68: "INVTRAPSTART",
            69: "INVTRAPEND",
            72: "TESTSTR",
            73: "START_LINK",
            75: "LINK",
            77: "STR",
            78: "MD_STR",
            81: "STYLE",
            82: "LINKSTYLE",
            83: "CLASSDEF",
            84: "CLASS",
            85: "CLICK",
            86: "DOWN",
            87: "UP",
            90: "idString[vertex]",
            91: "idString[class]",
            92: "CALLBACKNAME",
            93: "CALLBACKARGS",
            94: "HREF",
            95: "LINK_TARGET",
            96: "STR[link]",
            97: "STR[tooltip]",
            99: "DEFAULT",
            101: "INTERPOLATE",
            102: "NUM",
            103: "COMMA",
            106: "NODE_STRING",
            107: "UNIT",
            108: "BRKT",
            109: "PCT",
            111: "MINUS",
            112: "MULT",
            113: "UNICODE_TEXT",
            114: "TEXT",
            115: "TAGSTART",
            116: "EDGE_TEXT",
            118: "direction_tb",
            119: "direction_bt",
            120: "direction_rl",
            121: "direction_lr"
        },
        productions_: [
            0,
            [
                3,
                2
            ],
            [
                5,
                0
            ],
            [
                5,
                2
            ],
            [
                6,
                1
            ],
            [
                6,
                1
            ],
            [
                6,
                1
            ],
            [
                6,
                1
            ],
            [
                6,
                1
            ],
            [
                4,
                2
            ],
            [
                4,
                2
            ],
            [
                4,
                2
            ],
            [
                4,
                3
            ],
            [
                16,
                2
            ],
            [
                16,
                1
            ],
            [
                17,
                1
            ],
            [
                17,
                1
            ],
            [
                17,
                1
            ],
            [
                15,
                1
            ],
            [
                15,
                1
            ],
            [
                15,
                2
            ],
            [
                19,
                2
            ],
            [
                19,
                2
            ],
            [
                19,
                1
            ],
            [
                19,
                1
            ],
            [
                18,
                2
            ],
            [
                18,
                1
            ],
            [
                7,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                9
            ],
            [
                7,
                6
            ],
            [
                7,
                4
            ],
            [
                7,
                1
            ],
            [
                7,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                1
            ],
            [
                21,
                1
            ],
            [
                21,
                1
            ],
            [
                21,
                1
            ],
            [
                20,
                3
            ],
            [
                20,
                4
            ],
            [
                20,
                2
            ],
            [
                20,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                5
            ],
            [
                41,
                1
            ],
            [
                41,
                3
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                6
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                8
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                6
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                4
            ],
            [
                43,
                1
            ],
            [
                39,
                2
            ],
            [
                39,
                3
            ],
            [
                39,
                3
            ],
            [
                39,
                1
            ],
            [
                39,
                3
            ],
            [
                74,
                1
            ],
            [
                74,
                2
            ],
            [
                74,
                1
            ],
            [
                74,
                1
            ],
            [
                70,
                1
            ],
            [
                71,
                3
            ],
            [
                30,
                1
            ],
            [
                30,
                2
            ],
            [
                30,
                1
            ],
            [
                30,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                80,
                1
            ],
            [
                28,
                1
            ],
            [
                28,
                2
            ],
            [
                28,
                1
            ],
            [
                28,
                1
            ],
            [
                24,
                5
            ],
            [
                25,
                5
            ],
            [
                26,
                2
            ],
            [
                26,
                4
            ],
            [
                26,
                3
            ],
            [
                26,
                5
            ],
            [
                26,
                3
            ],
            [
                26,
                5
            ],
            [
                26,
                5
            ],
            [
                26,
                7
            ],
            [
                26,
                2
            ],
            [
                26,
                4
            ],
            [
                26,
                2
            ],
            [
                26,
                4
            ],
            [
                26,
                4
            ],
            [
                26,
                6
            ],
            [
                22,
                5
            ],
            [
                23,
                5
            ],
            [
                23,
                5
            ],
            [
                23,
                9
            ],
            [
                23,
                9
            ],
            [
                23,
                7
            ],
            [
                23,
                7
            ],
            [
                100,
                1
            ],
            [
                100,
                3
            ],
            [
                89,
                1
            ],
            [
                89,
                3
            ],
            [
                104,
                1
            ],
            [
                104,
                2
            ],
            [
                105,
                1
            ],
            [
                105,
                1
            ],
            [
                105,
                1
            ],
            [
                105,
                1
            ],
            [
                105,
                1
            ],
            [
                105,
                1
            ],
            [
                105,
                1
            ],
            [
                105,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                110,
                1
            ],
            [
                79,
                1
            ],
            [
                79,
                1
            ],
            [
                79,
                1
            ],
            [
                79,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                88,
                1
            ],
            [
                76,
                1
            ],
            [
                76,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                117,
                1
            ],
            [
                45,
                1
            ],
            [
                45,
                2
            ],
            [
                98,
                1
            ],
            [
                98,
                2
            ],
            [
                33,
                1
            ],
            [
                33,
                1
            ],
            [
                33,
                1
            ],
            [
                33,
                1
            ]
        ],
        performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {
            var $0 = $$.length - 1;
            switch(yystate){
                case 2:
                    this.$ = [];
                    break;
                case 3:
                    if (!Array.isArray($$[$0]) || $$[$0].length > 0) {
                        $$[$0 - 1].push($$[$0]);
                    }
                    this.$ = $$[$0 - 1];
                    break;
                case 4:
                case 176:
                    this.$ = $$[$0];
                    break;
                case 11:
                    yy.setDirection("TB");
                    this.$ = "TB";
                    break;
                case 12:
                    yy.setDirection($$[$0 - 1]);
                    this.$ = $$[$0 - 1];
                    break;
                case 27:
                    this.$ = $$[$0 - 1].nodes;
                    break;
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                    this.$ = [];
                    break;
                case 33:
                    this.$ = yy.addSubGraph($$[$0 - 6], $$[$0 - 1], $$[$0 - 4]);
                    break;
                case 34:
                    this.$ = yy.addSubGraph($$[$0 - 3], $$[$0 - 1], $$[$0 - 3]);
                    break;
                case 35:
                    this.$ = yy.addSubGraph(void 0, $$[$0 - 1], void 0);
                    break;
                case 37:
                    this.$ = $$[$0].trim();
                    yy.setAccTitle(this.$);
                    break;
                case 38:
                case 39:
                    this.$ = $$[$0].trim();
                    yy.setAccDescription(this.$);
                    break;
                case 43:
                    yy.addLink($$[$0 - 2].stmt, $$[$0], $$[$0 - 1]);
                    this.$ = {
                        stmt: $$[$0],
                        nodes: $$[$0].concat($$[$0 - 2].nodes)
                    };
                    break;
                case 44:
                    yy.addLink($$[$0 - 3].stmt, $$[$0 - 1], $$[$0 - 2]);
                    this.$ = {
                        stmt: $$[$0 - 1],
                        nodes: $$[$0 - 1].concat($$[$0 - 3].nodes)
                    };
                    break;
                case 45:
                    this.$ = {
                        stmt: $$[$0 - 1],
                        nodes: $$[$0 - 1]
                    };
                    break;
                case 46:
                    this.$ = {
                        stmt: $$[$0],
                        nodes: $$[$0]
                    };
                    break;
                case 47:
                    this.$ = [
                        $$[$0]
                    ];
                    break;
                case 48:
                    this.$ = $$[$0 - 4].concat($$[$0]);
                    break;
                case 49:
                    this.$ = $$[$0];
                    break;
                case 50:
                    this.$ = $$[$0 - 2];
                    yy.setClass($$[$0 - 2], $$[$0]);
                    break;
                case 51:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "square");
                    break;
                case 52:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "doublecircle");
                    break;
                case 53:
                    this.$ = $$[$0 - 5];
                    yy.addVertex($$[$0 - 5], $$[$0 - 2], "circle");
                    break;
                case 54:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "ellipse");
                    break;
                case 55:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "stadium");
                    break;
                case 56:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "subroutine");
                    break;
                case 57:
                    this.$ = $$[$0 - 7];
                    yy.addVertex($$[$0 - 7], $$[$0 - 1], "rect", void 0, void 0, void 0, Object.fromEntries([
                        [
                            $$[$0 - 5],
                            $$[$0 - 3]
                        ]
                    ]));
                    break;
                case 58:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "cylinder");
                    break;
                case 59:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "round");
                    break;
                case 60:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "diamond");
                    break;
                case 61:
                    this.$ = $$[$0 - 5];
                    yy.addVertex($$[$0 - 5], $$[$0 - 2], "hexagon");
                    break;
                case 62:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "odd");
                    break;
                case 63:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "trapezoid");
                    break;
                case 64:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "inv_trapezoid");
                    break;
                case 65:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "lean_right");
                    break;
                case 66:
                    this.$ = $$[$0 - 3];
                    yy.addVertex($$[$0 - 3], $$[$0 - 1], "lean_left");
                    break;
                case 67:
                    this.$ = $$[$0];
                    yy.addVertex($$[$0]);
                    break;
                case 68:
                    $$[$0 - 1].text = $$[$0];
                    this.$ = $$[$0 - 1];
                    break;
                case 69:
                case 70:
                    $$[$0 - 2].text = $$[$0 - 1];
                    this.$ = $$[$0 - 2];
                    break;
                case 71:
                    this.$ = $$[$0];
                    break;
                case 72:
                    var inf = yy.destructLink($$[$0], $$[$0 - 2]);
                    this.$ = {
                        "type": inf.type,
                        "stroke": inf.stroke,
                        "length": inf.length,
                        "text": $$[$0 - 1]
                    };
                    break;
                case 73:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 74:
                    this.$ = {
                        text: $$[$0 - 1].text + "" + $$[$0],
                        type: $$[$0 - 1].type
                    };
                    break;
                case 75:
                    this.$ = {
                        text: $$[$0],
                        type: "string"
                    };
                    break;
                case 76:
                    this.$ = {
                        text: $$[$0],
                        type: "markdown"
                    };
                    break;
                case 77:
                    var inf = yy.destructLink($$[$0]);
                    this.$ = {
                        "type": inf.type,
                        "stroke": inf.stroke,
                        "length": inf.length
                    };
                    break;
                case 78:
                    this.$ = $$[$0 - 1];
                    break;
                case 79:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 80:
                    this.$ = {
                        text: $$[$0 - 1].text + "" + $$[$0],
                        type: $$[$0 - 1].type
                    };
                    break;
                case 81:
                    this.$ = {
                        text: $$[$0],
                        type: "string"
                    };
                    break;
                case 82:
                case 97:
                    this.$ = {
                        text: $$[$0],
                        type: "markdown"
                    };
                    break;
                case 94:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 95:
                    this.$ = {
                        text: $$[$0 - 1].text + "" + $$[$0],
                        type: $$[$0 - 1].type
                    };
                    break;
                case 96:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 98:
                    this.$ = $$[$0 - 4];
                    yy.addClass($$[$0 - 2], $$[$0]);
                    break;
                case 99:
                    this.$ = $$[$0 - 4];
                    yy.setClass($$[$0 - 2], $$[$0]);
                    break;
                case 100:
                case 108:
                    this.$ = $$[$0 - 1];
                    yy.setClickEvent($$[$0 - 1], $$[$0]);
                    break;
                case 101:
                case 109:
                    this.$ = $$[$0 - 3];
                    yy.setClickEvent($$[$0 - 3], $$[$0 - 2]);
                    yy.setTooltip($$[$0 - 3], $$[$0]);
                    break;
                case 102:
                    this.$ = $$[$0 - 2];
                    yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);
                    break;
                case 103:
                    this.$ = $$[$0 - 4];
                    yy.setClickEvent($$[$0 - 4], $$[$0 - 3], $$[$0 - 2]);
                    yy.setTooltip($$[$0 - 4], $$[$0]);
                    break;
                case 104:
                    this.$ = $$[$0 - 2];
                    yy.setLink($$[$0 - 2], $$[$0]);
                    break;
                case 105:
                    this.$ = $$[$0 - 4];
                    yy.setLink($$[$0 - 4], $$[$0 - 2]);
                    yy.setTooltip($$[$0 - 4], $$[$0]);
                    break;
                case 106:
                    this.$ = $$[$0 - 4];
                    yy.setLink($$[$0 - 4], $$[$0 - 2], $$[$0]);
                    break;
                case 107:
                    this.$ = $$[$0 - 6];
                    yy.setLink($$[$0 - 6], $$[$0 - 4], $$[$0]);
                    yy.setTooltip($$[$0 - 6], $$[$0 - 2]);
                    break;
                case 110:
                    this.$ = $$[$0 - 1];
                    yy.setLink($$[$0 - 1], $$[$0]);
                    break;
                case 111:
                    this.$ = $$[$0 - 3];
                    yy.setLink($$[$0 - 3], $$[$0 - 2]);
                    yy.setTooltip($$[$0 - 3], $$[$0]);
                    break;
                case 112:
                    this.$ = $$[$0 - 3];
                    yy.setLink($$[$0 - 3], $$[$0 - 2], $$[$0]);
                    break;
                case 113:
                    this.$ = $$[$0 - 5];
                    yy.setLink($$[$0 - 5], $$[$0 - 4], $$[$0]);
                    yy.setTooltip($$[$0 - 5], $$[$0 - 2]);
                    break;
                case 114:
                    this.$ = $$[$0 - 4];
                    yy.addVertex($$[$0 - 2], void 0, void 0, $$[$0]);
                    break;
                case 115:
                    this.$ = $$[$0 - 4];
                    yy.updateLink([
                        $$[$0 - 2]
                    ], $$[$0]);
                    break;
                case 116:
                    this.$ = $$[$0 - 4];
                    yy.updateLink($$[$0 - 2], $$[$0]);
                    break;
                case 117:
                    this.$ = $$[$0 - 8];
                    yy.updateLinkInterpolate([
                        $$[$0 - 6]
                    ], $$[$0 - 2]);
                    yy.updateLink([
                        $$[$0 - 6]
                    ], $$[$0]);
                    break;
                case 118:
                    this.$ = $$[$0 - 8];
                    yy.updateLinkInterpolate($$[$0 - 6], $$[$0 - 2]);
                    yy.updateLink($$[$0 - 6], $$[$0]);
                    break;
                case 119:
                    this.$ = $$[$0 - 6];
                    yy.updateLinkInterpolate([
                        $$[$0 - 4]
                    ], $$[$0]);
                    break;
                case 120:
                    this.$ = $$[$0 - 6];
                    yy.updateLinkInterpolate($$[$0 - 4], $$[$0]);
                    break;
                case 121:
                case 123:
                    this.$ = [
                        $$[$0]
                    ];
                    break;
                case 122:
                case 124:
                    $$[$0 - 2].push($$[$0]);
                    this.$ = $$[$0 - 2];
                    break;
                case 126:
                    this.$ = $$[$0 - 1] + $$[$0];
                    break;
                case 174:
                    this.$ = $$[$0];
                    break;
                case 175:
                    this.$ = $$[$0 - 1] + "" + $$[$0];
                    break;
                case 177:
                    this.$ = $$[$0 - 1] + "" + $$[$0];
                    break;
                case 178:
                    this.$ = {
                        stmt: "dir",
                        value: "TB"
                    };
                    break;
                case 179:
                    this.$ = {
                        stmt: "dir",
                        value: "BT"
                    };
                    break;
                case 180:
                    this.$ = {
                        stmt: "dir",
                        value: "RL"
                    };
                    break;
                case 181:
                    this.$ = {
                        stmt: "dir",
                        value: "LR"
                    };
                    break;
            }
        },
        table: [
            {
                3: 1,
                4: 2,
                9: $V0,
                10: $V1,
                12: $V2
            },
            {
                1: [
                    3
                ]
            },
            o($V3, $V4, {
                5: 6
            }),
            {
                4: 7,
                9: $V0,
                10: $V1,
                12: $V2
            },
            {
                4: 8,
                9: $V0,
                10: $V1,
                12: $V2
            },
            {
                13: [
                    1,
                    9
                ],
                14: [
                    1,
                    10
                ]
            },
            {
                1: [
                    2,
                    1
                ],
                6: 11,
                7: 12,
                8: $V5,
                9: $V6,
                10: $V7,
                11: $V8,
                20: 17,
                22: 18,
                23: 19,
                24: 20,
                25: 21,
                26: 22,
                27: $V9,
                33: 24,
                34: $Va,
                36: $Vb,
                38: $Vc,
                40: 28,
                41: 38,
                42: $Vd,
                43: 39,
                45: 40,
                58: $Ve,
                81: $Vf,
                82: $Vg,
                83: $Vh,
                84: $Vi,
                85: $Vj,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs,
                118: $Vt,
                119: $Vu,
                120: $Vv,
                121: $Vw
            },
            o($V3, [
                2,
                9
            ]),
            o($V3, [
                2,
                10
            ]),
            o($V3, [
                2,
                11
            ]),
            {
                8: [
                    1,
                    54
                ],
                9: [
                    1,
                    55
                ],
                10: $Vx,
                15: 53,
                18: 56
            },
            o($Vy, [
                2,
                3
            ]),
            o($Vy, [
                2,
                4
            ]),
            o($Vy, [
                2,
                5
            ]),
            o($Vy, [
                2,
                6
            ]),
            o($Vy, [
                2,
                7
            ]),
            o($Vy, [
                2,
                8
            ]),
            {
                8: $Vz,
                9: $VA,
                11: $VB,
                21: 58,
                39: 59,
                70: 63,
                73: [
                    1,
                    64
                ],
                75: [
                    1,
                    65
                ]
            },
            {
                8: $Vz,
                9: $VA,
                11: $VB,
                21: 66
            },
            {
                8: $Vz,
                9: $VA,
                11: $VB,
                21: 67
            },
            {
                8: $Vz,
                9: $VA,
                11: $VB,
                21: 68
            },
            {
                8: $Vz,
                9: $VA,
                11: $VB,
                21: 69
            },
            {
                8: $Vz,
                9: $VA,
                11: $VB,
                21: 70
            },
            {
                8: $Vz,
                9: $VA,
                10: [
                    1,
                    71
                ],
                11: $VB,
                21: 72
            },
            o($Vy, [
                2,
                36
            ]),
            {
                35: [
                    1,
                    73
                ]
            },
            {
                37: [
                    1,
                    74
                ]
            },
            o($Vy, [
                2,
                39
            ]),
            o($VC, [
                2,
                46
            ], {
                18: 75,
                10: $Vx
            }),
            {
                10: [
                    1,
                    76
                ]
            },
            {
                10: [
                    1,
                    77
                ]
            },
            {
                10: [
                    1,
                    78
                ]
            },
            {
                10: [
                    1,
                    79
                ]
            },
            {
                14: $VD,
                42: $VE,
                58: $VF,
                77: [
                    1,
                    83
                ],
                86: $VG,
                92: [
                    1,
                    80
                ],
                94: [
                    1,
                    81
                ],
                98: 82,
                102: $VH,
                103: $VI,
                106: $VJ,
                108: $VK,
                111: $VL,
                112: $VM,
                113: $VN,
                117: 84
            },
            o($Vy, [
                2,
                178
            ]),
            o($Vy, [
                2,
                179
            ]),
            o($Vy, [
                2,
                180
            ]),
            o($Vy, [
                2,
                181
            ]),
            o($VO, [
                2,
                47
            ]),
            o($VO, [
                2,
                49
            ], {
                44: [
                    1,
                    96
                ]
            }),
            o($VP, [
                2,
                67
            ], {
                110: 109,
                29: [
                    1,
                    97
                ],
                42: $Vd,
                46: [
                    1,
                    98
                ],
                48: [
                    1,
                    99
                ],
                50: [
                    1,
                    100
                ],
                52: [
                    1,
                    101
                ],
                54: [
                    1,
                    102
                ],
                56: [
                    1,
                    103
                ],
                58: $Ve,
                61: [
                    1,
                    104
                ],
                63: [
                    1,
                    105
                ],
                65: [
                    1,
                    106
                ],
                66: [
                    1,
                    107
                ],
                68: [
                    1,
                    108
                ],
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                111: $Vq,
                112: $Vr,
                113: $Vs
            }),
            o($VQ, [
                2,
                174
            ]),
            o($VQ, [
                2,
                135
            ]),
            o($VQ, [
                2,
                136
            ]),
            o($VQ, [
                2,
                137
            ]),
            o($VQ, [
                2,
                138
            ]),
            o($VQ, [
                2,
                139
            ]),
            o($VQ, [
                2,
                140
            ]),
            o($VQ, [
                2,
                141
            ]),
            o($VQ, [
                2,
                142
            ]),
            o($VQ, [
                2,
                143
            ]),
            o($VQ, [
                2,
                144
            ]),
            o($VQ, [
                2,
                145
            ]),
            o($V3, [
                2,
                12
            ]),
            o($V3, [
                2,
                18
            ]),
            o($V3, [
                2,
                19
            ]),
            {
                9: [
                    1,
                    110
                ]
            },
            o($VR, [
                2,
                26
            ], {
                18: 111,
                10: $Vx
            }),
            o($Vy, [
                2,
                27
            ]),
            {
                40: 112,
                41: 38,
                42: $Vd,
                43: 39,
                45: 40,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            o($Vy, [
                2,
                40
            ]),
            o($Vy, [
                2,
                41
            ]),
            o($Vy, [
                2,
                42
            ]),
            o($VS, [
                2,
                71
            ], {
                71: 113,
                60: [
                    1,
                    115
                ],
                72: [
                    1,
                    114
                ]
            }),
            {
                74: 116,
                76: 117,
                77: [
                    1,
                    118
                ],
                78: [
                    1,
                    119
                ],
                113: $VT,
                116: $VU
            },
            o([
                42,
                58,
                60,
                72,
                86,
                99,
                102,
                103,
                106,
                108,
                111,
                112,
                113
            ], [
                2,
                77
            ]),
            o($Vy, [
                2,
                28
            ]),
            o($Vy, [
                2,
                29
            ]),
            o($Vy, [
                2,
                30
            ]),
            o($Vy, [
                2,
                31
            ]),
            o($Vy, [
                2,
                32
            ]),
            {
                10: $VV,
                12: $VW,
                14: $VX,
                27: $VY,
                28: 122,
                32: $VZ,
                42: $V_,
                58: $V$,
                73: $V01,
                77: [
                    1,
                    124
                ],
                78: [
                    1,
                    125
                ],
                80: 135,
                81: $V11,
                82: $V21,
                83: $V31,
                84: $V41,
                85: $V51,
                86: $V61,
                87: $V71,
                88: 123,
                102: $V81,
                106: $V91,
                108: $Va1,
                111: $Vb1,
                112: $Vc1,
                113: $Vd1
            },
            o($Ve1, $V4, {
                5: 148
            }),
            o($Vy, [
                2,
                37
            ]),
            o($Vy, [
                2,
                38
            ]),
            o($VC, [
                2,
                45
            ], {
                42: $Vf1
            }),
            {
                42: $Vd,
                45: 150,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            {
                99: [
                    1,
                    151
                ],
                100: 152,
                102: [
                    1,
                    153
                ]
            },
            {
                42: $Vd,
                45: 154,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            {
                42: $Vd,
                45: 155,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            o($Vg1, [
                2,
                100
            ], {
                10: [
                    1,
                    156
                ],
                93: [
                    1,
                    157
                ]
            }),
            {
                77: [
                    1,
                    158
                ]
            },
            o($Vg1, [
                2,
                108
            ], {
                117: 160,
                10: [
                    1,
                    159
                ],
                14: $VD,
                42: $VE,
                58: $VF,
                86: $VG,
                102: $VH,
                103: $VI,
                106: $VJ,
                108: $VK,
                111: $VL,
                112: $VM,
                113: $VN
            }),
            o($Vg1, [
                2,
                110
            ], {
                10: [
                    1,
                    161
                ]
            }),
            o($Vh1, [
                2,
                176
            ]),
            o($Vh1, [
                2,
                163
            ]),
            o($Vh1, [
                2,
                164
            ]),
            o($Vh1, [
                2,
                165
            ]),
            o($Vh1, [
                2,
                166
            ]),
            o($Vh1, [
                2,
                167
            ]),
            o($Vh1, [
                2,
                168
            ]),
            o($Vh1, [
                2,
                169
            ]),
            o($Vh1, [
                2,
                170
            ]),
            o($Vh1, [
                2,
                171
            ]),
            o($Vh1, [
                2,
                172
            ]),
            o($Vh1, [
                2,
                173
            ]),
            {
                42: $Vd,
                45: 162,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            {
                30: 163,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 171,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 173,
                48: [
                    1,
                    172
                ],
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 174,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 175,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 176,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                106: [
                    1,
                    177
                ]
            },
            {
                30: 178,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 179,
                63: [
                    1,
                    180
                ],
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 181,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 182,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 183,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($VQ, [
                2,
                175
            ]),
            o($V3, [
                2,
                20
            ]),
            o($VR, [
                2,
                25
            ]),
            o($VC, [
                2,
                43
            ], {
                18: 184,
                10: $Vx
            }),
            o($VS, [
                2,
                68
            ], {
                10: [
                    1,
                    185
                ]
            }),
            {
                10: [
                    1,
                    186
                ]
            },
            {
                30: 187,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                75: [
                    1,
                    188
                ],
                76: 189,
                113: $VT,
                116: $VU
            },
            o($Vo1, [
                2,
                73
            ]),
            o($Vo1, [
                2,
                75
            ]),
            o($Vo1, [
                2,
                76
            ]),
            o($Vo1, [
                2,
                161
            ]),
            o($Vo1, [
                2,
                162
            ]),
            {
                8: $Vz,
                9: $VA,
                10: $VV,
                11: $VB,
                12: $VW,
                14: $VX,
                21: 191,
                27: $VY,
                29: [
                    1,
                    190
                ],
                32: $VZ,
                42: $V_,
                58: $V$,
                73: $V01,
                80: 135,
                81: $V11,
                82: $V21,
                83: $V31,
                84: $V41,
                85: $V51,
                86: $V61,
                87: $V71,
                88: 192,
                102: $V81,
                106: $V91,
                108: $Va1,
                111: $Vb1,
                112: $Vc1,
                113: $Vd1
            },
            o($Vp1, [
                2,
                94
            ]),
            o($Vp1, [
                2,
                96
            ]),
            o($Vp1, [
                2,
                97
            ]),
            o($Vp1, [
                2,
                150
            ]),
            o($Vp1, [
                2,
                151
            ]),
            o($Vp1, [
                2,
                152
            ]),
            o($Vp1, [
                2,
                153
            ]),
            o($Vp1, [
                2,
                154
            ]),
            o($Vp1, [
                2,
                155
            ]),
            o($Vp1, [
                2,
                156
            ]),
            o($Vp1, [
                2,
                157
            ]),
            o($Vp1, [
                2,
                158
            ]),
            o($Vp1, [
                2,
                159
            ]),
            o($Vp1, [
                2,
                160
            ]),
            o($Vp1, [
                2,
                83
            ]),
            o($Vp1, [
                2,
                84
            ]),
            o($Vp1, [
                2,
                85
            ]),
            o($Vp1, [
                2,
                86
            ]),
            o($Vp1, [
                2,
                87
            ]),
            o($Vp1, [
                2,
                88
            ]),
            o($Vp1, [
                2,
                89
            ]),
            o($Vp1, [
                2,
                90
            ]),
            o($Vp1, [
                2,
                91
            ]),
            o($Vp1, [
                2,
                92
            ]),
            o($Vp1, [
                2,
                93
            ]),
            {
                6: 11,
                7: 12,
                8: $V5,
                9: $V6,
                10: $V7,
                11: $V8,
                20: 17,
                22: 18,
                23: 19,
                24: 20,
                25: 21,
                26: 22,
                27: $V9,
                32: [
                    1,
                    193
                ],
                33: 24,
                34: $Va,
                36: $Vb,
                38: $Vc,
                40: 28,
                41: 38,
                42: $Vd,
                43: 39,
                45: 40,
                58: $Ve,
                81: $Vf,
                82: $Vg,
                83: $Vh,
                84: $Vi,
                85: $Vj,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs,
                118: $Vt,
                119: $Vu,
                120: $Vv,
                121: $Vw
            },
            {
                10: $Vx,
                18: 194
            },
            {
                10: [
                    1,
                    195
                ],
                42: $Vd,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 109,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            {
                10: [
                    1,
                    196
                ]
            },
            {
                10: [
                    1,
                    197
                ],
                103: [
                    1,
                    198
                ]
            },
            o($Vq1, [
                2,
                121
            ]),
            {
                10: [
                    1,
                    199
                ],
                42: $Vd,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 109,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            {
                10: [
                    1,
                    200
                ],
                42: $Vd,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 109,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            {
                77: [
                    1,
                    201
                ]
            },
            o($Vg1, [
                2,
                102
            ], {
                10: [
                    1,
                    202
                ]
            }),
            o($Vg1, [
                2,
                104
            ], {
                10: [
                    1,
                    203
                ]
            }),
            {
                77: [
                    1,
                    204
                ]
            },
            o($Vh1, [
                2,
                177
            ]),
            {
                77: [
                    1,
                    205
                ],
                95: [
                    1,
                    206
                ]
            },
            o($VO, [
                2,
                50
            ], {
                110: 109,
                42: $Vd,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                111: $Vq,
                112: $Vr,
                113: $Vs
            }),
            {
                31: [
                    1,
                    207
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($Vr1, [
                2,
                79
            ]),
            o($Vr1, [
                2,
                81
            ]),
            o($Vr1, [
                2,
                82
            ]),
            o($Vr1, [
                2,
                146
            ]),
            o($Vr1, [
                2,
                147
            ]),
            o($Vr1, [
                2,
                148
            ]),
            o($Vr1, [
                2,
                149
            ]),
            {
                47: [
                    1,
                    209
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 210,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                49: [
                    1,
                    211
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                51: [
                    1,
                    212
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                53: [
                    1,
                    213
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                55: [
                    1,
                    214
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                58: [
                    1,
                    215
                ]
            },
            {
                62: [
                    1,
                    216
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                64: [
                    1,
                    217
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                30: 218,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                31: [
                    1,
                    219
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                65: $Vi1,
                67: [
                    1,
                    220
                ],
                69: [
                    1,
                    221
                ],
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                65: $Vi1,
                67: [
                    1,
                    223
                ],
                69: [
                    1,
                    222
                ],
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($VC, [
                2,
                44
            ], {
                42: $Vf1
            }),
            o($VS, [
                2,
                70
            ]),
            o($VS, [
                2,
                69
            ]),
            {
                60: [
                    1,
                    224
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($VS, [
                2,
                72
            ]),
            o($Vo1, [
                2,
                74
            ]),
            {
                30: 225,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($Ve1, $V4, {
                5: 226
            }),
            o($Vp1, [
                2,
                95
            ]),
            o($Vy, [
                2,
                35
            ]),
            {
                41: 227,
                42: $Vd,
                43: 39,
                45: 40,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            {
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                89: 228,
                102: $Vv1,
                104: 229,
                105: 230,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            },
            {
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                89: 239,
                101: [
                    1,
                    240
                ],
                102: $Vv1,
                104: 229,
                105: 230,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            },
            {
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                89: 241,
                101: [
                    1,
                    242
                ],
                102: $Vv1,
                104: 229,
                105: 230,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            },
            {
                102: [
                    1,
                    243
                ]
            },
            {
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                89: 244,
                102: $Vv1,
                104: 229,
                105: 230,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            },
            {
                42: $Vd,
                45: 245,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs
            },
            o($Vg1, [
                2,
                101
            ]),
            {
                77: [
                    1,
                    246
                ]
            },
            {
                77: [
                    1,
                    247
                ],
                95: [
                    1,
                    248
                ]
            },
            o($Vg1, [
                2,
                109
            ]),
            o($Vg1, [
                2,
                111
            ], {
                10: [
                    1,
                    249
                ]
            }),
            o($Vg1, [
                2,
                112
            ]),
            o($VP, [
                2,
                51
            ]),
            o($Vr1, [
                2,
                80
            ]),
            o($VP, [
                2,
                52
            ]),
            {
                49: [
                    1,
                    250
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($VP, [
                2,
                59
            ]),
            o($VP, [
                2,
                54
            ]),
            o($VP, [
                2,
                55
            ]),
            o($VP, [
                2,
                56
            ]),
            {
                106: [
                    1,
                    251
                ]
            },
            o($VP, [
                2,
                58
            ]),
            o($VP, [
                2,
                60
            ]),
            {
                64: [
                    1,
                    252
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($VP, [
                2,
                62
            ]),
            o($VP, [
                2,
                63
            ]),
            o($VP, [
                2,
                65
            ]),
            o($VP, [
                2,
                64
            ]),
            o($VP, [
                2,
                66
            ]),
            o([
                10,
                42,
                58,
                86,
                99,
                102,
                103,
                106,
                108,
                111,
                112,
                113
            ], [
                2,
                78
            ]),
            {
                31: [
                    1,
                    253
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                6: 11,
                7: 12,
                8: $V5,
                9: $V6,
                10: $V7,
                11: $V8,
                20: 17,
                22: 18,
                23: 19,
                24: 20,
                25: 21,
                26: 22,
                27: $V9,
                32: [
                    1,
                    254
                ],
                33: 24,
                34: $Va,
                36: $Vb,
                38: $Vc,
                40: 28,
                41: 38,
                42: $Vd,
                43: 39,
                45: 40,
                58: $Ve,
                81: $Vf,
                82: $Vg,
                83: $Vh,
                84: $Vi,
                85: $Vj,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs,
                118: $Vt,
                119: $Vu,
                120: $Vv,
                121: $Vw
            },
            o($VO, [
                2,
                48
            ]),
            o($Vg1, [
                2,
                114
            ], {
                103: $VA1
            }),
            o($VB1, [
                2,
                123
            ], {
                105: 256,
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                102: $Vv1,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            }),
            o($VC1, [
                2,
                125
            ]),
            o($VC1, [
                2,
                127
            ]),
            o($VC1, [
                2,
                128
            ]),
            o($VC1, [
                2,
                129
            ]),
            o($VC1, [
                2,
                130
            ]),
            o($VC1, [
                2,
                131
            ]),
            o($VC1, [
                2,
                132
            ]),
            o($VC1, [
                2,
                133
            ]),
            o($VC1, [
                2,
                134
            ]),
            o($Vg1, [
                2,
                115
            ], {
                103: $VA1
            }),
            {
                10: [
                    1,
                    257
                ]
            },
            o($Vg1, [
                2,
                116
            ], {
                103: $VA1
            }),
            {
                10: [
                    1,
                    258
                ]
            },
            o($Vq1, [
                2,
                122
            ]),
            o($Vg1, [
                2,
                98
            ], {
                103: $VA1
            }),
            o($Vg1, [
                2,
                99
            ], {
                110: 109,
                42: $Vd,
                58: $Ve,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                111: $Vq,
                112: $Vr,
                113: $Vs
            }),
            o($Vg1, [
                2,
                103
            ]),
            o($Vg1, [
                2,
                105
            ], {
                10: [
                    1,
                    259
                ]
            }),
            o($Vg1, [
                2,
                106
            ]),
            {
                95: [
                    1,
                    260
                ]
            },
            {
                49: [
                    1,
                    261
                ]
            },
            {
                60: [
                    1,
                    262
                ]
            },
            {
                64: [
                    1,
                    263
                ]
            },
            {
                8: $Vz,
                9: $VA,
                11: $VB,
                21: 264
            },
            o($Vy, [
                2,
                34
            ]),
            {
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                102: $Vv1,
                104: 265,
                105: 230,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            },
            o($VC1, [
                2,
                126
            ]),
            {
                14: $VD,
                42: $VE,
                58: $VF,
                86: $VG,
                98: 266,
                102: $VH,
                103: $VI,
                106: $VJ,
                108: $VK,
                111: $VL,
                112: $VM,
                113: $VN,
                117: 84
            },
            {
                14: $VD,
                42: $VE,
                58: $VF,
                86: $VG,
                98: 267,
                102: $VH,
                103: $VI,
                106: $VJ,
                108: $VK,
                111: $VL,
                112: $VM,
                113: $VN,
                117: 84
            },
            {
                95: [
                    1,
                    268
                ]
            },
            o($Vg1, [
                2,
                113
            ]),
            o($VP, [
                2,
                53
            ]),
            {
                30: 269,
                65: $Vi1,
                77: $Vj1,
                78: $Vk1,
                79: 164,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            o($VP, [
                2,
                61
            ]),
            o($Ve1, $V4, {
                5: 270
            }),
            o($VB1, [
                2,
                124
            ], {
                105: 256,
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                102: $Vv1,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            }),
            o($Vg1, [
                2,
                119
            ], {
                117: 160,
                10: [
                    1,
                    271
                ],
                14: $VD,
                42: $VE,
                58: $VF,
                86: $VG,
                102: $VH,
                103: $VI,
                106: $VJ,
                108: $VK,
                111: $VL,
                112: $VM,
                113: $VN
            }),
            o($Vg1, [
                2,
                120
            ], {
                117: 160,
                10: [
                    1,
                    272
                ],
                14: $VD,
                42: $VE,
                58: $VF,
                86: $VG,
                102: $VH,
                103: $VI,
                106: $VJ,
                108: $VK,
                111: $VL,
                112: $VM,
                113: $VN
            }),
            o($Vg1, [
                2,
                107
            ]),
            {
                31: [
                    1,
                    273
                ],
                65: $Vi1,
                79: 208,
                113: $Vl1,
                114: $Vm1,
                115: $Vn1
            },
            {
                6: 11,
                7: 12,
                8: $V5,
                9: $V6,
                10: $V7,
                11: $V8,
                20: 17,
                22: 18,
                23: 19,
                24: 20,
                25: 21,
                26: 22,
                27: $V9,
                32: [
                    1,
                    274
                ],
                33: 24,
                34: $Va,
                36: $Vb,
                38: $Vc,
                40: 28,
                41: 38,
                42: $Vd,
                43: 39,
                45: 40,
                58: $Ve,
                81: $Vf,
                82: $Vg,
                83: $Vh,
                84: $Vi,
                85: $Vj,
                86: $Vk,
                99: $Vl,
                102: $Vm,
                103: $Vn,
                106: $Vo,
                108: $Vp,
                110: 41,
                111: $Vq,
                112: $Vr,
                113: $Vs,
                118: $Vt,
                119: $Vu,
                120: $Vv,
                121: $Vw
            },
            {
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                89: 275,
                102: $Vv1,
                104: 229,
                105: 230,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            },
            {
                10: $Vs1,
                58: $Vt1,
                81: $Vu1,
                89: 276,
                102: $Vv1,
                104: 229,
                105: 230,
                106: $Vw1,
                107: $Vx1,
                108: $Vy1,
                109: $Vz1
            },
            o($VP, [
                2,
                57
            ]),
            o($Vy, [
                2,
                33
            ]),
            o($Vg1, [
                2,
                117
            ], {
                103: $VA1
            }),
            o($Vg1, [
                2,
                118
            ], {
                103: $VA1
            })
        ],
        defaultActions: {},
        parseError: function parseError(str, hash) {
            if (hash.recoverable) {
                this.trace(str);
            } else {
                var error = new Error(str);
                error.hash = hash;
                throw error;
            }
        },
        parse: function parse(input) {
            var self = this, stack = [
                0
            ], tstack = [], vstack = [
                null
            ], lstack = [], table = this.table, yytext = "", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;
            var args = lstack.slice.call(arguments, 1);
            var lexer2 = Object.create(this.lexer);
            var sharedState = {
                yy: {}
            };
            for(var k in this.yy){
                if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
                    sharedState.yy[k] = this.yy[k];
                }
            }
            lexer2.setInput(input, sharedState.yy);
            sharedState.yy.lexer = lexer2;
            sharedState.yy.parser = this;
            if (typeof lexer2.yylloc == "undefined") {
                lexer2.yylloc = {};
            }
            var yyloc = lexer2.yylloc;
            lstack.push(yyloc);
            var ranges = lexer2.options && lexer2.options.ranges;
            if (typeof sharedState.yy.parseError === "function") {
                this.parseError = sharedState.yy.parseError;
            } else {
                this.parseError = Object.getPrototypeOf(this).parseError;
            }
            function lex2() {
                var token;
                token = tstack.pop() || lexer2.lex() || EOF;
                if (typeof token !== "number") {
                    if (token instanceof Array) {
                        tstack = token;
                        token = tstack.pop();
                    }
                    token = self.symbols_[token] || token;
                }
                return token;
            }
            var symbol, state, action, r, yyval = {}, p, len, newState, expected;
            while(true){
                state = stack[stack.length - 1];
                if (this.defaultActions[state]) {
                    action = this.defaultActions[state];
                } else {
                    if (symbol === null || typeof symbol == "undefined") {
                        symbol = lex2();
                    }
                    action = table[state] && table[state][symbol];
                }
                if (typeof action === "undefined" || !action.length || !action[0]) {
                    var errStr = "";
                    expected = [];
                    for(p in table[state]){
                        if (this.terminals_[p] && p > TERROR) {
                            expected.push("'" + this.terminals_[p] + "'");
                        }
                    }
                    if (lexer2.showPosition) {
                        errStr = "Parse error on line " + (yylineno + 1) + ":\n" + lexer2.showPosition() + "\nExpecting " + expected.join(", ") + ", got '" + (this.terminals_[symbol] || symbol) + "'";
                    } else {
                        errStr = "Parse error on line " + (yylineno + 1) + ": Unexpected " + (symbol == EOF ? "end of input" : "'" + (this.terminals_[symbol] || symbol) + "'");
                    }
                    this.parseError(errStr, {
                        text: lexer2.match,
                        token: this.terminals_[symbol] || symbol,
                        line: lexer2.yylineno,
                        loc: yyloc,
                        expected
                    });
                }
                if (action[0] instanceof Array && action.length > 1) {
                    throw new Error("Parse Error: multiple actions possible at state: " + state + ", token: " + symbol);
                }
                switch(action[0]){
                    case 1:
                        stack.push(symbol);
                        vstack.push(lexer2.yytext);
                        lstack.push(lexer2.yylloc);
                        stack.push(action[1]);
                        symbol = null;
                        {
                            yyleng = lexer2.yyleng;
                            yytext = lexer2.yytext;
                            yylineno = lexer2.yylineno;
                            yyloc = lexer2.yylloc;
                        }
                        break;
                    case 2:
                        len = this.productions_[action[1]][1];
                        yyval.$ = vstack[vstack.length - len];
                        yyval._$ = {
                            first_line: lstack[lstack.length - (len || 1)].first_line,
                            last_line: lstack[lstack.length - 1].last_line,
                            first_column: lstack[lstack.length - (len || 1)].first_column,
                            last_column: lstack[lstack.length - 1].last_column
                        };
                        if (ranges) {
                            yyval._$.range = [
                                lstack[lstack.length - (len || 1)].range[0],
                                lstack[lstack.length - 1].range[1]
                            ];
                        }
                        r = this.performAction.apply(yyval, [
                            yytext,
                            yyleng,
                            yylineno,
                            sharedState.yy,
                            action[1],
                            vstack,
                            lstack
                        ].concat(args));
                        if (typeof r !== "undefined") {
                            return r;
                        }
                        if (len) {
                            stack = stack.slice(0, -1 * len * 2);
                            vstack = vstack.slice(0, -1 * len);
                            lstack = lstack.slice(0, -1 * len);
                        }
                        stack.push(this.productions_[action[1]][0]);
                        vstack.push(yyval.$);
                        lstack.push(yyval._$);
                        newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
                        stack.push(newState);
                        break;
                    case 3:
                        return true;
                }
            }
            return true;
        }
    };
    var lexer = function() {
        var lexer2 = {
            EOF: 1,
            parseError: function parseError(str, hash) {
                if (this.yy.parser) {
                    this.yy.parser.parseError(str, hash);
                } else {
                    throw new Error(str);
                }
            },
            // resets the lexer, sets new input
            setInput: function(input, yy) {
                this.yy = yy || this.yy || {};
                this._input = input;
                this._more = this._backtrack = this.done = false;
                this.yylineno = this.yyleng = 0;
                this.yytext = this.matched = this.match = "";
                this.conditionStack = [
                    "INITIAL"
                ];
                this.yylloc = {
                    first_line: 1,
                    first_column: 0,
                    last_line: 1,
                    last_column: 0
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        0,
                        0
                    ];
                }
                this.offset = 0;
                return this;
            },
            // consumes and returns one char from the input
            input: function() {
                var ch = this._input[0];
                this.yytext += ch;
                this.yyleng++;
                this.offset++;
                this.match += ch;
                this.matched += ch;
                var lines = ch.match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno++;
                    this.yylloc.last_line++;
                } else {
                    this.yylloc.last_column++;
                }
                if (this.options.ranges) {
                    this.yylloc.range[1]++;
                }
                this._input = this._input.slice(1);
                return ch;
            },
            // unshifts one char (or a string) into the input
            unput: function(ch) {
                var len = ch.length;
                var lines = ch.split(/(?:\r\n?|\n)/g);
                this._input = ch + this._input;
                this.yytext = this.yytext.substr(0, this.yytext.length - len);
                this.offset -= len;
                var oldLines = this.match.split(/(?:\r\n?|\n)/g);
                this.match = this.match.substr(0, this.match.length - 1);
                this.matched = this.matched.substr(0, this.matched.length - 1);
                if (lines.length - 1) {
                    this.yylineno -= lines.length - 1;
                }
                var r = this.yylloc.range;
                this.yylloc = {
                    first_line: this.yylloc.first_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.first_column,
                    last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        r[0],
                        r[0] + this.yyleng - len
                    ];
                }
                this.yyleng = this.yytext.length;
                return this;
            },
            // When called from action, caches matched text and appends it on next action
            more: function() {
                this._more = true;
                return this;
            },
            // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
            reject: function() {
                if (this.options.backtrack_lexer) {
                    this._backtrack = true;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
                return this;
            },
            // retain first n characters of the match
            less: function(n) {
                this.unput(this.match.slice(n));
            },
            // displays already matched input, i.e. for error messages
            pastInput: function() {
                var past = this.matched.substr(0, this.matched.length - this.match.length);
                return (past.length > 20 ? "..." : "") + past.substr(-20).replace(/\n/g, "");
            },
            // displays upcoming input, i.e. for error messages
            upcomingInput: function() {
                var next = this.match;
                if (next.length < 20) {
                    next += this._input.substr(0, 20 - next.length);
                }
                return (next.substr(0, 20) + (next.length > 20 ? "..." : "")).replace(/\n/g, "");
            },
            // displays the character position where the lexing error occurred, i.e. for error messages
            showPosition: function() {
                var pre = this.pastInput();
                var c = new Array(pre.length + 1).join("-");
                return pre + this.upcomingInput() + "\n" + c + "^";
            },
            // test the lexed token: return FALSE when not a match, otherwise return token
            test_match: function(match, indexed_rule) {
                var token, lines, backup;
                if (this.options.backtrack_lexer) {
                    backup = {
                        yylineno: this.yylineno,
                        yylloc: {
                            first_line: this.yylloc.first_line,
                            last_line: this.last_line,
                            first_column: this.yylloc.first_column,
                            last_column: this.yylloc.last_column
                        },
                        yytext: this.yytext,
                        match: this.match,
                        matches: this.matches,
                        matched: this.matched,
                        yyleng: this.yyleng,
                        offset: this.offset,
                        _more: this._more,
                        _input: this._input,
                        yy: this.yy,
                        conditionStack: this.conditionStack.slice(0),
                        done: this.done
                    };
                    if (this.options.ranges) {
                        backup.yylloc.range = this.yylloc.range.slice(0);
                    }
                }
                lines = match[0].match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno += lines.length;
                }
                this.yylloc = {
                    first_line: this.yylloc.last_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.last_column,
                    last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + match[0].length
                };
                this.yytext += match[0];
                this.match += match[0];
                this.matches = match;
                this.yyleng = this.yytext.length;
                if (this.options.ranges) {
                    this.yylloc.range = [
                        this.offset,
                        this.offset += this.yyleng
                    ];
                }
                this._more = false;
                this._backtrack = false;
                this._input = this._input.slice(match[0].length);
                this.matched += match[0];
                token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
                if (this.done && this._input) {
                    this.done = false;
                }
                if (token) {
                    return token;
                } else if (this._backtrack) {
                    for(var k in backup){
                        this[k] = backup[k];
                    }
                    return false;
                }
                return false;
            },
            // return next match in input
            next: function() {
                if (this.done) {
                    return this.EOF;
                }
                if (!this._input) {
                    this.done = true;
                }
                var token, match, tempMatch, index;
                if (!this._more) {
                    this.yytext = "";
                    this.match = "";
                }
                var rules = this._currentRules();
                for(var i = 0; i < rules.length; i++){
                    tempMatch = this._input.match(this.rules[rules[i]]);
                    if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                        match = tempMatch;
                        index = i;
                        if (this.options.backtrack_lexer) {
                            token = this.test_match(tempMatch, rules[i]);
                            if (token !== false) {
                                return token;
                            } else if (this._backtrack) {
                                match = false;
                                continue;
                            } else {
                                return false;
                            }
                        } else if (!this.options.flex) {
                            break;
                        }
                    }
                }
                if (match) {
                    token = this.test_match(match, rules[index]);
                    if (token !== false) {
                        return token;
                    }
                    return false;
                }
                if (this._input === "") {
                    return this.EOF;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". Unrecognized text.\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
            },
            // return next match that has a token
            lex: function lex2() {
                var r = this.next();
                if (r) {
                    return r;
                } else {
                    return this.lex();
                }
            },
            // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
            begin: function begin(condition) {
                this.conditionStack.push(condition);
            },
            // pop the previously active lexer condition state off the condition stack
            popState: function popState() {
                var n = this.conditionStack.length - 1;
                if (n > 0) {
                    return this.conditionStack.pop();
                } else {
                    return this.conditionStack[0];
                }
            },
            // produce the lexer rule set which is active for the currently active lexer condition state
            _currentRules: function _currentRules() {
                if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
                    return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
                } else {
                    return this.conditions["INITIAL"].rules;
                }
            },
            // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
            topState: function topState(n) {
                n = this.conditionStack.length - 1 - Math.abs(n || 0);
                if (n >= 0) {
                    return this.conditionStack[n];
                } else {
                    return "INITIAL";
                }
            },
            // alias for begin(condition)
            pushState: function pushState(condition) {
                this.begin(condition);
            },
            // return the number of states currently on the stack
            stateStackSize: function stateStackSize() {
                return this.conditionStack.length;
            },
            options: {},
            performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {
                switch($avoiding_name_collisions){
                    case 0:
                        this.begin("acc_title");
                        return 34;
                    case 1:
                        this.popState();
                        return "acc_title_value";
                    case 2:
                        this.begin("acc_descr");
                        return 36;
                    case 3:
                        this.popState();
                        return "acc_descr_value";
                    case 4:
                        this.begin("acc_descr_multiline");
                        break;
                    case 5:
                        this.popState();
                        break;
                    case 6:
                        return "acc_descr_multiline_value";
                    case 7:
                        this.begin("callbackname");
                        break;
                    case 8:
                        this.popState();
                        break;
                    case 9:
                        this.popState();
                        this.begin("callbackargs");
                        break;
                    case 10:
                        return 92;
                    case 11:
                        this.popState();
                        break;
                    case 12:
                        return 93;
                    case 13:
                        return "MD_STR";
                    case 14:
                        this.popState();
                        break;
                    case 15:
                        this.begin("md_string");
                        break;
                    case 16:
                        return "STR";
                    case 17:
                        this.popState();
                        break;
                    case 18:
                        this.pushState("string");
                        break;
                    case 19:
                        return 81;
                    case 20:
                        return 99;
                    case 21:
                        return 82;
                    case 22:
                        return 101;
                    case 23:
                        return 83;
                    case 24:
                        return 84;
                    case 25:
                        return 94;
                    case 26:
                        this.begin("click");
                        break;
                    case 27:
                        this.popState();
                        break;
                    case 28:
                        return 85;
                    case 29:
                        if (yy.lex.firstGraph()) {
                            this.begin("dir");
                        }
                        return 12;
                    case 30:
                        if (yy.lex.firstGraph()) {
                            this.begin("dir");
                        }
                        return 12;
                    case 31:
                        if (yy.lex.firstGraph()) {
                            this.begin("dir");
                        }
                        return 12;
                    case 32:
                        return 27;
                    case 33:
                        return 32;
                    case 34:
                        return 95;
                    case 35:
                        return 95;
                    case 36:
                        return 95;
                    case 37:
                        return 95;
                    case 38:
                        this.popState();
                        return 13;
                    case 39:
                        this.popState();
                        return 14;
                    case 40:
                        this.popState();
                        return 14;
                    case 41:
                        this.popState();
                        return 14;
                    case 42:
                        this.popState();
                        return 14;
                    case 43:
                        this.popState();
                        return 14;
                    case 44:
                        this.popState();
                        return 14;
                    case 45:
                        this.popState();
                        return 14;
                    case 46:
                        this.popState();
                        return 14;
                    case 47:
                        this.popState();
                        return 14;
                    case 48:
                        this.popState();
                        return 14;
                    case 49:
                        return 118;
                    case 50:
                        return 119;
                    case 51:
                        return 120;
                    case 52:
                        return 121;
                    case 53:
                        return 102;
                    case 54:
                        return 108;
                    case 55:
                        return 44;
                    case 56:
                        return 58;
                    case 57:
                        return 42;
                    case 58:
                        return 8;
                    case 59:
                        return 103;
                    case 60:
                        return 112;
                    case 61:
                        this.popState();
                        return 75;
                    case 62:
                        this.pushState("edgeText");
                        return 73;
                    case 63:
                        return 116;
                    case 64:
                        this.popState();
                        return 75;
                    case 65:
                        this.pushState("thickEdgeText");
                        return 73;
                    case 66:
                        return 116;
                    case 67:
                        this.popState();
                        return 75;
                    case 68:
                        this.pushState("dottedEdgeText");
                        return 73;
                    case 69:
                        return 116;
                    case 70:
                        return 75;
                    case 71:
                        this.popState();
                        return 51;
                    case 72:
                        return "TEXT";
                    case 73:
                        this.pushState("ellipseText");
                        return 50;
                    case 74:
                        this.popState();
                        return 53;
                    case 75:
                        this.pushState("text");
                        return 52;
                    case 76:
                        this.popState();
                        return 55;
                    case 77:
                        this.pushState("text");
                        return 54;
                    case 78:
                        return 56;
                    case 79:
                        this.pushState("text");
                        return 65;
                    case 80:
                        this.popState();
                        return 62;
                    case 81:
                        this.pushState("text");
                        return 61;
                    case 82:
                        this.popState();
                        return 47;
                    case 83:
                        this.pushState("text");
                        return 46;
                    case 84:
                        this.popState();
                        return 67;
                    case 85:
                        this.popState();
                        return 69;
                    case 86:
                        return 114;
                    case 87:
                        this.pushState("trapText");
                        return 66;
                    case 88:
                        this.pushState("trapText");
                        return 68;
                    case 89:
                        return 115;
                    case 90:
                        return 65;
                    case 91:
                        return 87;
                    case 92:
                        return "SEP";
                    case 93:
                        return 86;
                    case 94:
                        return 112;
                    case 95:
                        return 108;
                    case 96:
                        return 42;
                    case 97:
                        return 106;
                    case 98:
                        return 111;
                    case 99:
                        return 113;
                    case 100:
                        this.popState();
                        return 60;
                    case 101:
                        this.pushState("text");
                        return 60;
                    case 102:
                        this.popState();
                        return 49;
                    case 103:
                        this.pushState("text");
                        return 48;
                    case 104:
                        this.popState();
                        return 31;
                    case 105:
                        this.pushState("text");
                        return 29;
                    case 106:
                        this.popState();
                        return 64;
                    case 107:
                        this.pushState("text");
                        return 63;
                    case 108:
                        return "TEXT";
                    case 109:
                        return "QUOTE";
                    case 110:
                        return 9;
                    case 111:
                        return 10;
                    case 112:
                        return 11;
                }
            },
            rules: [
                /^(?:accTitle\s*:\s*)/,
                /^(?:(?!\n||)*[^\n]*)/,
                /^(?:accDescr\s*:\s*)/,
                /^(?:(?!\n||)*[^\n]*)/,
                /^(?:accDescr\s*\{\s*)/,
                /^(?:[\}])/,
                /^(?:[^\}]*)/,
                /^(?:call[\s]+)/,
                /^(?:\([\s]*\))/,
                /^(?:\()/,
                /^(?:[^(]*)/,
                /^(?:\))/,
                /^(?:[^)]*)/,
                /^(?:[^`"]+)/,
                /^(?:[`]["])/,
                /^(?:["][`])/,
                /^(?:[^"]+)/,
                /^(?:["])/,
                /^(?:["])/,
                /^(?:style\b)/,
                /^(?:default\b)/,
                /^(?:linkStyle\b)/,
                /^(?:interpolate\b)/,
                /^(?:classDef\b)/,
                /^(?:class\b)/,
                /^(?:href[\s])/,
                /^(?:click[\s]+)/,
                /^(?:[\s\n])/,
                /^(?:[^\s\n]*)/,
                /^(?:flowchart-elk\b)/,
                /^(?:graph\b)/,
                /^(?:flowchart\b)/,
                /^(?:subgraph\b)/,
                /^(?:end\b\s*)/,
                /^(?:_self\b)/,
                /^(?:_blank\b)/,
                /^(?:_parent\b)/,
                /^(?:_top\b)/,
                /^(?:(\r?\n)*\s*\n)/,
                /^(?:\s*LR\b)/,
                /^(?:\s*RL\b)/,
                /^(?:\s*TB\b)/,
                /^(?:\s*BT\b)/,
                /^(?:\s*TD\b)/,
                /^(?:\s*BR\b)/,
                /^(?:\s*<)/,
                /^(?:\s*>)/,
                /^(?:\s*\^)/,
                /^(?:\s*v\b)/,
                /^(?:.*direction\s+TB[^\n]*)/,
                /^(?:.*direction\s+BT[^\n]*)/,
                /^(?:.*direction\s+RL[^\n]*)/,
                /^(?:.*direction\s+LR[^\n]*)/,
                /^(?:[0-9]+)/,
                /^(?:#)/,
                /^(?::::)/,
                /^(?::)/,
                /^(?:&)/,
                /^(?:;)/,
                /^(?:,)/,
                /^(?:\*)/,
                /^(?:\s*[xo<]?--+[-xo>]\s*)/,
                /^(?:\s*[xo<]?--\s*)/,
                /^(?:[^-]|-(?!-)+)/,
                /^(?:\s*[xo<]?==+[=xo>]\s*)/,
                /^(?:\s*[xo<]?==\s*)/,
                /^(?:[^=]|=(?!))/,
                /^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,
                /^(?:\s*[xo<]?-\.\s*)/,
                /^(?:[^\.]|\.(?!))/,
                /^(?:\s*~~[\~]+\s*)/,
                /^(?:[-/\)][\)])/,
                /^(?:[^\(\)\[\]\{\}]|!\)+)/,
                /^(?:\(-)/,
                /^(?:\]\))/,
                /^(?:\(\[)/,
                /^(?:\]\])/,
                /^(?:\[\[)/,
                /^(?:\[\|)/,
                /^(?:>)/,
                /^(?:\)\])/,
                /^(?:\[\()/,
                /^(?:\)\)\))/,
                /^(?:\(\(\()/,
                /^(?:[\\(?=\])][\]])/,
                /^(?:\/(?=\])\])/,
                /^(?:\/(?!\])|\\(?!\])|[^\\\[\]\(\)\{\}\/]+)/,
                /^(?:\[\/)/,
                /^(?:\[\\)/,
                /^(?:<)/,
                /^(?:>)/,
                /^(?:\^)/,
                /^(?:\\\|)/,
                /^(?:v\b)/,
                /^(?:\*)/,
                /^(?:#)/,
                /^(?:&)/,
                /^(?:([A-Za-z0-9!"\#$%&'*+\.`?\\_\/]|-(?=[^\>\-\.])|(?!))+)/,
                /^(?:-)/,
                /^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,
                /^(?:\|)/,
                /^(?:\|)/,
                /^(?:\))/,
                /^(?:\()/,
                /^(?:\])/,
                /^(?:\[)/,
                /^(?:(\}))/,
                /^(?:\{)/,
                /^(?:[^\[\]\(\)\{\}\|\"]+)/,
                /^(?:")/,
                /^(?:(\r?\n)+)/,
                /^(?:\s)/,
                /^(?:$)/
            ],
            conditions: {
                "callbackargs": {
                    "rules": [
                        11,
                        12,
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "callbackname": {
                    "rules": [
                        8,
                        9,
                        10,
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "href": {
                    "rules": [
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "click": {
                    "rules": [
                        15,
                        18,
                        27,
                        28,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "dottedEdgeText": {
                    "rules": [
                        15,
                        18,
                        67,
                        69,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "thickEdgeText": {
                    "rules": [
                        15,
                        18,
                        64,
                        66,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "edgeText": {
                    "rules": [
                        15,
                        18,
                        61,
                        63,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "trapText": {
                    "rules": [
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        84,
                        85,
                        86,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "ellipseText": {
                    "rules": [
                        15,
                        18,
                        70,
                        71,
                        72,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "text": {
                    "rules": [
                        15,
                        18,
                        70,
                        73,
                        74,
                        75,
                        76,
                        77,
                        80,
                        81,
                        82,
                        83,
                        87,
                        88,
                        100,
                        101,
                        102,
                        103,
                        104,
                        105,
                        106,
                        107,
                        108
                    ],
                    "inclusive": false
                },
                "vertex": {
                    "rules": [
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "dir": {
                    "rules": [
                        15,
                        18,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45,
                        46,
                        47,
                        48,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "acc_descr_multiline": {
                    "rules": [
                        5,
                        6,
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "acc_descr": {
                    "rules": [
                        3,
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "acc_title": {
                    "rules": [
                        1,
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "md_string": {
                    "rules": [
                        13,
                        14,
                        15,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "string": {
                    "rules": [
                        15,
                        16,
                        17,
                        18,
                        70,
                        73,
                        75,
                        77,
                        81,
                        83,
                        87,
                        88,
                        101,
                        103,
                        105,
                        107
                    ],
                    "inclusive": false
                },
                "INITIAL": {
                    "rules": [
                        0,
                        2,
                        4,
                        7,
                        15,
                        18,
                        19,
                        20,
                        21,
                        22,
                        23,
                        24,
                        25,
                        26,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        49,
                        50,
                        51,
                        52,
                        53,
                        54,
                        55,
                        56,
                        57,
                        58,
                        59,
                        60,
                        61,
                        62,
                        64,
                        65,
                        67,
                        68,
                        70,
                        73,
                        75,
                        77,
                        78,
                        79,
                        81,
                        83,
                        87,
                        88,
                        89,
                        90,
                        91,
                        92,
                        93,
                        94,
                        95,
                        96,
                        97,
                        98,
                        99,
                        101,
                        103,
                        105,
                        107,
                        109,
                        110,
                        111,
                        112
                    ],
                    "inclusive": true
                }
            }
        };
        return lexer2;
    }();
    parser2.lexer = lexer;
    function Parser() {
        this.yy = {};
    }
    Parser.prototype = parser2;
    parser2.Parser = Parser;
    return new Parser();
}();
parser.parser = parser;
const parser$1 = parser;
const MERMAID_DOM_ID_PREFIX = "flowchart-";
let vertexCounter = 0;
let config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
let vertices = {};
let edges = [];
let classes = {};
let subGraphs = [];
let subGraphLookup = {};
let tooltips = {};
let subCount = 0;
let firstGraphFlag = true;
let direction;
let version;
let funs = [];
const sanitizeText = (txt)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["e"].sanitizeText(txt, config);
const lookUpDomId = function(id) {
    const vertexKeys = Object.keys(vertices);
    for (const vertexKey of vertexKeys){
        if (vertices[vertexKey].id === id) {
            return vertices[vertexKey].domId;
        }
    }
    return id;
};
const addVertex = function(_id, textObj, type, style, classes2, dir, props = {}) {
    let txt;
    let id = _id;
    if (id === void 0) {
        return;
    }
    if (id.trim().length === 0) {
        return;
    }
    if (vertices[id] === void 0) {
        vertices[id] = {
            id,
            labelType: "text",
            domId: MERMAID_DOM_ID_PREFIX + id + "-" + vertexCounter,
            styles: [],
            classes: []
        };
    }
    vertexCounter++;
    if (textObj !== void 0) {
        config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
        txt = sanitizeText(textObj.text.trim());
        vertices[id].labelType = textObj.type;
        if (txt[0] === '"' && txt[txt.length - 1] === '"') {
            txt = txt.substring(1, txt.length - 1);
        }
        vertices[id].text = txt;
    } else {
        if (vertices[id].text === void 0) {
            vertices[id].text = _id;
        }
    }
    if (type !== void 0) {
        vertices[id].type = type;
    }
    if (style !== void 0 && style !== null) {
        style.forEach(function(s) {
            vertices[id].styles.push(s);
        });
    }
    if (classes2 !== void 0 && classes2 !== null) {
        classes2.forEach(function(s) {
            vertices[id].classes.push(s);
        });
    }
    if (dir !== void 0) {
        vertices[id].dir = dir;
    }
    if (vertices[id].props === void 0) {
        vertices[id].props = props;
    } else if (props !== void 0) {
        Object.assign(vertices[id].props, props);
    }
};
const addSingleLink = function(_start, _end, type) {
    let start = _start;
    let end = _end;
    const edge = {
        start,
        end,
        type: void 0,
        text: "",
        labelType: "text"
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("abc78 Got edge...", edge);
    const linkTextObj = type.text;
    if (linkTextObj !== void 0) {
        edge.text = sanitizeText(linkTextObj.text.trim());
        if (edge.text[0] === '"' && edge.text[edge.text.length - 1] === '"') {
            edge.text = edge.text.substring(1, edge.text.length - 1);
        }
        edge.labelType = linkTextObj.type;
    }
    if (type !== void 0) {
        edge.type = type.type;
        edge.stroke = type.stroke;
        edge.length = type.length;
    }
    if ((edge == null ? void 0 : edge.length) > 10) {
        edge.length = 10;
    }
    if (edges.length < (config.maxEdges ?? 500)) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("abc78 pushing edge...");
        edges.push(edge);
    } else {
        throw new Error(`Edge limit exceeded. ${edges.length} edges found, but the limit is ${config.maxEdges}.

Initialize mermaid with maxEdges set to a higher number to allow more edges.
You cannot set this config via configuration inside the diagram as it is a secure config.
You have to call mermaid.initialize.`);
    }
};
const addLink = function(_start, _end, type) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("addLink (abc78)", _start, _end, type);
    let i, j;
    for(i = 0; i < _start.length; i++){
        for(j = 0; j < _end.length; j++){
            addSingleLink(_start[i], _end[j], type);
        }
    }
};
const updateLinkInterpolate = function(positions, interp) {
    positions.forEach(function(pos) {
        if (pos === "default") {
            edges.defaultInterpolate = interp;
        } else {
            edges[pos].interpolate = interp;
        }
    });
};
const updateLink = function(positions, style) {
    positions.forEach(function(pos) {
        if (pos >= edges.length) {
            throw new Error(`The index ${pos} for linkStyle is out of bounds. Valid indices for linkStyle are between 0 and ${edges.length - 1}. (Help: Ensure that the index is within the range of existing edges.)`);
        }
        if (pos === "default") {
            edges.defaultStyle = style;
        } else {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].isSubstringInArray("fill", style) === -1) {
                style.push("fill:none");
            }
            edges[pos].style = style;
        }
    });
};
const addClass = function(ids, style) {
    ids.split(",").forEach(function(id) {
        if (classes[id] === void 0) {
            classes[id] = {
                id,
                styles: [],
                textStyles: []
            };
        }
        if (style !== void 0 && style !== null) {
            style.forEach(function(s) {
                if (s.match("color")) {
                    const newStyle = s.replace("fill", "bgFill").replace("color", "fill");
                    classes[id].textStyles.push(newStyle);
                }
                classes[id].styles.push(s);
            });
        }
    });
};
const setDirection = function(dir) {
    direction = dir;
    if (direction.match(/.*</)) {
        direction = "RL";
    }
    if (direction.match(/.*\^/)) {
        direction = "BT";
    }
    if (direction.match(/.*>/)) {
        direction = "LR";
    }
    if (direction.match(/.*v/)) {
        direction = "TB";
    }
    if (direction === "TD") {
        direction = "TB";
    }
};
const setClass = function(ids, className) {
    ids.split(",").forEach(function(_id) {
        let id = _id;
        if (vertices[id] !== void 0) {
            vertices[id].classes.push(className);
        }
        if (subGraphLookup[id] !== void 0) {
            subGraphLookup[id].classes.push(className);
        }
    });
};
const setTooltip = function(ids, tooltip) {
    ids.split(",").forEach(function(id) {
        if (tooltip !== void 0) {
            tooltips[version === "gen-1" ? lookUpDomId(id) : id] = sanitizeText(tooltip);
        }
    });
};
const setClickFun = function(id, functionName, functionArgs) {
    let domId = lookUpDomId(id);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().securityLevel !== "loose") {
        return;
    }
    if (functionName === void 0) {
        return;
    }
    let argList = [];
    if (typeof functionArgs === "string") {
        argList = functionArgs.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);
        for(let i = 0; i < argList.length; i++){
            let item = argList[i].trim();
            if (item.charAt(0) === '"' && item.charAt(item.length - 1) === '"') {
                item = item.substr(1, item.length - 2);
            }
            argList[i] = item;
        }
    }
    if (argList.length === 0) {
        argList.push(id);
    }
    if (vertices[id] !== void 0) {
        vertices[id].haveCallback = true;
        funs.push(function() {
            const elem = document.querySelector(`[id="${domId}"]`);
            if (elem !== null) {
                elem.addEventListener("click", function() {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].runFunc(functionName, ...argList);
                }, false);
            }
        });
    }
};
const setLink = function(ids, linkStr, target) {
    ids.split(",").forEach(function(id) {
        if (vertices[id] !== void 0) {
            vertices[id].link = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].formatUrl(linkStr, config);
            vertices[id].linkTarget = target;
        }
    });
    setClass(ids, "clickable");
};
const getTooltip = function(id) {
    if (tooltips.hasOwnProperty(id)) {
        return tooltips[id];
    }
    return void 0;
};
const setClickEvent = function(ids, functionName, functionArgs) {
    ids.split(",").forEach(function(id) {
        setClickFun(id, functionName, functionArgs);
    });
    setClass(ids, "clickable");
};
const bindFunctions = function(element) {
    funs.forEach(function(fun) {
        fun(element);
    });
};
const getDirection = function() {
    return direction.trim();
};
const getVertices = function() {
    return vertices;
};
const getEdges = function() {
    return edges;
};
const getClasses = function() {
    return classes;
};
const setupToolTips = function(element) {
    let tooltipElem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(".mermaidTooltip");
    if ((tooltipElem._groups || tooltipElem)[0][0] === null) {
        tooltipElem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("body").append("div").attr("class", "mermaidTooltip").style("opacity", 0);
    }
    const svg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(element).select("svg");
    const nodes = svg.selectAll("g.node");
    nodes.on("mouseover", function() {
        const el = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this);
        const title = el.attr("title");
        if (title === null) {
            return;
        }
        const rect = this.getBoundingClientRect();
        tooltipElem.transition().duration(200).style("opacity", ".9");
        tooltipElem.text(el.attr("title")).style("left", window.scrollX + rect.left + (rect.right - rect.left) / 2 + "px").style("top", window.scrollY + rect.bottom + "px");
        tooltipElem.html(tooltipElem.html().replace(/&lt;br\/&gt;/g, "<br/>"));
        el.classed("hover", true);
    }).on("mouseout", function() {
        tooltipElem.transition().duration(500).style("opacity", 0);
        const el = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this);
        el.classed("hover", false);
    });
};
funs.push(setupToolTips);
const clear = function(ver = "gen-1") {
    vertices = {};
    classes = {};
    edges = [];
    funs = [
        setupToolTips
    ];
    subGraphs = [];
    subGraphLookup = {};
    subCount = 0;
    tooltips = {};
    firstGraphFlag = true;
    version = ver;
    config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["v"])();
};
const setGen = (ver)=>{
    version = ver || "gen-2";
};
const defaultStyle = function() {
    return "fill:#ffa;stroke: #f66; stroke-width: 3px; stroke-dasharray: 5, 5;fill:#ffa;stroke: #666;";
};
const addSubGraph = function(_id, list, _title) {
    let id = _id.text.trim();
    let title = _title.text;
    if (_id === _title && _title.text.match(/\s/)) {
        id = void 0;
    }
    function uniq(a) {
        const prims = {
            boolean: {},
            number: {},
            string: {}
        };
        const objs = [];
        let dir2;
        const nodeList2 = a.filter(function(item) {
            const type = typeof item;
            if (item.stmt && item.stmt === "dir") {
                dir2 = item.value;
                return false;
            }
            if (item.trim() === "") {
                return false;
            }
            if (type in prims) {
                return prims[type].hasOwnProperty(item) ? false : prims[type][item] = true;
            } else {
                return objs.includes(item) ? false : objs.push(item);
            }
        });
        return {
            nodeList: nodeList2,
            dir: dir2
        };
    }
    let nodeList = [];
    const { nodeList: nl, dir } = uniq(nodeList.concat.apply(nodeList, list));
    nodeList = nl;
    if (version === "gen-1") {
        for(let i = 0; i < nodeList.length; i++){
            nodeList[i] = lookUpDomId(nodeList[i]);
        }
    }
    id = id || "subGraph" + subCount;
    title = title || "";
    title = sanitizeText(title);
    subCount = subCount + 1;
    const subGraph = {
        id,
        nodes: nodeList,
        title: title.trim(),
        classes: [],
        dir,
        labelType: _title.type
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Adding", subGraph.id, subGraph.nodes, subGraph.dir);
    subGraph.nodes = makeUniq(subGraph, subGraphs).nodes;
    subGraphs.push(subGraph);
    subGraphLookup[id] = subGraph;
    return id;
};
const getPosForId = function(id) {
    for (const [i, subGraph] of subGraphs.entries()){
        if (subGraph.id === id) {
            return i;
        }
    }
    return -1;
};
let secCount = -1;
const posCrossRef = [];
const indexNodes2 = function(id, pos) {
    const nodes = subGraphs[pos].nodes;
    secCount = secCount + 1;
    if (secCount > 2e3) {
        return;
    }
    posCrossRef[secCount] = pos;
    if (subGraphs[pos].id === id) {
        return {
            result: true,
            count: 0
        };
    }
    let count = 0;
    let posCount = 1;
    while(count < nodes.length){
        const childPos = getPosForId(nodes[count]);
        if (childPos >= 0) {
            const res = indexNodes2(id, childPos);
            if (res.result) {
                return {
                    result: true,
                    count: posCount + res.count
                };
            } else {
                posCount = posCount + res.count;
            }
        }
        count = count + 1;
    }
    return {
        result: false,
        count: posCount
    };
};
const getDepthFirstPos = function(pos) {
    return posCrossRef[pos];
};
const indexNodes = function() {
    secCount = -1;
    if (subGraphs.length > 0) {
        indexNodes2("none", subGraphs.length - 1);
    }
};
const getSubGraphs = function() {
    return subGraphs;
};
const firstGraph = ()=>{
    if (firstGraphFlag) {
        firstGraphFlag = false;
        return true;
    }
    return false;
};
const destructStartLink = (_str)=>{
    let str = _str.trim();
    let type = "arrow_open";
    switch(str[0]){
        case "<":
            type = "arrow_point";
            str = str.slice(1);
            break;
        case "x":
            type = "arrow_cross";
            str = str.slice(1);
            break;
        case "o":
            type = "arrow_circle";
            str = str.slice(1);
            break;
    }
    let stroke = "normal";
    if (str.includes("=")) {
        stroke = "thick";
    }
    if (str.includes(".")) {
        stroke = "dotted";
    }
    return {
        type,
        stroke
    };
};
const countChar = (char, str)=>{
    const length = str.length;
    let count = 0;
    for(let i = 0; i < length; ++i){
        if (str[i] === char) {
            ++count;
        }
    }
    return count;
};
const destructEndLink = (_str)=>{
    const str = _str.trim();
    let line = str.slice(0, -1);
    let type = "arrow_open";
    switch(str.slice(-1)){
        case "x":
            type = "arrow_cross";
            if (str[0] === "x") {
                type = "double_" + type;
                line = line.slice(1);
            }
            break;
        case ">":
            type = "arrow_point";
            if (str[0] === "<") {
                type = "double_" + type;
                line = line.slice(1);
            }
            break;
        case "o":
            type = "arrow_circle";
            if (str[0] === "o") {
                type = "double_" + type;
                line = line.slice(1);
            }
            break;
    }
    let stroke = "normal";
    let length = line.length - 1;
    if (line[0] === "=") {
        stroke = "thick";
    }
    if (line[0] === "~") {
        stroke = "invisible";
    }
    let dots = countChar(".", line);
    if (dots) {
        stroke = "dotted";
        length = dots;
    }
    return {
        type,
        stroke,
        length
    };
};
const destructLink = (_str, _startStr)=>{
    const info = destructEndLink(_str);
    let startInfo;
    if (_startStr) {
        startInfo = destructStartLink(_startStr);
        if (startInfo.stroke !== info.stroke) {
            return {
                type: "INVALID",
                stroke: "INVALID"
            };
        }
        if (startInfo.type === "arrow_open") {
            startInfo.type = info.type;
        } else {
            if (startInfo.type !== info.type) {
                return {
                    type: "INVALID",
                    stroke: "INVALID"
                };
            }
            startInfo.type = "double_" + startInfo.type;
        }
        if (startInfo.type === "double_arrow") {
            startInfo.type = "double_arrow_point";
        }
        startInfo.length = info.length;
        return startInfo;
    }
    return info;
};
const exists = (allSgs, _id)=>{
    let res = false;
    allSgs.forEach((sg)=>{
        const pos = sg.nodes.indexOf(_id);
        if (pos >= 0) {
            res = true;
        }
    });
    return res;
};
const makeUniq = (sg, allSubgraphs)=>{
    const res = [];
    sg.nodes.forEach((_id, pos)=>{
        if (!exists(allSubgraphs, _id)) {
            res.push(sg.nodes[pos]);
        }
    });
    return {
        nodes: res
    };
};
const lex = {
    firstGraph
};
const flowDb = {
    defaultConfig: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["K"].flowchart,
    setAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["s"],
    getAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["g"],
    getAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["a"],
    setAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["b"],
    addVertex,
    lookUpDomId,
    addLink,
    updateLinkInterpolate,
    updateLink,
    addClass,
    setDirection,
    setClass,
    setTooltip,
    getTooltip,
    setClickEvent,
    setLink,
    bindFunctions,
    getDirection,
    getVertices,
    getEdges,
    getClasses,
    clear,
    setGen,
    defaultStyle,
    addSubGraph,
    getDepthFirstPos,
    indexNodes,
    getSubGraphs,
    destructLink,
    lex,
    exists,
    makeUniq,
    setDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["q"],
    getDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["t"]
};
const db = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
    __proto__: null,
    addClass,
    addLink,
    addSingleLink,
    addSubGraph,
    addVertex,
    bindFunctions,
    clear,
    default: flowDb,
    defaultStyle,
    destructLink,
    firstGraph,
    getClasses,
    getDepthFirstPos,
    getDirection,
    getEdges,
    getSubGraphs,
    getTooltip,
    getVertices,
    indexNodes,
    lex,
    lookUpDomId,
    setClass,
    setClickEvent,
    setDirection,
    setGen,
    setLink,
    updateLink,
    updateLinkInterpolate
}, Symbol.toStringTag, {
    value: "Module"
}));
;
}}),
"[project]/node_modules/mermaid/dist/createText-ca0c5216.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "a": (()=>createText),
    "c": (()=>computeDimensionOfText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mdast$2d$util$2d$from$2d$markdown$2f$dev$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mdast-util-from-markdown/dev/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ts$2d$dedent$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ts-dedent/esm/index.js [app-client] (ecmascript)");
;
;
;
function preprocessMarkdown(markdown) {
    const withoutMultipleNewlines = markdown.replace(/\n{2,}/g, "\n");
    const withoutExtraSpaces = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ts$2d$dedent$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dedent"])(withoutMultipleNewlines);
    return withoutExtraSpaces;
}
function markdownToLines(markdown) {
    const preprocessedMarkdown = preprocessMarkdown(markdown);
    const { children } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mdast$2d$util$2d$from$2d$markdown$2f$dev$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromMarkdown"])(preprocessedMarkdown);
    const lines = [
        []
    ];
    let currentLine = 0;
    function processNode(node, parentType = "normal") {
        if (node.type === "text") {
            const textLines = node.value.split("\n");
            textLines.forEach((textLine, index)=>{
                if (index !== 0) {
                    currentLine++;
                    lines.push([]);
                }
                textLine.split(" ").forEach((word)=>{
                    if (word) {
                        lines[currentLine].push({
                            content: word,
                            type: parentType
                        });
                    }
                });
            });
        } else if (node.type === "strong" || node.type === "emphasis") {
            node.children.forEach((contentNode)=>{
                processNode(contentNode, node.type);
            });
        }
    }
    children.forEach((treeNode)=>{
        if (treeNode.type === "paragraph") {
            treeNode.children.forEach((contentNode)=>{
                processNode(contentNode);
            });
        }
    });
    return lines;
}
function markdownToHTML(markdown) {
    const { children } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mdast$2d$util$2d$from$2d$markdown$2f$dev$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromMarkdown"])(markdown);
    function output(node) {
        if (node.type === "text") {
            return node.value.replace(/\n/g, "<br/>");
        } else if (node.type === "strong") {
            return `<strong>${node.children.map(output).join("")}</strong>`;
        } else if (node.type === "emphasis") {
            return `<em>${node.children.map(output).join("")}</em>`;
        } else if (node.type === "paragraph") {
            return `<p>${node.children.map(output).join("")}</p>`;
        }
        return `Unsupported markdown: ${node.type}`;
    }
    return children.map(output).join("");
}
function splitTextToChars(text) {
    if (Intl.Segmenter) {
        return [
            ...new Intl.Segmenter().segment(text)
        ].map((s)=>s.segment);
    }
    return [
        ...text
    ];
}
function splitWordToFitWidth(checkFit, word) {
    const characters = splitTextToChars(word.content);
    return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);
}
function splitWordToFitWidthRecursion(checkFit, usedChars, remainingChars, type) {
    if (remainingChars.length === 0) {
        return [
            {
                content: usedChars.join(""),
                type
            },
            {
                content: "",
                type
            }
        ];
    }
    const [nextChar, ...rest] = remainingChars;
    const newWord = [
        ...usedChars,
        nextChar
    ];
    if (checkFit([
        {
            content: newWord.join(""),
            type
        }
    ])) {
        return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);
    }
    if (usedChars.length === 0 && nextChar) {
        usedChars.push(nextChar);
        remainingChars.shift();
    }
    return [
        {
            content: usedChars.join(""),
            type
        },
        {
            content: remainingChars.join(""),
            type
        }
    ];
}
function splitLineToFitWidth(line, checkFit) {
    if (line.some(({ content })=>content.includes("\n"))) {
        throw new Error("splitLineToFitWidth does not support newlines in the line");
    }
    return splitLineToFitWidthRecursion(line, checkFit);
}
function splitLineToFitWidthRecursion(words, checkFit, lines = [], newLine = []) {
    if (words.length === 0) {
        if (newLine.length > 0) {
            lines.push(newLine);
        }
        return lines.length > 0 ? lines : [];
    }
    let joiner = "";
    if (words[0].content === " ") {
        joiner = " ";
        words.shift();
    }
    const nextWord = words.shift() ?? {
        content: " ",
        type: "normal"
    };
    const lineWithNextWord = [
        ...newLine
    ];
    if (joiner !== "") {
        lineWithNextWord.push({
            content: joiner,
            type: "normal"
        });
    }
    lineWithNextWord.push(nextWord);
    if (checkFit(lineWithNextWord)) {
        return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);
    }
    if (newLine.length > 0) {
        lines.push(newLine);
        words.unshift(nextWord);
    } else if (nextWord.content) {
        const [line, rest] = splitWordToFitWidth(checkFit, nextWord);
        lines.push([
            line
        ]);
        if (rest.content) {
            words.unshift(rest);
        }
    }
    return splitLineToFitWidthRecursion(words, checkFit, lines);
}
function applyStyle(dom, styleFn) {
    if (styleFn) {
        dom.attr("style", styleFn);
    }
}
function addHtmlSpan(element, node, width, classes, addBackground = false) {
    const fo = element.append("foreignObject");
    const div = fo.append("xhtml:div");
    const label = node.label;
    const labelClass = node.isNode ? "nodeLabel" : "edgeLabel";
    div.html(`
    <span class="${labelClass} ${classes}" ` + (node.labelStyle ? 'style="' + node.labelStyle + '"' : "") + ">" + label + "</span>");
    applyStyle(div, node.labelStyle);
    div.style("display", "table-cell");
    div.style("white-space", "nowrap");
    div.style("max-width", width + "px");
    div.attr("xmlns", "http://www.w3.org/1999/xhtml");
    if (addBackground) {
        div.attr("class", "labelBkg");
    }
    let bbox = div.node().getBoundingClientRect();
    if (bbox.width === width) {
        div.style("display", "table");
        div.style("white-space", "break-spaces");
        div.style("width", width + "px");
        bbox = div.node().getBoundingClientRect();
    }
    fo.style("width", bbox.width);
    fo.style("height", bbox.height);
    return fo.node();
}
function createTspan(textElement, lineIndex, lineHeight) {
    return textElement.append("tspan").attr("class", "text-outer-tspan").attr("x", 0).attr("y", lineIndex * lineHeight - 0.1 + "em").attr("dy", lineHeight + "em");
}
function computeWidthOfText(parentNode, lineHeight, line) {
    const testElement = parentNode.append("text");
    const testSpan = createTspan(testElement, 1, lineHeight);
    updateTextContentAndStyles(testSpan, line);
    const textLength = testSpan.node().getComputedTextLength();
    testElement.remove();
    return textLength;
}
function computeDimensionOfText(parentNode, lineHeight, text) {
    var _a;
    const testElement = parentNode.append("text");
    const testSpan = createTspan(testElement, 1, lineHeight);
    updateTextContentAndStyles(testSpan, [
        {
            content: text,
            type: "normal"
        }
    ]);
    const textDimension = (_a = testSpan.node()) == null ? void 0 : _a.getBoundingClientRect();
    if (textDimension) {
        testElement.remove();
    }
    return textDimension;
}
function createFormattedText(width, g, structuredText, addBackground = false) {
    const lineHeight = 1.1;
    const labelGroup = g.append("g");
    const bkg = labelGroup.insert("rect").attr("class", "background");
    const textElement = labelGroup.append("text").attr("y", "-10.1");
    let lineIndex = 0;
    for (const line of structuredText){
        const checkWidth = (line2)=>computeWidthOfText(labelGroup, lineHeight, line2) <= width;
        const linesUnderWidth = checkWidth(line) ? [
            line
        ] : splitLineToFitWidth(line, checkWidth);
        for (const preparedLine of linesUnderWidth){
            const tspan = createTspan(textElement, lineIndex, lineHeight);
            updateTextContentAndStyles(tspan, preparedLine);
            lineIndex++;
        }
    }
    if (addBackground) {
        const bbox = textElement.node().getBBox();
        const padding = 2;
        bkg.attr("x", -padding).attr("y", -padding).attr("width", bbox.width + 2 * padding).attr("height", bbox.height + 2 * padding);
        return labelGroup.node();
    } else {
        return textElement.node();
    }
}
function updateTextContentAndStyles(tspan, wrappedLine) {
    tspan.text("");
    wrappedLine.forEach((word, index)=>{
        const innerTspan = tspan.append("tspan").attr("font-style", word.type === "emphasis" ? "italic" : "normal").attr("class", "text-inner-tspan").attr("font-weight", word.type === "strong" ? "bold" : "normal");
        if (index === 0) {
            innerTspan.text(word.content);
        } else {
            innerTspan.text(" " + word.content);
        }
    });
}
const createText = (el, text = "", { style = "", isTitle = false, classes = "", useHtmlLabels = true, isNode = true, width = 200, addSvgBackground = false } = {})=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("createText", text, style, isTitle, classes, useHtmlLabels, isNode, addSvgBackground);
    if (useHtmlLabels) {
        const htmlText = markdownToHTML(text);
        const node = {
            isNode,
            label: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["M"])(htmlText).replace(/fa[blrs]?:fa-[\w-]+/g, // cspell: disable-line
            (s)=>`<i class='${s.replace(":", " ")}'></i>`),
            labelStyle: style.replace("fill:", "color:")
        };
        const vertexNode = addHtmlSpan(el, node, width, classes, addSvgBackground);
        return vertexNode;
    } else {
        const structuredText = markdownToLines(text);
        const svgLabel = createFormattedText(width, el, structuredText, addSvgBackground);
        return svgLabel;
    }
};
;
}}),
"[project]/node_modules/mermaid/dist/edges-066a5561.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "a": (()=>insertMarkers$1),
    "b": (()=>clear$1),
    "c": (()=>createLabel$1),
    "d": (()=>clear),
    "e": (()=>insertNode),
    "f": (()=>insertEdgeLabel),
    "g": (()=>getSubGraphTitleMargins),
    "h": (()=>insertEdge),
    "i": (()=>intersectRect$1),
    "j": (()=>positionEdgeLabel),
    "k": (()=>getLineFunctionsWithOffset),
    "l": (()=>labelHelper),
    "m": (()=>addEdgeMarkers),
    "p": (()=>positionNode),
    "s": (()=>setNodeElem),
    "u": (()=>updateNodeBounds)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-client] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__line$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/line.js [app-client] (ecmascript) <export default as line>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$basis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveBasis$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/curve/basis.js [app-client] (ecmascript) <export default as curveBasis>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$createText$2d$ca0c5216$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/createText-ca0c5216.js [app-client] (ecmascript)");
;
;
;
const insertMarkers = (elem, markerArray, type, id)=>{
    markerArray.forEach((markerName)=>{
        markers[markerName](elem, type, id);
    });
};
const extension = (elem, type, id)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Making markers for ", id);
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-extensionStart").attr("class", "marker extension " + type).attr("refX", 18).attr("refY", 7).attr("markerWidth", 190).attr("markerHeight", 240).attr("orient", "auto").append("path").attr("d", "M 1,7 L18,13 V 1 Z");
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-extensionEnd").attr("class", "marker extension " + type).attr("refX", 1).attr("refY", 7).attr("markerWidth", 20).attr("markerHeight", 28).attr("orient", "auto").append("path").attr("d", "M 1,1 V 13 L18,7 Z");
};
const composition = (elem, type, id)=>{
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-compositionStart").attr("class", "marker composition " + type).attr("refX", 18).attr("refY", 7).attr("markerWidth", 190).attr("markerHeight", 240).attr("orient", "auto").append("path").attr("d", "M 18,7 L9,13 L1,7 L9,1 Z");
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-compositionEnd").attr("class", "marker composition " + type).attr("refX", 1).attr("refY", 7).attr("markerWidth", 20).attr("markerHeight", 28).attr("orient", "auto").append("path").attr("d", "M 18,7 L9,13 L1,7 L9,1 Z");
};
const aggregation = (elem, type, id)=>{
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-aggregationStart").attr("class", "marker aggregation " + type).attr("refX", 18).attr("refY", 7).attr("markerWidth", 190).attr("markerHeight", 240).attr("orient", "auto").append("path").attr("d", "M 18,7 L9,13 L1,7 L9,1 Z");
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-aggregationEnd").attr("class", "marker aggregation " + type).attr("refX", 1).attr("refY", 7).attr("markerWidth", 20).attr("markerHeight", 28).attr("orient", "auto").append("path").attr("d", "M 18,7 L9,13 L1,7 L9,1 Z");
};
const dependency = (elem, type, id)=>{
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-dependencyStart").attr("class", "marker dependency " + type).attr("refX", 6).attr("refY", 7).attr("markerWidth", 190).attr("markerHeight", 240).attr("orient", "auto").append("path").attr("d", "M 5,7 L9,13 L1,7 L9,1 Z");
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-dependencyEnd").attr("class", "marker dependency " + type).attr("refX", 13).attr("refY", 7).attr("markerWidth", 20).attr("markerHeight", 28).attr("orient", "auto").append("path").attr("d", "M 18,7 L9,13 L14,7 L9,1 Z");
};
const lollipop = (elem, type, id)=>{
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-lollipopStart").attr("class", "marker lollipop " + type).attr("refX", 13).attr("refY", 7).attr("markerWidth", 190).attr("markerHeight", 240).attr("orient", "auto").append("circle").attr("stroke", "black").attr("fill", "transparent").attr("cx", 7).attr("cy", 7).attr("r", 6);
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-lollipopEnd").attr("class", "marker lollipop " + type).attr("refX", 1).attr("refY", 7).attr("markerWidth", 190).attr("markerHeight", 240).attr("orient", "auto").append("circle").attr("stroke", "black").attr("fill", "transparent").attr("cx", 7).attr("cy", 7).attr("r", 6);
};
const point = (elem, type, id)=>{
    elem.append("marker").attr("id", id + "_" + type + "-pointEnd").attr("class", "marker " + type).attr("viewBox", "0 0 10 10").attr("refX", 6).attr("refY", 5).attr("markerUnits", "userSpaceOnUse").attr("markerWidth", 12).attr("markerHeight", 12).attr("orient", "auto").append("path").attr("d", "M 0 0 L 10 5 L 0 10 z").attr("class", "arrowMarkerPath").style("stroke-width", 1).style("stroke-dasharray", "1,0");
    elem.append("marker").attr("id", id + "_" + type + "-pointStart").attr("class", "marker " + type).attr("viewBox", "0 0 10 10").attr("refX", 4.5).attr("refY", 5).attr("markerUnits", "userSpaceOnUse").attr("markerWidth", 12).attr("markerHeight", 12).attr("orient", "auto").append("path").attr("d", "M 0 5 L 10 10 L 10 0 z").attr("class", "arrowMarkerPath").style("stroke-width", 1).style("stroke-dasharray", "1,0");
};
const circle$1 = (elem, type, id)=>{
    elem.append("marker").attr("id", id + "_" + type + "-circleEnd").attr("class", "marker " + type).attr("viewBox", "0 0 10 10").attr("refX", 11).attr("refY", 5).attr("markerUnits", "userSpaceOnUse").attr("markerWidth", 11).attr("markerHeight", 11).attr("orient", "auto").append("circle").attr("cx", "5").attr("cy", "5").attr("r", "5").attr("class", "arrowMarkerPath").style("stroke-width", 1).style("stroke-dasharray", "1,0");
    elem.append("marker").attr("id", id + "_" + type + "-circleStart").attr("class", "marker " + type).attr("viewBox", "0 0 10 10").attr("refX", -1).attr("refY", 5).attr("markerUnits", "userSpaceOnUse").attr("markerWidth", 11).attr("markerHeight", 11).attr("orient", "auto").append("circle").attr("cx", "5").attr("cy", "5").attr("r", "5").attr("class", "arrowMarkerPath").style("stroke-width", 1).style("stroke-dasharray", "1,0");
};
const cross = (elem, type, id)=>{
    elem.append("marker").attr("id", id + "_" + type + "-crossEnd").attr("class", "marker cross " + type).attr("viewBox", "0 0 11 11").attr("refX", 12).attr("refY", 5.2).attr("markerUnits", "userSpaceOnUse").attr("markerWidth", 11).attr("markerHeight", 11).attr("orient", "auto").append("path").attr("d", "M 1,1 l 9,9 M 10,1 l -9,9").attr("class", "arrowMarkerPath").style("stroke-width", 2).style("stroke-dasharray", "1,0");
    elem.append("marker").attr("id", id + "_" + type + "-crossStart").attr("class", "marker cross " + type).attr("viewBox", "0 0 11 11").attr("refX", -1).attr("refY", 5.2).attr("markerUnits", "userSpaceOnUse").attr("markerWidth", 11).attr("markerHeight", 11).attr("orient", "auto").append("path").attr("d", "M 1,1 l 9,9 M 10,1 l -9,9").attr("class", "arrowMarkerPath").style("stroke-width", 2).style("stroke-dasharray", "1,0");
};
const barb = (elem, type, id)=>{
    elem.append("defs").append("marker").attr("id", id + "_" + type + "-barbEnd").attr("refX", 19).attr("refY", 7).attr("markerWidth", 20).attr("markerHeight", 14).attr("markerUnits", "strokeWidth").attr("orient", "auto").append("path").attr("d", "M 19,7 L9,13 L14,7 L9,1 Z");
};
const markers = {
    extension,
    composition,
    aggregation,
    dependency,
    lollipop,
    point,
    circle: circle$1,
    cross,
    barb
};
const insertMarkers$1 = insertMarkers;
function applyStyle(dom, styleFn) {
    if (styleFn) {
        dom.attr("style", styleFn);
    }
}
function addHtmlLabel(node) {
    const fo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(document.createElementNS("http://www.w3.org/2000/svg", "foreignObject"));
    const div = fo.append("xhtml:div");
    const label = node.label;
    const labelClass = node.isNode ? "nodeLabel" : "edgeLabel";
    div.html('<span class="' + labelClass + '" ' + (node.labelStyle ? 'style="' + node.labelStyle + '"' : "") + ">" + label + "</span>");
    applyStyle(div, node.labelStyle);
    div.style("display", "inline-block");
    div.style("white-space", "nowrap");
    div.attr("xmlns", "http://www.w3.org/1999/xhtml");
    return fo.node();
}
const createLabel = (_vertexText, style, isTitle, isNode)=>{
    let vertexText = _vertexText || "";
    if (typeof vertexText === "object") {
        vertexText = vertexText[0];
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
        vertexText = vertexText.replace(/\\n|\n/g, "<br />");
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("vertexText" + vertexText);
        const node = {
            isNode,
            label: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["M"])(vertexText).replace(/fa[blrs]?:fa-[\w-]+/g, // cspell: disable-line
            (s)=>`<i class='${s.replace(":", " ")}'></i>`),
            labelStyle: style.replace("fill:", "color:")
        };
        let vertexNode = addHtmlLabel(node);
        return vertexNode;
    } else {
        const svgLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
        svgLabel.setAttribute("style", style.replace("color:", "fill:"));
        let rows = [];
        if (typeof vertexText === "string") {
            rows = vertexText.split(/\\n|\n|<br\s*\/?>/gi);
        } else if (Array.isArray(vertexText)) {
            rows = vertexText;
        } else {
            rows = [];
        }
        for (const row of rows){
            const tspan = document.createElementNS("http://www.w3.org/2000/svg", "tspan");
            tspan.setAttributeNS("http://www.w3.org/XML/1998/namespace", "xml:space", "preserve");
            tspan.setAttribute("dy", "1em");
            tspan.setAttribute("x", "0");
            if (isTitle) {
                tspan.setAttribute("class", "title-row");
            } else {
                tspan.setAttribute("class", "row");
            }
            tspan.textContent = row.trim();
            svgLabel.appendChild(tspan);
        }
        return svgLabel;
    }
};
const createLabel$1 = createLabel;
const labelHelper = async (parent, node, _classes, isNode)=>{
    let classes;
    const useHtmlLabels = node.useHtmlLabels || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels);
    if (!_classes) {
        classes = "node default";
    } else {
        classes = _classes;
    }
    const shapeSvg = parent.insert("g").attr("class", classes).attr("id", node.domId || node.id);
    const label = shapeSvg.insert("g").attr("class", "label").attr("style", node.labelStyle);
    let labelText;
    if (node.labelText === void 0) {
        labelText = "";
    } else {
        labelText = typeof node.labelText === "string" ? node.labelText : node.labelText[0];
    }
    const textNode = label.node();
    let text;
    if (node.labelType === "markdown") {
        text = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$createText$2d$ca0c5216$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["a"])(label, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["d"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["M"])(labelText), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])()), {
            useHtmlLabels,
            width: node.width || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.wrappingWidth,
            classes: "markdown-node-label"
        });
    } else {
        text = textNode.appendChild(createLabel$1((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["d"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["M"])(labelText), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])()), node.labelStyle, false, isNode));
    }
    let bbox = text.getBBox();
    const halfPadding = node.padding / 2;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
        const div = text.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(text);
        const images = div.getElementsByTagName("img");
        if (images) {
            const noImgText = labelText.replace(/<img[^>]*>/g, "").trim() === "";
            await Promise.all([
                ...images
            ].map((img)=>new Promise((res)=>{
                    function setupImage() {
                        img.style.display = "flex";
                        img.style.flexDirection = "column";
                        if (noImgText) {
                            const bodyFontSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().fontSize ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().fontSize : window.getComputedStyle(document.body).fontSize;
                            const enlargingFactor = 5;
                            const width = parseInt(bodyFontSize, 10) * enlargingFactor + "px";
                            img.style.minWidth = width;
                            img.style.maxWidth = width;
                        } else {
                            img.style.width = "100%";
                        }
                        res(img);
                    }
                    setTimeout(()=>{
                        if (img.complete) {
                            setupImage();
                        }
                    });
                    img.addEventListener("error", setupImage);
                    img.addEventListener("load", setupImage);
                })));
        }
        bbox = div.getBoundingClientRect();
        dv.attr("width", bbox.width);
        dv.attr("height", bbox.height);
    }
    if (useHtmlLabels) {
        label.attr("transform", "translate(" + -bbox.width / 2 + ", " + -bbox.height / 2 + ")");
    } else {
        label.attr("transform", "translate(0, " + -bbox.height / 2 + ")");
    }
    if (node.centerLabel) {
        label.attr("transform", "translate(" + -bbox.width / 2 + ", " + -bbox.height / 2 + ")");
    }
    label.insert("rect", ":first-child");
    return {
        shapeSvg,
        bbox,
        halfPadding,
        label
    };
};
const updateNodeBounds = (node, element)=>{
    const bbox = element.node().getBBox();
    node.width = bbox.width;
    node.height = bbox.height;
};
function insertPolygonShape(parent, w, h, points) {
    return parent.insert("polygon", ":first-child").attr("points", points.map(function(d) {
        return d.x + "," + d.y;
    }).join(" ")).attr("class", "label-container").attr("transform", "translate(" + -w / 2 + "," + h / 2 + ")");
}
function intersectNode(node, point2) {
    return node.intersect(point2);
}
function intersectEllipse(node, rx, ry, point2) {
    var cx = node.x;
    var cy = node.y;
    var px = cx - point2.x;
    var py = cy - point2.y;
    var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);
    var dx = Math.abs(rx * ry * px / det);
    if (point2.x < cx) {
        dx = -dx;
    }
    var dy = Math.abs(rx * ry * py / det);
    if (point2.y < cy) {
        dy = -dy;
    }
    return {
        x: cx + dx,
        y: cy + dy
    };
}
function intersectCircle(node, rx, point2) {
    return intersectEllipse(node, rx, rx, point2);
}
function intersectLine(p1, p2, q1, q2) {
    var a1, a2, b1, b2, c1, c2;
    var r1, r2, r3, r4;
    var denom, offset, num;
    var x, y;
    a1 = p2.y - p1.y;
    b1 = p1.x - p2.x;
    c1 = p2.x * p1.y - p1.x * p2.y;
    r3 = a1 * q1.x + b1 * q1.y + c1;
    r4 = a1 * q2.x + b1 * q2.y + c1;
    if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {
        return;
    }
    a2 = q2.y - q1.y;
    b2 = q1.x - q2.x;
    c2 = q2.x * q1.y - q1.x * q2.y;
    r1 = a2 * p1.x + b2 * p1.y + c2;
    r2 = a2 * p2.x + b2 * p2.y + c2;
    if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {
        return;
    }
    denom = a1 * b2 - a2 * b1;
    if (denom === 0) {
        return;
    }
    offset = Math.abs(denom / 2);
    num = b1 * c2 - b2 * c1;
    x = num < 0 ? (num - offset) / denom : (num + offset) / denom;
    num = a2 * c1 - a1 * c2;
    y = num < 0 ? (num - offset) / denom : (num + offset) / denom;
    return {
        x,
        y
    };
}
function sameSign(r1, r2) {
    return r1 * r2 > 0;
}
function intersectPolygon(node, polyPoints, point2) {
    var x1 = node.x;
    var y1 = node.y;
    var intersections = [];
    var minX = Number.POSITIVE_INFINITY;
    var minY = Number.POSITIVE_INFINITY;
    if (typeof polyPoints.forEach === "function") {
        polyPoints.forEach(function(entry) {
            minX = Math.min(minX, entry.x);
            minY = Math.min(minY, entry.y);
        });
    } else {
        minX = Math.min(minX, polyPoints.x);
        minY = Math.min(minY, polyPoints.y);
    }
    var left = x1 - node.width / 2 - minX;
    var top = y1 - node.height / 2 - minY;
    for(var i = 0; i < polyPoints.length; i++){
        var p1 = polyPoints[i];
        var p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];
        var intersect2 = intersectLine(node, point2, {
            x: left + p1.x,
            y: top + p1.y
        }, {
            x: left + p2.x,
            y: top + p2.y
        });
        if (intersect2) {
            intersections.push(intersect2);
        }
    }
    if (!intersections.length) {
        return node;
    }
    if (intersections.length > 1) {
        intersections.sort(function(p, q) {
            var pdx = p.x - point2.x;
            var pdy = p.y - point2.y;
            var distp = Math.sqrt(pdx * pdx + pdy * pdy);
            var qdx = q.x - point2.x;
            var qdy = q.y - point2.y;
            var distq = Math.sqrt(qdx * qdx + qdy * qdy);
            return distp < distq ? -1 : distp === distq ? 0 : 1;
        });
    }
    return intersections[0];
}
const intersectRect = (node, point2)=>{
    var x = node.x;
    var y = node.y;
    var dx = point2.x - x;
    var dy = point2.y - y;
    var w = node.width / 2;
    var h = node.height / 2;
    var sx, sy;
    if (Math.abs(dy) * w > Math.abs(dx) * h) {
        if (dy < 0) {
            h = -h;
        }
        sx = dy === 0 ? 0 : h * dx / dy;
        sy = h;
    } else {
        if (dx < 0) {
            w = -w;
        }
        sx = w;
        sy = dx === 0 ? 0 : w * dy / dx;
    }
    return {
        x: x + sx,
        y: y + sy
    };
};
const intersectRect$1 = intersectRect;
const intersect = {
    node: intersectNode,
    circle: intersectCircle,
    ellipse: intersectEllipse,
    polygon: intersectPolygon,
    rect: intersectRect$1
};
const note = async (parent, node)=>{
    const useHtmlLabels = node.useHtmlLabels || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels;
    if (!useHtmlLabels) {
        node.centerLabel = true;
    }
    const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, "node " + node.classes, true);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Classes = ", node.classes);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    rect2.attr("rx", node.rx).attr("ry", node.ry).attr("x", -bbox.width / 2 - halfPadding).attr("y", -bbox.height / 2 - halfPadding).attr("width", bbox.width + node.padding).attr("height", bbox.height + node.padding);
    updateNodeBounds(node, rect2);
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
const note$1 = note;
const expandAndDeduplicateDirections = (directions)=>{
    const uniqueDirections = /* @__PURE__ */ new Set();
    for (const direction of directions){
        switch(direction){
            case "x":
                uniqueDirections.add("right");
                uniqueDirections.add("left");
                break;
            case "y":
                uniqueDirections.add("up");
                uniqueDirections.add("down");
                break;
            default:
                uniqueDirections.add(direction);
                break;
        }
    }
    return uniqueDirections;
};
const getArrowPoints = (duplicatedDirections, bbox, node)=>{
    const directions = expandAndDeduplicateDirections(duplicatedDirections);
    const f = 2;
    const height = bbox.height + 2 * node.padding;
    const midpoint = height / f;
    const width = bbox.width + 2 * midpoint + node.padding;
    const padding = node.padding / 2;
    if (directions.has("right") && directions.has("left") && directions.has("up") && directions.has("down")) {
        return [
            // Bottom
            {
                x: 0,
                y: 0
            },
            {
                x: midpoint,
                y: 0
            },
            {
                x: width / 2,
                y: 2 * padding
            },
            {
                x: width - midpoint,
                y: 0
            },
            {
                x: width,
                y: 0
            },
            // Right
            {
                x: width,
                y: -height / 3
            },
            {
                x: width + 2 * padding,
                y: -height / 2
            },
            {
                x: width,
                y: -2 * height / 3
            },
            {
                x: width,
                y: -height
            },
            // Top
            {
                x: width - midpoint,
                y: -height
            },
            {
                x: width / 2,
                y: -height - 2 * padding
            },
            {
                x: midpoint,
                y: -height
            },
            // Left
            {
                x: 0,
                y: -height
            },
            {
                x: 0,
                y: -2 * height / 3
            },
            {
                x: -2 * padding,
                y: -height / 2
            },
            {
                x: 0,
                y: -height / 3
            }
        ];
    }
    if (directions.has("right") && directions.has("left") && directions.has("up")) {
        return [
            {
                x: midpoint,
                y: 0
            },
            {
                x: width - midpoint,
                y: 0
            },
            {
                x: width,
                y: -height / 2
            },
            {
                x: width - midpoint,
                y: -height
            },
            {
                x: midpoint,
                y: -height
            },
            {
                x: 0,
                y: -height / 2
            }
        ];
    }
    if (directions.has("right") && directions.has("left") && directions.has("down")) {
        return [
            {
                x: 0,
                y: 0
            },
            {
                x: midpoint,
                y: -height
            },
            {
                x: width - midpoint,
                y: -height
            },
            {
                x: width,
                y: 0
            }
        ];
    }
    if (directions.has("right") && directions.has("up") && directions.has("down")) {
        return [
            {
                x: 0,
                y: 0
            },
            {
                x: width,
                y: -midpoint
            },
            {
                x: width,
                y: -height + midpoint
            },
            {
                x: 0,
                y: -height
            }
        ];
    }
    if (directions.has("left") && directions.has("up") && directions.has("down")) {
        return [
            {
                x: width,
                y: 0
            },
            {
                x: 0,
                y: -midpoint
            },
            {
                x: 0,
                y: -height + midpoint
            },
            {
                x: width,
                y: -height
            }
        ];
    }
    if (directions.has("right") && directions.has("left")) {
        return [
            {
                x: midpoint,
                y: 0
            },
            {
                x: midpoint,
                y: -padding
            },
            {
                x: width - midpoint,
                y: -padding
            },
            {
                x: width - midpoint,
                y: 0
            },
            {
                x: width,
                y: -height / 2
            },
            {
                x: width - midpoint,
                y: -height
            },
            {
                x: width - midpoint,
                y: -height + padding
            },
            {
                x: midpoint,
                y: -height + padding
            },
            {
                x: midpoint,
                y: -height
            },
            {
                x: 0,
                y: -height / 2
            }
        ];
    }
    if (directions.has("up") && directions.has("down")) {
        return [
            // Bottom center
            {
                x: width / 2,
                y: 0
            },
            // Left pont of bottom arrow
            {
                x: 0,
                y: -padding
            },
            {
                x: midpoint,
                y: -padding
            },
            // Left top over vertical section
            {
                x: midpoint,
                y: -height + padding
            },
            {
                x: 0,
                y: -height + padding
            },
            // Top of arrow
            {
                x: width / 2,
                y: -height
            },
            {
                x: width,
                y: -height + padding
            },
            // Top of right vertical bar
            {
                x: width - midpoint,
                y: -height + padding
            },
            {
                x: width - midpoint,
                y: -padding
            },
            {
                x: width,
                y: -padding
            }
        ];
    }
    if (directions.has("right") && directions.has("up")) {
        return [
            {
                x: 0,
                y: 0
            },
            {
                x: width,
                y: -midpoint
            },
            {
                x: 0,
                y: -height
            }
        ];
    }
    if (directions.has("right") && directions.has("down")) {
        return [
            {
                x: 0,
                y: 0
            },
            {
                x: width,
                y: 0
            },
            {
                x: 0,
                y: -height
            }
        ];
    }
    if (directions.has("left") && directions.has("up")) {
        return [
            {
                x: width,
                y: 0
            },
            {
                x: 0,
                y: -midpoint
            },
            {
                x: width,
                y: -height
            }
        ];
    }
    if (directions.has("left") && directions.has("down")) {
        return [
            {
                x: width,
                y: 0
            },
            {
                x: 0,
                y: 0
            },
            {
                x: width,
                y: -height
            }
        ];
    }
    if (directions.has("right")) {
        return [
            {
                x: midpoint,
                y: -padding
            },
            {
                x: midpoint,
                y: -padding
            },
            {
                x: width - midpoint,
                y: -padding
            },
            {
                x: width - midpoint,
                y: 0
            },
            {
                x: width,
                y: -height / 2
            },
            {
                x: width - midpoint,
                y: -height
            },
            {
                x: width - midpoint,
                y: -height + padding
            },
            // top left corner of arrow
            {
                x: midpoint,
                y: -height + padding
            },
            {
                x: midpoint,
                y: -height + padding
            }
        ];
    }
    if (directions.has("left")) {
        return [
            {
                x: midpoint,
                y: 0
            },
            {
                x: midpoint,
                y: -padding
            },
            // Two points, the right corners
            {
                x: width - midpoint,
                y: -padding
            },
            {
                x: width - midpoint,
                y: -height + padding
            },
            {
                x: midpoint,
                y: -height + padding
            },
            {
                x: midpoint,
                y: -height
            },
            {
                x: 0,
                y: -height / 2
            }
        ];
    }
    if (directions.has("up")) {
        return [
            // Bottom center
            {
                x: midpoint,
                y: -padding
            },
            // Left top over vertical section
            {
                x: midpoint,
                y: -height + padding
            },
            {
                x: 0,
                y: -height + padding
            },
            // Top of arrow
            {
                x: width / 2,
                y: -height
            },
            {
                x: width,
                y: -height + padding
            },
            // Top of right vertical bar
            {
                x: width - midpoint,
                y: -height + padding
            },
            {
                x: width - midpoint,
                y: -padding
            }
        ];
    }
    if (directions.has("down")) {
        return [
            // Bottom center
            {
                x: width / 2,
                y: 0
            },
            // Left pont of bottom arrow
            {
                x: 0,
                y: -padding
            },
            {
                x: midpoint,
                y: -padding
            },
            // Left top over vertical section
            {
                x: midpoint,
                y: -height + padding
            },
            {
                x: width - midpoint,
                y: -height + padding
            },
            {
                x: width - midpoint,
                y: -padding
            },
            {
                x: width,
                y: -padding
            }
        ];
    }
    return [
        {
            x: 0,
            y: 0
        }
    ];
};
const formatClass = (str)=>{
    if (str) {
        return " " + str;
    }
    return "";
};
const getClassesFromNode = (node, otherClasses)=>{
    return `${otherClasses ? otherClasses : "node default"}${formatClass(node.classes)} ${formatClass(node.class)}`;
};
const question = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const s = w + h;
    const points = [
        {
            x: s / 2,
            y: 0
        },
        {
            x: s,
            y: -s / 2
        },
        {
            x: s / 2,
            y: -s
        },
        {
            x: 0,
            y: -s / 2
        }
    ];
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Question main (Circle)");
    const questionElem = insertPolygonShape(shapeSvg, s, s, points);
    questionElem.attr("style", node.style);
    updateNodeBounds(node, questionElem);
    node.intersect = function(point2) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Intersect called");
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const choice = (parent, node)=>{
    const shapeSvg = parent.insert("g").attr("class", "node default").attr("id", node.domId || node.id);
    const s = 28;
    const points = [
        {
            x: 0,
            y: s / 2
        },
        {
            x: s / 2,
            y: 0
        },
        {
            x: 0,
            y: -s / 2
        },
        {
            x: -s / 2,
            y: 0
        }
    ];
    const choice2 = shapeSvg.insert("polygon", ":first-child").attr("points", points.map(function(d) {
        return d.x + "," + d.y;
    }).join(" "));
    choice2.attr("class", "state-start").attr("r", 7).attr("width", 28).attr("height", 28);
    node.width = 28;
    node.height = 28;
    node.intersect = function(point2) {
        return intersect.circle(node, 14, point2);
    };
    return shapeSvg;
};
const hexagon = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const f = 4;
    const h = bbox.height + node.padding;
    const m = h / f;
    const w = bbox.width + 2 * m + node.padding;
    const points = [
        {
            x: m,
            y: 0
        },
        {
            x: w - m,
            y: 0
        },
        {
            x: w,
            y: -h / 2
        },
        {
            x: w - m,
            y: -h
        },
        {
            x: m,
            y: -h
        },
        {
            x: 0,
            y: -h / 2
        }
    ];
    const hex = insertPolygonShape(shapeSvg, w, h, points);
    hex.attr("style", node.style);
    updateNodeBounds(node, hex);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const block_arrow = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, void 0, true);
    const f = 2;
    const h = bbox.height + 2 * node.padding;
    const m = h / f;
    const w = bbox.width + 2 * m + node.padding;
    const points = getArrowPoints(node.directions, bbox, node);
    const blockArrow = insertPolygonShape(shapeSvg, w, h, points);
    blockArrow.attr("style", node.style);
    updateNodeBounds(node, blockArrow);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const rect_left_inv_arrow = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const points = [
        {
            x: -h / 2,
            y: 0
        },
        {
            x: w,
            y: 0
        },
        {
            x: w,
            y: -h
        },
        {
            x: -h / 2,
            y: -h
        },
        {
            x: 0,
            y: -h / 2
        }
    ];
    const el = insertPolygonShape(shapeSvg, w, h, points);
    el.attr("style", node.style);
    node.width = w + h;
    node.height = h;
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const lean_right = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const points = [
        {
            x: -2 * h / 6,
            y: 0
        },
        {
            x: w - h / 6,
            y: 0
        },
        {
            x: w + 2 * h / 6,
            y: -h
        },
        {
            x: h / 6,
            y: -h
        }
    ];
    const el = insertPolygonShape(shapeSvg, w, h, points);
    el.attr("style", node.style);
    updateNodeBounds(node, el);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const lean_left = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const points = [
        {
            x: 2 * h / 6,
            y: 0
        },
        {
            x: w + h / 6,
            y: 0
        },
        {
            x: w - 2 * h / 6,
            y: -h
        },
        {
            x: -h / 6,
            y: -h
        }
    ];
    const el = insertPolygonShape(shapeSvg, w, h, points);
    el.attr("style", node.style);
    updateNodeBounds(node, el);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const trapezoid = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const points = [
        {
            x: -2 * h / 6,
            y: 0
        },
        {
            x: w + 2 * h / 6,
            y: 0
        },
        {
            x: w - h / 6,
            y: -h
        },
        {
            x: h / 6,
            y: -h
        }
    ];
    const el = insertPolygonShape(shapeSvg, w, h, points);
    el.attr("style", node.style);
    updateNodeBounds(node, el);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const inv_trapezoid = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const points = [
        {
            x: h / 6,
            y: 0
        },
        {
            x: w - h / 6,
            y: 0
        },
        {
            x: w + 2 * h / 6,
            y: -h
        },
        {
            x: -2 * h / 6,
            y: -h
        }
    ];
    const el = insertPolygonShape(shapeSvg, w, h, points);
    el.attr("style", node.style);
    updateNodeBounds(node, el);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const rect_right_inv_arrow = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const points = [
        {
            x: 0,
            y: 0
        },
        {
            x: w + h / 2,
            y: 0
        },
        {
            x: w,
            y: -h / 2
        },
        {
            x: w + h / 2,
            y: -h
        },
        {
            x: 0,
            y: -h
        }
    ];
    const el = insertPolygonShape(shapeSvg, w, h, points);
    el.attr("style", node.style);
    updateNodeBounds(node, el);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const cylinder = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const rx = w / 2;
    const ry = rx / (2.5 + w / 50);
    const h = bbox.height + ry + node.padding;
    const shape = "M 0," + ry + " a " + rx + "," + ry + " 0,0,0 " + w + " 0 a " + rx + "," + ry + " 0,0,0 " + -w + " 0 l 0," + h + " a " + rx + "," + ry + " 0,0,0 " + w + " 0 l 0," + -h;
    const el = shapeSvg.attr("label-offset-y", ry).insert("path", ":first-child").attr("style", node.style).attr("d", shape).attr("transform", "translate(" + -w / 2 + "," + -(h / 2 + ry) + ")");
    updateNodeBounds(node, el);
    node.intersect = function(point2) {
        const pos = intersect.rect(node, point2);
        const x = pos.x - node.x;
        if (rx != 0 && (Math.abs(x) < node.width / 2 || Math.abs(x) == node.width / 2 && Math.abs(pos.y - node.y) > node.height / 2 - ry)) {
            let y = ry * ry * (1 - x * x / (rx * rx));
            if (y != 0) {
                y = Math.sqrt(y);
            }
            y = ry - y;
            if (point2.y - node.y > 0) {
                y = -y;
            }
            pos.y += y;
        }
        return pos;
    };
    return shapeSvg;
};
const rect = async (parent, node)=>{
    const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, "node " + node.classes + " " + node.class, true);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const totalWidth = node.positioned ? node.width : bbox.width + node.padding;
    const totalHeight = node.positioned ? node.height : bbox.height + node.padding;
    const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;
    const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;
    rect2.attr("class", "basic label-container").attr("style", node.style).attr("rx", node.rx).attr("ry", node.ry).attr("x", x).attr("y", y).attr("width", totalWidth).attr("height", totalHeight);
    if (node.props) {
        const propKeys = new Set(Object.keys(node.props));
        if (node.props.borders) {
            applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);
            propKeys.delete("borders");
        }
        propKeys.forEach((propKey)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn(`Unknown node property ${propKey}`);
        });
    }
    updateNodeBounds(node, rect2);
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
const composite = async (parent, node)=>{
    const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, "node " + node.classes, true);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const totalWidth = node.positioned ? node.width : bbox.width + node.padding;
    const totalHeight = node.positioned ? node.height : bbox.height + node.padding;
    const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;
    const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;
    rect2.attr("class", "basic cluster composite label-container").attr("style", node.style).attr("rx", node.rx).attr("ry", node.ry).attr("x", x).attr("y", y).attr("width", totalWidth).attr("height", totalHeight);
    if (node.props) {
        const propKeys = new Set(Object.keys(node.props));
        if (node.props.borders) {
            applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);
            propKeys.delete("borders");
        }
        propKeys.forEach((propKey)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn(`Unknown node property ${propKey}`);
        });
    }
    updateNodeBounds(node, rect2);
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
const labelRect = async (parent, node)=>{
    const { shapeSvg } = await labelHelper(parent, node, "label", true);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Classes = ", node.class);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const totalWidth = 0;
    const totalHeight = 0;
    rect2.attr("width", totalWidth).attr("height", totalHeight);
    shapeSvg.attr("class", "label edgeLabel");
    if (node.props) {
        const propKeys = new Set(Object.keys(node.props));
        if (node.props.borders) {
            applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);
            propKeys.delete("borders");
        }
        propKeys.forEach((propKey)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn(`Unknown node property ${propKey}`);
        });
    }
    updateNodeBounds(node, rect2);
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
function applyNodePropertyBorders(rect2, borders, totalWidth, totalHeight) {
    const strokeDashArray = [];
    const addBorder = (length)=>{
        strokeDashArray.push(length, 0);
    };
    const skipBorder = (length)=>{
        strokeDashArray.push(0, length);
    };
    if (borders.includes("t")) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("add top border");
        addBorder(totalWidth);
    } else {
        skipBorder(totalWidth);
    }
    if (borders.includes("r")) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("add right border");
        addBorder(totalHeight);
    } else {
        skipBorder(totalHeight);
    }
    if (borders.includes("b")) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("add bottom border");
        addBorder(totalWidth);
    } else {
        skipBorder(totalWidth);
    }
    if (borders.includes("l")) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("add left border");
        addBorder(totalHeight);
    } else {
        skipBorder(totalHeight);
    }
    rect2.attr("stroke-dasharray", strokeDashArray.join(" "));
}
const rectWithTitle = (parent, node)=>{
    let classes;
    if (!node.classes) {
        classes = "node default";
    } else {
        classes = "node " + node.classes;
    }
    const shapeSvg = parent.insert("g").attr("class", classes).attr("id", node.domId || node.id);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const innerLine = shapeSvg.insert("line");
    const label = shapeSvg.insert("g").attr("class", "label");
    const text2 = node.labelText.flat ? node.labelText.flat() : node.labelText;
    let title = "";
    if (typeof text2 === "object") {
        title = text2[0];
    } else {
        title = text2;
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Label text abc79", title, text2, typeof text2 === "object");
    const text = label.node().appendChild(createLabel$1(title, node.labelStyle, true, true));
    let bbox = {
        width: 0,
        height: 0
    };
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
        const div = text.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(text);
        bbox = div.getBoundingClientRect();
        dv.attr("width", bbox.width);
        dv.attr("height", bbox.height);
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Text 2", text2);
    const textRows = text2.slice(1, text2.length);
    let titleBox = text.getBBox();
    const descr = label.node().appendChild(createLabel$1(textRows.join ? textRows.join("<br/>") : textRows, node.labelStyle, true, true));
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
        const div = descr.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(descr);
        bbox = div.getBoundingClientRect();
        dv.attr("width", bbox.width);
        dv.attr("height", bbox.height);
    }
    const halfPadding = node.padding / 2;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(descr).attr("transform", "translate( " + // (titleBox.width - bbox.width) / 2 +
    (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) + ", " + (titleBox.height + halfPadding + 5) + ")");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(text).attr("transform", "translate( " + // (titleBox.width - bbox.width) / 2 +
    (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) + ", 0)");
    bbox = label.node().getBBox();
    label.attr("transform", "translate(" + -bbox.width / 2 + ", " + (-bbox.height / 2 - halfPadding + 3) + ")");
    rect2.attr("class", "outer title-state").attr("x", -bbox.width / 2 - halfPadding).attr("y", -bbox.height / 2 - halfPadding).attr("width", bbox.width + node.padding).attr("height", bbox.height + node.padding);
    innerLine.attr("class", "divider").attr("x1", -bbox.width / 2 - halfPadding).attr("x2", bbox.width / 2 + halfPadding).attr("y1", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding).attr("y2", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);
    updateNodeBounds(node, rect2);
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
const stadium = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const h = bbox.height + node.padding;
    const w = bbox.width + h / 4 + node.padding;
    const rect2 = shapeSvg.insert("rect", ":first-child").attr("style", node.style).attr("rx", h / 2).attr("ry", h / 2).attr("x", -w / 2).attr("y", -h / 2).attr("width", w).attr("height", h);
    updateNodeBounds(node, rect2);
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
const circle = async (parent, node)=>{
    const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const circle2 = shapeSvg.insert("circle", ":first-child");
    circle2.attr("style", node.style).attr("rx", node.rx).attr("ry", node.ry).attr("r", bbox.width / 2 + halfPadding).attr("width", bbox.width + node.padding).attr("height", bbox.height + node.padding);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Circle main");
    updateNodeBounds(node, circle2);
    node.intersect = function(point2) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Circle intersect", node, bbox.width / 2 + halfPadding, point2);
        return intersect.circle(node, bbox.width / 2 + halfPadding, point2);
    };
    return shapeSvg;
};
const doublecircle = async (parent, node)=>{
    const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const gap = 5;
    const circleGroup = shapeSvg.insert("g", ":first-child");
    const outerCircle = circleGroup.insert("circle");
    const innerCircle = circleGroup.insert("circle");
    circleGroup.attr("class", node.class);
    outerCircle.attr("style", node.style).attr("rx", node.rx).attr("ry", node.ry).attr("r", bbox.width / 2 + halfPadding + gap).attr("width", bbox.width + node.padding + gap * 2).attr("height", bbox.height + node.padding + gap * 2);
    innerCircle.attr("style", node.style).attr("rx", node.rx).attr("ry", node.ry).attr("r", bbox.width / 2 + halfPadding).attr("width", bbox.width + node.padding).attr("height", bbox.height + node.padding);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("DoubleCircle main");
    updateNodeBounds(node, outerCircle);
    node.intersect = function(point2) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("DoubleCircle intersect", node, bbox.width / 2 + halfPadding + gap, point2);
        return intersect.circle(node, bbox.width / 2 + halfPadding + gap, point2);
    };
    return shapeSvg;
};
const subroutine = async (parent, node)=>{
    const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node, void 0), true);
    const w = bbox.width + node.padding;
    const h = bbox.height + node.padding;
    const points = [
        {
            x: 0,
            y: 0
        },
        {
            x: w,
            y: 0
        },
        {
            x: w,
            y: -h
        },
        {
            x: 0,
            y: -h
        },
        {
            x: 0,
            y: 0
        },
        {
            x: -8,
            y: 0
        },
        {
            x: w + 8,
            y: 0
        },
        {
            x: w + 8,
            y: -h
        },
        {
            x: -8,
            y: -h
        },
        {
            x: -8,
            y: 0
        }
    ];
    const el = insertPolygonShape(shapeSvg, w, h, points);
    el.attr("style", node.style);
    updateNodeBounds(node, el);
    node.intersect = function(point2) {
        return intersect.polygon(node, points, point2);
    };
    return shapeSvg;
};
const start = (parent, node)=>{
    const shapeSvg = parent.insert("g").attr("class", "node default").attr("id", node.domId || node.id);
    const circle2 = shapeSvg.insert("circle", ":first-child");
    circle2.attr("class", "state-start").attr("r", 7).attr("width", 14).attr("height", 14);
    updateNodeBounds(node, circle2);
    node.intersect = function(point2) {
        return intersect.circle(node, 7, point2);
    };
    return shapeSvg;
};
const forkJoin = (parent, node, dir)=>{
    const shapeSvg = parent.insert("g").attr("class", "node default").attr("id", node.domId || node.id);
    let width = 70;
    let height = 10;
    if (dir === "LR") {
        width = 10;
        height = 70;
    }
    const shape = shapeSvg.append("rect").attr("x", -1 * width / 2).attr("y", -1 * height / 2).attr("width", width).attr("height", height).attr("class", "fork-join");
    updateNodeBounds(node, shape);
    node.height = node.height + node.padding / 2;
    node.width = node.width + node.padding / 2;
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
const end = (parent, node)=>{
    const shapeSvg = parent.insert("g").attr("class", "node default").attr("id", node.domId || node.id);
    const innerCircle = shapeSvg.insert("circle", ":first-child");
    const circle2 = shapeSvg.insert("circle", ":first-child");
    circle2.attr("class", "state-start").attr("r", 7).attr("width", 14).attr("height", 14);
    innerCircle.attr("class", "state-end").attr("r", 5).attr("width", 10).attr("height", 10);
    updateNodeBounds(node, circle2);
    node.intersect = function(point2) {
        return intersect.circle(node, 7, point2);
    };
    return shapeSvg;
};
const class_box = (parent, node)=>{
    const halfPadding = node.padding / 2;
    const rowPadding = 4;
    const lineHeight = 8;
    let classes;
    if (!node.classes) {
        classes = "node default";
    } else {
        classes = "node " + node.classes;
    }
    const shapeSvg = parent.insert("g").attr("class", classes).attr("id", node.domId || node.id);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const topLine = shapeSvg.insert("line");
    const bottomLine = shapeSvg.insert("line");
    let maxWidth = 0;
    let maxHeight = rowPadding;
    const labelContainer = shapeSvg.insert("g").attr("class", "label");
    let verticalPos = 0;
    const hasInterface = node.classData.annotations && node.classData.annotations[0];
    const interfaceLabelText = node.classData.annotations[0] ? "«" + node.classData.annotations[0] + "»" : "";
    const interfaceLabel = labelContainer.node().appendChild(createLabel$1(interfaceLabelText, node.labelStyle, true, true));
    let interfaceBBox = interfaceLabel.getBBox();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
        const div = interfaceLabel.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(interfaceLabel);
        interfaceBBox = div.getBoundingClientRect();
        dv.attr("width", interfaceBBox.width);
        dv.attr("height", interfaceBBox.height);
    }
    if (node.classData.annotations[0]) {
        maxHeight += interfaceBBox.height + rowPadding;
        maxWidth += interfaceBBox.width;
    }
    let classTitleString = node.classData.label;
    if (node.classData.type !== void 0 && node.classData.type !== "") {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels) {
            classTitleString += "&lt;" + node.classData.type + "&gt;";
        } else {
            classTitleString += "<" + node.classData.type + ">";
        }
    }
    const classTitleLabel = labelContainer.node().appendChild(createLabel$1(classTitleString, node.labelStyle, true, true));
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(classTitleLabel).attr("class", "classTitle");
    let classTitleBBox = classTitleLabel.getBBox();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
        const div = classTitleLabel.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(classTitleLabel);
        classTitleBBox = div.getBoundingClientRect();
        dv.attr("width", classTitleBBox.width);
        dv.attr("height", classTitleBBox.height);
    }
    maxHeight += classTitleBBox.height + rowPadding;
    if (classTitleBBox.width > maxWidth) {
        maxWidth = classTitleBBox.width;
    }
    const classAttributes = [];
    node.classData.members.forEach((member)=>{
        const parsedInfo = member.getDisplayDetails();
        let parsedText = parsedInfo.displayText;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels) {
            parsedText = parsedText.replace(/</g, "&lt;").replace(/>/g, "&gt;");
        }
        const lbl = labelContainer.node().appendChild(createLabel$1(parsedText, parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle, true, true));
        let bbox = lbl.getBBox();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
            const div = lbl.children[0];
            const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(lbl);
            bbox = div.getBoundingClientRect();
            dv.attr("width", bbox.width);
            dv.attr("height", bbox.height);
        }
        if (bbox.width > maxWidth) {
            maxWidth = bbox.width;
        }
        maxHeight += bbox.height + rowPadding;
        classAttributes.push(lbl);
    });
    maxHeight += lineHeight;
    const classMethods = [];
    node.classData.methods.forEach((member)=>{
        const parsedInfo = member.getDisplayDetails();
        let displayText = parsedInfo.displayText;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels) {
            displayText = displayText.replace(/</g, "&lt;").replace(/>/g, "&gt;");
        }
        const lbl = labelContainer.node().appendChild(createLabel$1(displayText, parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle, true, true));
        let bbox = lbl.getBBox();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
            const div = lbl.children[0];
            const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(lbl);
            bbox = div.getBoundingClientRect();
            dv.attr("width", bbox.width);
            dv.attr("height", bbox.height);
        }
        if (bbox.width > maxWidth) {
            maxWidth = bbox.width;
        }
        maxHeight += bbox.height + rowPadding;
        classMethods.push(lbl);
    });
    maxHeight += lineHeight;
    if (hasInterface) {
        let diffX2 = (maxWidth - interfaceBBox.width) / 2;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(interfaceLabel).attr("transform", "translate( " + (-1 * maxWidth / 2 + diffX2) + ", " + -1 * maxHeight / 2 + ")");
        verticalPos = interfaceBBox.height + rowPadding;
    }
    let diffX = (maxWidth - classTitleBBox.width) / 2;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(classTitleLabel).attr("transform", "translate( " + (-1 * maxWidth / 2 + diffX) + ", " + (-1 * maxHeight / 2 + verticalPos) + ")");
    verticalPos += classTitleBBox.height + rowPadding;
    topLine.attr("class", "divider").attr("x1", -maxWidth / 2 - halfPadding).attr("x2", maxWidth / 2 + halfPadding).attr("y1", -maxHeight / 2 - halfPadding + lineHeight + verticalPos).attr("y2", -maxHeight / 2 - halfPadding + lineHeight + verticalPos);
    verticalPos += lineHeight;
    classAttributes.forEach((lbl)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(lbl).attr("transform", "translate( " + -maxWidth / 2 + ", " + (-1 * maxHeight / 2 + verticalPos + lineHeight / 2) + ")");
        const memberBBox = lbl == null ? void 0 : lbl.getBBox();
        verticalPos += ((memberBBox == null ? void 0 : memberBBox.height) ?? 0) + rowPadding;
    });
    verticalPos += lineHeight;
    bottomLine.attr("class", "divider").attr("x1", -maxWidth / 2 - halfPadding).attr("x2", maxWidth / 2 + halfPadding).attr("y1", -maxHeight / 2 - halfPadding + lineHeight + verticalPos).attr("y2", -maxHeight / 2 - halfPadding + lineHeight + verticalPos);
    verticalPos += lineHeight;
    classMethods.forEach((lbl)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(lbl).attr("transform", "translate( " + -maxWidth / 2 + ", " + (-1 * maxHeight / 2 + verticalPos) + ")");
        const memberBBox = lbl == null ? void 0 : lbl.getBBox();
        verticalPos += ((memberBBox == null ? void 0 : memberBBox.height) ?? 0) + rowPadding;
    });
    rect2.attr("style", node.style).attr("class", "outer title-state").attr("x", -maxWidth / 2 - halfPadding).attr("y", -(maxHeight / 2) - halfPadding).attr("width", maxWidth + node.padding).attr("height", maxHeight + node.padding);
    updateNodeBounds(node, rect2);
    node.intersect = function(point2) {
        return intersect.rect(node, point2);
    };
    return shapeSvg;
};
const shapes = {
    rhombus: question,
    composite,
    question,
    rect,
    labelRect,
    rectWithTitle,
    choice,
    circle,
    doublecircle,
    stadium,
    hexagon,
    block_arrow,
    rect_left_inv_arrow,
    lean_right,
    lean_left,
    trapezoid,
    inv_trapezoid,
    rect_right_inv_arrow,
    cylinder,
    start,
    end,
    note: note$1,
    subroutine,
    fork: forkJoin,
    join: forkJoin,
    class_box
};
let nodeElems = {};
const insertNode = async (elem, node, dir)=>{
    let newEl;
    let el;
    if (node.link) {
        let target;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().securityLevel === "sandbox") {
            target = "_top";
        } else if (node.linkTarget) {
            target = node.linkTarget || "_blank";
        }
        newEl = elem.insert("svg:a").attr("xlink:href", node.link).attr("target", target);
        el = await shapes[node.shape](newEl, node, dir);
    } else {
        el = await shapes[node.shape](elem, node, dir);
        newEl = el;
    }
    if (node.tooltip) {
        el.attr("title", node.tooltip);
    }
    if (node.class) {
        el.attr("class", "node default " + node.class);
    }
    newEl.attr("data-node", "true");
    newEl.attr("data-id", node.id);
    nodeElems[node.id] = newEl;
    if (node.haveCallback) {
        nodeElems[node.id].attr("class", nodeElems[node.id].attr("class") + " clickable");
    }
    return newEl;
};
const setNodeElem = (elem, node)=>{
    nodeElems[node.id] = elem;
};
const clear$1 = ()=>{
    nodeElems = {};
};
const positionNode = (node)=>{
    const el = nodeElems[node.id];
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Transforming node", node.diff, node, "translate(" + (node.x - node.width / 2 - 5) + ", " + node.width / 2 + ")");
    const padding = 8;
    const diff = node.diff || 0;
    if (node.clusterNode) {
        el.attr("transform", "translate(" + (node.x + diff - node.width / 2) + ", " + (node.y - node.height / 2 - padding) + ")");
    } else {
        el.attr("transform", "translate(" + node.x + ", " + node.y + ")");
    }
    return diff;
};
const getSubGraphTitleMargins = ({ flowchart })=>{
    var _a, _b;
    const subGraphTitleTopMargin = ((_a = flowchart == null ? void 0 : flowchart.subGraphTitleMargin) == null ? void 0 : _a.top) ?? 0;
    const subGraphTitleBottomMargin = ((_b = flowchart == null ? void 0 : flowchart.subGraphTitleMargin) == null ? void 0 : _b.bottom) ?? 0;
    const subGraphTitleTotalMargin = subGraphTitleTopMargin + subGraphTitleBottomMargin;
    return {
        subGraphTitleTopMargin,
        subGraphTitleBottomMargin,
        subGraphTitleTotalMargin
    };
};
const markerOffsets = {
    aggregation: 18,
    extension: 18,
    composition: 18,
    dependency: 6,
    lollipop: 13.5,
    arrow_point: 5.3
};
function calculateDeltaAndAngle(point1, point2) {
    if (point1 === void 0 || point2 === void 0) {
        return {
            angle: 0,
            deltaX: 0,
            deltaY: 0
        };
    }
    point1 = pointTransformer(point1);
    point2 = pointTransformer(point2);
    const [x1, y1] = [
        point1.x,
        point1.y
    ];
    const [x2, y2] = [
        point2.x,
        point2.y
    ];
    const deltaX = x2 - x1;
    const deltaY = y2 - y1;
    return {
        angle: Math.atan(deltaY / deltaX),
        deltaX,
        deltaY
    };
}
const pointTransformer = (data)=>{
    if (Array.isArray(data)) {
        return {
            x: data[0],
            y: data[1]
        };
    }
    return data;
};
const getLineFunctionsWithOffset = (edge)=>{
    return {
        x: function(d, i, data) {
            let offset = 0;
            if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {
                const { angle, deltaX } = calculateDeltaAndAngle(data[0], data[1]);
                offset = markerOffsets[edge.arrowTypeStart] * Math.cos(angle) * (deltaX >= 0 ? 1 : -1);
            } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {
                const { angle, deltaX } = calculateDeltaAndAngle(data[data.length - 1], data[data.length - 2]);
                offset = markerOffsets[edge.arrowTypeEnd] * Math.cos(angle) * (deltaX >= 0 ? 1 : -1);
            }
            return pointTransformer(d).x + offset;
        },
        y: function(d, i, data) {
            let offset = 0;
            if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {
                const { angle, deltaY } = calculateDeltaAndAngle(data[0], data[1]);
                offset = markerOffsets[edge.arrowTypeStart] * Math.abs(Math.sin(angle)) * (deltaY >= 0 ? 1 : -1);
            } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {
                const { angle, deltaY } = calculateDeltaAndAngle(data[data.length - 1], data[data.length - 2]);
                offset = markerOffsets[edge.arrowTypeEnd] * Math.abs(Math.sin(angle)) * (deltaY >= 0 ? 1 : -1);
            }
            return pointTransformer(d).y + offset;
        }
    };
};
const addEdgeMarkers = (svgPath, edge, url, id, diagramType)=>{
    if (edge.arrowTypeStart) {
        addEdgeMarker(svgPath, "start", edge.arrowTypeStart, url, id, diagramType);
    }
    if (edge.arrowTypeEnd) {
        addEdgeMarker(svgPath, "end", edge.arrowTypeEnd, url, id, diagramType);
    }
};
const arrowTypesMap = {
    arrow_cross: "cross",
    arrow_point: "point",
    arrow_barb: "barb",
    arrow_circle: "circle",
    aggregation: "aggregation",
    extension: "extension",
    composition: "composition",
    dependency: "dependency",
    lollipop: "lollipop"
};
const addEdgeMarker = (svgPath, position, arrowType, url, id, diagramType)=>{
    const endMarkerType = arrowTypesMap[arrowType];
    if (!endMarkerType) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn(`Unknown arrow type: ${arrowType}`);
        return;
    }
    const suffix = position === "start" ? "Start" : "End";
    svgPath.attr(`marker-${position}`, `url(${url}#${id}_${diagramType}-${endMarkerType}${suffix})`);
};
let edgeLabels = {};
let terminalLabels = {};
const clear = ()=>{
    edgeLabels = {};
    terminalLabels = {};
};
const insertEdgeLabel = (elem, edge)=>{
    const useHtmlLabels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels);
    const labelElement = edge.labelType === "markdown" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$createText$2d$ca0c5216$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["a"])(elem, edge.label, {
        style: edge.labelStyle,
        useHtmlLabels,
        addSvgBackground: true
    }) : createLabel$1(edge.label, edge.labelStyle);
    const edgeLabel = elem.insert("g").attr("class", "edgeLabel");
    const label = edgeLabel.insert("g").attr("class", "label");
    label.node().appendChild(labelElement);
    let bbox = labelElement.getBBox();
    if (useHtmlLabels) {
        const div = labelElement.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(labelElement);
        bbox = div.getBoundingClientRect();
        dv.attr("width", bbox.width);
        dv.attr("height", bbox.height);
    }
    label.attr("transform", "translate(" + -bbox.width / 2 + ", " + -bbox.height / 2 + ")");
    edgeLabels[edge.id] = edgeLabel;
    edge.width = bbox.width;
    edge.height = bbox.height;
    let fo;
    if (edge.startLabelLeft) {
        const startLabelElement = createLabel$1(edge.startLabelLeft, edge.labelStyle);
        const startEdgeLabelLeft = elem.insert("g").attr("class", "edgeTerminals");
        const inner = startEdgeLabelLeft.insert("g").attr("class", "inner");
        fo = inner.node().appendChild(startLabelElement);
        const slBox = startLabelElement.getBBox();
        inner.attr("transform", "translate(" + -slBox.width / 2 + ", " + -slBox.height / 2 + ")");
        if (!terminalLabels[edge.id]) {
            terminalLabels[edge.id] = {};
        }
        terminalLabels[edge.id].startLeft = startEdgeLabelLeft;
        setTerminalWidth(fo, edge.startLabelLeft);
    }
    if (edge.startLabelRight) {
        const startLabelElement = createLabel$1(edge.startLabelRight, edge.labelStyle);
        const startEdgeLabelRight = elem.insert("g").attr("class", "edgeTerminals");
        const inner = startEdgeLabelRight.insert("g").attr("class", "inner");
        fo = startEdgeLabelRight.node().appendChild(startLabelElement);
        inner.node().appendChild(startLabelElement);
        const slBox = startLabelElement.getBBox();
        inner.attr("transform", "translate(" + -slBox.width / 2 + ", " + -slBox.height / 2 + ")");
        if (!terminalLabels[edge.id]) {
            terminalLabels[edge.id] = {};
        }
        terminalLabels[edge.id].startRight = startEdgeLabelRight;
        setTerminalWidth(fo, edge.startLabelRight);
    }
    if (edge.endLabelLeft) {
        const endLabelElement = createLabel$1(edge.endLabelLeft, edge.labelStyle);
        const endEdgeLabelLeft = elem.insert("g").attr("class", "edgeTerminals");
        const inner = endEdgeLabelLeft.insert("g").attr("class", "inner");
        fo = inner.node().appendChild(endLabelElement);
        const slBox = endLabelElement.getBBox();
        inner.attr("transform", "translate(" + -slBox.width / 2 + ", " + -slBox.height / 2 + ")");
        endEdgeLabelLeft.node().appendChild(endLabelElement);
        if (!terminalLabels[edge.id]) {
            terminalLabels[edge.id] = {};
        }
        terminalLabels[edge.id].endLeft = endEdgeLabelLeft;
        setTerminalWidth(fo, edge.endLabelLeft);
    }
    if (edge.endLabelRight) {
        const endLabelElement = createLabel$1(edge.endLabelRight, edge.labelStyle);
        const endEdgeLabelRight = elem.insert("g").attr("class", "edgeTerminals");
        const inner = endEdgeLabelRight.insert("g").attr("class", "inner");
        fo = inner.node().appendChild(endLabelElement);
        const slBox = endLabelElement.getBBox();
        inner.attr("transform", "translate(" + -slBox.width / 2 + ", " + -slBox.height / 2 + ")");
        endEdgeLabelRight.node().appendChild(endLabelElement);
        if (!terminalLabels[edge.id]) {
            terminalLabels[edge.id] = {};
        }
        terminalLabels[edge.id].endRight = endEdgeLabelRight;
        setTerminalWidth(fo, edge.endLabelRight);
    }
    return labelElement;
};
function setTerminalWidth(fo, value) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels && fo) {
        fo.style.width = value.length * 9 + "px";
        fo.style.height = "12px";
    }
}
const positionEdgeLabel = (edge, paths)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Moving label abc88 ", edge.id, edge.label, edgeLabels[edge.id], paths);
    let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;
    const siteConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
    const { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);
    if (edge.label) {
        const el = edgeLabels[edge.id];
        let x = edge.x;
        let y = edge.y;
        if (path) {
            const pos = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].calcLabelPosition(path);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Moving label " + edge.label + " from (", x, ",", y, ") to (", pos.x, ",", pos.y, ") abc88");
            if (paths.updatedPath) {
                x = pos.x;
                y = pos.y;
            }
        }
        el.attr("transform", `translate(${x}, ${y + subGraphTitleTotalMargin / 2})`);
    }
    if (edge.startLabelLeft) {
        const el = terminalLabels[edge.id].startLeft;
        let x = edge.x;
        let y = edge.y;
        if (path) {
            const pos = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, "start_left", path);
            x = pos.x;
            y = pos.y;
        }
        el.attr("transform", `translate(${x}, ${y})`);
    }
    if (edge.startLabelRight) {
        const el = terminalLabels[edge.id].startRight;
        let x = edge.x;
        let y = edge.y;
        if (path) {
            const pos = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, "start_right", path);
            x = pos.x;
            y = pos.y;
        }
        el.attr("transform", `translate(${x}, ${y})`);
    }
    if (edge.endLabelLeft) {
        const el = terminalLabels[edge.id].endLeft;
        let x = edge.x;
        let y = edge.y;
        if (path) {
            const pos = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, "end_left", path);
            x = pos.x;
            y = pos.y;
        }
        el.attr("transform", `translate(${x}, ${y})`);
    }
    if (edge.endLabelRight) {
        const el = terminalLabels[edge.id].endRight;
        let x = edge.x;
        let y = edge.y;
        if (path) {
            const pos = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, "end_right", path);
            x = pos.x;
            y = pos.y;
        }
        el.attr("transform", `translate(${x}, ${y})`);
    }
};
const outsideNode = (node, point2)=>{
    const x = node.x;
    const y = node.y;
    const dx = Math.abs(point2.x - x);
    const dy = Math.abs(point2.y - y);
    const w = node.width / 2;
    const h = node.height / 2;
    if (dx >= w || dy >= h) {
        return true;
    }
    return false;
};
const intersection = (node, outsidePoint, insidePoint)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(outsidePoint)}
  insidePoint : ${JSON.stringify(insidePoint)}
  node        : x:${node.x} y:${node.y} w:${node.width} h:${node.height}`);
    const x = node.x;
    const y = node.y;
    const dx = Math.abs(x - insidePoint.x);
    const w = node.width / 2;
    let r = insidePoint.x < outsidePoint.x ? w - dx : w + dx;
    const h = node.height / 2;
    const Q = Math.abs(outsidePoint.y - insidePoint.y);
    const R = Math.abs(outsidePoint.x - insidePoint.x);
    if (Math.abs(y - outsidePoint.y) * w > Math.abs(x - outsidePoint.x) * h) {
        let q = insidePoint.y < outsidePoint.y ? outsidePoint.y - h - y : y - h - outsidePoint.y;
        r = R * q / Q;
        const res = {
            x: insidePoint.x < outsidePoint.x ? insidePoint.x + r : insidePoint.x - R + r,
            y: insidePoint.y < outsidePoint.y ? insidePoint.y + Q - q : insidePoint.y - Q + q
        };
        if (r === 0) {
            res.x = outsidePoint.x;
            res.y = outsidePoint.y;
        }
        if (R === 0) {
            res.x = outsidePoint.x;
        }
        if (Q === 0) {
            res.y = outsidePoint.y;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug(`abc89 topp/bott calc, Q ${Q}, q ${q}, R ${R}, r ${r}`, res);
        return res;
    } else {
        if (insidePoint.x < outsidePoint.x) {
            r = outsidePoint.x - w - x;
        } else {
            r = x - w - outsidePoint.x;
        }
        let q = Q * r / R;
        let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x - R + r;
        let _y = insidePoint.y < outsidePoint.y ? insidePoint.y + q : insidePoint.y - q;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug(`sides calc abc89, Q ${Q}, q ${q}, R ${R}, r ${r}`, {
            _x,
            _y
        });
        if (r === 0) {
            _x = outsidePoint.x;
            _y = outsidePoint.y;
        }
        if (R === 0) {
            _x = outsidePoint.x;
        }
        if (Q === 0) {
            _y = outsidePoint.y;
        }
        return {
            x: _x,
            y: _y
        };
    }
};
const cutPathAtIntersect = (_points, boundaryNode)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("abc88 cutPathAtIntersect", _points, boundaryNode);
    let points = [];
    let lastPointOutside = _points[0];
    let isInside = false;
    _points.forEach((point2)=>{
        if (!outsideNode(boundaryNode, point2) && !isInside) {
            const inter = intersection(boundaryNode, lastPointOutside, point2);
            let pointPresent = false;
            points.forEach((p)=>{
                pointPresent = pointPresent || p.x === inter.x && p.y === inter.y;
            });
            if (!points.some((e)=>e.x === inter.x && e.y === inter.y)) {
                points.push(inter);
            }
            isInside = true;
        } else {
            lastPointOutside = point2;
            if (!isInside) {
                points.push(point2);
            }
        }
    });
    return points;
};
const insertEdge = function(elem, e, edge, clusterDb, diagramType, graph, id) {
    let points = edge.points;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("abc88 InsertEdge: edge=", edge, "e=", e);
    let pointsHasChanged = false;
    const tail = graph.node(e.v);
    var head = graph.node(e.w);
    if ((head == null ? void 0 : head.intersect) && (tail == null ? void 0 : tail.intersect)) {
        points = points.slice(1, edge.points.length - 1);
        points.unshift(tail.intersect(points[0]));
        points.push(head.intersect(points[points.length - 1]));
    }
    if (edge.toCluster) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("to cluster abc88", clusterDb[edge.toCluster]);
        points = cutPathAtIntersect(edge.points, clusterDb[edge.toCluster].node);
        pointsHasChanged = true;
    }
    if (edge.fromCluster) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("from cluster abc88", clusterDb[edge.fromCluster]);
        points = cutPathAtIntersect(points.reverse(), clusterDb[edge.fromCluster].node).reverse();
        pointsHasChanged = true;
    }
    const lineData = points.filter((p)=>!Number.isNaN(p.y));
    let curve = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$basis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveBasis$3e$__["curveBasis"];
    if (edge.curve && (diagramType === "graph" || diagramType === "flowchart")) {
        curve = edge.curve;
    }
    const { x, y } = getLineFunctionsWithOffset(edge);
    const lineFunction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__line$3e$__["line"])().x(x).y(y).curve(curve);
    let strokeClasses;
    switch(edge.thickness){
        case "normal":
            strokeClasses = "edge-thickness-normal";
            break;
        case "thick":
            strokeClasses = "edge-thickness-thick";
            break;
        case "invisible":
            strokeClasses = "edge-thickness-thick";
            break;
        default:
            strokeClasses = "";
    }
    switch(edge.pattern){
        case "solid":
            strokeClasses += " edge-pattern-solid";
            break;
        case "dotted":
            strokeClasses += " edge-pattern-dotted";
            break;
        case "dashed":
            strokeClasses += " edge-pattern-dashed";
            break;
    }
    const svgPath = elem.append("path").attr("d", lineFunction(lineData)).attr("id", edge.id).attr("class", " " + strokeClasses + (edge.classes ? " " + edge.classes : "")).attr("style", edge.style);
    let url = "";
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.arrowMarkerAbsolute || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().state.arrowMarkerAbsolute) {
        url = window.location.protocol + "//" + window.location.host + window.location.pathname + window.location.search;
        url = url.replace(/\(/g, "\\(");
        url = url.replace(/\)/g, "\\)");
    }
    addEdgeMarkers(svgPath, edge, url, id, diagramType);
    let paths = {};
    if (pointsHasChanged) {
        paths.updatedPath = points;
    }
    paths.originalPath = edge.points;
    return paths;
};
;
}}),
"[project]/node_modules/mermaid/dist/index-01f381cb.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "r": (()=>render)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2f$layout$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre/layout.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/json.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/edges-066a5561.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$graph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/graph.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$createText$2d$ca0c5216$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/createText-ca0c5216.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-client] (ecmascript) <export default as select>");
;
;
;
;
;
;
;
let clusterDb = {};
let descendants = {};
let parents = {};
const clear$1 = ()=>{
    descendants = {};
    parents = {};
    clusterDb = {};
};
const isDescendant = (id, ancestorId)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("In isDescendant", ancestorId, " ", id, " = ", descendants[ancestorId].includes(id));
    if (descendants[ancestorId].includes(id)) {
        return true;
    }
    return false;
};
const edgeInCluster = (edge, clusterId)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Descendants of ", clusterId, " is ", descendants[clusterId]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edge is ", edge);
    if (edge.v === clusterId) {
        return false;
    }
    if (edge.w === clusterId) {
        return false;
    }
    if (!descendants[clusterId]) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Tilt, ", clusterId, ",not in descendants");
        return false;
    }
    return descendants[clusterId].includes(edge.v) || isDescendant(edge.v, clusterId) || isDescendant(edge.w, clusterId) || descendants[clusterId].includes(edge.w);
};
const copy = (clusterId, graph, newGraph, rootId)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Copying children of ", clusterId, "root", rootId, "data", graph.node(clusterId), rootId);
    const nodes = graph.children(clusterId) || [];
    if (clusterId !== rootId) {
        nodes.push(clusterId);
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Copying (nodes) clusterId", clusterId, "nodes", nodes);
    nodes.forEach((node)=>{
        if (graph.children(node).length > 0) {
            copy(node, graph, newGraph, rootId);
        } else {
            const data = graph.node(node);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("cp ", node, " to ", rootId, " with parent ", clusterId);
            newGraph.setNode(node, data);
            if (rootId !== graph.parent(node)) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Setting parent", node, graph.parent(node));
                newGraph.setParent(node, graph.parent(node));
            }
            if (clusterId !== rootId && node !== clusterId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Setting parent", node, clusterId);
                newGraph.setParent(node, clusterId);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("In copy ", clusterId, "root", rootId, "data", graph.node(clusterId), rootId);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Not Setting parent for node=", node, "cluster!==rootId", clusterId !== rootId, "node!==clusterId", node !== clusterId);
            }
            const edges = graph.edges(node);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Copying Edges", edges);
            edges.forEach((edge)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edge", edge);
                const data2 = graph.edge(edge.v, edge.w, edge.name);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edge data", data2, rootId);
                try {
                    if (edgeInCluster(edge, rootId)) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Copying as ", edge.v, edge.w, data2, edge.name);
                        newGraph.setEdge(edge.v, edge.w, data2, edge.name);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("newGraph edges ", newGraph.edges(), newGraph.edge(newGraph.edges()[0]));
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Skipping copy of edge ", edge.v, "-->", edge.w, " rootId: ", rootId, " clusterId:", clusterId);
                    }
                } catch (e) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].error(e);
                }
            });
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Removing node", node);
        graph.removeNode(node);
    });
};
const extractDescendants = (id, graph)=>{
    const children = graph.children(id);
    let res = [
        ...children
    ];
    for (const child of children){
        parents[child] = id;
        res = [
            ...res,
            ...extractDescendants(child, graph)
        ];
    }
    return res;
};
const findNonClusterChild = (id, graph)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Searching", id);
    const children = graph.children(id);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Searching children of id ", id, children);
    if (children.length < 1) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("This is a valid node", id);
        return id;
    }
    for (const child of children){
        const _id = findNonClusterChild(child, graph);
        if (_id) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Found replacement for", id, " => ", _id);
            return _id;
        }
    }
};
const getAnchorId = (id)=>{
    if (!clusterDb[id]) {
        return id;
    }
    if (!clusterDb[id].externalConnections) {
        return id;
    }
    if (clusterDb[id]) {
        return clusterDb[id].id;
    }
    return id;
};
const adjustClustersAndEdges = (graph, depth)=>{
    if (!graph || depth > 10) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Opting out, no graph ");
        return;
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Opting in, graph ");
    }
    graph.nodes().forEach(function(id) {
        const children = graph.children(id);
        if (children.length > 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Cluster identified", id, " Replacement id in edges: ", findNonClusterChild(id, graph));
            descendants[id] = extractDescendants(id, graph);
            clusterDb[id] = {
                id: findNonClusterChild(id, graph),
                clusterData: graph.node(id)
            };
        }
    });
    graph.nodes().forEach(function(id) {
        const children = graph.children(id);
        const edges = graph.edges();
        if (children.length > 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Cluster identified", id, descendants);
            edges.forEach((edge)=>{
                if (edge.v !== id && edge.w !== id) {
                    const d1 = isDescendant(edge.v, id);
                    const d2 = isDescendant(edge.w, id);
                    if (d1 ^ d2) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Edge: ", edge, " leaves cluster ", id);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Descendants of XXX ", id, ": ", descendants[id]);
                        clusterDb[id].externalConnections = true;
                    }
                }
            });
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Not a cluster ", id, descendants);
        }
    });
    for (let id of Object.keys(clusterDb)){
        const nonClusterChild = clusterDb[id].id;
        const parent = graph.parent(nonClusterChild);
        if (parent !== id && clusterDb[parent] && !clusterDb[parent].externalConnections) {
            clusterDb[id].id = parent;
        }
    }
    graph.edges().forEach(function(e) {
        const edge = graph.edge(e);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Edge " + e.v + " -> " + e.w + ": " + JSON.stringify(e));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Edge " + e.v + " -> " + e.w + ": " + JSON.stringify(graph.edge(e)));
        let v = e.v;
        let w = e.w;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Fix XXX", clusterDb, "ids:", e.v, e.w, "Translating: ", clusterDb[e.v], " --- ", clusterDb[e.w]);
        if (clusterDb[e.v] && clusterDb[e.w] && clusterDb[e.v] === clusterDb[e.w]) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Fixing and trixing link to self - removing XXX", e.v, e.w, e.name);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Fixing and trixing - removing XXX", e.v, e.w, e.name);
            v = getAnchorId(e.v);
            w = getAnchorId(e.w);
            graph.removeEdge(e.v, e.w, e.name);
            const specialId = e.w + "---" + e.v;
            graph.setNode(specialId, {
                domId: specialId,
                id: specialId,
                labelStyle: "",
                labelText: edge.label,
                padding: 0,
                shape: "labelRect",
                style: ""
            });
            const edge1 = structuredClone(edge);
            const edge2 = structuredClone(edge);
            edge1.label = "";
            edge1.arrowTypeEnd = "none";
            edge2.label = "";
            edge1.fromCluster = e.v;
            edge2.toCluster = e.v;
            graph.setEdge(v, specialId, edge1, e.name + "-cyclic-special");
            graph.setEdge(specialId, w, edge2, e.name + "-cyclic-special");
        } else if (clusterDb[e.v] || clusterDb[e.w]) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Fixing and trixing - removing XXX", e.v, e.w, e.name);
            v = getAnchorId(e.v);
            w = getAnchorId(e.w);
            graph.removeEdge(e.v, e.w, e.name);
            if (v !== e.v) {
                const parent = graph.parent(v);
                clusterDb[parent].externalConnections = true;
                edge.fromCluster = e.v;
            }
            if (w !== e.w) {
                const parent = graph.parent(w);
                clusterDb[parent].externalConnections = true;
                edge.toCluster = e.w;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Fix Replacing with XXX", v, w, e.name);
            graph.setEdge(v, w, edge, e.name);
        }
    });
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Adjusted Graph", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph));
    extractor(graph, 0);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace(clusterDb);
};
const extractor = (graph, depth)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("extractor - ", depth, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph), graph.children("D"));
    if (depth > 10) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].error("Bailing out");
        return;
    }
    let nodes = graph.nodes();
    let hasChildren = false;
    for (const node of nodes){
        const children = graph.children(node);
        hasChildren = hasChildren || children.length > 0;
    }
    if (!hasChildren) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Done, no node has children", graph.nodes());
        return;
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Nodes = ", nodes, depth);
    for (const node of nodes){
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Extracting node", node, clusterDb, clusterDb[node] && !clusterDb[node].externalConnections, !graph.parent(node), graph.node(node), graph.children("D"), " Depth ", depth);
        if (!clusterDb[node]) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Not a cluster", node, depth);
        } else if (!clusterDb[node].externalConnections && // !graph.parent(node) &&
        graph.children(node) && graph.children(node).length > 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Cluster without external connections, without a parent and with children", node, depth);
            const graphSettings = graph.graph();
            let dir = graphSettings.rankdir === "TB" ? "LR" : "TB";
            if (clusterDb[node] && clusterDb[node].clusterData && clusterDb[node].clusterData.dir) {
                dir = clusterDb[node].clusterData.dir;
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Fixing dir", clusterDb[node].clusterData.dir, dir);
            }
            const clusterGraph = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$graph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Graph"]({
                multigraph: true,
                compound: true
            }).setGraph({
                rankdir: dir,
                // Todo: set proper spacing
                nodesep: 50,
                ranksep: 50,
                marginx: 8,
                marginy: 8
            }).setDefaultEdgeLabel(function() {
                return {};
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Old graph before copy", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph));
            copy(node, graph, clusterGraph, node);
            graph.setNode(node, {
                clusterNode: true,
                id: node,
                clusterData: clusterDb[node].clusterData,
                labelText: clusterDb[node].labelText,
                graph: clusterGraph
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("New graph after copy node: (", node, ")", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(clusterGraph));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug("Old graph after copy", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph));
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Cluster ** ", node, " **not meeting the criteria !externalConnections:", !clusterDb[node].externalConnections, " no parent: ", !graph.parent(node), " children ", graph.children(node) && graph.children(node).length > 0, graph.children("D"), depth);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].debug(clusterDb);
        }
    }
    nodes = graph.nodes();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("New list of nodes", nodes);
    for (const node of nodes){
        const data = graph.node(node);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn(" Now next level", node, data);
        if (data.clusterNode) {
            extractor(data.graph, depth + 1);
        }
    }
};
const sorter = (graph, nodes)=>{
    if (nodes.length === 0) {
        return [];
    }
    let result = Object.assign(nodes);
    nodes.forEach((node)=>{
        const children = graph.children(node);
        const sorted = sorter(graph, children);
        result = [
            ...result,
            ...sorted
        ];
    });
    return result;
};
const sortNodesByHierarchy = (graph)=>sorter(graph, graph.children());
const rect = (parent, node)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Creating subgraph rect for ", node.id, node);
    const siteConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
    const shapeSvg = parent.insert("g").attr("class", "cluster" + (node.class ? " " + node.class : "")).attr("id", node.id);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const useHtmlLabels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])(siteConfig.flowchart.htmlLabels);
    const label = shapeSvg.insert("g").attr("class", "cluster-label");
    const text = node.labelType === "markdown" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$createText$2d$ca0c5216$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["a"])(label, node.labelText, {
        style: node.labelStyle,
        useHtmlLabels
    }) : label.node().appendChild((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])(node.labelText, node.labelStyle, void 0, true));
    let bbox = text.getBBox();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])(siteConfig.flowchart.htmlLabels)) {
        const div = text.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(text);
        bbox = div.getBoundingClientRect();
        dv.attr("width", bbox.width);
        dv.attr("height", bbox.height);
    }
    const padding = 0 * node.padding;
    const halfPadding = padding / 2;
    const width = node.width <= bbox.width + padding ? bbox.width + padding : node.width;
    if (node.width <= bbox.width + padding) {
        node.diff = (bbox.width - node.width) / 2 - node.padding / 2;
    } else {
        node.diff = -node.padding / 2;
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Data ", node, JSON.stringify(node));
    rect2.attr("style", node.style).attr("rx", node.rx).attr("ry", node.ry).attr("x", node.x - width / 2).attr("y", node.y - node.height / 2 - halfPadding).attr("width", width).attr("height", node.height + padding);
    const { subGraphTitleTopMargin } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["g"])(siteConfig);
    if (useHtmlLabels) {
        label.attr("transform", // This puts the label on top of the box instead of inside it
        `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`);
    } else {
        label.attr("transform", // This puts the label on top of the box instead of inside it
        `translate(${node.x}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`);
    }
    const rectBox = rect2.node().getBBox();
    node.width = rectBox.width;
    node.height = rectBox.height;
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["i"])(node, point);
    };
    return shapeSvg;
};
const noteGroup = (parent, node)=>{
    const shapeSvg = parent.insert("g").attr("class", "note-cluster").attr("id", node.id);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const padding = 0 * node.padding;
    const halfPadding = padding / 2;
    rect2.attr("rx", node.rx).attr("ry", node.ry).attr("x", node.x - node.width / 2 - halfPadding).attr("y", node.y - node.height / 2 - halfPadding).attr("width", node.width + padding).attr("height", node.height + padding).attr("fill", "none");
    const rectBox = rect2.node().getBBox();
    node.width = rectBox.width;
    node.height = rectBox.height;
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["i"])(node, point);
    };
    return shapeSvg;
};
const roundedWithTitle = (parent, node)=>{
    const siteConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
    const shapeSvg = parent.insert("g").attr("class", node.classes).attr("id", node.id);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const label = shapeSvg.insert("g").attr("class", "cluster-label");
    const innerRect = shapeSvg.append("rect");
    const text = label.node().appendChild((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])(node.labelText, node.labelStyle, void 0, true));
    let bbox = text.getBBox();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])(siteConfig.flowchart.htmlLabels)) {
        const div = text.children[0];
        const dv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(text);
        bbox = div.getBoundingClientRect();
        dv.attr("width", bbox.width);
        dv.attr("height", bbox.height);
    }
    bbox = text.getBBox();
    const padding = 0 * node.padding;
    const halfPadding = padding / 2;
    const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;
    if (node.width <= bbox.width + node.padding) {
        node.diff = (bbox.width + node.padding * 0 - node.width) / 2;
    } else {
        node.diff = -node.padding / 2;
    }
    rect2.attr("class", "outer").attr("x", node.x - width / 2 - halfPadding).attr("y", node.y - node.height / 2 - halfPadding).attr("width", width + padding).attr("height", node.height + padding);
    innerRect.attr("class", "inner").attr("x", node.x - width / 2 - halfPadding).attr("y", node.y - node.height / 2 - halfPadding + bbox.height - 1).attr("width", width + padding).attr("height", node.height + padding - bbox.height - 3);
    const { subGraphTitleTopMargin } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["g"])(siteConfig);
    label.attr("transform", `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 - node.padding / 3 + ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])(siteConfig.flowchart.htmlLabels) ? 5 : 3) + subGraphTitleTopMargin})`);
    const rectBox = rect2.node().getBBox();
    node.height = rectBox.height;
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["i"])(node, point);
    };
    return shapeSvg;
};
const divider = (parent, node)=>{
    const shapeSvg = parent.insert("g").attr("class", node.classes).attr("id", node.id);
    const rect2 = shapeSvg.insert("rect", ":first-child");
    const padding = 0 * node.padding;
    const halfPadding = padding / 2;
    rect2.attr("class", "divider").attr("x", node.x - node.width / 2 - halfPadding).attr("y", node.y - node.height / 2).attr("width", node.width + padding).attr("height", node.height + padding);
    const rectBox = rect2.node().getBBox();
    node.width = rectBox.width;
    node.height = rectBox.height;
    node.diff = -node.padding / 2;
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["i"])(node, point);
    };
    return shapeSvg;
};
const shapes = {
    rect,
    roundedWithTitle,
    noteGroup,
    divider
};
let clusterElems = {};
const insertCluster = (elem, node)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Inserting cluster");
    const shape = node.shape || "rect";
    clusterElems[node.id] = shapes[shape](elem, node);
};
const clear = ()=>{
    clusterElems = {};
};
const recursiveRender = async (_elem, graph, diagramType, id, parentCluster, siteConfig)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Graph in recursive render: XXX", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph), parentCluster);
    const dir = graph.graph().rankdir;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Dir in recursive render - dir:", dir);
    const elem = _elem.insert("g").attr("class", "root");
    if (!graph.nodes()) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("No nodes found for", graph);
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Recursive render XXX", graph.nodes());
    }
    if (graph.edges().length > 0) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Recursive edges", graph.edge(graph.edges()[0]));
    }
    const clusters = elem.insert("g").attr("class", "clusters");
    const edgePaths = elem.insert("g").attr("class", "edgePaths");
    const edgeLabels = elem.insert("g").attr("class", "edgeLabels");
    const nodes = elem.insert("g").attr("class", "nodes");
    await Promise.all(graph.nodes().map(async function(v) {
        const node = graph.node(v);
        if (parentCluster !== void 0) {
            const data = JSON.parse(JSON.stringify(parentCluster.clusterData));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Setting data for cluster XXX (", v, ") ", data, parentCluster);
            graph.setNode(parentCluster.id, data);
            if (!graph.parent(v)) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].trace("Setting parent", v, parentCluster.id);
                graph.setParent(v, parentCluster.id, data);
            }
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("(Insert) Node XXX" + v + ": " + JSON.stringify(graph.node(v)));
        if (node && node.clusterNode) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Cluster identified", v, node.width, graph.node(v));
            const o = await recursiveRender(nodes, node.graph, diagramType, id, graph.node(v), siteConfig);
            const newEl = o.elem;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"])(node, newEl);
            node.diff = o.diff || 0;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Node bounds (abc123)", v, node, node.width, node.x, node.y);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["s"])(newEl, node);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Recursive render complete ", newEl, node);
        } else {
            if (graph.children(v).length > 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Cluster - the non recursive path XXX", v, node.id, node, graph);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info(findNonClusterChild(node.id, graph));
                clusterDb[node.id] = {
                    id: findNonClusterChild(node.id, graph),
                    node
                };
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Node - the non recursive path", v, node.id, node);
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["e"])(nodes, graph.node(v), dir);
            }
        }
    }));
    graph.edges().forEach(function(e) {
        const edge = graph.edge(e.v, e.w, e.name);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edge " + e.v + " -> " + e.w + ": " + JSON.stringify(e));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edge " + e.v + " -> " + e.w + ": ", e, " ", JSON.stringify(graph.edge(e)));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Fix", clusterDb, "ids:", e.v, e.w, "Translating: ", clusterDb[e.v], clusterDb[e.w]);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["f"])(edgeLabels, edge);
    });
    graph.edges().forEach(function(e) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edge " + e.v + " -> " + e.w + ": " + JSON.stringify(e));
    });
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("#############################################");
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("###                Layout                 ###");
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("#############################################");
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info(graph);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2f$layout$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["layout"])(graph);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Graph after layout:", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph));
    let diff = 0;
    const { subGraphTitleTotalMargin } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["g"])(siteConfig);
    sortNodesByHierarchy(graph).forEach(function(v) {
        const node = graph.node(v);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Position " + v + ": " + JSON.stringify(graph.node(v)));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Position " + v + ": (" + node.x, "," + node.y, ") width: ", node.width, " height: ", node.height);
        if (node && node.clusterNode) {
            node.y += subGraphTitleTotalMargin;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["p"])(node);
        } else {
            if (graph.children(v).length > 0) {
                node.height += subGraphTitleTotalMargin;
                insertCluster(clusters, node);
                clusterDb[node.id].node = node;
            } else {
                node.y += subGraphTitleTotalMargin / 2;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["p"])(node);
            }
        }
    });
    graph.edges().forEach(function(e) {
        const edge = graph.edge(e);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edge " + e.v + " -> " + e.w + ": " + JSON.stringify(edge), edge);
        edge.points.forEach((point)=>point.y += subGraphTitleTotalMargin / 2);
        const paths = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["h"])(edgePaths, e, edge, clusterDb, diagramType, graph, id);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["j"])(edge, paths);
    });
    graph.nodes().forEach(function(v) {
        const n = graph.node(v);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info(v, n.type, n.diff);
        if (n.type === "group") {
            diff = n.diff;
        }
    });
    return {
        elem,
        diff
    };
};
const render = async (elem, graph, markers, diagramType, id)=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["a"])(elem, markers, diagramType, id);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["b"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$edges$2d$066a5561$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["d"])();
    clear();
    clear$1();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Graph at first:", JSON.stringify((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph)));
    adjustClustersAndEdges(graph);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Graph after:", JSON.stringify((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["write"])(graph)));
    const siteConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
    await recursiveRender(elem, graph, diagramType, id, void 0, siteConfig);
};
;
}}),
"[project]/node_modules/mermaid/dist/styles-483fbfea.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "a": (()=>flowStyles),
    "f": (()=>flowRendererV2)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$graph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/graph.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/curve/linear.js [app-client] (ecmascript) <export default as curveLinear>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-client] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$selectAll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__selectAll$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/selectAll.js [app-client] (ecmascript) <export default as selectAll>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$index$2d$01f381cb$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/index-01f381cb.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$label$2f$add$2d$html$2d$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre-js/label/add-html-label.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$channel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__channel$3e$__ = __turbopack_context__.i("[project]/node_modules/khroma/dist/methods/channel.js [app-client] (ecmascript) <export default as channel>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$rgba$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__rgba$3e$__ = __turbopack_context__.i("[project]/node_modules/khroma/dist/methods/rgba.js [app-client] (ecmascript) <export default as rgba>");
;
;
;
;
;
;
const conf = {};
const setConf = function(cnf) {
    const keys = Object.keys(cnf);
    for (const key of keys){
        conf[key] = cnf[key];
    }
};
const addVertices = async function(vert, g, svgId, root, doc, diagObj) {
    const svg = root.select(`[id="${svgId}"]`);
    const keys = Object.keys(vert);
    for (const id of keys){
        const vertex = vert[id];
        let classStr = "default";
        if (vertex.classes.length > 0) {
            classStr = vertex.classes.join(" ");
        }
        classStr = classStr + " flowchart-label";
        const styles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"])(vertex.styles);
        let vertexText = vertex.text !== void 0 ? vertex.text : vertex.id;
        let vertexNode;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("vertex", vertex, vertex.labelType);
        if (vertex.labelType === "markdown") {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("vertex", vertex, vertex.labelType);
        } else {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
                const node = {
                    label: vertexText
                };
                vertexNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$label$2f$add$2d$html$2d$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addHtmlLabel"])(svg, node).node();
                vertexNode.parentNode.removeChild(vertexNode);
            } else {
                const svgLabel = doc.createElementNS("http://www.w3.org/2000/svg", "text");
                svgLabel.setAttribute("style", styles.labelStyle.replace("color:", "fill:"));
                const rows = vertexText.split(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["e"].lineBreakRegex);
                for (const row of rows){
                    const tspan = doc.createElementNS("http://www.w3.org/2000/svg", "tspan");
                    tspan.setAttributeNS("http://www.w3.org/XML/1998/namespace", "xml:space", "preserve");
                    tspan.setAttribute("dy", "1em");
                    tspan.setAttribute("x", "1");
                    tspan.textContent = row;
                    svgLabel.appendChild(tspan);
                }
                vertexNode = svgLabel;
            }
        }
        let radius = 0;
        let _shape = "";
        switch(vertex.type){
            case "round":
                radius = 5;
                _shape = "rect";
                break;
            case "square":
                _shape = "rect";
                break;
            case "diamond":
                _shape = "question";
                break;
            case "hexagon":
                _shape = "hexagon";
                break;
            case "odd":
                _shape = "rect_left_inv_arrow";
                break;
            case "lean_right":
                _shape = "lean_right";
                break;
            case "lean_left":
                _shape = "lean_left";
                break;
            case "trapezoid":
                _shape = "trapezoid";
                break;
            case "inv_trapezoid":
                _shape = "inv_trapezoid";
                break;
            case "odd_right":
                _shape = "rect_left_inv_arrow";
                break;
            case "circle":
                _shape = "circle";
                break;
            case "ellipse":
                _shape = "ellipse";
                break;
            case "stadium":
                _shape = "stadium";
                break;
            case "subroutine":
                _shape = "subroutine";
                break;
            case "cylinder":
                _shape = "cylinder";
                break;
            case "group":
                _shape = "rect";
                break;
            case "doublecircle":
                _shape = "doublecircle";
                break;
            default:
                _shape = "rect";
        }
        const labelText = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["r"])(vertexText, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])());
        g.setNode(vertex.id, {
            labelStyle: styles.labelStyle,
            shape: _shape,
            labelText,
            labelType: vertex.labelType,
            rx: radius,
            ry: radius,
            class: classStr,
            style: styles.style,
            id: vertex.id,
            link: vertex.link,
            linkTarget: vertex.linkTarget,
            tooltip: diagObj.db.getTooltip(vertex.id) || "",
            domId: diagObj.db.lookUpDomId(vertex.id),
            haveCallback: vertex.haveCallback,
            width: vertex.type === "group" ? 500 : void 0,
            dir: vertex.dir,
            type: vertex.type,
            props: vertex.props,
            padding: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.padding
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("setNode", {
            labelStyle: styles.labelStyle,
            labelType: vertex.labelType,
            shape: _shape,
            labelText,
            rx: radius,
            ry: radius,
            class: classStr,
            style: styles.style,
            id: vertex.id,
            domId: diagObj.db.lookUpDomId(vertex.id),
            width: vertex.type === "group" ? 500 : void 0,
            type: vertex.type,
            dir: vertex.dir,
            props: vertex.props,
            padding: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.padding
        });
    }
};
const addEdges = async function(edges, g, diagObj) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("abc78 edges = ", edges);
    let cnt = 0;
    let linkIdCnt = {};
    let defaultStyle;
    let defaultLabelStyle;
    if (edges.defaultStyle !== void 0) {
        const defaultStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"])(edges.defaultStyle);
        defaultStyle = defaultStyles.style;
        defaultLabelStyle = defaultStyles.labelStyle;
    }
    for (const edge of edges){
        cnt++;
        const linkIdBase = "L-" + edge.start + "-" + edge.end;
        if (linkIdCnt[linkIdBase] === void 0) {
            linkIdCnt[linkIdBase] = 0;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("abc78 new entry", linkIdBase, linkIdCnt[linkIdBase]);
        } else {
            linkIdCnt[linkIdBase]++;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("abc78 new entry", linkIdBase, linkIdCnt[linkIdBase]);
        }
        let linkId = linkIdBase + "-" + linkIdCnt[linkIdBase];
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("abc78 new link id to be used is", linkIdBase, linkId, linkIdCnt[linkIdBase]);
        const linkNameStart = "LS-" + edge.start;
        const linkNameEnd = "LE-" + edge.end;
        const edgeData = {
            style: "",
            labelStyle: ""
        };
        edgeData.minlen = edge.length || 1;
        if (edge.type === "arrow_open") {
            edgeData.arrowhead = "none";
        } else {
            edgeData.arrowhead = "normal";
        }
        edgeData.arrowTypeStart = "arrow_open";
        edgeData.arrowTypeEnd = "arrow_open";
        switch(edge.type){
            case "double_arrow_cross":
                edgeData.arrowTypeStart = "arrow_cross";
            case "arrow_cross":
                edgeData.arrowTypeEnd = "arrow_cross";
                break;
            case "double_arrow_point":
                edgeData.arrowTypeStart = "arrow_point";
            case "arrow_point":
                edgeData.arrowTypeEnd = "arrow_point";
                break;
            case "double_arrow_circle":
                edgeData.arrowTypeStart = "arrow_circle";
            case "arrow_circle":
                edgeData.arrowTypeEnd = "arrow_circle";
                break;
        }
        let style = "";
        let labelStyle = "";
        switch(edge.stroke){
            case "normal":
                style = "fill:none;";
                if (defaultStyle !== void 0) {
                    style = defaultStyle;
                }
                if (defaultLabelStyle !== void 0) {
                    labelStyle = defaultLabelStyle;
                }
                edgeData.thickness = "normal";
                edgeData.pattern = "solid";
                break;
            case "dotted":
                edgeData.thickness = "normal";
                edgeData.pattern = "dotted";
                edgeData.style = "fill:none;stroke-width:2px;stroke-dasharray:3;";
                break;
            case "thick":
                edgeData.thickness = "thick";
                edgeData.pattern = "solid";
                edgeData.style = "stroke-width: 3.5px;fill:none;";
                break;
            case "invisible":
                edgeData.thickness = "invisible";
                edgeData.pattern = "solid";
                edgeData.style = "stroke-width: 0;fill:none;";
                break;
        }
        if (edge.style !== void 0) {
            const styles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"])(edge.style);
            style = styles.style;
            labelStyle = styles.labelStyle;
        }
        edgeData.style = edgeData.style += style;
        edgeData.labelStyle = edgeData.labelStyle += labelStyle;
        if (edge.interpolate !== void 0) {
            edgeData.curve = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["n"])(edge.interpolate, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__["curveLinear"]);
        } else if (edges.defaultInterpolate !== void 0) {
            edgeData.curve = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["n"])(edges.defaultInterpolate, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__["curveLinear"]);
        } else {
            edgeData.curve = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["n"])(conf.curve, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__["curveLinear"]);
        }
        if (edge.text === void 0) {
            if (edge.style !== void 0) {
                edgeData.arrowheadStyle = "fill: #333";
            }
        } else {
            edgeData.arrowheadStyle = "fill: #333";
            edgeData.labelpos = "c";
        }
        edgeData.labelType = edge.labelType;
        edgeData.label = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["r"])(edge.text.replace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["e"].lineBreakRegex, "\n"), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])());
        if (edge.style === void 0) {
            edgeData.style = edgeData.style || "stroke: #333; stroke-width: 1.5px;fill:none;";
        }
        edgeData.labelStyle = edgeData.labelStyle.replace("color:", "fill:");
        edgeData.id = linkId;
        edgeData.classes = "flowchart-link " + linkNameStart + " " + linkNameEnd;
        g.setEdge(edge.start, edge.end, edgeData, cnt);
    }
};
const getClasses = function(text, diagObj) {
    return diagObj.db.getClasses();
};
const draw = async function(text, id, _version, diagObj) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Drawing flowchart");
    let dir = diagObj.db.getDirection();
    if (dir === void 0) {
        dir = "TD";
    }
    const { securityLevel, flowchart: conf2 } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
    const nodeSpacing = conf2.nodeSpacing || 50;
    const rankSpacing = conf2.rankSpacing || 50;
    let sandboxElement;
    if (securityLevel === "sandbox") {
        sandboxElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("#i" + id);
    }
    const root = securityLevel === "sandbox" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(sandboxElement.nodes()[0].contentDocument.body) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("body");
    const doc = securityLevel === "sandbox" ? sandboxElement.nodes()[0].contentDocument : document;
    const g = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$graph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Graph"]({
        multigraph: true,
        compound: true
    }).setGraph({
        rankdir: dir,
        nodesep: nodeSpacing,
        ranksep: rankSpacing,
        marginx: 0,
        marginy: 0
    }).setDefaultEdgeLabel(function() {
        return {};
    });
    let subG;
    const subGraphs = diagObj.db.getSubGraphs();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Subgraphs - ", subGraphs);
    for(let i2 = subGraphs.length - 1; i2 >= 0; i2--){
        subG = subGraphs[i2];
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Subgraph - ", subG);
        diagObj.db.addVertex(subG.id, {
            text: subG.title,
            type: subG.labelType
        }, "group", void 0, subG.classes, subG.dir);
    }
    const vert = diagObj.db.getVertices();
    const edges = diagObj.db.getEdges();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Edges", edges);
    let i = 0;
    for(i = subGraphs.length - 1; i >= 0; i--){
        subG = subGraphs[i];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$selectAll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__selectAll$3e$__["selectAll"])("cluster").append("text");
        for(let j = 0; j < subG.nodes.length; j++){
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Setting up subgraphs", subG.nodes[j], subG.id);
            g.setParent(subG.nodes[j], subG.id);
        }
    }
    await addVertices(vert, g, id, root, doc, diagObj);
    await addEdges(edges, g);
    const svg = root.select(`[id="${id}"]`);
    const element = root.select("#" + id + " g");
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$index$2d$01f381cb$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["r"])(element, g, [
        "point",
        "circle",
        "cross"
    ], "flowchart", id);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["u"].insertTitle(svg, "flowchartTitleText", conf2.titleTopMargin, diagObj.db.getDiagramTitle());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["o"])(g, svg, conf2.diagramPadding, conf2.useMaxWidth);
    diagObj.db.indexNodes("subGraph" + i);
    if (!conf2.htmlLabels) {
        const labels = doc.querySelectorAll('[id="' + id + '"] .edgeLabel .label');
        for (const label of labels){
            const dim = label.getBBox();
            const rect = doc.createElementNS("http://www.w3.org/2000/svg", "rect");
            rect.setAttribute("rx", 0);
            rect.setAttribute("ry", 0);
            rect.setAttribute("width", dim.width);
            rect.setAttribute("height", dim.height);
            label.insertBefore(rect, label.firstChild);
        }
    }
    const keys = Object.keys(vert);
    keys.forEach(function(key) {
        const vertex = vert[key];
        if (vertex.link) {
            const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("#" + id + ' [id="' + key + '"]');
            if (node) {
                const link = doc.createElementNS("http://www.w3.org/2000/svg", "a");
                link.setAttributeNS("http://www.w3.org/2000/svg", "class", vertex.classes.join(" "));
                link.setAttributeNS("http://www.w3.org/2000/svg", "href", vertex.link);
                link.setAttributeNS("http://www.w3.org/2000/svg", "rel", "noopener");
                if (securityLevel === "sandbox") {
                    link.setAttributeNS("http://www.w3.org/2000/svg", "target", "_top");
                } else if (vertex.linkTarget) {
                    link.setAttributeNS("http://www.w3.org/2000/svg", "target", vertex.linkTarget);
                }
                const linkNode = node.insert(function() {
                    return link;
                }, ":first-child");
                const shape = node.select(".label-container");
                if (shape) {
                    linkNode.append(function() {
                        return shape.node();
                    });
                }
                const label = node.select(".label");
                if (label) {
                    linkNode.append(function() {
                        return label.node();
                    });
                }
            }
        }
    });
};
const flowRendererV2 = {
    setConf,
    addVertices,
    addEdges,
    getClasses,
    draw
};
const fade = (color, opacity)=>{
    const channel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$channel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__channel$3e$__["channel"];
    const r = channel(color, "r");
    const g = channel(color, "g");
    const b = channel(color, "b");
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$rgba$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__rgba$3e$__["rgba"])(r, g, b, opacity);
};
const getStyles = (options)=>`.label {
    font-family: ${options.fontFamily};
    color: ${options.nodeTextColor || options.textColor};
  }
  .cluster-label text {
    fill: ${options.titleColor};
  }
  .cluster-label span,p {
    color: ${options.titleColor};
  }

  .label text,span,p {
    fill: ${options.nodeTextColor || options.textColor};
    color: ${options.nodeTextColor || options.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${options.mainBkg};
    stroke: ${options.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .katex path {
    fill: #000;
    stroke: #000;
    stroke-width: 1px;
  }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${options.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${options.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${options.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${options.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${options.edgeLabelBackground};
      fill: ${options.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${fade(options.edgeLabelBackground, 0.5)};
    // background-color: 
  }

  .cluster rect {
    fill: ${options.clusterBkg};
    stroke: ${options.clusterBorder};
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${options.titleColor};
  }

  .cluster span,p {
    color: ${options.titleColor};
  }
  /* .cluster div {
    color: ${options.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${options.fontFamily};
    font-size: 12px;
    background: ${options.tertiaryColor};
    border: 1px solid ${options.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${options.textColor};
  }
`;
const flowStyles = getStyles;
;
}}),
"[project]/node_modules/mermaid/dist/flowDiagram-b222e15a.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "diagram": (()=>diagram)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$flowDb$2d$c1833063$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/flowDb-c1833063.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$graph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/graph.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-client] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/curve/linear.js [app-client] (ecmascript) <export default as curveLinear>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$selectAll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__selectAll$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/selectAll.js [app-client] (ecmascript) <export default as selectAll>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$render$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre-js/render.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre-js/util.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$label$2f$add$2d$html$2d$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre-js/label/add-html-label.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre-js/intersect/intersect-polygon.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$rect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre-js/intersect/intersect-rect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$styles$2d$483fbfea$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/styles-483fbfea.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ts$2d$dedent$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ts-dedent/esm/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@braintree/sanitize-url/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dompurify/dist/purify.es.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/dagre/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$json$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dagre-d3-es/src/graphlib/json.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function question(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const s = (w + h) * 0.9;
    const points = [
        {
            x: s / 2,
            y: 0
        },
        {
            x: s,
            y: -s / 2
        },
        {
            x: s / 2,
            y: -s
        },
        {
            x: 0,
            y: -s / 2
        }
    ];
    const shapeSvg = insertPolygonShape(parent, s, s, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function hexagon(parent, bbox, node) {
    const f = 4;
    const h = bbox.height;
    const m = h / f;
    const w = bbox.width + 2 * m;
    const points = [
        {
            x: m,
            y: 0
        },
        {
            x: w - m,
            y: 0
        },
        {
            x: w,
            y: -h / 2
        },
        {
            x: w - m,
            y: -h
        },
        {
            x: m,
            y: -h
        },
        {
            x: 0,
            y: -h / 2
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function rect_left_inv_arrow(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const points = [
        {
            x: -h / 2,
            y: 0
        },
        {
            x: w,
            y: 0
        },
        {
            x: w,
            y: -h
        },
        {
            x: -h / 2,
            y: -h
        },
        {
            x: 0,
            y: -h / 2
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function lean_right(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const points = [
        {
            x: -2 * h / 6,
            y: 0
        },
        {
            x: w - h / 6,
            y: 0
        },
        {
            x: w + 2 * h / 6,
            y: -h
        },
        {
            x: h / 6,
            y: -h
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function lean_left(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const points = [
        {
            x: 2 * h / 6,
            y: 0
        },
        {
            x: w + h / 6,
            y: 0
        },
        {
            x: w - 2 * h / 6,
            y: -h
        },
        {
            x: -h / 6,
            y: -h
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function trapezoid(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const points = [
        {
            x: -2 * h / 6,
            y: 0
        },
        {
            x: w + 2 * h / 6,
            y: 0
        },
        {
            x: w - h / 6,
            y: -h
        },
        {
            x: h / 6,
            y: -h
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function inv_trapezoid(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const points = [
        {
            x: h / 6,
            y: 0
        },
        {
            x: w - h / 6,
            y: 0
        },
        {
            x: w + 2 * h / 6,
            y: -h
        },
        {
            x: -2 * h / 6,
            y: -h
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function rect_right_inv_arrow(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const points = [
        {
            x: 0,
            y: 0
        },
        {
            x: w + h / 2,
            y: 0
        },
        {
            x: w,
            y: -h / 2
        },
        {
            x: w + h / 2,
            y: -h
        },
        {
            x: 0,
            y: -h
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function stadium(parent, bbox, node) {
    const h = bbox.height;
    const w = bbox.width + h / 4;
    const shapeSvg = parent.insert("rect", ":first-child").attr("rx", h / 2).attr("ry", h / 2).attr("x", -w / 2).attr("y", -h / 2).attr("width", w).attr("height", h);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$rect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectRect"])(node, point);
    };
    return shapeSvg;
}
function subroutine(parent, bbox, node) {
    const w = bbox.width;
    const h = bbox.height;
    const points = [
        {
            x: 0,
            y: 0
        },
        {
            x: w,
            y: 0
        },
        {
            x: w,
            y: -h
        },
        {
            x: 0,
            y: -h
        },
        {
            x: 0,
            y: 0
        },
        {
            x: -8,
            y: 0
        },
        {
            x: w + 8,
            y: 0
        },
        {
            x: w + 8,
            y: -h
        },
        {
            x: -8,
            y: -h
        },
        {
            x: -8,
            y: 0
        }
    ];
    const shapeSvg = insertPolygonShape(parent, w, h, points);
    node.intersect = function(point) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$polygon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectPolygon"])(node, points, point);
    };
    return shapeSvg;
}
function cylinder(parent, bbox, node) {
    const w = bbox.width;
    const rx = w / 2;
    const ry = rx / (2.5 + w / 50);
    const h = bbox.height + ry;
    const shape = "M 0," + ry + " a " + rx + "," + ry + " 0,0,0 " + w + " 0 a " + rx + "," + ry + " 0,0,0 " + -w + " 0 l 0," + h + " a " + rx + "," + ry + " 0,0,0 " + w + " 0 l 0," + -h;
    const shapeSvg = parent.attr("label-offset-y", ry).insert("path", ":first-child").attr("d", shape).attr("transform", "translate(" + -w / 2 + "," + -(h / 2 + ry) + ")");
    node.intersect = function(point) {
        const pos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$intersect$2f$intersect$2d$rect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intersectRect"])(node, point);
        const x = pos.x - node.x;
        if (rx != 0 && (Math.abs(x) < node.width / 2 || Math.abs(x) == node.width / 2 && Math.abs(pos.y - node.y) > node.height / 2 - ry)) {
            let y = ry * ry * (1 - x * x / (rx * rx));
            if (y != 0) {
                y = Math.sqrt(y);
            }
            y = ry - y;
            if (point.y - node.y > 0) {
                y = -y;
            }
            pos.y += y;
        }
        return pos;
    };
    return shapeSvg;
}
function addToRender(render2) {
    render2.shapes().question = question;
    render2.shapes().hexagon = hexagon;
    render2.shapes().stadium = stadium;
    render2.shapes().subroutine = subroutine;
    render2.shapes().cylinder = cylinder;
    render2.shapes().rect_left_inv_arrow = rect_left_inv_arrow;
    render2.shapes().lean_right = lean_right;
    render2.shapes().lean_left = lean_left;
    render2.shapes().trapezoid = trapezoid;
    render2.shapes().inv_trapezoid = inv_trapezoid;
    render2.shapes().rect_right_inv_arrow = rect_right_inv_arrow;
}
function addToRenderV2(addShape) {
    addShape({
        question
    });
    addShape({
        hexagon
    });
    addShape({
        stadium
    });
    addShape({
        subroutine
    });
    addShape({
        cylinder
    });
    addShape({
        rect_left_inv_arrow
    });
    addShape({
        lean_right
    });
    addShape({
        lean_left
    });
    addShape({
        trapezoid
    });
    addShape({
        inv_trapezoid
    });
    addShape({
        rect_right_inv_arrow
    });
}
function insertPolygonShape(parent, w, h, points) {
    return parent.insert("polygon", ":first-child").attr("points", points.map(function(d) {
        return d.x + "," + d.y;
    }).join(" ")).attr("transform", "translate(" + -w / 2 + "," + h / 2 + ")");
}
const flowChartShapes = {
    addToRender,
    addToRenderV2
};
const conf = {};
const setConf = function(cnf) {
    const keys = Object.keys(cnf);
    for (const key of keys){
        conf[key] = cnf[key];
    }
};
const addVertices = async function(vert, g, svgId, root, _doc, diagObj) {
    const svg = !root ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(`[id="${svgId}"]`) : root.select(`[id="${svgId}"]`);
    const doc = !_doc ? document : _doc;
    const keys = Object.keys(vert);
    for (const id of keys){
        const vertex = vert[id];
        let classStr = "default";
        if (vertex.classes.length > 0) {
            classStr = vertex.classes.join(" ");
        }
        const styles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"])(vertex.styles);
        let vertexText = vertex.text !== void 0 ? vertex.text : vertex.id;
        let vertexNode;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
            const node = {
                label: await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["r"])(vertexText.replace(/fa[blrs]?:fa-[\w-]+/g, // cspell:disable-line
                (s)=>`<i class='${s.replace(":", " ")}'></i>`), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])())
            };
            vertexNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$label$2f$add$2d$html$2d$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addHtmlLabel"])(svg, node).node();
            vertexNode.parentNode.removeChild(vertexNode);
        } else {
            const svgLabel = doc.createElementNS("http://www.w3.org/2000/svg", "text");
            svgLabel.setAttribute("style", styles.labelStyle.replace("color:", "fill:"));
            const rows = vertexText.split(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["e"].lineBreakRegex);
            for (const row of rows){
                const tspan = doc.createElementNS("http://www.w3.org/2000/svg", "tspan");
                tspan.setAttributeNS("http://www.w3.org/XML/1998/namespace", "xml:space", "preserve");
                tspan.setAttribute("dy", "1em");
                tspan.setAttribute("x", "1");
                tspan.textContent = row;
                svgLabel.appendChild(tspan);
            }
            vertexNode = svgLabel;
        }
        let radius = 0;
        let _shape = "";
        switch(vertex.type){
            case "round":
                radius = 5;
                _shape = "rect";
                break;
            case "square":
                _shape = "rect";
                break;
            case "diamond":
                _shape = "question";
                break;
            case "hexagon":
                _shape = "hexagon";
                break;
            case "odd":
                _shape = "rect_left_inv_arrow";
                break;
            case "lean_right":
                _shape = "lean_right";
                break;
            case "lean_left":
                _shape = "lean_left";
                break;
            case "trapezoid":
                _shape = "trapezoid";
                break;
            case "inv_trapezoid":
                _shape = "inv_trapezoid";
                break;
            case "odd_right":
                _shape = "rect_left_inv_arrow";
                break;
            case "circle":
                _shape = "circle";
                break;
            case "ellipse":
                _shape = "ellipse";
                break;
            case "stadium":
                _shape = "stadium";
                break;
            case "subroutine":
                _shape = "subroutine";
                break;
            case "cylinder":
                _shape = "cylinder";
                break;
            case "group":
                _shape = "rect";
                break;
            default:
                _shape = "rect";
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Adding node", vertex.id, vertex.domId);
        g.setNode(diagObj.db.lookUpDomId(vertex.id), {
            labelType: "svg",
            labelStyle: styles.labelStyle,
            shape: _shape,
            label: vertexNode,
            rx: radius,
            ry: radius,
            class: classStr,
            style: styles.style,
            id: diagObj.db.lookUpDomId(vertex.id)
        });
    }
};
const addEdges = async function(edges, g, diagObj) {
    let cnt = 0;
    let defaultStyle;
    let defaultLabelStyle;
    if (edges.defaultStyle !== void 0) {
        const defaultStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"])(edges.defaultStyle);
        defaultStyle = defaultStyles.style;
        defaultLabelStyle = defaultStyles.labelStyle;
    }
    for (const edge of edges){
        cnt++;
        const linkId = "L-" + edge.start + "-" + edge.end;
        const linkNameStart = "LS-" + edge.start;
        const linkNameEnd = "LE-" + edge.end;
        const edgeData = {};
        if (edge.type === "arrow_open") {
            edgeData.arrowhead = "none";
        } else {
            edgeData.arrowhead = "normal";
        }
        let style = "";
        let labelStyle = "";
        if (edge.style !== void 0) {
            const styles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"])(edge.style);
            style = styles.style;
            labelStyle = styles.labelStyle;
        } else {
            switch(edge.stroke){
                case "normal":
                    style = "fill:none";
                    if (defaultStyle !== void 0) {
                        style = defaultStyle;
                    }
                    if (defaultLabelStyle !== void 0) {
                        labelStyle = defaultLabelStyle;
                    }
                    break;
                case "dotted":
                    style = "fill:none;stroke-width:2px;stroke-dasharray:3;";
                    break;
                case "thick":
                    style = " stroke-width: 3.5px;fill:none";
                    break;
            }
        }
        edgeData.style = style;
        edgeData.labelStyle = labelStyle;
        if (edge.interpolate !== void 0) {
            edgeData.curve = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["n"])(edge.interpolate, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__["curveLinear"]);
        } else if (edges.defaultInterpolate !== void 0) {
            edgeData.curve = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["n"])(edges.defaultInterpolate, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__["curveLinear"]);
        } else {
            edgeData.curve = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["n"])(conf.curve, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$curve$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__curveLinear$3e$__["curveLinear"]);
        }
        if (edge.text === void 0) {
            if (edge.style !== void 0) {
                edgeData.arrowheadStyle = "fill: #333";
            }
        } else {
            edgeData.arrowheadStyle = "fill: #333";
            edgeData.labelpos = "c";
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["m"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])().flowchart.htmlLabels)) {
                edgeData.labelType = "html";
                edgeData.label = `<span id="L-${linkId}" class="edgeLabel L-${linkNameStart}' L-${linkNameEnd}" style="${edgeData.labelStyle}">${await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["r"])(edge.text.replace(/fa[blrs]?:fa-[\w-]+/g, // cspell:disable-line
                (s)=>`<i class='${s.replace(":", " ")}'></i>`), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])())}</span>`;
            } else {
                edgeData.labelType = "text";
                edgeData.label = edge.text.replace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["e"].lineBreakRegex, "\n");
                if (edge.style === void 0) {
                    edgeData.style = edgeData.style || "stroke: #333; stroke-width: 1.5px;fill:none";
                }
                edgeData.labelStyle = edgeData.labelStyle.replace("color:", "fill:");
            }
        }
        edgeData.id = linkId;
        edgeData.class = linkNameStart + " " + linkNameEnd;
        edgeData.minlen = edge.length || 1;
        g.setEdge(diagObj.db.lookUpDomId(edge.start), diagObj.db.lookUpDomId(edge.end), edgeData, cnt);
    }
};
const getClasses = function(text, diagObj) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Extracting classes");
    return diagObj.db.getClasses();
};
const draw = async function(text, id, _version, diagObj) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].info("Drawing flowchart");
    const { securityLevel, flowchart: conf2 } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["c"])();
    let sandboxElement;
    if (securityLevel === "sandbox") {
        sandboxElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("#i" + id);
    }
    const root = securityLevel === "sandbox" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(sandboxElement.nodes()[0].contentDocument.body) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("body");
    const doc = securityLevel === "sandbox" ? sandboxElement.nodes()[0].contentDocument : document;
    let dir = diagObj.db.getDirection();
    if (dir === void 0) {
        dir = "TD";
    }
    const nodeSpacing = conf2.nodeSpacing || 50;
    const rankSpacing = conf2.rankSpacing || 50;
    const g = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$graphlib$2f$graph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Graph"]({
        multigraph: true,
        compound: true
    }).setGraph({
        rankdir: dir,
        nodesep: nodeSpacing,
        ranksep: rankSpacing,
        marginx: 8,
        marginy: 8
    }).setDefaultEdgeLabel(function() {
        return {};
    });
    let subG;
    const subGraphs = diagObj.db.getSubGraphs();
    for(let i2 = subGraphs.length - 1; i2 >= 0; i2--){
        subG = subGraphs[i2];
        diagObj.db.addVertex(subG.id, subG.title, "group", void 0, subG.classes);
    }
    const vert = diagObj.db.getVertices();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Get vertices", vert);
    const edges = diagObj.db.getEdges();
    let i = 0;
    for(i = subGraphs.length - 1; i >= 0; i--){
        subG = subGraphs[i];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$selectAll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__selectAll$3e$__["selectAll"])("cluster").append("text");
        for(let j = 0; j < subG.nodes.length; j++){
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["l"].warn("Setting subgraph", subG.nodes[j], diagObj.db.lookUpDomId(subG.nodes[j]), diagObj.db.lookUpDomId(subG.id));
            g.setParent(diagObj.db.lookUpDomId(subG.nodes[j]), diagObj.db.lookUpDomId(subG.id));
        }
    }
    await addVertices(vert, g, id, root, doc, diagObj);
    await addEdges(edges, g, diagObj);
    const render$1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$render$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["render"]();
    flowChartShapes.addToRender(render$1);
    render$1.arrows().none = function normal(parent, id2, edge, type) {
        const marker = parent.append("marker").attr("id", id2).attr("viewBox", "0 0 10 10").attr("refX", 9).attr("refY", 5).attr("markerUnits", "strokeWidth").attr("markerWidth", 8).attr("markerHeight", 6).attr("orient", "auto");
        const path = marker.append("path").attr("d", "M 0 0 L 0 0 L 0 0 z");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dagre$2d$d3$2d$es$2f$src$2f$dagre$2d$js$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applyStyle"])(path, edge[type + "Style"]);
    };
    render$1.arrows().normal = function normal(parent, id2) {
        const marker = parent.append("marker").attr("id", id2).attr("viewBox", "0 0 10 10").attr("refX", 9).attr("refY", 5).attr("markerUnits", "strokeWidth").attr("markerWidth", 8).attr("markerHeight", 6).attr("orient", "auto");
        marker.append("path").attr("d", "M 0 0 L 10 5 L 0 10 z").attr("class", "arrowheadPath").style("stroke-width", 1).style("stroke-dasharray", "1,0");
    };
    const svg = root.select(`[id="${id}"]`);
    const element = root.select("#" + id + " g");
    render$1(element, g);
    element.selectAll("g.node").attr("title", function() {
        return diagObj.db.getTooltip(this.id);
    });
    diagObj.db.indexNodes("subGraph" + i);
    for(i = 0; i < subGraphs.length; i++){
        subG = subGraphs[i];
        if (subG.title !== "undefined") {
            const clusterRects = doc.querySelectorAll("#" + id + ' [id="' + diagObj.db.lookUpDomId(subG.id) + '"] rect');
            const clusterEl = doc.querySelectorAll("#" + id + ' [id="' + diagObj.db.lookUpDomId(subG.id) + '"]');
            const xPos = clusterRects[0].x.baseVal.value;
            const yPos = clusterRects[0].y.baseVal.value;
            const _width = clusterRects[0].width.baseVal.value;
            const cluster = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(clusterEl[0]);
            const te = cluster.select(".label");
            te.attr("transform", `translate(${xPos + _width / 2}, ${yPos + 14})`);
            te.attr("id", id + "Text");
            for(let j = 0; j < subG.classes.length; j++){
                clusterEl[0].classList.add(subG.classes[j]);
            }
        }
    }
    if (!conf2.htmlLabels) {
        const labels = doc.querySelectorAll('[id="' + id + '"] .edgeLabel .label');
        for (const label of labels){
            const dim = label.getBBox();
            const rect = doc.createElementNS("http://www.w3.org/2000/svg", "rect");
            rect.setAttribute("rx", 0);
            rect.setAttribute("ry", 0);
            rect.setAttribute("width", dim.width);
            rect.setAttribute("height", dim.height);
            label.insertBefore(rect, label.firstChild);
        }
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["o"])(g, svg, conf2.diagramPadding, conf2.useMaxWidth);
    const keys = Object.keys(vert);
    keys.forEach(function(key) {
        const vertex = vert[key];
        if (vertex.link) {
            const node = root.select("#" + id + ' [id="' + diagObj.db.lookUpDomId(key) + '"]');
            if (node) {
                const link = doc.createElementNS("http://www.w3.org/2000/svg", "a");
                link.setAttributeNS("http://www.w3.org/2000/svg", "class", vertex.classes.join(" "));
                link.setAttributeNS("http://www.w3.org/2000/svg", "href", vertex.link);
                link.setAttributeNS("http://www.w3.org/2000/svg", "rel", "noopener");
                if (securityLevel === "sandbox") {
                    link.setAttributeNS("http://www.w3.org/2000/svg", "target", "_top");
                } else if (vertex.linkTarget) {
                    link.setAttributeNS("http://www.w3.org/2000/svg", "target", vertex.linkTarget);
                }
                const linkNode = node.insert(function() {
                    return link;
                }, ":first-child");
                const shape = node.select(".label-container");
                if (shape) {
                    linkNode.append(function() {
                        return shape.node();
                    });
                }
                const label = node.select(".label");
                if (label) {
                    linkNode.append(function() {
                        return label.node();
                    });
                }
            }
        }
    });
};
const flowRenderer = {
    setConf,
    addVertices,
    addEdges,
    getClasses,
    draw
};
const diagram = {
    parser: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$flowDb$2d$c1833063$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["p"],
    db: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$flowDb$2d$c1833063$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["f"],
    renderer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$styles$2d$483fbfea$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["f"],
    styles: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$styles$2d$483fbfea$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["a"],
    init: (cnf)=>{
        if (!cnf.flowchart) {
            cnf.flowchart = {};
        }
        cnf.flowchart.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;
        flowRenderer.setConf(cnf.flowchart);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$flowDb$2d$c1833063$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["f"].clear();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$flowDb$2d$c1833063$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["f"].setGen("gen-1");
    }
};
;
}}),
}]);

//# sourceMappingURL=node_modules_mermaid_dist_80d016cc._.js.map