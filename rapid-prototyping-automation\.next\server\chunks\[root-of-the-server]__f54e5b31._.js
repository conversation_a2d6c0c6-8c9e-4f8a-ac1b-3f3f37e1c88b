module.exports = {

"[project]/.next-internal/server/app/api/generate-technical-requirements/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/app/api/generate-technical-requirements/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@anthropic-ai/sdk/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Anthropic__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/@anthropic-ai/sdk/client.mjs [app-route] (ecmascript) <export Anthropic as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/docx/dist/index.mjs [app-route] (ecmascript)");
;
;
;
// For development environments with SSL certificate issues
if ("TURBOPACK compile-time truthy", 1) {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}
// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
    console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}
const anthropic = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Anthropic__as__default$3e$__["default"]({
    apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development'
});
const TECHNICAL_REQUIREMENTS_PROMPT = `Role:
You are a seasoned Solution Architect and technical writer.

Action:
1. Load and parse the content attached.   
2. Extract its sections (Executive Summary, Background & Context, Key Challenges, Core Needs, HMW Problem Statement, Constraints & Success Criteria, Next Steps).  
3. Translate those insights into a Technical Solution Document that describes what needs to be built—and how—to address the challenges and deliver a prototype.

Context:
The source document captures a Design Thinking workshop defining a problem statement. Your solution must align to that business context and success criteria.

Expectation:
Produce a polished technical analysis with these sections:

1. Title and Overview
   - Infer the title from the solution being proposed and write a catchy title that encompasses the solution.  
   - Executive Summary: Briefly restate the problem and summarize the proposed technical approach.

2. Solution Architecture Overview  
   - High-level system components and interactions
   - Cloud-native architecture approach
   - Integration strategy

3. Component Design  
   - For each major component:  
     - Purpose and functionality
     - Technology choices and rationale
     - Key interfaces and protocols  
     - Data schemas and formats
     - Scalability considerations

4. Data Flow & Integration  
   - Step-by-step flow from source systems → ingestion → processing → storage → visualization
   - Integration points with data sources based on the industry and client context
   - Real-time processing requirements

5. Security & Compliance  
   - Authentication and authorization model 
   - Data encryption at rest and in transit  
   - Audit logging and compliance requirements
   - Privacy and data protection measures

6. Non-Functional Requirements  
   - Scalability targets and auto-scaling strategy
   - Availability and resilience (SLA targets)
   - Performance benchmarks and optimization
   - Monitoring, logging, and supportability

7. Prototype Scope & MVP Features  
   - Minimum viable product capabilities to validate the concept
   - Core features for initial release
   - Success metrics and validation criteria
   - Timeline and development milestones

8. Implementation Roadmap
   - Phase 1: Foundation and core infrastructure
   - Phase 2: Core functionality and integrations
   - Phase 3: Advanced features and optimization
   - Risk mitigation strategies

9. Next Steps & Recommendations  
   - 3–5 action items to move from design → prototype build
   - Technology spike tasks or POC validations
   - Resource requirements and team structure

Please provide a comprehensive technical solution that addresses all the challenges identified in the problem statement while being practical and implementable.`;
async function POST(request) {
    try {
        const { problemStatementContent } = await request.json();
        if (!problemStatementContent) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No problem statement content provided'
            }, {
                status: 400
            });
        }
        // Check if API key is configured
        if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
            console.error('ANTHROPIC_API_KEY is not properly configured - creating demo document');
            // For development, create demo markdown content
            const demoContent = createDemoTechnicalMarkdown();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                content: demoContent,
                message: 'Demo technical requirements document generated. Configure Claude API key for real document generation.'
            });
        }
        console.log('Generating technical requirements from problem statement...');
        console.log(`Problem statement content length: ${problemStatementContent.length} characters`);
        // Call Claude API
        console.log('Calling Claude API with Sonnet 4 for technical requirements...');
        const fullPrompt = `${TECHNICAL_REQUIREMENTS_PROMPT}\n\nProblem Statement Document Content:\n${problemStatementContent}`;
        console.log(`Full prompt length: ${fullPrompt.length} characters`);
        let response;
        try {
            response = await anthropic.messages.create({
                model: 'claude-sonnet-4-20250514',
                max_tokens: 10000,
                messages: [
                    {
                        role: 'user',
                        content: fullPrompt
                    }
                ]
            });
            console.log('Claude API call successful for technical requirements');
        } catch (apiError) {
            console.error('Claude API Error:', apiError);
            // Handle specific API errors
            if (apiError.status === 401) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid API key. Please check your Anthropic API key configuration.'
                }, {
                    status: 401
                });
            } else if (apiError.status === 429) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Rate limit exceeded. Please try again in a few minutes.'
                }, {
                    status: 429
                });
            } else if (apiError.message?.includes('model')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Model not available. Your API key may not have access to Claude Sonnet 4.'
                }, {
                    status: 400
                });
            } else {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: `Claude API error: ${apiError.message || 'Unknown error'}`
                }, {
                    status: 500
                });
            }
        }
        const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
        // Format the analysis as Markdown document
        const markdownContent = formatAsTechnicalMarkdown(analysisText);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            content: markdownContent,
            message: 'Technical requirements document generated successfully with Claude Sonnet 4!'
        });
    } catch (error) {
        console.error('Error generating technical requirements:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate technical requirements document'
        }, {
            status: 500
        });
    }
}
function formatAsTechnicalMarkdown(analysisText) {
    const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    return `# Technical Solution Document

**Date:** ${currentDate}
**Generated by:** Claude Sonnet 4 AI Analysis

---

${analysisText}

---

*This technical solution document was automatically generated using AI analysis. Please review and validate the technical specifications before proceeding with implementation.*`;
}
function createDemoTechnicalMarkdown() {
    const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    return `# Technical Solution Document

**Date:** ${currentDate}
**Generated by:** Demo Mode (Configure Claude API key for real analysis)

---

## Solution Architecture Overview

This is a demo technical solution document. To generate real technical requirements from your problem statement, please configure your Claude API key in the environment variables.

## Component Design

### Frontend Components
- **User Interface**: Modern React-based web application
- **State Management**: Redux or Context API for application state
- **Routing**: React Router for navigation
- **Styling**: Tailwind CSS for responsive design

### Backend Components
- **API Gateway**: RESTful API with Express.js
- **Authentication**: JWT-based authentication system
- **Database**: PostgreSQL for data persistence
- **Caching**: Redis for performance optimization

## Data Flow & Integration

1. **User Interaction**: Frontend captures user input
2. **API Processing**: Backend validates and processes requests
3. **Data Storage**: Information persisted to database
4. **Response**: Results returned to frontend for display

## Security & Compliance

- **Authentication**: Multi-factor authentication support
- **Authorization**: Role-based access control
- **Data Encryption**: TLS 1.3 for data in transit
- **Compliance**: GDPR and SOC 2 compliance framework

## Non-Functional Requirements

- **Performance**: Sub-200ms API response times
- **Scalability**: Support for 10,000+ concurrent users
- **Availability**: 99.9% uptime SLA
- **Security**: Regular security audits and penetration testing

## Next Steps

1. Configure Claude API key for real document generation
2. Review and validate technical specifications
3. Proceed to architecture diagram generation
4. Generate implementation prompts for development

---

*This is a demo document. Configure your Claude API key to generate real technical requirements from problem statements.*`;
}
async function createTechnicalRequirementsDocument(analysisText) {
    // Parse the analysis text to extract structured information
    const sections = parseTechnicalAnalysisText(analysisText);
    const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"]({
        sections: [
            {
                properties: {},
                children: [
                    // Title Page
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.title || "Technical Solution Document",
                                bold: true,
                                size: 32,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].TITLE,
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 400
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: `Date: ${new Date().toLocaleDateString()}`,
                                size: 24
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Prepared by: Solution Architect",
                                size: 20,
                                italics: true
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 800
                        }
                    }),
                    // Executive Summary
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Executive Summary",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.executiveSummary || "Technical solution summary will be generated based on problem statement analysis.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Solution Architecture Overview
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Solution Architecture Overview",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.architecture || "Cloud-native architecture overview and system components will be detailed here.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Component Design
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Component Design",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.components || "Detailed component design including technology choices and interfaces.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Data Flow & Integration
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Data Flow & Integration",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.dataFlow || "Data flow and integration patterns for the solution.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Security & Compliance
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Security & Compliance",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.security || "Security architecture and compliance requirements.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Non-Functional Requirements
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Non-Functional Requirements",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.nonFunctional || "Performance, scalability, and reliability requirements.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Prototype Scope & MVP Features
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Prototype Scope & MVP Features",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.mvp || "Minimum viable product scope and key features for validation.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Next Steps & Recommendations
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Next Steps & Recommendations",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.nextSteps || "Implementation roadmap and recommended next steps.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Full Analysis
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Detailed Technical Analysis",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: analysisText,
                                size: 20
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    })
                ]
            }
        ]
    });
    return doc;
}
function parseTechnicalAnalysisText(text) {
    const sections = {};
    // Try to extract key sections from the Claude response
    const titleMatch = text.match(/title[:\s]*([^\n]+)/i);
    sections.title = titleMatch ? titleMatch[1].trim() : null;
    const summaryMatch = text.match(/executive summary[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.executiveSummary = summaryMatch ? summaryMatch[1].trim() : null;
    const architectureMatch = text.match(/architecture[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.architecture = architectureMatch ? architectureMatch[1].trim() : null;
    const componentsMatch = text.match(/component[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.components = componentsMatch ? componentsMatch[1].trim() : null;
    const dataFlowMatch = text.match(/data flow[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.dataFlow = dataFlowMatch ? dataFlowMatch[1].trim() : null;
    const securityMatch = text.match(/security[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.security = securityMatch ? securityMatch[1].trim() : null;
    const nonFunctionalMatch = text.match(/non-functional[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.nonFunctional = nonFunctionalMatch ? nonFunctionalMatch[1].trim() : null;
    const mvpMatch = text.match(/mvp|prototype[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.mvp = mvpMatch ? mvpMatch[1].trim() : null;
    const stepsMatch = text.match(/next steps[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.nextSteps = stepsMatch ? stepsMatch[1].trim() : null;
    return sections;
}
async function createMockTechnicalDocument() {
    const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"]({
        sections: [
            {
                properties: {},
                children: [
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Technical Solution Document - Demo",
                                bold: true,
                                size: 32,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].TITLE,
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 400
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "⚠️ Demo Mode - Claude API Not Configured",
                                bold: true,
                                size: 24,
                                color: "DC2626"
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 400
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "This is a demonstration document. Configure your Anthropic API key to generate real technical requirements.",
                                size: 22,
                                color: "DC2626"
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 600
                        }
                    })
                ]
            }
        ]
    });
    return doc;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f54e5b31._.js.map