import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
  console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

const IMPLEMENTATION_PROMPTS_PROMPT = `You are a senior full-stack developer and DevOps engineer specializing in creating detailed implementation prompts for AI-powered development tools like Loveable AI.

Your task is to analyze the technical requirements document and generate four specific, actionable prompts that can be copy-pasted directly into Loveable AI to build a complete prototype solution.

IMPORTANT: Generate exactly four distinct prompts in the following format:

=== FRONTEND PROMPT ===
[Detailed frontend implementation prompt for Loveable AI]

=== BACKEND API PROMPT ===
[Detailed backend API implementation prompt for Loveable AI]

=== DATABASE SCHEMA PROMPT ===
[Detailed database schema implementation prompt for Loveable AI]

=== DEVOPS DEPLOYMENT ===
[JSON configuration for Ansible/Terraform deployment]

For each prompt, follow these guidelines:

**FRONTEND PROMPT:**
- Specify the exact UI framework (React, Vue, Angular, etc.)
- Detail all pages, components, and user flows
- Include styling framework (Tailwind, Material-UI, etc.)
- Specify responsive design requirements
- Include authentication and state management
- Detail API integration points
- Include accessibility requirements

**BACKEND API PROMPT:**
- Specify the backend technology stack
- Detail all API endpoints with methods and parameters
- Include authentication and authorization logic
- Specify data validation and error handling
- Include middleware and security measures
- Detail database integration
- Include logging and monitoring

**DATABASE SCHEMA PROMPT:**
- Specify the database type (PostgreSQL, MongoDB, etc.)
- Detail all tables/collections with fields and types
- Include relationships and constraints
- Specify indexes for performance
- Include data migration strategies
- Detail backup and recovery procedures

**DEVOPS DEPLOYMENT:**
- Provide a complete JSON configuration
- Include infrastructure as code (Terraform or Ansible)
- Specify cloud provider and services
- Include CI/CD pipeline configuration
- Detail monitoring and logging setup
- Include security configurations
- Specify scaling and load balancing

Each prompt should be:
- Specific and actionable
- Ready to copy-paste into Loveable AI
- Include all necessary technical details
- Follow best practices for the technology stack
- Include error handling and edge cases
- Be production-ready focused

Analyze the technical requirements and create comprehensive implementation prompts.`;

export async function POST(request: NextRequest) {
  try {
    const { technicalRequirementsContent } = await request.json();
    
    if (!technicalRequirementsContent) {
      return NextResponse.json({ error: 'No technical requirements content provided' }, { status: 400 });
    }

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo prompts');
      
      // For development, return demo prompts
      return NextResponse.json({
        success: true,
        prompts: {
          frontend: createDemoFrontendPrompt(),
          backend: createDemoBackendPrompt(),
          database: createDemoDatabasePrompt(),
          devops: createDemoDevOpsConfig()
        },
        message: 'Demo implementation prompts generated. Configure Claude API key for real prompt generation.'
      });
    }

    console.log('Generating implementation prompts from technical requirements...');
    console.log(`Technical requirements content length: ${technicalRequirementsContent.length} characters`);

    // Call Claude API with Sonnet 4
    console.log('Calling Claude API with Sonnet 4 for implementation prompts...');
    const fullPrompt = `${IMPLEMENTATION_PROMPTS_PROMPT}\n\nTechnical Requirements Document Content:\n${technicalRequirementsContent}`;
    console.log(`Full prompt length: ${fullPrompt.length} characters`);
    
    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 4000,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude API call successful for implementation prompts');
    } catch (apiError: any) {
      console.error('Claude API Error:', apiError);
      
      // Handle specific API errors
      if (apiError.status === 401) {
        return NextResponse.json({ 
          error: 'Invalid API key. Please check your Anthropic API key configuration.' 
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({ 
          error: 'Rate limit exceeded. Please try again in a few minutes.' 
        }, { status: 429 });
      } else if (apiError.message?.includes('model')) {
        return NextResponse.json({ 
          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.' 
        }, { status: 400 });
      } else {
        return NextResponse.json({ 
          error: `Claude API error: ${apiError.message || 'Unknown error'}` 
        }, { status: 500 });
      }
    }

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
    
    console.log('=== CLAUDE PROMPTS RESPONSE DEBUG ===');
    console.log('Full response length:', analysisText.length);
    console.log('Response preview:', analysisText.substring(0, 300));
    console.log('=== END DEBUG ===');
    
    // Parse the response to extract the four prompts
    const parsedPrompts = parseImplementationPrompts(analysisText);
    
    console.log('Parsed prompts:', {
      frontendLength: parsedPrompts.frontend.length,
      backendLength: parsedPrompts.backend.length,
      databaseLength: parsedPrompts.database.length,
      devopsLength: parsedPrompts.devops.length
    });
    
    return NextResponse.json({
      success: true,
      prompts: parsedPrompts,
      fullResponse: analysisText,
      message: 'Implementation prompts generated successfully with Claude Sonnet 4!'
    });

  } catch (error) {
    console.error('Error generating implementation prompts:', error);
    return NextResponse.json(
      { error: 'Failed to generate implementation prompts' }, 
      { status: 500 }
    );
  }
}

function parseImplementationPrompts(text: string) {
  const result = {
    frontend: '',
    backend: '',
    database: '',
    devops: ''
  };
  
  console.log('Parsing implementation prompts, length:', text.length);
  
  // Extract Frontend prompt
  const frontendMatch = text.match(/=== FRONTEND PROMPT ===\s*([\s\S]*?)(?=\s*=== [A-Z]|$)/i);
  if (frontendMatch) {
    result.frontend = frontendMatch[1].trim();
  }
  
  // Extract Backend API prompt
  const backendMatch = text.match(/=== BACKEND API PROMPT ===\s*([\s\S]*?)(?=\s*=== [A-Z]|$)/i);
  if (backendMatch) {
    result.backend = backendMatch[1].trim();
  }
  
  // Extract Database Schema prompt
  const databaseMatch = text.match(/=== DATABASE SCHEMA PROMPT ===\s*([\s\S]*?)(?=\s*=== [A-Z]|$)/i);
  if (databaseMatch) {
    result.database = databaseMatch[1].trim();
  }
  
  // Extract DevOps Deployment
  const devopsMatch = text.match(/=== DEVOPS DEPLOYMENT ===\s*([\s\S]*?)(?=\s*=== [A-Z]|$)/i);
  if (devopsMatch) {
    result.devops = devopsMatch[1].trim();
  }
  
  // If structured format not found, try to extract any JSON for DevOps
  if (!result.devops) {
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      result.devops = jsonMatch[0];
    }
  }
  
  console.log('Parsed prompts result:', {
    frontendLength: result.frontend.length,
    backendLength: result.backend.length,
    databaseLength: result.database.length,
    devopsLength: result.devops.length
  });
  
  return result;
}

// Demo functions for when API key is not configured
function createDemoFrontendPrompt(): string {
  return `Create a modern React application with TypeScript and Tailwind CSS for a cloud-native solution dashboard.

Requirements:
- Landing page with hero section and feature overview
- User authentication (login/register) with JWT
- Main dashboard with navigation sidebar
- Data visualization components using Chart.js
- Responsive design for mobile and desktop
- Real-time updates using WebSocket connection
- Form validation and error handling
- Loading states and skeleton screens
- Accessibility compliance (WCAG 2.1)

Tech Stack:
- React 18 with TypeScript
- Tailwind CSS for styling
- React Router for navigation
- React Query for API state management
- Chart.js for data visualization
- Socket.io-client for real-time features

Configure API integration for backend endpoints and implement proper error boundaries.`;
}

function createDemoBackendPrompt(): string {
  return `Build a Node.js REST API with Express and TypeScript for a cloud-native solution backend.

Requirements:
- RESTful API with proper HTTP methods and status codes
- JWT authentication and authorization middleware
- User management (CRUD operations)
- Data processing and analytics endpoints
- File upload and storage capabilities
- Real-time WebSocket support
- Input validation and sanitization
- Error handling and logging
- API rate limiting and security headers
- Database integration with connection pooling

Tech Stack:
- Node.js with Express and TypeScript
- JWT for authentication
- Bcrypt for password hashing
- Multer for file uploads
- Socket.io for real-time features
- Helmet for security headers
- Express-rate-limit for rate limiting
- Winston for logging

Implement proper middleware chain and database integration patterns.`;
}

function createDemoDatabasePrompt(): string {
  return `Design a PostgreSQL database schema for a cloud-native solution with proper relationships and indexes.

Requirements:
- User management tables with roles and permissions
- Data storage tables with proper relationships
- Audit logging and versioning
- Performance indexes on frequently queried columns
- Data validation constraints
- Backup and recovery procedures
- Migration scripts for schema updates

Schema Design:
- Users table with authentication data
- Profiles table with user details
- Data tables with foreign key relationships
- Audit logs table for tracking changes
- Sessions table for active user sessions
- Configuration table for application settings

Include proper data types, constraints, and indexes for optimal performance.`;
}

function createDemoDevOpsConfig(): any {
  return {
    "terraform": {
      "provider": {
        "aws": {
          "region": "us-west-2"
        }
      },
      "resource": {
        "aws_instance": {
          "web_server": {
            "ami": "ami-0c55b159cbfafe1d0",
            "instance_type": "t3.micro",
            "tags": {
              "Name": "WebServer",
              "Environment": "production"
            }
          }
        },
        "aws_rds_instance": {
          "database": {
            "engine": "postgres",
            "engine_version": "13.7",
            "instance_class": "db.t3.micro",
            "allocated_storage": 20,
            "db_name": "appdb",
            "username": "dbuser",
            "password": "changeme123",
            "skip_final_snapshot": true
          }
        }
      }
    },
    "ansible": {
      "playbook": [
        {
          "name": "Deploy Application",
          "hosts": "webservers",
          "tasks": [
            {
              "name": "Install Node.js",
              "yum": {
                "name": "nodejs",
                "state": "present"
              }
            },
            {
              "name": "Deploy application",
              "copy": {
                "src": "./app",
                "dest": "/opt/app"
              }
            },
            {
              "name": "Start application service",
              "systemd": {
                "name": "app",
                "state": "started",
                "enabled": true
              }
            }
          ]
        }
      ]
    }
  };
}
