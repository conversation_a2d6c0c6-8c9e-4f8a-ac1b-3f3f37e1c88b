import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';
import mammoth from 'mammoth';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
  console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

const PROBLEM_STATEMENT_PROMPT = `You are a senior business analyst and product strategist specializing in creating comprehensive problem statement documents from conversation transcripts.

Your task is to analyze the conversation transcript and generate a professional problem statement document that includes:

1. **Executive Summary** - High-level overview of the business challenge
2. **Background & Context** - Setting and circumstances leading to this workshop
3. **Key Business Challenges** - Specific problems identified with supporting quotes
4. **Core User Needs & Pain Points** - What users are struggling with
5. **How Might We Problem Statement** - Clear, actionable problem framing
6. **Constraints & Assumptions** - Technical, business, and resource limitations
7. **Success Criteria** - How we'll measure if the solution works
8. **Next Steps & Recommendations** - Immediate actions to take
9. **Key Insights & Critical Quotes** - Important verbatim statements from stakeholders

Format the output as a professional business document with clear sections, bullet points, and direct quotes from the transcript where relevant. Use a tone that's professional yet accessible, suitable for both technical and business stakeholders.

Focus on extracting actionable insights and creating a clear problem statement that can guide solution development.`;

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    console.log(`Processing file: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);

    // Extract text content from the uploaded file
    let textContent = '';
    
    if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      // Handle .docx files
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ arrayBuffer });
      textContent = result.value;
    } else if (file.type === 'text/plain' || file.name.endsWith('.txt') || file.name.endsWith('.md')) {
      // Handle text files
      textContent = await file.text();
    } else {
      return NextResponse.json({ 
        error: 'Unsupported file type. Please upload a .txt, .md, or .docx file.' 
      }, { status: 400 });
    }

    if (!textContent.trim()) {
      return NextResponse.json({ 
        error: 'No text content found in the uploaded file.' 
      }, { status: 400 });
    }

    console.log(`Extracted text content: ${textContent.length} characters`);

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo document');
      
      // For development, return demo content
      const demoContent = createDemoMarkdownDocument(file.name);
      return NextResponse.json({
        success: true,
        content: demoContent,
        message: 'Demo problem statement generated. Configure Claude API key for real document generation.'
      });
    }

    console.log('Generating problem statement from transcript...');
    console.log(`Transcript content length: ${textContent.length} characters`);

    // Call Claude API with Sonnet 4
    console.log('Calling Claude API with Sonnet 4 for problem statement...');
    const fullPrompt = `${PROBLEM_STATEMENT_PROMPT}\n\nConversation Transcript Content:\n${textContent}`;
    console.log(`Full prompt length: ${fullPrompt.length} characters`);
    
    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 10000,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude API call successful for problem statement');
    } catch (apiError: any) {
      console.error('Claude API Error:', apiError);
      
      // Handle specific API errors
      if (apiError.status === 401) {
        return NextResponse.json({ 
          error: 'Invalid API key. Please check your Anthropic API key configuration.' 
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({ 
          error: 'Rate limit exceeded. Please try again in a few minutes.' 
        }, { status: 429 });
      } else if (apiError.message?.includes('model')) {
        return NextResponse.json({ 
          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.' 
        }, { status: 400 });
      } else {
        return NextResponse.json({ 
          error: `Claude API error: ${apiError.message || 'Unknown error'}` 
        }, { status: 500 });
      }
    }

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Format the analysis as Markdown document
    const markdownContent = formatAsMarkdownDocument(analysisText, file.name);
    
    return NextResponse.json({
      success: true,
      content: markdownContent, // Return formatted markdown content
      message: 'Problem statement document generated successfully with Claude Sonnet 4!'
    });

  } catch (error) {
    console.error('Error generating problem statement:', error);
    return NextResponse.json(
      { error: 'Failed to generate problem statement' }, 
      { status: 500 }
    );
  }
}

function formatAsMarkdownDocument(analysisText: string, originalFileName: string): string {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return `# Problem Statement Document

**Generated from:** ${originalFileName}  
**Date:** ${currentDate}  
**Generated by:** Claude Sonnet 4 AI Analysis  

---

${analysisText}

---

*This document was automatically generated using AI analysis of the provided conversation transcript. Please review and validate the content before proceeding with implementation.*`;
}

function createDemoMarkdownDocument(originalFileName: string): string {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return `# Problem Statement Document

**Generated from:** ${originalFileName}  
**Date:** ${currentDate}  
**Generated by:** Demo Mode (Configure Claude API key for real analysis)  

---

## Executive Summary

This is a demo problem statement document. To generate real problem statements from your conversation transcripts, please configure your Claude API key in the environment variables.

## Key Business Challenges

- **Demo Challenge 1**: Sample business challenge that would be extracted from your transcript
- **Demo Challenge 2**: Another example challenge with supporting context
- **Demo Challenge 3**: Technical or operational challenge identified

## Core User Needs & Pain Points

- Users need a streamlined solution for their workflow
- Current processes are manual and time-consuming
- Integration between systems is lacking

## How Might We Problem Statement

**How might we** create a solution that addresses the core business challenges while providing a seamless user experience and scalable technical architecture?

## Success Criteria

- Improved user satisfaction scores
- Reduced processing time
- Increased system reliability
- Better integration capabilities

## Next Steps & Recommendations

1. Configure Claude API key for real document generation
2. Upload your conversation transcript
3. Review and validate the generated problem statement
4. Proceed to technical requirements generation

---

*This is a demo document. Configure your Claude API key to generate real problem statements from conversation transcripts.*`;
}

// Removed Word document functions - now using Markdown format
