import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } from 'docx';
import mammoth from 'mammoth';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

const PROBLEM_STATEMENT_PROMPT = `You are a top 0.1% senior innovation consultant and professional technical writer. 

1. Load & Analyze  
   - Open and read the entire transcript file attached.  
   - Strip out all timestamps; focus only on speaker content, context, and meaning.

2. Deep Content Extraction
   - Identify every meeting attendee from the transcript  
   - Identify every business challenge the customer articulates.  
   - Capture direct quotes (paraphrased or exact) that illustrate those pain points.  
   - List all attendees from DXC proposed solution ideas, including any technical requirements mentioned.  
   - Note CTO's facilitation questions, concerns, and prioritization recommendations.

3. Craft a Polished Analysis
   - Build a structured analysis with these sections:
     1. Title and Participants
        - Infer the title from the intended solution outlined in the transcript and propose a catchy title  
        - List all attendees and their roles
     2. Executive Summary (1 paragraph)  
        - Summarize the workshop goal and outcome in 3–4 sentences—no timestamps.  
     3. Background & Context (½ page)  
        - Why this session was convened, the user's strategic imperatives, and desired outcomes.  
     4. Key Business Challenges  
        - For each challenge, include:  
          - Name of Challenge  
          - Description (2–3 sentences) with a representative quote.  
     5. Core User Needs & Pain Points  
        - List of needs and pain points with supporting details or quotes
     6. How Might We… Problem Statement  
        - Craft one compelling HMW sentence that unifies all the challenges and needs.  
     7. Constraints & Success Criteria  
        - List constraints and corresponding success criteria
     8. Next Steps & Recommendations (1 page)  
        - Three to five actionable recommendations to guide prototype/MVP design, each with rationale.  
     9. Key Insights & Quotes  
        - A list of the 5–7 most critical quotes or insights—no timestamps, just speaker name and content.

4. Output Format
   - Provide the response in structured JSON format with clear sections
   - No timestamps or file metadata in the response
   - Focus on actionable insights and clear problem articulation

Please analyze the transcript and provide a comprehensive problem statement analysis.`;

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Read file content
    let transcriptContent = '';
    
    if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
      transcriptContent = await file.text();
    } else if (file.name.endsWith('.docx')) {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ buffer: Buffer.from(arrayBuffer) });
      transcriptContent = result.value;
    } else {
      // For other formats, try to read as text
      transcriptContent = await file.text();
    }

    if (!transcriptContent.trim()) {
      return NextResponse.json({ error: 'File appears to be empty or unreadable' }, { status: 400 });
    }

    // Call Claude API
    const response = await anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 4000,
      messages: [
        {
          role: 'user',
          content: `${PROBLEM_STATEMENT_PROMPT}\n\nTranscript Content:\n${transcriptContent}`
        }
      ]
    });

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Parse the analysis and create Word document
    const doc = await createWordDocument(analysisText, file.name);
    
    // Generate the document buffer
    const buffer = await Packer.toBuffer(doc);
    
    // Return the document as a downloadable file
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': 'attachment; filename="Problem_Statement.docx"',
      },
    });

  } catch (error) {
    console.error('Error generating problem statement:', error);
    return NextResponse.json(
      { error: 'Failed to generate problem statement' }, 
      { status: 500 }
    );
  }
}

async function createWordDocument(analysisText: string, originalFileName: string): Promise<Document> {
  // Parse the analysis text to extract structured information
  const sections = parseAnalysisText(analysisText);
  
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        // Title Page
        new Paragraph({
          children: [
            new TextRun({
              text: sections.title || "Problem Statement Analysis",
              bold: true,
              size: 32,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.TITLE,
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: `Analysis Date: ${new Date().toLocaleDateString()}`,
              size: 24
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: `Source: ${originalFileName}`,
              size: 20,
              italics: true
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 800 }
        }),

        // Executive Summary
        new Paragraph({
          children: [
            new TextRun({
              text: "Executive Summary",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.executiveSummary || "Executive summary will be generated based on transcript analysis.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Background & Context
        new Paragraph({
          children: [
            new TextRun({
              text: "Background & Context",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.background || "Background and context will be extracted from the transcript analysis.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Key Business Challenges
        new Paragraph({
          children: [
            new TextRun({
              text: "Key Business Challenges",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.challenges || "Business challenges will be identified and detailed from the transcript.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Problem Statement
        new Paragraph({
          children: [
            new TextRun({
              text: "How Might We… Problem Statement",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.problemStatement || "How might we address the core challenges identified in this analysis?",
              size: 22,
              bold: true,
              color: "DC2626"
            })
          ],
          spacing: { after: 400 }
        }),

        // Next Steps
        new Paragraph({
          children: [
            new TextRun({
              text: "Next Steps & Recommendations",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.nextSteps || "Actionable recommendations will be provided based on the analysis.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Raw Analysis
        new Paragraph({
          children: [
            new TextRun({
              text: "Detailed Analysis",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: analysisText,
              size: 20
            })
          ],
          spacing: { after: 400 }
        })
      ]
    }]
  });

  return doc;
}

function parseAnalysisText(text: string) {
  // Simple parsing logic - in a real implementation, you'd want more sophisticated parsing
  const sections: any = {};
  
  // Try to extract key sections from the Claude response
  const titleMatch = text.match(/title[:\s]*([^\n]+)/i);
  sections.title = titleMatch ? titleMatch[1].trim() : null;
  
  const summaryMatch = text.match(/executive summary[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.executiveSummary = summaryMatch ? summaryMatch[1].trim() : null;
  
  const backgroundMatch = text.match(/background[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.background = backgroundMatch ? backgroundMatch[1].trim() : null;
  
  const challengesMatch = text.match(/challenges[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.challenges = challengesMatch ? challengesMatch[1].trim() : null;
  
  const problemMatch = text.match(/how might we[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.problemStatement = problemMatch ? problemMatch[1].trim() : null;
  
  const stepsMatch = text.match(/next steps[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.nextSteps = stepsMatch ? stepsMatch[1].trim() : null;
  
  return sections;
}
