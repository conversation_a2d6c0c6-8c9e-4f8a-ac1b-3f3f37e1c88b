{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/mermaid/dist/svgDrawCommon-5e1cfd1d.js"], "sourcesContent": ["import { sanitizeUrl } from \"@braintree/sanitize-url\";\nimport { J as lineBreakRegex } from \"./mermaid-6dc72991.js\";\nconst drawRect = (element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  rectData.rx !== void 0 && rectElement.attr(\"rx\", rectData.rx);\n  rectData.ry !== void 0 && rectElement.attr(\"ry\", rectData.ry);\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  rectData.class !== void 0 && rectElement.attr(\"class\", rectData.class);\n  return rectElement;\n};\nconst drawBackgroundRect = (element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n};\nconst drawText = (element, textData) => {\n  const nText = textData.text.replace(lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  textData.class !== void 0 && textElem.attr(\"class\", textData.class);\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n};\nconst drawImage = (elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n};\nconst drawEmbeddedImage = (element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n};\nconst getNoteRect = () => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n};\nconst getTextObj = () => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n};\nexport {\n  drawBackgroundRect as a,\n  drawEmbeddedImage as b,\n  drawImage as c,\n  drawRect as d,\n  getTextObj as e,\n  drawText as f,\n  getNoteRect as g\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AACA,MAAM,WAAW,CAAC,SAAS;IACzB,MAAM,cAAc,QAAQ,MAAM,CAAC;IACnC,YAAY,IAAI,CAAC,KAAK,SAAS,CAAC;IAChC,YAAY,IAAI,CAAC,KAAK,SAAS,CAAC;IAChC,YAAY,IAAI,CAAC,QAAQ,SAAS,IAAI;IACtC,YAAY,IAAI,CAAC,UAAU,SAAS,MAAM;IAC1C,YAAY,IAAI,CAAC,SAAS,SAAS,KAAK;IACxC,YAAY,IAAI,CAAC,UAAU,SAAS,MAAM;IAC1C,IAAI,SAAS,IAAI,EAAE;QACjB,YAAY,IAAI,CAAC,QAAQ,SAAS,IAAI;IACxC;IACA,SAAS,EAAE,KAAK,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,SAAS,EAAE;IAC5D,SAAS,EAAE,KAAK,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,SAAS,EAAE;IAC5D,IAAI,SAAS,KAAK,KAAK,KAAK,GAAG;QAC7B,IAAK,MAAM,WAAW,SAAS,KAAK,CAAE;YACpC,YAAY,IAAI,CAAC,SAAS,SAAS,KAAK,CAAC,QAAQ;QACnD;IACF;IACA,SAAS,KAAK,KAAK,KAAK,KAAK,YAAY,IAAI,CAAC,SAAS,SAAS,KAAK;IACrE,OAAO;AACT;AACA,MAAM,qBAAqB,CAAC,SAAS;IACnC,MAAM,WAAW;QACf,GAAG,OAAO,MAAM;QAChB,GAAG,OAAO,MAAM;QAChB,OAAO,OAAO,KAAK,GAAG,OAAO,MAAM;QACnC,QAAQ,OAAO,KAAK,GAAG,OAAO,MAAM;QACpC,MAAM,OAAO,IAAI;QACjB,QAAQ,OAAO,MAAM;QACrB,OAAO;IACT;IACA,MAAM,cAAc,SAAS,SAAS;IACtC,YAAY,KAAK;AACnB;AACA,MAAM,WAAW,CAAC,SAAS;IACzB,MAAM,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,yJAAA,CAAA,IAAc,EAAE;IACpD,MAAM,WAAW,QAAQ,MAAM,CAAC;IAChC,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC;IAC7B,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC;IAC7B,SAAS,IAAI,CAAC,SAAS;IACvB,SAAS,KAAK,CAAC,eAAe,SAAS,MAAM;IAC7C,SAAS,KAAK,KAAK,KAAK,KAAK,SAAS,IAAI,CAAC,SAAS,SAAS,KAAK;IAClE,MAAM,QAAQ,SAAS,MAAM,CAAC;IAC9B,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG,SAAS,UAAU,GAAG;IACnD,MAAM,IAAI,CAAC;IACX,OAAO;AACT;AACA,MAAM,YAAY,CAAC,MAAM,GAAG,GAAG;IAC7B,MAAM,eAAe,KAAK,MAAM,CAAC;IACjC,aAAa,IAAI,CAAC,KAAK;IACvB,aAAa,IAAI,CAAC,KAAK;IACvB,MAAM,gBAAgB,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;IAClC,aAAa,IAAI,CAAC,cAAc;AAClC;AACA,MAAM,oBAAoB,CAAC,SAAS,GAAG,GAAG;IACxC,MAAM,eAAe,QAAQ,MAAM,CAAC;IACpC,aAAa,IAAI,CAAC,KAAK;IACvB,aAAa,IAAI,CAAC,KAAK;IACvB,MAAM,gBAAgB,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;IAClC,aAAa,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,eAAe;AACrD;AACA,MAAM,cAAc;IAClB,MAAM,eAAe;QACnB,GAAG;QACH,GAAG;QACH,OAAO;QACP,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,IAAI;QACJ,IAAI;IACN;IACA,OAAO;AACT;AACA,MAAM,aAAa;IACjB,MAAM,aAAa;QACjB,GAAG;QACH,GAAG;QACH,OAAO;QACP,QAAQ;QACR,eAAe;QACf,OAAO;QACP,YAAY;QACZ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/mermaid/dist/c4Diagram-ae766693.js"], "sourcesContent": ["import { s as setAccTitle, g as getAccTitle, a as getAccDescription, b as setAccDescription, c as getConfig, d as sanitizeText, e as common, f as assignWithDepth, h as calculateTextWidth, l as log, i as configureSvgSize, w as wrapLabel, j as calculateTextHeight } from \"./mermaid-6dc72991.js\";\nimport { select } from \"d3\";\nimport { d as drawRect$1, g as getNoteRect } from \"./svgDrawCommon-5e1cfd1d.js\";\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nimport \"ts-dedent\";\nimport \"dayjs\";\nimport \"dompurify\";\nimport \"khroma\";\nimport \"lodash-es/memoize.js\";\nimport \"lodash-es/merge.js\";\nimport \"stylis\";\nimport \"lodash-es/isEmpty.js\";\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [1, 24], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 63], $V6 = [1, 64], $V7 = [1, 65], $V8 = [1, 66], $V9 = [1, 67], $Va = [1, 68], $Vb = [1, 69], $Vc = [1, 29], $Vd = [1, 30], $Ve = [1, 31], $Vf = [1, 32], $Vg = [1, 33], $Vh = [1, 34], $Vi = [1, 35], $Vj = [1, 36], $Vk = [1, 37], $Vl = [1, 38], $Vm = [1, 39], $Vn = [1, 40], $Vo = [1, 41], $Vp = [1, 42], $Vq = [1, 43], $Vr = [1, 44], $Vs = [1, 45], $Vt = [1, 46], $Vu = [1, 47], $Vv = [1, 48], $Vw = [1, 50], $Vx = [1, 51], $Vy = [1, 52], $Vz = [1, 53], $VA = [1, 54], $VB = [1, 55], $VC = [1, 56], $VD = [1, 57], $VE = [1, 58], $VF = [1, 59], $VG = [1, 60], $VH = [14, 42], $VI = [14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VJ = [12, 14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VK = [1, 82], $VL = [1, 83], $VM = [1, 84], $VN = [1, 85], $VO = [12, 14, 42], $VP = [12, 14, 33, 42], $VQ = [12, 14, 33, 42, 76, 77, 79, 80], $VR = [12, 33], $VS = [34, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"direction\": 5, \"direction_tb\": 6, \"direction_bt\": 7, \"direction_rl\": 8, \"direction_lr\": 9, \"graphConfig\": 10, \"C4_CONTEXT\": 11, \"NEWLINE\": 12, \"statements\": 13, \"EOF\": 14, \"C4_CONTAINER\": 15, \"C4_COMPONENT\": 16, \"C4_DYNAMIC\": 17, \"C4_DEPLOYMENT\": 18, \"otherStatements\": 19, \"diagramStatements\": 20, \"otherStatement\": 21, \"title\": 22, \"accDescription\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"boundaryStatement\": 29, \"boundaryStartStatement\": 30, \"boundaryStopStatement\": 31, \"boundaryStart\": 32, \"LBRACE\": 33, \"ENTERPRISE_BOUNDARY\": 34, \"attributes\": 35, \"SYSTEM_BOUNDARY\": 36, \"BOUNDARY\": 37, \"CONTAINER_BOUNDARY\": 38, \"NODE\": 39, \"NODE_L\": 40, \"NODE_R\": 41, \"RBRACE\": 42, \"diagramStatement\": 43, \"PERSON\": 44, \"PERSON_EXT\": 45, \"SYSTEM\": 46, \"SYSTEM_DB\": 47, \"SYSTEM_QUEUE\": 48, \"SYSTEM_EXT\": 49, \"SYSTEM_EXT_DB\": 50, \"SYSTEM_EXT_QUEUE\": 51, \"CONTAINER\": 52, \"CONTAINER_DB\": 53, \"CONTAINER_QUEUE\": 54, \"CONTAINER_EXT\": 55, \"CONTAINER_EXT_DB\": 56, \"CONTAINER_EXT_QUEUE\": 57, \"COMPONENT\": 58, \"COMPONENT_DB\": 59, \"COMPONENT_QUEUE\": 60, \"COMPONENT_EXT\": 61, \"COMPONENT_EXT_DB\": 62, \"COMPONENT_EXT_QUEUE\": 63, \"REL\": 64, \"BIREL\": 65, \"REL_U\": 66, \"REL_D\": 67, \"REL_L\": 68, \"REL_R\": 69, \"REL_B\": 70, \"REL_INDEX\": 71, \"UPDATE_EL_STYLE\": 72, \"UPDATE_REL_STYLE\": 73, \"UPDATE_LAYOUT_CONFIG\": 74, \"attribute\": 75, \"STR\": 76, \"STR_KEY\": 77, \"STR_VALUE\": 78, \"ATTRIBUTE\": 79, \"ATTRIBUTE_EMPTY\": 80, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"direction_tb\", 7: \"direction_bt\", 8: \"direction_rl\", 9: \"direction_lr\", 11: \"C4_CONTEXT\", 12: \"NEWLINE\", 14: \"EOF\", 15: \"C4_CONTAINER\", 16: \"C4_COMPONENT\", 17: \"C4_DYNAMIC\", 18: \"C4_DEPLOYMENT\", 22: \"title\", 23: \"accDescription\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"LBRACE\", 34: \"ENTERPRISE_BOUNDARY\", 36: \"SYSTEM_BOUNDARY\", 37: \"BOUNDARY\", 38: \"CONTAINER_BOUNDARY\", 39: \"NODE\", 40: \"NODE_L\", 41: \"NODE_R\", 42: \"RBRACE\", 44: \"PERSON\", 45: \"PERSON_EXT\", 46: \"SYSTEM\", 47: \"SYSTEM_DB\", 48: \"SYSTEM_QUEUE\", 49: \"SYSTEM_EXT\", 50: \"SYSTEM_EXT_DB\", 51: \"SYSTEM_EXT_QUEUE\", 52: \"CONTAINER\", 53: \"CONTAINER_DB\", 54: \"CONTAINER_QUEUE\", 55: \"CONTAINER_EXT\", 56: \"CONTAINER_EXT_DB\", 57: \"CONTAINER_EXT_QUEUE\", 58: \"COMPONENT\", 59: \"COMPONENT_DB\", 60: \"COMPONENT_QUEUE\", 61: \"COMPONENT_EXT\", 62: \"COMPONENT_EXT_DB\", 63: \"COMPONENT_EXT_QUEUE\", 64: \"REL\", 65: \"BIREL\", 66: \"REL_U\", 67: \"REL_D\", 68: \"REL_L\", 69: \"REL_R\", 70: \"REL_B\", 71: \"REL_INDEX\", 72: \"UPDATE_EL_STYLE\", 73: \"UPDATE_REL_STYLE\", 74: \"UPDATE_LAYOUT_CONFIG\", 76: \"STR\", 77: \"STR_KEY\", 78: \"STR_VALUE\", 79: \"ATTRIBUTE\", 80: \"ATTRIBUTE_EMPTY\" },\n    productions_: [0, [3, 1], [3, 1], [5, 1], [5, 1], [5, 1], [5, 1], [4, 1], [10, 4], [10, 4], [10, 4], [10, 4], [10, 4], [13, 1], [13, 1], [13, 2], [19, 1], [19, 2], [19, 3], [21, 1], [21, 1], [21, 2], [21, 2], [21, 1], [29, 3], [30, 3], [30, 3], [30, 4], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [31, 1], [20, 1], [20, 2], [20, 3], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 1], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [35, 1], [35, 2], [75, 1], [75, 2], [75, 1], [75, 1]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setDirection(\"TB\");\n          break;\n        case 4:\n          yy.setDirection(\"BT\");\n          break;\n        case 5:\n          yy.setDirection(\"RL\");\n          break;\n        case 6:\n          yy.setDirection(\"LR\");\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n        case 12:\n          yy.setC4Type($$[$0 - 3]);\n          break;\n        case 19:\n          yy.setTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 20:\n          yy.setAccDescription($$[$0].substring(15));\n          this.$ = $$[$0].substring(15);\n          break;\n        case 21:\n          this.$ = $$[$0].trim();\n          yy.setTitle(this.$);\n          break;\n        case 22:\n        case 23:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n        case 29:\n          $$[$0].splice(2, 0, \"ENTERPRISE\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 30:\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 31:\n          $$[$0].splice(2, 0, \"CONTAINER\");\n          yy.addContainerBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 32:\n          yy.addDeploymentNode(\"node\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 33:\n          yy.addDeploymentNode(\"nodeL\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 34:\n          yy.addDeploymentNode(\"nodeR\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 35:\n          yy.popBoundaryParseStack();\n          break;\n        case 39:\n          yy.addPersonOrSystem(\"person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.addPersonOrSystem(\"external_person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 41:\n          yy.addPersonOrSystem(\"system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 42:\n          yy.addPersonOrSystem(\"system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 43:\n          yy.addPersonOrSystem(\"system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 44:\n          yy.addPersonOrSystem(\"external_system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addPersonOrSystem(\"external_system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 46:\n          yy.addPersonOrSystem(\"external_system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 47:\n          yy.addContainer(\"container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          yy.addContainer(\"container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 49:\n          yy.addContainer(\"container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 50:\n          yy.addContainer(\"external_container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 51:\n          yy.addContainer(\"external_container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 52:\n          yy.addContainer(\"external_container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 53:\n          yy.addComponent(\"component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 54:\n          yy.addComponent(\"component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 55:\n          yy.addComponent(\"component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 56:\n          yy.addComponent(\"external_component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 57:\n          yy.addComponent(\"external_component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          yy.addComponent(\"external_component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 60:\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 61:\n          yy.addRel(\"birel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 62:\n          yy.addRel(\"rel_u\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 63:\n          yy.addRel(\"rel_d\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 64:\n          yy.addRel(\"rel_l\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 65:\n          yy.addRel(\"rel_r\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 66:\n          yy.addRel(\"rel_b\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 67:\n          $$[$0].splice(0, 1);\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 68:\n          yy.updateElStyle(\"update_el_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 69:\n          yy.updateRelStyle(\"update_rel_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 70:\n          yy.updateLayoutConfig(\"update_layout_config\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 71:\n          this.$ = [$$[$0]];\n          break;\n        case 72:\n          $$[$0].unshift($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 73:\n        case 75:\n          this.$ = $$[$0].trim();\n          break;\n        case 74:\n          let kv = {};\n          kv[$$[$0 - 1].trim()] = $$[$0].trim();\n          this.$ = kv;\n          break;\n        case 76:\n          this.$ = \"\";\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 7: [1, 6], 8: [1, 7], 9: [1, 8], 10: 4, 11: [1, 9], 15: [1, 10], 16: [1, 11], 17: [1, 12], 18: [1, 13] }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 7] }, { 1: [2, 3] }, { 1: [2, 4] }, { 1: [2, 5] }, { 1: [2, 6] }, { 12: [1, 14] }, { 12: [1, 15] }, { 12: [1, 16] }, { 12: [1, 17] }, { 12: [1, 18] }, { 13: 19, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 70, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 71, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 72, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 73, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 14: [1, 74] }, o($VH, [2, 13], { 43: 23, 29: 49, 30: 61, 32: 62, 20: 75, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VH, [2, 14]), o($VI, [2, 16], { 12: [1, 76] }), o($VH, [2, 36], { 12: [1, 77] }), o($VJ, [2, 19]), o($VJ, [2, 20]), { 25: [1, 78] }, { 27: [1, 79] }, o($VJ, [2, 23]), { 35: 80, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 86, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 87, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 88, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 89, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 90, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 91, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 92, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 93, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 94, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 95, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 96, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 97, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 98, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 99, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 100, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 101, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 102, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 103, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 104, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, o($VO, [2, 59]), { 35: 105, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 106, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 107, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 108, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 109, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 110, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 111, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 112, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 113, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 114, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 115, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 20: 116, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 12: [1, 118], 33: [1, 117] }, { 35: 119, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 120, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 121, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 122, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 123, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 124, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 125, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 14: [1, 126] }, { 14: [1, 127] }, { 14: [1, 128] }, { 14: [1, 129] }, { 1: [2, 8] }, o($VH, [2, 15]), o($VI, [2, 17], { 21: 22, 19: 130, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4 }), o($VH, [2, 37], { 19: 20, 20: 21, 21: 22, 43: 23, 29: 49, 30: 61, 32: 62, 13: 131, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VJ, [2, 21]), o($VJ, [2, 22]), o($VO, [2, 39]), o($VP, [2, 71], { 75: 81, 35: 132, 76: $VK, 77: $VL, 79: $VM, 80: $VN }), o($VQ, [2, 73]), { 78: [1, 133] }, o($VQ, [2, 75]), o($VQ, [2, 76]), o($VO, [2, 40]), o($VO, [2, 41]), o($VO, [2, 42]), o($VO, [2, 43]), o($VO, [2, 44]), o($VO, [2, 45]), o($VO, [2, 46]), o($VO, [2, 47]), o($VO, [2, 48]), o($VO, [2, 49]), o($VO, [2, 50]), o($VO, [2, 51]), o($VO, [2, 52]), o($VO, [2, 53]), o($VO, [2, 54]), o($VO, [2, 55]), o($VO, [2, 56]), o($VO, [2, 57]), o($VO, [2, 58]), o($VO, [2, 60]), o($VO, [2, 61]), o($VO, [2, 62]), o($VO, [2, 63]), o($VO, [2, 64]), o($VO, [2, 65]), o($VO, [2, 66]), o($VO, [2, 67]), o($VO, [2, 68]), o($VO, [2, 69]), o($VO, [2, 70]), { 31: 134, 42: [1, 135] }, { 12: [1, 136] }, { 33: [1, 137] }, o($VR, [2, 28]), o($VR, [2, 29]), o($VR, [2, 30]), o($VR, [2, 31]), o($VR, [2, 32]), o($VR, [2, 33]), o($VR, [2, 34]), { 1: [2, 9] }, { 1: [2, 10] }, { 1: [2, 11] }, { 1: [2, 12] }, o($VI, [2, 18]), o($VH, [2, 38]), o($VP, [2, 72]), o($VQ, [2, 74]), o($VO, [2, 24]), o($VO, [2, 35]), o($VS, [2, 25]), o($VS, [2, 26], { 12: [1, 138] }), o($VS, [2, 27])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 7], 5: [2, 3], 6: [2, 4], 7: [2, 5], 8: [2, 6], 74: [2, 8], 126: [2, 9], 127: [2, 10], 128: [2, 11], 129: [2, 12] },\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c2 = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c2 + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: {},\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 6;\n          case 1:\n            return 7;\n          case 2:\n            return 8;\n          case 3:\n            return 9;\n          case 4:\n            return 22;\n          case 5:\n            return 23;\n          case 6:\n            this.begin(\"acc_title\");\n            return 24;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n          case 8:\n            this.begin(\"acc_descr\");\n            return 26;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n          case 13:\n            break;\n          case 14:\n            c;\n            break;\n          case 15:\n            return 12;\n          case 16:\n            break;\n          case 17:\n            return 11;\n          case 18:\n            return 15;\n          case 19:\n            return 16;\n          case 20:\n            return 17;\n          case 21:\n            return 18;\n          case 22:\n            this.begin(\"person_ext\");\n            return 45;\n          case 23:\n            this.begin(\"person\");\n            return 44;\n          case 24:\n            this.begin(\"system_ext_queue\");\n            return 51;\n          case 25:\n            this.begin(\"system_ext_db\");\n            return 50;\n          case 26:\n            this.begin(\"system_ext\");\n            return 49;\n          case 27:\n            this.begin(\"system_queue\");\n            return 48;\n          case 28:\n            this.begin(\"system_db\");\n            return 47;\n          case 29:\n            this.begin(\"system\");\n            return 46;\n          case 30:\n            this.begin(\"boundary\");\n            return 37;\n          case 31:\n            this.begin(\"enterprise_boundary\");\n            return 34;\n          case 32:\n            this.begin(\"system_boundary\");\n            return 36;\n          case 33:\n            this.begin(\"container_ext_queue\");\n            return 57;\n          case 34:\n            this.begin(\"container_ext_db\");\n            return 56;\n          case 35:\n            this.begin(\"container_ext\");\n            return 55;\n          case 36:\n            this.begin(\"container_queue\");\n            return 54;\n          case 37:\n            this.begin(\"container_db\");\n            return 53;\n          case 38:\n            this.begin(\"container\");\n            return 52;\n          case 39:\n            this.begin(\"container_boundary\");\n            return 38;\n          case 40:\n            this.begin(\"component_ext_queue\");\n            return 63;\n          case 41:\n            this.begin(\"component_ext_db\");\n            return 62;\n          case 42:\n            this.begin(\"component_ext\");\n            return 61;\n          case 43:\n            this.begin(\"component_queue\");\n            return 60;\n          case 44:\n            this.begin(\"component_db\");\n            return 59;\n          case 45:\n            this.begin(\"component\");\n            return 58;\n          case 46:\n            this.begin(\"node\");\n            return 39;\n          case 47:\n            this.begin(\"node\");\n            return 39;\n          case 48:\n            this.begin(\"node_l\");\n            return 40;\n          case 49:\n            this.begin(\"node_r\");\n            return 41;\n          case 50:\n            this.begin(\"rel\");\n            return 64;\n          case 51:\n            this.begin(\"birel\");\n            return 65;\n          case 52:\n            this.begin(\"rel_u\");\n            return 66;\n          case 53:\n            this.begin(\"rel_u\");\n            return 66;\n          case 54:\n            this.begin(\"rel_d\");\n            return 67;\n          case 55:\n            this.begin(\"rel_d\");\n            return 67;\n          case 56:\n            this.begin(\"rel_l\");\n            return 68;\n          case 57:\n            this.begin(\"rel_l\");\n            return 68;\n          case 58:\n            this.begin(\"rel_r\");\n            return 69;\n          case 59:\n            this.begin(\"rel_r\");\n            return 69;\n          case 60:\n            this.begin(\"rel_b\");\n            return 70;\n          case 61:\n            this.begin(\"rel_index\");\n            return 71;\n          case 62:\n            this.begin(\"update_el_style\");\n            return 72;\n          case 63:\n            this.begin(\"update_rel_style\");\n            return 73;\n          case 64:\n            this.begin(\"update_layout_config\");\n            return 74;\n          case 65:\n            return \"EOF_IN_STRUCT\";\n          case 66:\n            this.begin(\"attribute\");\n            return \"ATTRIBUTE_EMPTY\";\n          case 67:\n            this.begin(\"attribute\");\n            break;\n          case 68:\n            this.popState();\n            this.popState();\n            break;\n          case 69:\n            return 80;\n          case 70:\n            break;\n          case 71:\n            return 80;\n          case 72:\n            this.begin(\"string\");\n            break;\n          case 73:\n            this.popState();\n            break;\n          case 74:\n            return \"STR\";\n          case 75:\n            this.begin(\"string_kv\");\n            break;\n          case 76:\n            this.begin(\"string_kv_key\");\n            return \"STR_KEY\";\n          case 77:\n            this.popState();\n            this.begin(\"string_kv_value\");\n            break;\n          case 78:\n            return \"STR_VALUE\";\n          case 79:\n            this.popState();\n            this.popState();\n            break;\n          case 80:\n            return \"STR\";\n          case 81:\n            return \"LBRACE\";\n          case 82:\n            return \"RBRACE\";\n          case 83:\n            return \"SPACE\";\n          case 84:\n            return \"EOL\";\n          case 85:\n            return 14;\n        }\n      },\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:title\\s[^#\\n;]+)/, /^(?:accDescription\\s[^#\\n;]+)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:C4Context\\b)/, /^(?:C4Container\\b)/, /^(?:C4Component\\b)/, /^(?:C4Dynamic\\b)/, /^(?:C4Deployment\\b)/, /^(?:Person_Ext\\b)/, /^(?:Person\\b)/, /^(?:SystemQueue_Ext\\b)/, /^(?:SystemDb_Ext\\b)/, /^(?:System_Ext\\b)/, /^(?:SystemQueue\\b)/, /^(?:SystemDb\\b)/, /^(?:System\\b)/, /^(?:Boundary\\b)/, /^(?:Enterprise_Boundary\\b)/, /^(?:System_Boundary\\b)/, /^(?:ContainerQueue_Ext\\b)/, /^(?:ContainerDb_Ext\\b)/, /^(?:Container_Ext\\b)/, /^(?:ContainerQueue\\b)/, /^(?:ContainerDb\\b)/, /^(?:Container\\b)/, /^(?:Container_Boundary\\b)/, /^(?:ComponentQueue_Ext\\b)/, /^(?:ComponentDb_Ext\\b)/, /^(?:Component_Ext\\b)/, /^(?:ComponentQueue\\b)/, /^(?:ComponentDb\\b)/, /^(?:Component\\b)/, /^(?:Deployment_Node\\b)/, /^(?:Node\\b)/, /^(?:Node_L\\b)/, /^(?:Node_R\\b)/, /^(?:Rel\\b)/, /^(?:BiRel\\b)/, /^(?:Rel_Up\\b)/, /^(?:Rel_U\\b)/, /^(?:Rel_Down\\b)/, /^(?:Rel_D\\b)/, /^(?:Rel_Left\\b)/, /^(?:Rel_L\\b)/, /^(?:Rel_Right\\b)/, /^(?:Rel_R\\b)/, /^(?:Rel_Back\\b)/, /^(?:RelIndex\\b)/, /^(?:UpdateElementStyle\\b)/, /^(?:UpdateRelStyle\\b)/, /^(?:UpdateLayoutConfig\\b)/, /^(?:$)/, /^(?:[(][ ]*[,])/, /^(?:[(])/, /^(?:[)])/, /^(?:,,)/, /^(?:,)/, /^(?:[ ]*[\"][\"])/, /^(?:[ ]*[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[ ]*[\\$])/, /^(?:[^=]*)/, /^(?:[=][ ]*[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[^,]+)/, /^(?:\\{)/, /^(?:\\})/, /^(?:[\\s]+)/, /^(?:[\\n\\r]+)/, /^(?:$)/],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"string_kv_value\": { \"rules\": [78, 79], \"inclusive\": false }, \"string_kv_key\": { \"rules\": [77], \"inclusive\": false }, \"string_kv\": { \"rules\": [76], \"inclusive\": false }, \"string\": { \"rules\": [73, 74], \"inclusive\": false }, \"attribute\": { \"rules\": [68, 69, 70, 71, 72, 75, 80], \"inclusive\": false }, \"update_layout_config\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_rel_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_el_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_b\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_d\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_u\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_bi\": { \"rules\": [], \"inclusive\": false }, \"rel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"index\": { \"rules\": [], \"inclusive\": false }, \"rel_index\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext_queue\": { \"rules\": [], \"inclusive\": false }, \"component_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"birel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"enterprise_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 81, 82, 83, 84, 85], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst parser$1 = parser;\nlet c4ShapeArray = [];\nlet boundaryParseStack = [\"\"];\nlet currentBoundaryParse = \"global\";\nlet parentBoundaryParse = \"\";\nlet boundaries = [\n  {\n    alias: \"global\",\n    label: { text: \"global\" },\n    type: { text: \"global\" },\n    tags: null,\n    link: null,\n    parentBoundary: \"\"\n  }\n];\nlet rels = [];\nlet title = \"\";\nlet wrapEnabled = false;\nlet c4ShapeInRow$1 = 4;\nlet c4BoundaryInRow$1 = 2;\nvar c4Type;\nconst getC4Type = function() {\n  return c4Type;\n};\nconst setC4Type = function(c4TypeParam) {\n  let sanitizedText = sanitizeText(c4TypeParam, getConfig());\n  c4Type = sanitizedText;\n};\nconst addRel = function(type, from, to, label, techn, descr, sprite, tags, link) {\n  if (type === void 0 || type === null || from === void 0 || from === null || to === void 0 || to === null || label === void 0 || label === null) {\n    return;\n  }\n  let rel = {};\n  const old = rels.find((rel2) => rel2.from === from && rel2.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n  if (techn === void 0 || techn === null) {\n    rel.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    rel.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n};\nconst addPersonOrSystem = function(typeC4Shape, alias, label, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem2) => personOrSystem2.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n  if (label === void 0 || label === null) {\n    personOrSystem.label = { text: \"\" };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n  if (descr === void 0 || descr === null) {\n    personOrSystem.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n};\nconst addContainer = function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let container = {};\n  const old = c4ShapeArray.find((container2) => container2.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n  if (label === void 0 || label === null) {\n    container.label = { text: \"\" };\n  } else {\n    container.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    container.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    container.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n};\nconst addComponent = function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let component = {};\n  const old = c4ShapeArray.find((component2) => component2.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n  if (label === void 0 || label === null) {\n    component.label = { text: \"\" };\n  } else {\n    component.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    component.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    component.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n};\nconst addPersonOrSystemBoundary = function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"system\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst addContainerBoundary = function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"container\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst addDeploymentNode = function(nodeType, alias, label, type, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"node\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    boundary.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst popBoundaryParseStack = function() {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst updateElStyle = function(typeC4Shape, elementName, bgColor, fontColor, borderColor, shadowing, shape, sprite, techn, legendText, legendSprite) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === void 0) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === void 0) {\n      return;\n    }\n  }\n  if (bgColor !== void 0 && bgColor !== null) {\n    if (typeof bgColor === \"object\") {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== void 0 && fontColor !== null) {\n    if (typeof fontColor === \"object\") {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== void 0 && borderColor !== null) {\n    if (typeof borderColor === \"object\") {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== void 0 && shadowing !== null) {\n    if (typeof shadowing === \"object\") {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== void 0 && shape !== null) {\n    if (typeof shape === \"object\") {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== void 0 && sprite !== null) {\n    if (typeof sprite === \"object\") {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== void 0 && techn !== null) {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== void 0 && legendText !== null) {\n    if (typeof legendText === \"object\") {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== void 0 && legendSprite !== null) {\n    if (typeof legendSprite === \"object\") {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n};\nconst updateRelStyle = function(typeC4Shape, from, to, textColor, lineColor, offsetX, offsetY) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === void 0) {\n    return;\n  }\n  if (textColor !== void 0 && textColor !== null) {\n    if (typeof textColor === \"object\") {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== void 0 && lineColor !== null) {\n    if (typeof lineColor === \"object\") {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== void 0 && offsetX !== null) {\n    if (typeof offsetX === \"object\") {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== void 0 && offsetY !== null) {\n    if (typeof offsetY === \"object\") {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n};\nconst updateLayoutConfig = function(typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow$1;\n  let c4BoundaryInRowValue = c4BoundaryInRow$1;\n  if (typeof c4ShapeInRowParam === \"object\") {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === \"object\") {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow$1 = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow$1 = c4BoundaryInRowValue;\n  }\n};\nconst getC4ShapeInRow = function() {\n  return c4ShapeInRow$1;\n};\nconst getC4BoundaryInRow = function() {\n  return c4BoundaryInRow$1;\n};\nconst getCurrentBoundaryParse = function() {\n  return currentBoundaryParse;\n};\nconst getParentBoundaryParse = function() {\n  return parentBoundaryParse;\n};\nconst getC4ShapeArray = function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n};\nconst getC4Shape = function(alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n};\nconst getC4ShapeKeys = function(parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n};\nconst getBoundaries = function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n};\nconst getBoundarys = getBoundaries;\nconst getRels = function() {\n  return rels;\n};\nconst getTitle = function() {\n  return title;\n};\nconst setWrap = function(wrapSetting) {\n  wrapEnabled = wrapSetting;\n};\nconst autoWrap = function() {\n  return wrapEnabled;\n};\nconst clear = function() {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: \"global\",\n      label: { text: \"global\" },\n      type: { text: \"global\" },\n      tags: null,\n      link: null,\n      parentBoundary: \"\"\n    }\n  ];\n  parentBoundaryParse = \"\";\n  currentBoundaryParse = \"global\";\n  boundaryParseStack = [\"\"];\n  rels = [];\n  boundaryParseStack = [\"\"];\n  title = \"\";\n  wrapEnabled = false;\n  c4ShapeInRow$1 = 4;\n  c4BoundaryInRow$1 = 2;\n};\nconst LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25\n};\nconst ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nconst PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nconst setTitle = function(txt) {\n  let sanitizedText = sanitizeText(txt, getConfig());\n  title = sanitizedText;\n};\nconst db = {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getConfig: () => getConfig().c4,\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type\n  // apply,\n};\nconst drawRect = function(elem, rectData) {\n  return drawRect$1(elem, rectData);\n};\nconst drawImage = function(elem, width, height, x, y, link) {\n  const imageElem = elem.append(\"image\");\n  imageElem.attr(\"width\", width);\n  imageElem.attr(\"height\", height);\n  imageElem.attr(\"x\", x);\n  imageElem.attr(\"y\", y);\n  let sanitizedLink = link.startsWith(\"data:image/png;base64\") ? link : sanitizeUrl(link);\n  imageElem.attr(\"xlink:href\", sanitizedLink);\n};\nconst drawRels$1 = (elem, rels2, conf2) => {\n  const relsElem = elem.append(\"g\");\n  let i = 0;\n  for (let rel of rels2) {\n    let textColor = rel.textColor ? rel.textColor : \"#444444\";\n    let strokeColor = rel.lineColor ? rel.lineColor : \"#444444\";\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n    let url = \"\";\n    if (i === 0) {\n      let line = relsElem.append(\"line\");\n      line.attr(\"x1\", rel.startPoint.x);\n      line.attr(\"y1\", rel.startPoint.y);\n      line.attr(\"x2\", rel.endPoint.x);\n      line.attr(\"y2\", rel.endPoint.y);\n      line.attr(\"stroke-width\", \"1\");\n      line.attr(\"stroke\", strokeColor);\n      line.style(\"fill\", \"none\");\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append(\"path\");\n      line.attr(\"fill\", \"none\").attr(\"stroke-width\", \"1\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,starty Qcontrolx,controly stopx,stopy \".replaceAll(\"startx\", rel.startPoint.x).replaceAll(\"starty\", rel.startPoint.y).replaceAll(\n          \"controlx\",\n          rel.startPoint.x + (rel.endPoint.x - rel.startPoint.x) / 2 - (rel.endPoint.x - rel.startPoint.x) / 4\n        ).replaceAll(\"controly\", rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2).replaceAll(\"stopx\", rel.endPoint.x).replaceAll(\"stopy\", rel.endPoint.y)\n      );\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n    }\n    let messageConf = conf2.messageFont();\n    _drawTextCandidateFunc(conf2)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n    if (rel.techn && rel.techn.text !== \"\") {\n      messageConf = conf2.messageFont();\n      _drawTextCandidateFunc(conf2)(\n        \"[\" + rel.techn.text + \"]\",\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + conf2.messageFontSize + 5 + offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, \"font-style\": \"italic\" },\n        messageConf\n      );\n    }\n  }\n};\nconst drawBoundary$1 = function(elem, boundary, conf2) {\n  const boundaryElem = elem.append(\"g\");\n  let fillColor = boundary.bgColor ? boundary.bgColor : \"none\";\n  let strokeColor = boundary.borderColor ? boundary.borderColor : \"#444444\";\n  let fontColor = boundary.fontColor ? boundary.fontColor : \"black\";\n  let attrsValue = { \"stroke-width\": 1, \"stroke-dasharray\": \"7.0,7.0\" };\n  if (boundary.nodeType) {\n    attrsValue = { \"stroke-width\": 1 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue\n  };\n  drawRect(boundaryElem, rectData);\n  let boundaryConf = conf2.boundaryFont();\n  boundaryConf.fontWeight = \"bold\";\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: \"#444444\" },\n    boundaryConf\n  );\n  if (boundary.type && boundary.type.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n  if (boundary.descr && boundary.descr.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n};\nconst drawC4Shape = function(elem, c4Shape, conf2) {\n  var _a;\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf2[c4Shape.typeC4Shape.text + \"_bg_color\"];\n  let strokeColor = c4Shape.borderColor ? c4Shape.borderColor : conf2[c4Shape.typeC4Shape.text + \"_border_color\"];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : \"#FFFFFF\";\n  let personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n      break;\n    case \"external_person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=\";\n      break;\n  }\n  const c4ShapeElem = elem.append(\"g\");\n  c4ShapeElem.attr(\"class\", \"person-man\");\n  const rect = getNoteRect();\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n    case \"system\":\n    case \"external_system\":\n    case \"container\":\n    case \"external_container\":\n    case \"component\":\n    case \"external_component\":\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { \"stroke-width\": 0.5 };\n      drawRect(c4ShapeElem, rect);\n      break;\n    case \"system_db\":\n    case \"external_system_db\":\n    case \"container_db\":\n    case \"external_container_db\":\n    case \"component_db\":\n    case \"external_component_db\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2).replaceAll(\"height\", c4Shape.height)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2)\n      );\n      break;\n    case \"system_queue\":\n    case \"external_system_queue\":\n    case \"container_queue\":\n    case \"external_container_queue\":\n    case \"component_queue\":\n    case \"external_component_queue\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"width\", c4Shape.width).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half\".replaceAll(\"startx\", c4Shape.x + c4Shape.width).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      break;\n  }\n  let c4ShapeFontConf = getC4ShapeFont(conf2, c4Shape.typeC4Shape.text);\n  c4ShapeElem.append(\"text\").attr(\"fill\", fontColor).attr(\"font-family\", c4ShapeFontConf.fontFamily).attr(\"font-size\", c4ShapeFontConf.fontSize - 2).attr(\"font-style\", \"italic\").attr(\"lengthAdjust\", \"spacing\").attr(\"textLength\", c4Shape.typeC4Shape.width).attr(\"x\", c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2).attr(\"y\", c4Shape.y + c4Shape.typeC4Shape.Y).text(\"<<\" + c4Shape.typeC4Shape.text + \">>\");\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n  let textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontWeight = \"bold\";\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n  textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontColor = fontColor;\n  if (c4Shape.techn && ((_a = c4Shape.techn) == null ? void 0 : _a.text) !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  }\n  if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n    textFontConf = conf2.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n  return c4Shape.height;\n};\nconst insertDatabaseIcon = function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n};\nconst insertComputerIcon = function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n};\nconst insertClockIcon = function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n};\nconst insertArrowHead = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\");\n};\nconst insertArrowEnd = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowend\").attr(\"refX\", 1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 10 0 L 0 5 L 10 10 z\");\n};\nconst insertArrowFilledHead = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n};\nconst insertDynamicNumber = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n};\nconst insertArrowCrossHead = function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 16).attr(\"refY\", 4);\n  marker.append(\"path\").attr(\"fill\", \"black\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 9,2 V 6 L16,4 Z\");\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 0,1 L 6,7 M 6,1 L 0,7\");\n};\nconst getC4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n};\nconst _drawTextCandidateFunc = function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { fontSize, fontFamily, fontWeight } = conf2;\n    const lines = content.split(common.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - fontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").style(\"font-size\", fontSize).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily);\n      text.append(\"tspan\").attr(\"dy\", dy).text(lines[i]).attr(\"alignment-baseline\", \"mathematical\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nconst svgDraw = {\n  drawRect,\n  drawBoundary: drawBoundary$1,\n  drawC4Shape,\n  drawRels: drawRels$1,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon\n};\nlet globalBoundaryMaxX = 0, globalBoundaryMaxY = 0;\nlet c4ShapeInRow = 4;\nlet c4BoundaryInRow = 2;\nparser.yy = db;\nlet conf = {};\nclass Bounds {\n  constructor(diagObj) {\n    this.name = \"\";\n    this.data = {};\n    this.data.startx = void 0;\n    this.data.stopx = void 0;\n    this.data.starty = void 0;\n    this.data.stopy = void 0;\n    this.data.widthLimit = void 0;\n    this.nextData = {};\n    this.nextData.startx = void 0;\n    this.nextData.stopx = void 0;\n    this.nextData.starty = void 0;\n    this.nextData.stopy = void 0;\n    this.nextData.cnt = 0;\n    setConf(diagObj.db.getConfig());\n  }\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx = this.nextData.startx === this.nextData.stopx ? this.nextData.stopx + c4Shape.margin : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (_startx >= this.data.widthLimit || _stopx >= this.data.widthLimit || this.nextData.cnt > c4ShapeInRow) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n    this.updateVal(this.data, \"startx\", _startx, Math.min);\n    this.updateVal(this.data, \"starty\", _starty, Math.min);\n    this.updateVal(this.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.data, \"stopy\", _stopy, Math.max);\n    this.updateVal(this.nextData, \"startx\", _startx, Math.min);\n    this.updateVal(this.nextData, \"starty\", _starty, Math.min);\n    this.updateVal(this.nextData, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.nextData, \"stopy\", _stopy, Math.max);\n  }\n  init(diagObj) {\n    this.name = \"\";\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      widthLimit: void 0\n    };\n    this.nextData = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      cnt: 0\n    };\n    setConf(diagObj.db.getConfig());\n  }\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n}\nconst setConf = function(cnf) {\n  assignWithDepth(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n};\nconst c4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n};\nconst boundaryFont = (cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight\n  };\n};\nconst messageFont = (cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n};\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = wrapLabel(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(common.lineBreakRegex).length;\n      c4Shape[textType].width = textLimitWidth;\n      c4Shape[textType].height = calculateTextHeight(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(common.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          calculateTextWidth(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = calculateTextHeight(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n    }\n  }\n}\nconst drawBoundary = function(diagram2, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n  boundary.label.y = conf.c4ShapeMargin - 35;\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = \"bold\";\n  let textLimitWidth = calculateTextWidth(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH(\"label\", boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n  svgDraw.drawBoundary(diagram2, boundary, conf);\n};\nconst drawC4ShapeArray = function(currentBounds, diagram2, c4ShapeArray2, c4ShapeKeys) {\n  let Y = 0;\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray2[c4ShapeKey];\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = calculateTextWidth(\n      \"«\" + c4Shape.typeC4Shape.text + \"»\",\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case \"person\":\n      case \"external_person\":\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\"label\", c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape[\"label\"].Y = Y + 8;\n    Y = c4Shape[\"label\"].Y + c4Shape[\"label\"].height;\n    if (c4Shape.type && c4Shape.type.text !== \"\") {\n      c4Shape.type.text = \"[\" + c4Shape.type.text + \"]\";\n      let c4ShapeTypeConf2 = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"type\", c4Shape, c4ShapeTextWrap, c4ShapeTypeConf2, textLimitWidth);\n      c4Shape[\"type\"].Y = Y + 5;\n      Y = c4Shape[\"type\"].Y + c4Shape[\"type\"].height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== \"\") {\n      c4Shape.techn.text = \"[\" + c4Shape.techn.text + \"]\";\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH(\"techn\", c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape[\"techn\"].Y = Y + 5;\n      Y = c4Shape[\"techn\"].Y + c4Shape[\"techn\"].height;\n    }\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n    if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"descr\", c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape[\"descr\"].Y = Y + 20;\n      Y = c4Shape[\"descr\"].Y + c4Shape[\"descr\"].height;\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape[\"descr\"].textLines * 5;\n    }\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n    currentBounds.insert(c4Shape);\n    svgDraw.drawC4Shape(diagram2, c4Shape, conf);\n  }\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n};\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n}\nlet getIntersectPoint = function(fromNode, endPoint) {\n  let x1 = fromNode.x;\n  let y1 = fromNode.y;\n  let x2 = endPoint.x;\n  let y2 = endPoint.y;\n  let fromCenterX = x1 + fromNode.width / 2;\n  let fromCenterY = y1 + fromNode.height / 2;\n  let dx = Math.abs(x1 - x2);\n  let dy = Math.abs(y1 - y2);\n  let tanDYX = dy / dx;\n  let fromDYX = fromNode.height / fromNode.width;\n  let returnPoint = null;\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + fromNode.height / 2 * dx / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - fromNode.width / 2 * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - fromNode.height / 2 * dx / dy, y1);\n    }\n  }\n  return returnPoint;\n};\nlet getIntersectPoints = function(fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint, endPoint };\n};\nconst drawRels = function(diagram2, rels2, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels2) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === \"C4Dynamic\") {\n      rel.label.text = i + \": \" + rel.label.text;\n    }\n    let textLimitWidth = calculateTextWidth(rel.label.text, relConf);\n    calcC4ShapeTextWH(\"label\", rel, relTextWrap, relConf, textLimitWidth);\n    if (rel.techn && rel.techn.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.techn.text, relConf);\n      calcC4ShapeTextWH(\"techn\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    if (rel.descr && rel.descr.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.descr.text, relConf);\n      calcC4ShapeTextWH(\"descr\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw.drawRels(diagram2, rels2, conf);\n};\nfunction drawInsideBoundary(diagram2, parentBoundaryAlias, parentBounds, currentBoundaries, diagObj) {\n  let currentBounds = new Bounds(diagObj);\n  currentBounds.data.widthLimit = parentBounds.data.widthLimit / Math.min(c4BoundaryInRow, currentBoundaries.length);\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\n      \"label\",\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary[\"label\"].Y = Y + 8;\n    Y = currentBoundary[\"label\"].Y + currentBoundary[\"label\"].height;\n    if (currentBoundary.type && currentBoundary.type.text !== \"\") {\n      currentBoundary.type.text = \"[\" + currentBoundary.type.text + \"]\";\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        \"type\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary[\"type\"].Y = Y + 5;\n      Y = currentBoundary[\"type\"].Y + currentBoundary[\"type\"].height;\n    }\n    if (currentBoundary.descr && currentBoundary.descr.text !== \"\") {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        \"descr\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary[\"descr\"].Y = Y + 20;\n      Y = currentBoundary[\"descr\"].Y + currentBoundary[\"descr\"].height;\n    }\n    if (i == 0 || i % c4BoundaryInRow === 0) {\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      let _x = currentBounds.data.stopx !== currentBounds.data.startx ? currentBounds.data.stopx + conf.diagramMarginX : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram2,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n    if (nextCurrentBoundaries.length > 0) {\n      drawInsideBoundary(\n        diagram2,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    if (currentBoundary.alias !== \"global\") {\n      drawBoundary(diagram2, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\nconst draw = function(_text, id, _version, diagObj) {\n  conf = getConfig().c4;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  let db2 = diagObj.db;\n  diagObj.db.setWrap(conf.wrap);\n  c4ShapeInRow = db2.getC4ShapeInRow();\n  c4BoundaryInRow = db2.getC4BoundaryInRow();\n  log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n  svgDraw.insertComputerIcon(diagram2);\n  svgDraw.insertDatabaseIcon(diagram2);\n  svgDraw.insertClockIcon(diagram2);\n  let screenBounds = new Bounds(diagObj);\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n  const title2 = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys(\"\");\n  drawInsideBoundary(diagram2, \"\", screenBounds, currentBoundaries, diagObj);\n  svgDraw.insertArrowHead(diagram2);\n  svgDraw.insertArrowEnd(diagram2);\n  svgDraw.insertArrowCrossHead(diagram2);\n  svgDraw.insertArrowFilledHead(diagram2);\n  drawRels(diagram2, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n  const box = screenBounds.data;\n  let boxHeight = box.stopy - box.starty;\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title2) {\n    diagram2.append(\"text\").text(title2).attr(\"x\", (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX).attr(\"y\", box.starty + conf.diagramMarginY);\n  }\n  configureSvgSize(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title2 ? 60 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  log.debug(`models:`, box);\n};\nconst renderer = {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary,\n  setConf,\n  draw\n};\nconst getStyles = (options) => `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`;\nconst styles = getStyles;\nconst diagram = {\n  parser: parser$1,\n  db,\n  renderer,\n  styles,\n  init: ({ c4, wrap }) => {\n    renderer.setConf(c4);\n    db.setWrap(wrap);\n  }\n};\nexport {\n  diagram\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAMA,IAAI,SAAS;IACX,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;QAC1B,IAAK,KAAK,MAAM,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG;QAElD,OAAO;IACT,GAAG,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAI;KAAG,EAAE,MAAM;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG,EAAE,MAAM;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAI;QAAI;KAAG,EAAE,MAAM;QAAC;QAAI;QAAI;QAAI;KAAG,EAAE,MAAM;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG,EAAE,MAAM;QAAC;QAAI;KAAG,EAAE,MAAM;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IAC1yC,IAAI,UAAU;QACZ,OAAO,SAAS,SAChB;QACA,IAAI,CAAC;QACL,UAAU;YAAE,SAAS;YAAG,SAAS;YAAG,cAAc;YAAG,aAAa;YAAG,gBAAgB;YAAG,gBAAgB;YAAG,gBAAgB;YAAG,gBAAgB;YAAG,eAAe;YAAI,cAAc;YAAI,WAAW;YAAI,cAAc;YAAI,OAAO;YAAI,gBAAgB;YAAI,gBAAgB;YAAI,cAAc;YAAI,iBAAiB;YAAI,mBAAmB;YAAI,qBAAqB;YAAI,kBAAkB;YAAI,SAAS;YAAI,kBAAkB;YAAI,aAAa;YAAI,mBAAmB;YAAI,aAAa;YAAI,mBAAmB;YAAI,6BAA6B;YAAI,qBAAqB;YAAI,0BAA0B;YAAI,yBAAyB;YAAI,iBAAiB;YAAI,UAAU;YAAI,uBAAuB;YAAI,cAAc;YAAI,mBAAmB;YAAI,YAAY;YAAI,sBAAsB;YAAI,QAAQ;YAAI,UAAU;YAAI,UAAU;YAAI,UAAU;YAAI,oBAAoB;YAAI,UAAU;YAAI,cAAc;YAAI,UAAU;YAAI,aAAa;YAAI,gBAAgB;YAAI,cAAc;YAAI,iBAAiB;YAAI,oBAAoB;YAAI,aAAa;YAAI,gBAAgB;YAAI,mBAAmB;YAAI,iBAAiB;YAAI,oBAAoB;YAAI,uBAAuB;YAAI,aAAa;YAAI,gBAAgB;YAAI,mBAAmB;YAAI,iBAAiB;YAAI,oBAAoB;YAAI,uBAAuB;YAAI,OAAO;YAAI,SAAS;YAAI,SAAS;YAAI,SAAS;YAAI,SAAS;YAAI,SAAS;YAAI,SAAS;YAAI,aAAa;YAAI,mBAAmB;YAAI,oBAAoB;YAAI,wBAAwB;YAAI,aAAa;YAAI,OAAO;YAAI,WAAW;YAAI,aAAa;YAAI,aAAa;YAAI,mBAAmB;YAAI,WAAW;YAAG,QAAQ;QAAE;QACzgD,YAAY;YAAE,GAAG;YAAS,GAAG;YAAgB,GAAG;YAAgB,GAAG;YAAgB,GAAG;YAAgB,IAAI;YAAc,IAAI;YAAW,IAAI;YAAO,IAAI;YAAgB,IAAI;YAAgB,IAAI;YAAc,IAAI;YAAiB,IAAI;YAAS,IAAI;YAAkB,IAAI;YAAa,IAAI;YAAmB,IAAI;YAAa,IAAI;YAAmB,IAAI;YAA6B,IAAI;YAAU,IAAI;YAAuB,IAAI;YAAmB,IAAI;YAAY,IAAI;YAAsB,IAAI;YAAQ,IAAI;YAAU,IAAI;YAAU,IAAI;YAAU,IAAI;YAAU,IAAI;YAAc,IAAI;YAAU,IAAI;YAAa,IAAI;YAAgB,IAAI;YAAc,IAAI;YAAiB,IAAI;YAAoB,IAAI;YAAa,IAAI;YAAgB,IAAI;YAAmB,IAAI;YAAiB,IAAI;YAAoB,IAAI;YAAuB,IAAI;YAAa,IAAI;YAAgB,IAAI;YAAmB,IAAI;YAAiB,IAAI;YAAoB,IAAI;YAAuB,IAAI;YAAO,IAAI;YAAS,IAAI;YAAS,IAAI;YAAS,IAAI;YAAS,IAAI;YAAS,IAAI;YAAS,IAAI;YAAa,IAAI;YAAmB,IAAI;YAAoB,IAAI;YAAwB,IAAI;YAAO,IAAI;YAAW,IAAI;YAAa,IAAI;YAAa,IAAI;QAAkB;QACtrC,cAAc;YAAC;YAAG;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;SAAC;QACtrB,eAAe,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;YAC7E,IAAI,KAAK,GAAG,MAAM,GAAG;YACrB,OAAQ;gBACN,KAAK;oBACH,GAAG,YAAY,CAAC;oBAChB;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC;oBAChB;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC;oBAChB;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC;oBAChB;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE;oBACvB;gBACF,KAAK;oBACH,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC7B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC1B;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;oBACtC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC1B;gBACF,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;oBACpB,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAClB;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;oBACpB,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBAC3B;gBACF,KAAK;gBACL,KAAK;oBACH,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG;oBACpB,GAAG,yBAAyB,IAAI,EAAE,CAAC,GAAG;oBACtC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,yBAAyB,IAAI,EAAE,CAAC,GAAG;oBACtC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG;oBACpB,GAAG,oBAAoB,IAAI,EAAE,CAAC,GAAG;oBACjC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC,GAAG;oBACtC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,YAAY,EAAE,CAAC,GAAG;oBACvC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,YAAY,EAAE,CAAC,GAAG;oBACvC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,qBAAqB;oBACxB;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,aAAa,EAAE,CAAC,GAAG;oBACxC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,sBAAsB,EAAE,CAAC,GAAG;oBACjD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,aAAa,EAAE,CAAC,GAAG;oBACxC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,gBAAgB,EAAE,CAAC,GAAG;oBAC3C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,GAAG;oBAC9C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,sBAAsB,EAAE,CAAC,GAAG;oBACjD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,yBAAyB,EAAE,CAAC,GAAG;oBACpD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,iBAAiB,CAAC,4BAA4B,EAAE,CAAC,GAAG;oBACvD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC,GAAG;oBACtC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC,GAAG;oBACzC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC,GAAG;oBAC5C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,yBAAyB,EAAE,CAAC,GAAG;oBAC/C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,4BAA4B,EAAE,CAAC,GAAG;oBAClD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,+BAA+B,EAAE,CAAC,GAAG;oBACrD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC,GAAG;oBACtC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC,GAAG;oBACzC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC,GAAG;oBAC5C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,yBAAyB,EAAE,CAAC,GAAG;oBAC/C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,4BAA4B,EAAE,CAAC,GAAG;oBAClD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,YAAY,CAAC,+BAA+B,EAAE,CAAC,GAAG;oBACrD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG;oBAC1B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG;oBAC5B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG;oBAC5B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG;oBAC5B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG;oBAC5B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG;oBAC5B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG;oBAC5B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG;oBACjB,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG;oBAC1B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,aAAa,CAAC,sBAAsB,EAAE,CAAC,GAAG;oBAC7C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,cAAc,CAAC,uBAAuB,EAAE,CAAC,GAAG;oBAC/C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,kBAAkB,CAAC,2BAA2B,EAAE,CAAC,GAAG;oBACvD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG;wBAAC,EAAE,CAAC,GAAG;qBAAC;oBACjB;gBACF,KAAK;oBACH,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;oBACzB,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;oBACpB;gBACF,KAAK;oBACH,IAAI,KAAK,CAAC;oBACV,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;oBACnC,IAAI,CAAC,CAAC,GAAG;oBACT;gBACF,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG;oBACT;YACJ;QACF;QACA,OAAO;YAAC;gBAAE,GAAG;gBAAG,GAAG;gBAAG,GAAG;gBAAG,GAAG;oBAAC;oBAAG;iBAAE;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;gBAAE,IAAI;gBAAG,IAAI;oBAAC;oBAAG;iBAAE;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,GAAG;oBAAC;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG,EAAE;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG,EAAE;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAG,EAAE;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG,EAAE;gBAAE,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAG,EAAE;gBAAE,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG,EAAE;gBAAE,IAAI;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;YAAI;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG;gBAAE,IAAI;gBAAK,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,GAAG;oBAAC;oBAAG;iBAAG;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG,EAAE;gBAAE,IAAI;oBAAC;oBAAG;iBAAI;YAAC;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAG;SAAE;QAC15O,gBAAgB;YAAE,GAAG;gBAAC;gBAAG;aAAE;YAAE,GAAG;gBAAC;gBAAG;aAAE;YAAE,GAAG;gBAAC;gBAAG;aAAE;YAAE,GAAG;gBAAC;gBAAG;aAAE;YAAE,GAAG;gBAAC;gBAAG;aAAE;YAAE,GAAG;gBAAC;gBAAG;aAAE;YAAE,GAAG;gBAAC;gBAAG;aAAE;YAAE,IAAI;gBAAC;gBAAG;aAAE;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,KAAK;gBAAC;gBAAG;aAAG;YAAE,KAAK;gBAAC;gBAAG;aAAG;YAAE,KAAK;gBAAC;gBAAG;aAAG;QAAC;QACjK,YAAY,SAAS,WAAW,GAAG,EAAE,IAAI;YACvC,IAAI,KAAK,WAAW,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC;YACb,OAAO;gBACL,IAAI,QAAQ,IAAI,MAAM;gBACtB,MAAM,IAAI,GAAG;gBACb,MAAM;YACR;QACF;QACA,OAAO,SAAS,MAAM,KAAK;YACzB,IAAI,OAAO,IAAI,EAAE,QAAQ;gBAAC;aAAE,EAAE,SAAS,EAAE,EAAE,SAAS;gBAAC;aAAK,EAAE,SAAS,EAAE,EAAE,QAAQ,IAAI,CAAC,KAAK,EAAE,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM;YACtJ,IAAI,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW;YACxC,IAAI,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK;YACrC,IAAI,cAAc;gBAAE,IAAI,CAAC;YAAE;YAC3B,IAAK,IAAI,KAAK,IAAI,CAAC,EAAE,CAAE;gBACrB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI;oBACpD,YAAY,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE;gBAChC;YACF;YACA,OAAO,QAAQ,CAAC,OAAO,YAAY,EAAE;YACrC,YAAY,EAAE,CAAC,KAAK,GAAG;YACvB,YAAY,EAAE,CAAC,MAAM,GAAG,IAAI;YAC5B,IAAI,OAAO,OAAO,MAAM,IAAI,aAAa;gBACvC,OAAO,MAAM,GAAG,CAAC;YACnB;YACA,IAAI,QAAQ,OAAO,MAAM;YACzB,OAAO,IAAI,CAAC;YACZ,IAAI,SAAS,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM;YACpD,IAAI,OAAO,YAAY,EAAE,CAAC,UAAU,KAAK,YAAY;gBACnD,IAAI,CAAC,UAAU,GAAG,YAAY,EAAE,CAAC,UAAU;YAC7C,OAAO;gBACL,IAAI,CAAC,UAAU,GAAG,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAC1D;YACA,SAAS;gBACP,IAAI;gBACJ,QAAQ,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM;gBACxC,IAAI,OAAO,UAAU,UAAU;oBAC7B,IAAI,iBAAiB,OAAO;wBAC1B,SAAS;wBACT,QAAQ,OAAO,GAAG;oBACpB;oBACA,QAAQ,KAAK,QAAQ,CAAC,MAAM,IAAI;gBAClC;gBACA,OAAO;YACT;YACA,IAAI,QAAQ,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;YAC5D,MAAO,KAAM;gBACX,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;gBAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;oBAC9B,SAAS,IAAI,CAAC,cAAc,CAAC,MAAM;gBACrC,OAAO;oBACL,IAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;wBACnD,SAAS;oBACX;oBACA,SAAS,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO;gBAC/C;gBACA,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;oBACjE,IAAI,SAAS;oBACb,WAAW,EAAE;oBACb,IAAK,KAAK,KAAK,CAAC,MAAM,CAAE;wBACtB,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,IAAI,QAAQ;4BACpC,SAAS,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG;wBAC3C;oBACF;oBACA,IAAI,OAAO,YAAY,EAAE;wBACvB,SAAS,yBAAyB,CAAC,WAAW,CAAC,IAAI,QAAQ,OAAO,YAAY,KAAK,iBAAiB,SAAS,IAAI,CAAC,QAAQ,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,MAAM,IAAI;oBAC9K,OAAO;wBACL,SAAS,yBAAyB,CAAC,WAAW,CAAC,IAAI,kBAAkB,CAAC,UAAU,MAAM,iBAAiB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;oBACxJ;oBACA,IAAI,CAAC,UAAU,CAAC,QAAQ;wBACtB,MAAM,OAAO,KAAK;wBAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI;wBAClC,MAAM,OAAO,QAAQ;wBACrB,KAAK;wBACL;oBACF;gBACF;gBACA,IAAI,MAAM,CAAC,EAAE,YAAY,SAAS,OAAO,MAAM,GAAG,GAAG;oBACnD,MAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc;gBAC9F;gBACA,OAAQ,MAAM,CAAC,EAAE;oBACf,KAAK;wBACH,MAAM,IAAI,CAAC;wBACX,OAAO,IAAI,CAAC,OAAO,MAAM;wBACzB,OAAO,IAAI,CAAC,OAAO,MAAM;wBACzB,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE;wBACpB,SAAS;wBACT;4BACE,SAAS,OAAO,MAAM;4BACtB,SAAS,OAAO,MAAM;4BACtB,WAAW,OAAO,QAAQ;4BAC1B,QAAQ,OAAO,MAAM;wBACvB;wBACA;oBACF,KAAK;wBACH,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;wBACrC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI;wBACrC,MAAM,EAAE,GAAG;4BACT,YAAY,MAAM,CAAC,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU;4BACzD,WAAW,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,SAAS;4BAC9C,cAAc,MAAM,CAAC,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY;4BAC7D,aAAa,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,WAAW;wBACpD;wBACA,IAAI,QAAQ;4BACV,MAAM,EAAE,CAAC,KAAK,GAAG;gCACf,MAAM,CAAC,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;gCAC3C,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE;6BACnC;wBACH;wBACA,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO;4BAClC;4BACA;4BACA;4BACA,YAAY,EAAE;4BACd,MAAM,CAAC,EAAE;4BACT;4BACA;yBACD,CAAC,MAAM,CAAC;wBACT,IAAI,OAAO,MAAM,aAAa;4BAC5B,OAAO;wBACT;wBACA,IAAI,KAAK;4BACP,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM;4BAClC,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI;4BAC9B,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI;wBAChC;wBACA,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;wBAC1C,OAAO,IAAI,CAAC,MAAM,CAAC;wBACnB,OAAO,IAAI,CAAC,MAAM,EAAE;wBACpB,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;wBAClE,MAAM,IAAI,CAAC;wBACX;oBACF,KAAK;wBACH,OAAO;gBACX;YACF;YACA,OAAO;QACT;IACF;IACA,IAAI,QAAQ;QACV,IAAI,SAAS;YACX,KAAK;YACL,YAAY,SAAS,WAAW,GAAG,EAAE,IAAI;gBACvC,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBAClB,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK;gBACjC,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF;YACA,mCAAmC;YACnC,UAAU,SAAS,KAAK,EAAE,EAAE;gBAC1B,IAAI,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC;gBAC5B,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG;gBAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG;gBAC1C,IAAI,CAAC,cAAc,GAAG;oBAAC;iBAAU;gBACjC,IAAI,CAAC,MAAM,GAAG;oBACZ,YAAY;oBACZ,cAAc;oBACd,WAAW;oBACX,aAAa;gBACf;gBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;wBAAC;wBAAG;qBAAE;gBAC5B;gBACA,IAAI,CAAC,MAAM,GAAG;gBACd,OAAO,IAAI;YACb;YACA,+CAA+C;YAC/C,OAAO;gBACL,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;gBACvB,IAAI,CAAC,MAAM,IAAI;gBACf,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,KAAK,IAAI;gBACd,IAAI,CAAC,OAAO,IAAI;gBAChB,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,IAAI,OAAO;oBACT,IAAI,CAAC,QAAQ;oBACb,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvB,OAAO;oBACL,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;gBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACtB;gBACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChC,OAAO;YACT;YACA,iDAAiD;YACjD,OAAO,SAAS,EAAE;gBAChB,IAAI,MAAM,GAAG,MAAM;gBACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;gBACzD,IAAI,CAAC,MAAM,IAAI;gBACf,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBACtD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;gBAC5D,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,IAAI,CAAC,QAAQ,IAAI,MAAM,MAAM,GAAG;gBAClC;gBACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;gBACzB,IAAI,CAAC,MAAM,GAAG;oBACZ,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU;oBAClC,WAAW,IAAI,CAAC,QAAQ,GAAG;oBAC3B,cAAc,IAAI,CAAC,MAAM,CAAC,YAAY;oBACtC,aAAa,QAAQ,CAAC,MAAM,MAAM,KAAK,SAAS,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG;gBAC1L;gBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;wBAAC,CAAC,CAAC,EAAE;wBAAE,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG;qBAAI;gBACtD;gBACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;gBAChC,OAAO,IAAI;YACb;YACA,6EAA6E;YAC7E,MAAM;gBACJ,IAAI,CAAC,KAAK,GAAG;gBACb,OAAO,IAAI;YACb;YACA,kJAAkJ;YAClJ,QAAQ;gBACN,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;oBAChC,IAAI,CAAC,UAAU,GAAG;gBACpB,OAAO;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,qIAAqI,IAAI,CAAC,YAAY,IAAI;wBAChO,MAAM;wBACN,OAAO;wBACP,MAAM,IAAI,CAAC,QAAQ;oBACrB;gBACF;gBACA,OAAO,IAAI;YACb;YACA,yCAAyC;YACzC,MAAM,SAAS,CAAC;gBACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YAC9B;YACA,0DAA0D;YAC1D,WAAW;gBACT,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;gBACzE,OAAO,CAAC,KAAK,MAAM,GAAG,KAAK,QAAQ,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO;YAC3E;YACA,mDAAmD;YACnD,eAAe;gBACb,IAAI,OAAO,IAAI,CAAC,KAAK;gBACrB,IAAI,KAAK,MAAM,GAAG,IAAI;oBACpB,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,KAAK,MAAM;gBAChD;gBACA,OAAO,CAAC,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,GAAG,KAAK,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO;YAC/E;YACA,2FAA2F;YAC3F,cAAc;gBACZ,IAAI,MAAM,IAAI,CAAC,SAAS;gBACxB,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC;gBACxC,OAAO,MAAM,IAAI,CAAC,aAAa,KAAK,OAAO,KAAK;YAClD;YACA,8EAA8E;YAC9E,YAAY,SAAS,KAAK,EAAE,YAAY;gBACtC,IAAI,OAAO,OAAO;gBAClB,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;oBAChC,SAAS;wBACP,UAAU,IAAI,CAAC,QAAQ;wBACvB,QAAQ;4BACN,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU;4BAClC,WAAW,IAAI,CAAC,SAAS;4BACzB,cAAc,IAAI,CAAC,MAAM,CAAC,YAAY;4BACtC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;wBACtC;wBACA,QAAQ,IAAI,CAAC,MAAM;wBACnB,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS,IAAI,CAAC,OAAO;wBACrB,SAAS,IAAI,CAAC,OAAO;wBACrB,QAAQ,IAAI,CAAC,MAAM;wBACnB,QAAQ,IAAI,CAAC,MAAM;wBACnB,OAAO,IAAI,CAAC,KAAK;wBACjB,QAAQ,IAAI,CAAC,MAAM;wBACnB,IAAI,IAAI,CAAC,EAAE;wBACX,gBAAgB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;wBAC1C,MAAM,IAAI,CAAC,IAAI;oBACjB;oBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;wBACvB,OAAO,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;oBAChD;gBACF;gBACA,QAAQ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;gBACvB,IAAI,OAAO;oBACT,IAAI,CAAC,QAAQ,IAAI,MAAM,MAAM;gBAC/B;gBACA,IAAI,CAAC,MAAM,GAAG;oBACZ,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;oBACjC,WAAW,IAAI,CAAC,QAAQ,GAAG;oBAC3B,cAAc,IAAI,CAAC,MAAM,CAAC,WAAW;oBACrC,aAAa,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;gBACrJ;gBACA,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;gBACvB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE;gBACtB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;gBAChC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;wBAAC,IAAI,CAAC,MAAM;wBAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;qBAAC;gBAC/D;gBACA,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM;gBAC/C,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE;gBACxB,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,cAAc,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE;gBACtH,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;oBAC5B,IAAI,CAAC,IAAI,GAAG;gBACd;gBACA,IAAI,OAAO;oBACT,OAAO;gBACT,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;oBAC1B,IAAK,IAAI,KAAK,OAAQ;wBACpB,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;oBACrB;oBACA,OAAO;gBACT;gBACA,OAAO;YACT;YACA,6BAA6B;YAC7B,MAAM;gBACJ,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,OAAO,IAAI,CAAC,GAAG;gBACjB;gBACA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAChB,IAAI,CAAC,IAAI,GAAG;gBACd;gBACA,IAAI,OAAO,OAAO,WAAW;gBAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;oBACf,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,CAAC,KAAK,GAAG;gBACf;gBACA,IAAI,QAAQ,IAAI,CAAC,aAAa;gBAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClD,IAAI,aAAa,CAAC,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG;wBAClE,QAAQ;wBACR,QAAQ;wBACR,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;4BAChC,QAAQ,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,CAAC,EAAE;4BAC3C,IAAI,UAAU,OAAO;gCACnB,OAAO;4BACT,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;gCAC1B,QAAQ;gCACR;4BACF,OAAO;gCACL,OAAO;4BACT;wBACF,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;4BAC7B;wBACF;oBACF;gBACF;gBACA,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,MAAM;oBAC3C,IAAI,UAAU,OAAO;wBACnB,OAAO;oBACT;oBACA,OAAO;gBACT;gBACA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;oBACtB,OAAO,IAAI,CAAC,GAAG;gBACjB,OAAO;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,2BAA2B,IAAI,CAAC,YAAY,IAAI;wBACtH,MAAM;wBACN,OAAO;wBACP,MAAM,IAAI,CAAC,QAAQ;oBACrB;gBACF;YACF;YACA,qCAAqC;YACrC,KAAK,SAAS;gBACZ,IAAI,IAAI,IAAI,CAAC,IAAI;gBACjB,IAAI,GAAG;oBACL,OAAO;gBACT,OAAO;oBACL,OAAO,IAAI,CAAC,GAAG;gBACjB;YACF;YACA,wGAAwG;YACxG,OAAO,SAAS,MAAM,SAAS;gBAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YAC3B;YACA,0EAA0E;YAC1E,UAAU,SAAS;gBACjB,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;gBACrC,IAAI,IAAI,GAAG;oBACT,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG;gBAChC,OAAO;oBACL,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE;gBAC/B;YACF;YACA,4FAA4F;YAC5F,eAAe,SAAS;gBACtB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,EAAE;oBACrF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK;gBACnF,OAAO;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK;gBACzC;YACF;YACA,oJAAoJ;YACpJ,UAAU,SAAS,SAAS,CAAC;gBAC3B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,KAAK,GAAG,CAAC,KAAK;gBACnD,IAAI,KAAK,GAAG;oBACV,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE;gBAC/B,OAAO;oBACL,OAAO;gBACT;YACF;YACA,6BAA6B;YAC7B,WAAW,SAAS,UAAU,SAAS;gBACrC,IAAI,CAAC,KAAK,CAAC;YACb;YACA,qDAAqD;YACrD,gBAAgB,SAAS;gBACvB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;YACnC;YACA,SAAS,CAAC;YACV,eAAe,SAAS,UAAU,EAAE,EAAE,GAAG,EAAE,yBAAyB,EAAE,QAAQ;gBAC5E,OAAQ;oBACN,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX;oBACF,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH;oBACF,KAAK;wBACH;wBACA;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX;oBACF,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb,IAAI,CAAC,QAAQ;wBACb;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX;oBACF,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX;oBACF,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb,IAAI,CAAC,KAAK,CAAC;wBACX;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb,IAAI,CAAC,QAAQ;wBACb;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;gBACX;YACF;YACA,OAAO;gBAAC;gBAA+B;gBAA+B;gBAA+B;gBAA+B;gBAAwB;gBAAiC;gBAAwB;gBAAwB;gBAAwB;gBAAwB;gBAAyB;gBAAa;gBAAe;gBAAiC;gBAAyB;gBAAoB;gBAAY;gBAAoB;gBAAsB;gBAAsB;gBAAoB;gBAAuB;gBAAqB;gBAAiB;gBAA0B;gBAAuB;gBAAqB;gBAAsB;gBAAmB;gBAAiB;gBAAmB;gBAA8B;gBAA0B;gBAA6B;gBAA0B;gBAAwB;gBAAyB;gBAAsB;gBAAoB;gBAA6B;gBAA6B;gBAA0B;gBAAwB;gBAAyB;gBAAsB;gBAAoB;gBAA0B;gBAAe;gBAAiB;gBAAiB;gBAAc;gBAAgB;gBAAiB;gBAAgB;gBAAmB;gBAAgB;gBAAmB;gBAAgB;gBAAoB;gBAAgB;gBAAmB;gBAAmB;gBAA6B;gBAAyB;gBAA6B;gBAAU;gBAAmB;gBAAY;gBAAY;gBAAW;gBAAU;gBAAmB;gBAAgB;gBAAY;gBAAc;gBAAiB;gBAAc;gBAAmB;gBAAc;gBAAY;gBAAc;gBAAW;gBAAW;gBAAc;gBAAgB;aAAS;YACptD,YAAY;gBAAE,uBAAuB;oBAAE,SAAS;wBAAC;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;qBAAE;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;qBAAE;oBAAE,aAAa;gBAAM;gBAAG,mBAAmB;oBAAE,SAAS;wBAAC;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,iBAAiB;oBAAE,SAAS;wBAAC;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,UAAU;oBAAE,SAAS;wBAAC;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,wBAAwB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,oBAAoB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,mBAAmB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,SAAS;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,SAAS;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,SAAS;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,SAAS;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,SAAS;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,UAAU;oBAAE,SAAS,EAAE;oBAAE,aAAa;gBAAM;gBAAG,OAAO;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,UAAU;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,UAAU;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,QAAQ;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,SAAS;oBAAE,SAAS,EAAE;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,uBAAuB;oBAAE,SAAS,EAAE;oBAAE,aAAa;gBAAM;gBAAG,oBAAoB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,iBAAiB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,mBAAmB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,gBAAgB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,sBAAsB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,uBAAuB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,oBAAoB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,iBAAiB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,mBAAmB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,gBAAgB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,SAAS;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,mBAAmB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,uBAAuB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,YAAY;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,oBAAoB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,iBAAiB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,cAAc;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,gBAAgB;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,UAAU;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,cAAc;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,UAAU;oBAAE,SAAS;wBAAC;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,WAAW;oBAAE,SAAS;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAK;YAAE;QAC3yG;QACA,OAAO;IACT;IACA,QAAQ,KAAK,GAAG;IAChB,SAAS;QACP,IAAI,CAAC,EAAE,GAAG,CAAC;IACb;IACA,OAAO,SAAS,GAAG;IACnB,QAAQ,MAAM,GAAG;IACjB,OAAO,IAAI;AACb;AACA,OAAO,MAAM,GAAG;AAChB,MAAM,WAAW;AACjB,IAAI,eAAe,EAAE;AACrB,IAAI,qBAAqB;IAAC;CAAG;AAC7B,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;AAC1B,IAAI,aAAa;IACf;QACE,OAAO;QACP,OAAO;YAAE,MAAM;QAAS;QACxB,MAAM;YAAE,MAAM;QAAS;QACvB,MAAM;QACN,MAAM;QACN,gBAAgB;IAClB;CACD;AACD,IAAI,OAAO,EAAE;AACb,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,oBAAoB;AACxB,IAAI;AACJ,MAAM,YAAY;IAChB,OAAO;AACT;AACA,MAAM,YAAY,SAAS,WAAW;IACpC,IAAI,gBAAgB,CAAA,GAAA,yJAAA,CAAA,IAAY,AAAD,EAAE,aAAa,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACtD,SAAS;AACX;AACA,MAAM,SAAS,SAAS,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IAC7E,IAAI,SAAS,KAAK,KAAK,SAAS,QAAQ,SAAS,KAAK,KAAK,SAAS,QAAQ,OAAO,KAAK,KAAK,OAAO,QAAQ,UAAU,KAAK,KAAK,UAAU,MAAM;QAC9I;IACF;IACA,IAAI,MAAM,CAAC;IACX,MAAM,MAAM,KAAK,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK,QAAQ,KAAK,EAAE,KAAK;IAClE,IAAI,KAAK;QACP,MAAM;IACR,OAAO;QACL,KAAK,IAAI,CAAC;IACZ;IACA,IAAI,IAAI,GAAG;IACX,IAAI,IAAI,GAAG;IACX,IAAI,EAAE,GAAG;IACT,IAAI,KAAK,GAAG;QAAE,MAAM;IAAM;IAC1B,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,IAAI,KAAK,GAAG;YAAE,MAAM;QAAG;IACzB,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QAC3B,OAAO;YACL,IAAI,KAAK,GAAG;gBAAE,MAAM;YAAM;QAC5B;IACF;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,IAAI,KAAK,GAAG;YAAE,MAAM;QAAG;IACzB,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QAC3B,OAAO;YACL,IAAI,KAAK,GAAG;gBAAE,MAAM;YAAM;QAC5B;IACF;IACA,IAAI,OAAO,WAAW,UAAU;QAC9B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE;QAC5C,GAAG,CAAC,IAAI,GAAG;IACb,OAAO;QACL,IAAI,MAAM,GAAG;IACf;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,GAAG,CAAC,IAAI,GAAG;IACb,OAAO;QACL,IAAI,IAAI,GAAG;IACb;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,GAAG,CAAC,IAAI,GAAG;IACb,OAAO;QACL,IAAI,IAAI,GAAG;IACb;IACA,IAAI,IAAI,GAAG;AACb;AACA,MAAM,oBAAoB,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IACrF,IAAI,UAAU,QAAQ,UAAU,MAAM;QACpC;IACF;IACA,IAAI,iBAAiB,CAAC;IACtB,MAAM,MAAM,aAAa,IAAI,CAAC,CAAC,kBAAoB,gBAAgB,KAAK,KAAK;IAC7E,IAAI,OAAO,UAAU,IAAI,KAAK,EAAE;QAC9B,iBAAiB;IACnB,OAAO;QACL,eAAe,KAAK,GAAG;QACvB,aAAa,IAAI,CAAC;IACpB;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,eAAe,KAAK,GAAG;YAAE,MAAM;QAAG;IACpC,OAAO;QACL,eAAe,KAAK,GAAG;YAAE,MAAM;QAAM;IACvC;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,eAAe,KAAK,GAAG;YAAE,MAAM;QAAG;IACpC,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,cAAc,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QACtC,OAAO;YACL,eAAe,KAAK,GAAG;gBAAE,MAAM;YAAM;QACvC;IACF;IACA,IAAI,OAAO,WAAW,UAAU;QAC9B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE;QAC5C,cAAc,CAAC,IAAI,GAAG;IACxB,OAAO;QACL,eAAe,MAAM,GAAG;IAC1B;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,cAAc,CAAC,IAAI,GAAG;IACxB,OAAO;QACL,eAAe,IAAI,GAAG;IACxB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,cAAc,CAAC,IAAI,GAAG;IACxB,OAAO;QACL,eAAe,IAAI,GAAG;IACxB;IACA,eAAe,WAAW,GAAG;QAAE,MAAM;IAAY;IACjD,eAAe,cAAc,GAAG;IAChC,eAAe,IAAI,GAAG;AACxB;AACA,MAAM,eAAe,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IACvF,IAAI,UAAU,QAAQ,UAAU,MAAM;QACpC;IACF;IACA,IAAI,YAAY,CAAC;IACjB,MAAM,MAAM,aAAa,IAAI,CAAC,CAAC,aAAe,WAAW,KAAK,KAAK;IACnE,IAAI,OAAO,UAAU,IAAI,KAAK,EAAE;QAC9B,YAAY;IACd,OAAO;QACL,UAAU,KAAK,GAAG;QAClB,aAAa,IAAI,CAAC;IACpB;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,UAAU,KAAK,GAAG;YAAE,MAAM;QAAG;IAC/B,OAAO;QACL,UAAU,KAAK,GAAG;YAAE,MAAM;QAAM;IAClC;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,UAAU,KAAK,GAAG;YAAE,MAAM;QAAG;IAC/B,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,SAAS,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QACjC,OAAO;YACL,UAAU,KAAK,GAAG;gBAAE,MAAM;YAAM;QAClC;IACF;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,UAAU,KAAK,GAAG;YAAE,MAAM;QAAG;IAC/B,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,SAAS,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QACjC,OAAO;YACL,UAAU,KAAK,GAAG;gBAAE,MAAM;YAAM;QAClC;IACF;IACA,IAAI,OAAO,WAAW,UAAU;QAC9B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE;QAC5C,SAAS,CAAC,IAAI,GAAG;IACnB,OAAO;QACL,UAAU,MAAM,GAAG;IACrB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,SAAS,CAAC,IAAI,GAAG;IACnB,OAAO;QACL,UAAU,IAAI,GAAG;IACnB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,SAAS,CAAC,IAAI,GAAG;IACnB,OAAO;QACL,UAAU,IAAI,GAAG;IACnB;IACA,UAAU,IAAI,GAAG;IACjB,UAAU,WAAW,GAAG;QAAE,MAAM;IAAY;IAC5C,UAAU,cAAc,GAAG;AAC7B;AACA,MAAM,eAAe,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IACvF,IAAI,UAAU,QAAQ,UAAU,MAAM;QACpC;IACF;IACA,IAAI,YAAY,CAAC;IACjB,MAAM,MAAM,aAAa,IAAI,CAAC,CAAC,aAAe,WAAW,KAAK,KAAK;IACnE,IAAI,OAAO,UAAU,IAAI,KAAK,EAAE;QAC9B,YAAY;IACd,OAAO;QACL,UAAU,KAAK,GAAG;QAClB,aAAa,IAAI,CAAC;IACpB;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,UAAU,KAAK,GAAG;YAAE,MAAM;QAAG;IAC/B,OAAO;QACL,UAAU,KAAK,GAAG;YAAE,MAAM;QAAM;IAClC;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,UAAU,KAAK,GAAG;YAAE,MAAM;QAAG;IAC/B,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,SAAS,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QACjC,OAAO;YACL,UAAU,KAAK,GAAG;gBAAE,MAAM;YAAM;QAClC;IACF;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,UAAU,KAAK,GAAG;YAAE,MAAM;QAAG;IAC/B,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,SAAS,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QACjC,OAAO;YACL,UAAU,KAAK,GAAG;gBAAE,MAAM;YAAM;QAClC;IACF;IACA,IAAI,OAAO,WAAW,UAAU;QAC9B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE;QAC5C,SAAS,CAAC,IAAI,GAAG;IACnB,OAAO;QACL,UAAU,MAAM,GAAG;IACrB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,SAAS,CAAC,IAAI,GAAG;IACnB,OAAO;QACL,UAAU,IAAI,GAAG;IACnB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,SAAS,CAAC,IAAI,GAAG;IACnB,OAAO;QACL,UAAU,IAAI,GAAG;IACnB;IACA,UAAU,IAAI,GAAG;IACjB,UAAU,WAAW,GAAG;QAAE,MAAM;IAAY;IAC5C,UAAU,cAAc,GAAG;AAC7B;AACA,MAAM,4BAA4B,SAAS,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IACvE,IAAI,UAAU,QAAQ,UAAU,MAAM;QACpC;IACF;IACA,IAAI,WAAW,CAAC;IAChB,MAAM,MAAM,WAAW,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;IAC/D,IAAI,OAAO,UAAU,IAAI,KAAK,EAAE;QAC9B,WAAW;IACb,OAAO;QACL,SAAS,KAAK,GAAG;QACjB,WAAW,IAAI,CAAC;IAClB;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,SAAS,KAAK,GAAG;YAAE,MAAM;QAAG;IAC9B,OAAO;QACL,SAAS,KAAK,GAAG;YAAE,MAAM;QAAM;IACjC;IACA,IAAI,SAAS,KAAK,KAAK,SAAS,MAAM;QACpC,SAAS,IAAI,GAAG;YAAE,MAAM;QAAS;IACnC,OAAO;QACL,IAAI,OAAO,SAAS,UAAU;YAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;YAC1C,QAAQ,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QAChC,OAAO;YACL,SAAS,IAAI,GAAG;gBAAE,MAAM;YAAK;QAC/B;IACF;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,QAAQ,CAAC,IAAI,GAAG;IAClB,OAAO;QACL,SAAS,IAAI,GAAG;IAClB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,QAAQ,CAAC,IAAI,GAAG;IAClB,OAAO;QACL,SAAS,IAAI,GAAG;IAClB;IACA,SAAS,cAAc,GAAG;IAC1B,SAAS,IAAI,GAAG;IAChB,sBAAsB;IACtB,uBAAuB;IACvB,mBAAmB,IAAI,CAAC;AAC1B;AACA,MAAM,uBAAuB,SAAS,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAClE,IAAI,UAAU,QAAQ,UAAU,MAAM;QACpC;IACF;IACA,IAAI,WAAW,CAAC;IAChB,MAAM,MAAM,WAAW,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;IAC/D,IAAI,OAAO,UAAU,IAAI,KAAK,EAAE;QAC9B,WAAW;IACb,OAAO;QACL,SAAS,KAAK,GAAG;QACjB,WAAW,IAAI,CAAC;IAClB;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,SAAS,KAAK,GAAG;YAAE,MAAM;QAAG;IAC9B,OAAO;QACL,SAAS,KAAK,GAAG;YAAE,MAAM;QAAM;IACjC;IACA,IAAI,SAAS,KAAK,KAAK,SAAS,MAAM;QACpC,SAAS,IAAI,GAAG;YAAE,MAAM;QAAY;IACtC,OAAO;QACL,IAAI,OAAO,SAAS,UAAU;YAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;YAC1C,QAAQ,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QAChC,OAAO;YACL,SAAS,IAAI,GAAG;gBAAE,MAAM;YAAK;QAC/B;IACF;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,QAAQ,CAAC,IAAI,GAAG;IAClB,OAAO;QACL,SAAS,IAAI,GAAG;IAClB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,QAAQ,CAAC,IAAI,GAAG;IAClB,OAAO;QACL,SAAS,IAAI,GAAG;IAClB;IACA,SAAS,cAAc,GAAG;IAC1B,SAAS,IAAI,GAAG;IAChB,sBAAsB;IACtB,uBAAuB;IACvB,mBAAmB,IAAI,CAAC;AAC1B;AACA,MAAM,oBAAoB,SAAS,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IACxF,IAAI,UAAU,QAAQ,UAAU,MAAM;QACpC;IACF;IACA,IAAI,WAAW,CAAC;IAChB,MAAM,MAAM,WAAW,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;IAC/D,IAAI,OAAO,UAAU,IAAI,KAAK,EAAE;QAC9B,WAAW;IACb,OAAO;QACL,SAAS,KAAK,GAAG;QACjB,WAAW,IAAI,CAAC;IAClB;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,SAAS,KAAK,GAAG;YAAE,MAAM;QAAG;IAC9B,OAAO;QACL,SAAS,KAAK,GAAG;YAAE,MAAM;QAAM;IACjC;IACA,IAAI,SAAS,KAAK,KAAK,SAAS,MAAM;QACpC,SAAS,IAAI,GAAG;YAAE,MAAM;QAAO;IACjC,OAAO;QACL,IAAI,OAAO,SAAS,UAAU;YAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;YAC1C,QAAQ,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QAChC,OAAO;YACL,SAAS,IAAI,GAAG;gBAAE,MAAM;YAAK;QAC/B;IACF;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,SAAS,KAAK,GAAG;YAAE,MAAM;QAAG;IAC9B,OAAO;QACL,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,QAAQ,CAAC,IAAI,GAAG;gBAAE,MAAM;YAAM;QAChC,OAAO;YACL,SAAS,KAAK,GAAG;gBAAE,MAAM;YAAM;QACjC;IACF;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,QAAQ,CAAC,IAAI,GAAG;IAClB,OAAO;QACL,SAAS,IAAI,GAAG;IAClB;IACA,IAAI,OAAO,SAAS,UAAU;QAC5B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1C,QAAQ,CAAC,IAAI,GAAG;IAClB,OAAO;QACL,SAAS,IAAI,GAAG;IAClB;IACA,SAAS,QAAQ,GAAG;IACpB,SAAS,cAAc,GAAG;IAC1B,SAAS,IAAI,GAAG;IAChB,sBAAsB;IACtB,uBAAuB;IACvB,mBAAmB,IAAI,CAAC;AAC1B;AACA,MAAM,wBAAwB;IAC5B,uBAAuB;IACvB,mBAAmB,GAAG;IACtB,sBAAsB,mBAAmB,GAAG;IAC5C,mBAAmB,IAAI,CAAC;AAC1B;AACA,MAAM,gBAAgB,SAAS,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY;IACjJ,IAAI,MAAM,aAAa,IAAI,CAAC,CAAC,UAAY,QAAQ,KAAK,KAAK;IAC3D,IAAI,QAAQ,KAAK,GAAG;QAClB,MAAM,WAAW,IAAI,CAAC,CAAC,UAAY,QAAQ,KAAK,KAAK;QACrD,IAAI,QAAQ,KAAK,GAAG;YAClB;QACF;IACF;IACA,IAAI,YAAY,KAAK,KAAK,YAAY,MAAM;QAC1C,IAAI,OAAO,YAAY,UAAU;YAC/B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC7C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,OAAO,GAAG;QAChB;IACF;IACA,IAAI,cAAc,KAAK,KAAK,cAAc,MAAM;QAC9C,IAAI,OAAO,cAAc,UAAU;YACjC,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,UAAU,CAAC,EAAE;YAC/C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,SAAS,GAAG;QAClB;IACF;IACA,IAAI,gBAAgB,KAAK,KAAK,gBAAgB,MAAM;QAClD,IAAI,OAAO,gBAAgB,UAAU;YACnC,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,YAAY,CAAC,EAAE;YACjD,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,WAAW,GAAG;QACpB;IACF;IACA,IAAI,cAAc,KAAK,KAAK,cAAc,MAAM;QAC9C,IAAI,OAAO,cAAc,UAAU;YACjC,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,UAAU,CAAC,EAAE;YAC/C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,SAAS,GAAG;QAClB;IACF;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,KAAK,GAAG;QACd;IACF;IACA,IAAI,WAAW,KAAK,KAAK,WAAW,MAAM;QACxC,IAAI,OAAO,WAAW,UAAU;YAC9B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE;YAC5C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,MAAM,GAAG;QACf;IACF;IACA,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;QACtC,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,KAAK,GAAG;QACd;IACF;IACA,IAAI,eAAe,KAAK,KAAK,eAAe,MAAM;QAChD,IAAI,OAAO,eAAe,UAAU;YAClC,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,WAAW,CAAC,EAAE;YAChD,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,UAAU,GAAG;QACnB;IACF;IACA,IAAI,iBAAiB,KAAK,KAAK,iBAAiB,MAAM;QACpD,IAAI,OAAO,iBAAiB,UAAU;YACpC,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,EAAE;YAClD,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,YAAY,GAAG;QACrB;IACF;AACF;AACA,MAAM,iBAAiB,SAAS,WAAW,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;IAC3F,MAAM,MAAM,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,EAAE,KAAK;IAC/D,IAAI,QAAQ,KAAK,GAAG;QAClB;IACF;IACA,IAAI,cAAc,KAAK,KAAK,cAAc,MAAM;QAC9C,IAAI,OAAO,cAAc,UAAU;YACjC,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,UAAU,CAAC,EAAE;YAC/C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,SAAS,GAAG;QAClB;IACF;IACA,IAAI,cAAc,KAAK,KAAK,cAAc,MAAM;QAC9C,IAAI,OAAO,cAAc,UAAU;YACjC,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,UAAU,CAAC,EAAE;YAC/C,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,IAAI,SAAS,GAAG;QAClB;IACF;IACA,IAAI,YAAY,KAAK,KAAK,YAAY,MAAM;QAC1C,IAAI,OAAO,YAAY,UAAU;YAC/B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC7C,GAAG,CAAC,IAAI,GAAG,SAAS;QACtB,OAAO;YACL,IAAI,OAAO,GAAG,SAAS;QACzB;IACF;IACA,IAAI,YAAY,KAAK,KAAK,YAAY,MAAM;QAC1C,IAAI,OAAO,YAAY,UAAU;YAC/B,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC7C,GAAG,CAAC,IAAI,GAAG,SAAS;QACtB,OAAO;YACL,IAAI,OAAO,GAAG,SAAS;QACzB;IACF;AACF;AACA,MAAM,qBAAqB,SAAS,WAAW,EAAE,iBAAiB,EAAE,oBAAoB;IACtF,IAAI,oBAAoB;IACxB,IAAI,uBAAuB;IAC3B,IAAI,OAAO,sBAAsB,UAAU;QACzC,MAAM,QAAQ,OAAO,MAAM,CAAC,kBAAkB,CAAC,EAAE;QACjD,oBAAoB,SAAS;IAC/B,OAAO;QACL,oBAAoB,SAAS;IAC/B;IACA,IAAI,OAAO,yBAAyB,UAAU;QAC5C,MAAM,QAAQ,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAE;QACpD,uBAAuB,SAAS;IAClC,OAAO;QACL,uBAAuB,SAAS;IAClC;IACA,IAAI,qBAAqB,GAAG;QAC1B,iBAAiB;IACnB;IACA,IAAI,wBAAwB,GAAG;QAC7B,oBAAoB;IACtB;AACF;AACA,MAAM,kBAAkB;IACtB,OAAO;AACT;AACA,MAAM,qBAAqB;IACzB,OAAO;AACT;AACA,MAAM,0BAA0B;IAC9B,OAAO;AACT;AACA,MAAM,yBAAyB;IAC7B,OAAO;AACT;AACA,MAAM,kBAAkB,SAAS,cAAc;IAC7C,IAAI,mBAAmB,KAAK,KAAK,mBAAmB,MAAM;QACxD,OAAO;IACT,OAAO;QACL,OAAO,aAAa,MAAM,CAAC,CAAC;YAC1B,OAAO,eAAe,cAAc,KAAK;QAC3C;IACF;AACF;AACA,MAAM,aAAa,SAAS,KAAK;IAC/B,OAAO,aAAa,IAAI,CAAC,CAAC,iBAAmB,eAAe,KAAK,KAAK;AACxE;AACA,MAAM,iBAAiB,SAAS,cAAc;IAC5C,OAAO,OAAO,IAAI,CAAC,gBAAgB;AACrC;AACA,MAAM,gBAAgB,SAAS,cAAc;IAC3C,IAAI,mBAAmB,KAAK,KAAK,mBAAmB,MAAM;QACxD,OAAO;IACT,OAAO;QACL,OAAO,WAAW,MAAM,CAAC,CAAC,WAAa,SAAS,cAAc,KAAK;IACrE;AACF;AACA,MAAM,eAAe;AACrB,MAAM,UAAU;IACd,OAAO;AACT;AACA,MAAM,WAAW;IACf,OAAO;AACT;AACA,MAAM,UAAU,SAAS,WAAW;IAClC,cAAc;AAChB;AACA,MAAM,WAAW;IACf,OAAO;AACT;AACA,MAAM,QAAQ;IACZ,eAAe,EAAE;IACjB,aAAa;QACX;YACE,OAAO;YACP,OAAO;gBAAE,MAAM;YAAS;YACxB,MAAM;gBAAE,MAAM;YAAS;YACvB,MAAM;YACN,MAAM;YACN,gBAAgB;QAClB;KACD;IACD,sBAAsB;IACtB,uBAAuB;IACvB,qBAAqB;QAAC;KAAG;IACzB,OAAO,EAAE;IACT,qBAAqB;QAAC;KAAG;IACzB,QAAQ;IACR,cAAc;IACd,iBAAiB;IACjB,oBAAoB;AACtB;AACA,MAAM,WAAW;IACf,OAAO;IACP,QAAQ;IACR,MAAM;IACN,aAAa;IACb,cAAc;IACd,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,UAAU;IACV,WAAW;IACX,UAAU;IACV,SAAS;IACT,WAAW;IACX,SAAS;IACT,cAAc;IACd,YAAY;IACZ,WAAW;IACX,SAAS;IACT,SAAS;IACT,YAAY;IACZ,UAAU;IACV,aAAa;IACb,cAAc;AAChB;AACA,MAAM,YAAY;IAChB,QAAQ;IACR,MAAM;AACR;AACA,MAAM,YAAY;IAChB,QAAQ;IACR,SAAS;IACT,MAAM;AACR;AACA,MAAM,WAAW,SAAS,GAAG;IAC3B,IAAI,gBAAgB,CAAA,GAAA,yJAAA,CAAA,IAAY,AAAD,EAAE,KAAK,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IAC9C,QAAQ;AACV;AACA,MAAM,KAAK;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAA,yJAAA,CAAA,IAAW;IACX,aAAA,yJAAA,CAAA,IAAW;IACX,mBAAA,yJAAA,CAAA,IAAiB;IACjB,mBAAA,yJAAA,CAAA,IAAiB;IACjB,WAAW,IAAM,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD,IAAI,EAAE;IAC/B;IACA;IACA;IACA;IACA;IACA;AAEF;AACA,MAAM,WAAW,SAAS,IAAI,EAAE,QAAQ;IACtC,OAAO,CAAA,GAAA,+JAAA,CAAA,IAAU,AAAD,EAAE,MAAM;AAC1B;AACA,MAAM,YAAY,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI;IACxD,MAAM,YAAY,KAAK,MAAM,CAAC;IAC9B,UAAU,IAAI,CAAC,SAAS;IACxB,UAAU,IAAI,CAAC,UAAU;IACzB,UAAU,IAAI,CAAC,KAAK;IACpB,UAAU,IAAI,CAAC,KAAK;IACpB,IAAI,gBAAgB,KAAK,UAAU,CAAC,2BAA2B,OAAO,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;IAClF,UAAU,IAAI,CAAC,cAAc;AAC/B;AACA,MAAM,aAAa,CAAC,MAAM,OAAO;IAC/B,MAAM,WAAW,KAAK,MAAM,CAAC;IAC7B,IAAI,IAAI;IACR,KAAK,IAAI,OAAO,MAAO;QACrB,IAAI,YAAY,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG;QAChD,IAAI,cAAc,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG;QAClD,IAAI,UAAU,IAAI,OAAO,GAAG,SAAS,IAAI,OAAO,IAAI;QACpD,IAAI,UAAU,IAAI,OAAO,GAAG,SAAS,IAAI,OAAO,IAAI;QACpD,IAAI,MAAM;QACV,IAAI,MAAM,GAAG;YACX,IAAI,OAAO,SAAS,MAAM,CAAC;YAC3B,KAAK,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC;YAChC,KAAK,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC;YAChC,KAAK,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC;YAC9B,KAAK,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC;YAC9B,KAAK,IAAI,CAAC,gBAAgB;YAC1B,KAAK,IAAI,CAAC,UAAU;YACpB,KAAK,KAAK,CAAC,QAAQ;YACnB,IAAI,IAAI,IAAI,KAAK,SAAS;gBACxB,KAAK,IAAI,CAAC,cAAc,SAAS,MAAM;YACzC;YACA,IAAI,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,SAAS;gBAChD,KAAK,IAAI,CAAC,gBAAgB,SAAS,MAAM;YAC3C;YACA,IAAI,CAAC;QACP,OAAO;YACL,IAAI,OAAO,SAAS,MAAM,CAAC;YAC3B,KAAK,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,UAAU,aAAa,IAAI,CAClF,KACA,iDAAiD,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,EAAE,UAAU,CACvI,YACA,IAAI,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,GACnG,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,IAAI,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,IAAI,QAAQ,CAAC,CAAC;YAE7J,IAAI,IAAI,IAAI,KAAK,SAAS;gBACxB,KAAK,IAAI,CAAC,cAAc,SAAS,MAAM;YACzC;YACA,IAAI,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,SAAS;gBAChD,KAAK,IAAI,CAAC,gBAAgB,SAAS,MAAM;YAC3C;QACF;QACA,IAAI,cAAc,MAAM,WAAW;QACnC,uBAAuB,OACrB,IAAI,KAAK,CAAC,IAAI,EACd,UACA,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,IAAI,SAC/F,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,IAAI,SAC/F,IAAI,KAAK,CAAC,KAAK,EACf,IAAI,KAAK,CAAC,MAAM,EAChB;YAAE,MAAM;QAAU,GAClB;QAEF,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI;YACtC,cAAc,MAAM,WAAW;YAC/B,uBAAuB,OACrB,MAAM,IAAI,KAAK,CAAC,IAAI,GAAG,KACvB,UACA,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,IAAI,SAC/F,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,IAAI,MAAM,eAAe,GAAG,IAAI,SAC3H,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,KAAK,GACzC,IAAI,KAAK,CAAC,MAAM,EAChB;gBAAE,MAAM;gBAAW,cAAc;YAAS,GAC1C;QAEJ;IACF;AACF;AACA,MAAM,iBAAiB,SAAS,IAAI,EAAE,QAAQ,EAAE,KAAK;IACnD,MAAM,eAAe,KAAK,MAAM,CAAC;IACjC,IAAI,YAAY,SAAS,OAAO,GAAG,SAAS,OAAO,GAAG;IACtD,IAAI,cAAc,SAAS,WAAW,GAAG,SAAS,WAAW,GAAG;IAChE,IAAI,YAAY,SAAS,SAAS,GAAG,SAAS,SAAS,GAAG;IAC1D,IAAI,aAAa;QAAE,gBAAgB;QAAG,oBAAoB;IAAU;IACpE,IAAI,SAAS,QAAQ,EAAE;QACrB,aAAa;YAAE,gBAAgB;QAAE;IACnC;IACA,IAAI,WAAW;QACb,GAAG,SAAS,CAAC;QACb,GAAG,SAAS,CAAC;QACb,MAAM;QACN,QAAQ;QACR,OAAO,SAAS,KAAK;QACrB,QAAQ,SAAS,MAAM;QACvB,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IACA,SAAS,cAAc;IACvB,IAAI,eAAe,MAAM,YAAY;IACrC,aAAa,UAAU,GAAG;IAC1B,aAAa,QAAQ,GAAG,aAAa,QAAQ,GAAG;IAChD,aAAa,SAAS,GAAG;IACzB,uBAAuB,OACrB,SAAS,KAAK,CAAC,IAAI,EACnB,cACA,SAAS,CAAC,EACV,SAAS,CAAC,GAAG,SAAS,KAAK,CAAC,CAAC,EAC7B,SAAS,KAAK,EACd,SAAS,MAAM,EACf;QAAE,MAAM;IAAU,GAClB;IAEF,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK,IAAI;QAC9C,eAAe,MAAM,YAAY;QACjC,aAAa,SAAS,GAAG;QACzB,uBAAuB,OACrB,SAAS,IAAI,CAAC,IAAI,EAClB,cACA,SAAS,CAAC,EACV,SAAS,CAAC,GAAG,SAAS,IAAI,CAAC,CAAC,EAC5B,SAAS,KAAK,EACd,SAAS,MAAM,EACf;YAAE,MAAM;QAAU,GAClB;IAEJ;IACA,IAAI,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,IAAI,KAAK,IAAI;QAChD,eAAe,MAAM,YAAY;QACjC,aAAa,QAAQ,GAAG,aAAa,QAAQ,GAAG;QAChD,aAAa,SAAS,GAAG;QACzB,uBAAuB,OACrB,SAAS,KAAK,CAAC,IAAI,EACnB,cACA,SAAS,CAAC,EACV,SAAS,CAAC,GAAG,SAAS,KAAK,CAAC,CAAC,EAC7B,SAAS,KAAK,EACd,SAAS,MAAM,EACf;YAAE,MAAM;QAAU,GAClB;IAEJ;AACF;AACA,MAAM,cAAc,SAAS,IAAI,EAAE,OAAO,EAAE,KAAK;IAC/C,IAAI;IACJ,IAAI,YAAY,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,KAAK,CAAC,QAAQ,WAAW,CAAC,IAAI,GAAG,YAAY;IACjG,IAAI,cAAc,QAAQ,WAAW,GAAG,QAAQ,WAAW,GAAG,KAAK,CAAC,QAAQ,WAAW,CAAC,IAAI,GAAG,gBAAgB;IAC/G,IAAI,YAAY,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG;IACxD,IAAI,YAAY;IAChB,OAAQ,QAAQ,WAAW,CAAC,IAAI;QAC9B,KAAK;YACH,YAAY;YACZ;QACF,KAAK;YACH,YAAY;YACZ;IACJ;IACA,MAAM,cAAc,KAAK,MAAM,CAAC;IAChC,YAAY,IAAI,CAAC,SAAS;IAC1B,MAAM,OAAO,CAAA,GAAA,+JAAA,CAAA,IAAW,AAAD;IACvB,OAAQ,QAAQ,WAAW,CAAC,IAAI;QAC9B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,KAAK,CAAC,GAAG,QAAQ,CAAC;YAClB,KAAK,CAAC,GAAG,QAAQ,CAAC;YAClB,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK,GAAG,QAAQ,KAAK;YAC1B,KAAK,MAAM,GAAG,QAAQ,MAAM;YAC5B,KAAK,MAAM,GAAG;YACd,KAAK,EAAE,GAAG;YACV,KAAK,EAAE,GAAG;YACV,KAAK,KAAK,GAAG;gBAAE,gBAAgB;YAAI;YACnC,SAAS,aAAa;YACtB;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,YAAY,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,WAAW,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,UAAU,aAAa,IAAI,CAC7G,KACA,4HAA4H,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,UAAU,CAAC,QAAQ,QAAQ,KAAK,GAAG,GAAG,UAAU,CAAC,UAAU,QAAQ,MAAM;YAEvQ,YAAY,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,UAAU,aAAa,IAAI,CAC1G,KACA,0DAA0D,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,UAAU,CAAC,QAAQ,QAAQ,KAAK,GAAG;YAE/J;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,YAAY,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,WAAW,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,UAAU,aAAa,IAAI,CAC7G,KACA,kHAAkH,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,UAAU,CAAC,SAAS,QAAQ,KAAK,EAAE,UAAU,CAAC,QAAQ,QAAQ,MAAM,GAAG;YAE3P,YAAY,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,UAAU,aAAa,IAAI,CAC1G,KACA,2DAA2D,UAAU,CAAC,UAAU,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,UAAU,CAAC,QAAQ,QAAQ,MAAM,GAAG;YAEjL;IACJ;IACA,IAAI,kBAAkB,eAAe,OAAO,QAAQ,WAAW,CAAC,IAAI;IACpE,YAAY,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,WAAW,IAAI,CAAC,eAAe,gBAAgB,UAAU,EAAE,IAAI,CAAC,aAAa,gBAAgB,QAAQ,GAAG,GAAG,IAAI,CAAC,cAAc,UAAU,IAAI,CAAC,gBAAgB,WAAW,IAAI,CAAC,cAAc,QAAQ,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,QAAQ,CAAC,GAAG,QAAQ,KAAK,GAAG,IAAI,QAAQ,WAAW,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,KAAK,QAAQ,CAAC,GAAG,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,QAAQ,WAAW,CAAC,IAAI,GAAG;IAC3Z,OAAQ,QAAQ,WAAW,CAAC,IAAI;QAC9B,KAAK;QACL,KAAK;YACH,UACE,aACA,IACA,IACA,QAAQ,CAAC,GAAG,QAAQ,KAAK,GAAG,IAAI,IAChC,QAAQ,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC,EAC3B;YAEF;IACJ;IACA,IAAI,eAAe,KAAK,CAAC,QAAQ,WAAW,CAAC,IAAI,GAAG,OAAO;IAC3D,aAAa,UAAU,GAAG;IAC1B,aAAa,QAAQ,GAAG,aAAa,QAAQ,GAAG;IAChD,aAAa,SAAS,GAAG;IACzB,uBAAuB,OACrB,QAAQ,KAAK,CAAC,IAAI,EAClB,aACA,QAAQ,CAAC,EACT,QAAQ,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC,EAC3B,QAAQ,KAAK,EACb,QAAQ,MAAM,EACd;QAAE,MAAM;IAAU,GAClB;IAEF,eAAe,KAAK,CAAC,QAAQ,WAAW,CAAC,IAAI,GAAG,OAAO;IACvD,aAAa,SAAS,GAAG;IACzB,IAAI,QAAQ,KAAK,IAAI,CAAC,CAAC,KAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI;QAC7E,uBAAuB,OACrB,QAAQ,KAAK,CAAC,IAAI,EAClB,aACA,QAAQ,CAAC,EACT,QAAQ,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC,EAC3B,QAAQ,KAAK,EACb,QAAQ,MAAM,EACd;YAAE,MAAM;YAAW,cAAc;QAAS,GAC1C;IAEJ,OAAO,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI;QACnD,uBAAuB,OACrB,QAAQ,IAAI,CAAC,IAAI,EACjB,aACA,QAAQ,CAAC,EACT,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC,EAC1B,QAAQ,KAAK,EACb,QAAQ,MAAM,EACd;YAAE,MAAM;YAAW,cAAc;QAAS,GAC1C;IAEJ;IACA,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK,IAAI;QAC9C,eAAe,MAAM,UAAU;QAC/B,aAAa,SAAS,GAAG;QACzB,uBAAuB,OACrB,QAAQ,KAAK,CAAC,IAAI,EAClB,aACA,QAAQ,CAAC,EACT,QAAQ,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC,EAC3B,QAAQ,KAAK,EACb,QAAQ,MAAM,EACd;YAAE,MAAM;QAAU,GAClB;IAEJ;IACA,OAAO,QAAQ,MAAM;AACvB;AACA,MAAM,qBAAqB,SAAS,IAAI;IACtC,KAAK,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,aAAa,WAAW,IAAI,CAAC,aAAa,WAAW,MAAM,CAAC,QAAQ,IAAI,CAAC,aAAa,aAAa,IAAI,CACtK,KACA;AAEJ;AACA,MAAM,qBAAqB,SAAS,IAAI;IACtC,KAAK,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,UAAU,MAAM,MAAM,CAAC,QAAQ,IAAI,CAAC,aAAa,aAAa,IAAI,CACrJ,KACA;AAEJ;AACA,MAAM,kBAAkB,SAAS,IAAI;IACnC,KAAK,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,UAAU,MAAM,MAAM,CAAC,QAAQ,IAAI,CAAC,aAAa,aAAa,IAAI,CAClJ,KACA;AAEJ;AACA,MAAM,kBAAkB,SAAS,IAAI;IACnC,KAAK,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,aAAa,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,kBAAkB,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK;AACtO;AACA,MAAM,iBAAiB,SAAS,IAAI;IAClC,KAAK,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,kBAAkB,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK;AACrO;AACA,MAAM,wBAAwB,SAAS,IAAI;IACzC,KAAK,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,eAAe,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK;AACnM;AACA,MAAM,sBAAsB,SAAS,IAAI;IACvC,KAAK,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,kBAAkB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK;AACvO;AACA,MAAM,uBAAuB,SAAS,IAAI;IACxC,MAAM,OAAO,KAAK,MAAM,CAAC;IACzB,MAAM,SAAS,KAAK,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,aAAa,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;IAClK,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,SAAS,IAAI,CAAC,UAAU,WAAW,KAAK,CAAC,oBAAoB,QAAQ,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,KAAK;IAC9I,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,UAAU,WAAW,KAAK,CAAC,oBAAoB,QAAQ,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,KAAK;AAC/I;AACA,MAAM,iBAAiB,CAAC,KAAK;IAC3B,OAAO;QACL,YAAY,GAAG,CAAC,cAAc,aAAa;QAC3C,UAAU,GAAG,CAAC,cAAc,WAAW;QACvC,YAAY,GAAG,CAAC,cAAc,aAAa;IAC7C;AACF;AACA,MAAM,yBAAyB;IAC7B,SAAS,OAAO,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACxD,MAAM,OAAO,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,GAAG,KAAK,CAAC,eAAe,UAAU,IAAI,CAAC;QACzH,cAAc,MAAM;IACtB;IACA,SAAS,QAAQ,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK;QAChE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG;QAC7C,MAAM,QAAQ,QAAQ,KAAK,CAAC,yJAAA,CAAA,IAAM,CAAC,cAAc;QACjD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,KAAK,IAAI,WAAW,WAAW,CAAC,MAAM,MAAM,GAAG,CAAC,IAAI;YAC1D,MAAM,OAAO,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,UAAU,IAAI,CAAC,qBAAqB,UAAU,KAAK,CAAC,aAAa,UAAU,KAAK,CAAC,eAAe,YAAY,KAAK,CAAC,eAAe;YAC1N,KAAK,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,sBAAsB;YAC9E,cAAc,MAAM;QACtB;IACF;IACA,SAAS,KAAK,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK;QAC7D,MAAM,IAAI,EAAE,MAAM,CAAC;QACnB,MAAM,IAAI,EAAE,MAAM,CAAC,iBAAiB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU;QAClG,MAAM,OAAO,EAAE,MAAM,CAAC,aAAa,KAAK,CAAC,WAAW,SAAS,KAAK,CAAC,UAAU,QAAQ,KAAK,CAAC,SAAS;QACpG,KAAK,MAAM,CAAC,OAAO,KAAK,CAAC,WAAW,cAAc,KAAK,CAAC,cAAc,UAAU,KAAK,CAAC,kBAAkB,UAAU,IAAI,CAAC;QACvH,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW;QACpD,cAAc,MAAM;IACtB;IACA,SAAS,cAAc,MAAM,EAAE,iBAAiB;QAC9C,IAAK,MAAM,OAAO,kBAAmB;YACnC,IAAI,kBAAkB,cAAc,CAAC,MAAM;gBACzC,OAAO,IAAI,CAAC,KAAK,iBAAiB,CAAC,IAAI;YACzC;QACF;IACF;IACA,OAAO,SAAS,KAAK;QACnB,OAAO,MAAM,aAAa,KAAK,OAAO,OAAO,MAAM,aAAa,KAAK,QAAQ,SAAS;IACxF;AACF;AACA,MAAM,UAAU;IACd;IACA,cAAc;IACd;IACA,UAAU;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;AACA,IAAI,qBAAqB,GAAG,qBAAqB;AACjD,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,OAAO,EAAE,GAAG;AACZ,IAAI,OAAO,CAAC;AACZ,MAAM;IACJ,YAAY,OAAO,CAAE;QACnB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK;QAC5B,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK;QAC5B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK;QAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK;QAC5B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK;QAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG;QACpB,QAAQ,QAAQ,EAAE,CAAC,SAAS;IAC9B;IACA,QAAQ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;QACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;QACxC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;IAC1C;IACA,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;QAC5B,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG;YACvB,GAAG,CAAC,IAAI,GAAG;QACb,OAAO;YACL,GAAG,CAAC,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI;QAC9B;IACF;IACA,OAAO,OAAO,EAAE;QACd,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG;QACxC,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,MAAM,GAAG;QAC3I,IAAI,SAAS,UAAU,QAAQ,KAAK;QACpC,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,MAAM,GAAG;QACtD,IAAI,SAAS,UAAU,QAAQ,MAAM;QACrC,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,cAAc;YACzG,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,MAAM,GAAG,KAAK,gBAAgB;YACvE,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,MAAM,GAAG;YACjD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,SAAS,UAAU,QAAQ,KAAK;YACtD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,SAAS,UAAU,QAAQ,MAAM;YACvD,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG;QACtB;QACA,QAAQ,CAAC,GAAG;QACZ,QAAQ,CAAC,GAAG;QACZ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,SAAS,KAAK,GAAG;QACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,SAAS,KAAK,GAAG;QACrD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,QAAQ,KAAK,GAAG;QACnD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,QAAQ,KAAK,GAAG;QACnD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,SAAS,KAAK,GAAG;QACzD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,SAAS,KAAK,GAAG;QACzD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,QAAQ,KAAK,GAAG;QACvD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,QAAQ,KAAK,GAAG;IACzD;IACA,KAAK,OAAO,EAAE;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;YACV,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,YAAY,KAAK;QACnB;QACA,IAAI,CAAC,QAAQ,GAAG;YACd,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,QAAQ,KAAK;YACb,OAAO,KAAK;YACZ,KAAK;QACP;QACA,QAAQ,QAAQ,EAAE,CAAC,SAAS;IAC9B;IACA,eAAe,MAAM,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;IACrB;AACF;AACA,MAAM,UAAU,SAAS,GAAG;IAC1B,CAAA,GAAA,yJAAA,CAAA,IAAe,AAAD,EAAE,MAAM;IACtB,IAAI,IAAI,UAAU,EAAE;QAClB,KAAK,gBAAgB,GAAG,KAAK,gBAAgB,GAAG,KAAK,iBAAiB,GAAG,IAAI,UAAU;IACzF;IACA,IAAI,IAAI,QAAQ,EAAE;QAChB,KAAK,cAAc,GAAG,KAAK,cAAc,GAAG,KAAK,eAAe,GAAG,IAAI,QAAQ;IACjF;IACA,IAAI,IAAI,UAAU,EAAE;QAClB,KAAK,gBAAgB,GAAG,KAAK,gBAAgB,GAAG,KAAK,iBAAiB,GAAG,IAAI,UAAU;IACzF;AACF;AACA,MAAM,cAAc,CAAC,KAAK;IACxB,OAAO;QACL,YAAY,GAAG,CAAC,cAAc,aAAa;QAC3C,UAAU,GAAG,CAAC,cAAc,WAAW;QACvC,YAAY,GAAG,CAAC,cAAc,aAAa;IAC7C;AACF;AACA,MAAM,eAAe,CAAC;IACpB,OAAO;QACL,YAAY,IAAI,kBAAkB;QAClC,UAAU,IAAI,gBAAgB;QAC9B,YAAY,IAAI,kBAAkB;IACpC;AACF;AACA,MAAM,cAAc,CAAC;IACnB,OAAO;QACL,YAAY,IAAI,iBAAiB;QACjC,UAAU,IAAI,eAAe;QAC7B,YAAY,IAAI,iBAAiB;IACnC;AACF;AACA,SAAS,kBAAkB,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,cAAc;IACrF,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE;QAC5B,IAAI,iBAAiB;YACnB,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB;YAC3E,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,yJAAA,CAAA,IAAM,CAAC,cAAc,EAAE,MAAM;YACxF,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG;YAC1B,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAA,GAAA,yJAAA,CAAA,IAAmB,AAAD,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE;QACzE,OAAO;YACL,IAAI,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,yJAAA,CAAA,IAAM,CAAC,cAAc;YAC9D,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,MAAM;YAC1C,IAAI,aAAa;YACjB,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG;YAC3B,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG;YAC1B,KAAK,MAAM,QAAQ,MAAO;gBACxB,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK,GAAG,CAChC,CAAA,GAAA,yJAAA,CAAA,IAAkB,AAAD,EAAE,MAAM,WACzB,OAAO,CAAC,SAAS,CAAC,KAAK;gBAEzB,aAAa,CAAA,GAAA,yJAAA,CAAA,IAAmB,AAAD,EAAE,MAAM;gBACvC,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG;YACxD;QACF;IACF;AACF;AACA,MAAM,eAAe,SAAS,QAAQ,EAAE,QAAQ,EAAE,MAAM;IACtD,SAAS,CAAC,GAAG,OAAO,IAAI,CAAC,MAAM;IAC/B,SAAS,CAAC,GAAG,OAAO,IAAI,CAAC,MAAM;IAC/B,SAAS,KAAK,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,MAAM;IACvD,SAAS,MAAM,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,MAAM;IACxD,SAAS,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,GAAG;IACxC,IAAI,mBAAmB,SAAS,IAAI,IAAI,KAAK,IAAI;IACjD,IAAI,oBAAoB,aAAa;IACrC,kBAAkB,QAAQ,GAAG,kBAAkB,QAAQ,GAAG;IAC1D,kBAAkB,UAAU,GAAG;IAC/B,IAAI,iBAAiB,CAAA,GAAA,yJAAA,CAAA,IAAkB,AAAD,EAAE,SAAS,KAAK,CAAC,IAAI,EAAE;IAC7D,kBAAkB,SAAS,UAAU,kBAAkB,mBAAmB;IAC1E,QAAQ,YAAY,CAAC,UAAU,UAAU;AAC3C;AACA,MAAM,mBAAmB,SAAS,aAAa,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW;IACnF,IAAI,IAAI;IACR,KAAK,MAAM,cAAc,YAAa;QACpC,IAAI;QACJ,MAAM,UAAU,aAAa,CAAC,WAAW;QACzC,IAAI,kBAAkB,YAAY,MAAM,QAAQ,WAAW,CAAC,IAAI;QAChE,gBAAgB,QAAQ,GAAG,gBAAgB,QAAQ,GAAG;QACtD,QAAQ,WAAW,CAAC,KAAK,GAAG,CAAA,GAAA,yJAAA,CAAA,IAAkB,AAAD,EAC3C,MAAM,QAAQ,WAAW,CAAC,IAAI,GAAG,KACjC;QAEF,QAAQ,WAAW,CAAC,MAAM,GAAG,gBAAgB,QAAQ,GAAG;QACxD,QAAQ,WAAW,CAAC,CAAC,GAAG,KAAK,cAAc;QAC3C,IAAI,QAAQ,WAAW,CAAC,CAAC,GAAG,QAAQ,WAAW,CAAC,MAAM,GAAG;QACzD,QAAQ,KAAK,GAAG;YAAE,OAAO;YAAG,QAAQ;YAAG,GAAG;QAAE;QAC5C,OAAQ,QAAQ,WAAW,CAAC,IAAI;YAC9B,KAAK;YACL,KAAK;gBACH,QAAQ,KAAK,CAAC,KAAK,GAAG;gBACtB,QAAQ,KAAK,CAAC,MAAM,GAAG;gBACvB,QAAQ,KAAK,CAAC,CAAC,GAAG;gBAClB,IAAI,QAAQ,KAAK,CAAC,CAAC,GAAG,QAAQ,KAAK,CAAC,MAAM;gBAC1C;QACJ;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,QAAQ,KAAK,CAAC,KAAK,GAAG;YACtB,QAAQ,KAAK,CAAC,MAAM,GAAG;YACvB,QAAQ,KAAK,CAAC,CAAC,GAAG;YAClB,IAAI,QAAQ,KAAK,CAAC,CAAC,GAAG,QAAQ,KAAK,CAAC,MAAM;QAC5C;QACA,IAAI,kBAAkB,QAAQ,IAAI,IAAI,KAAK,IAAI;QAC/C,IAAI,iBAAiB,KAAK,KAAK,GAAG,KAAK,cAAc,GAAG;QACxD,IAAI,mBAAmB,YAAY,MAAM,QAAQ,WAAW,CAAC,IAAI;QACjE,iBAAiB,QAAQ,GAAG,iBAAiB,QAAQ,GAAG;QACxD,iBAAiB,UAAU,GAAG;QAC9B,kBAAkB,SAAS,SAAS,iBAAiB,kBAAkB;QACvE,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI;QACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM;QAChD,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI;YAC5C,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,QAAQ,IAAI,CAAC,IAAI,GAAG;YAC9C,IAAI,mBAAmB,YAAY,MAAM,QAAQ,WAAW,CAAC,IAAI;YACjE,kBAAkB,QAAQ,SAAS,iBAAiB,kBAAkB;YACtE,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI;YACxB,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM;QAChD,OAAO,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK,IAAI;YACrD,QAAQ,KAAK,CAAC,IAAI,GAAG,MAAM,QAAQ,KAAK,CAAC,IAAI,GAAG;YAChD,IAAI,mBAAmB,YAAY,MAAM,QAAQ,KAAK,CAAC,IAAI;YAC3D,kBAAkB,SAAS,SAAS,iBAAiB,kBAAkB;YACvE,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI;YACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM;QAClD;QACA,IAAI,aAAa;QACjB,IAAI,YAAY,QAAQ,KAAK,CAAC,KAAK;QACnC,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK,IAAI;YAC9C,IAAI,mBAAmB,YAAY,MAAM,QAAQ,WAAW,CAAC,IAAI;YACjE,kBAAkB,SAAS,SAAS,iBAAiB,kBAAkB;YACvE,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI;YACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM;YAChD,YAAY,KAAK,GAAG,CAAC,QAAQ,KAAK,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK;YAC7D,aAAa,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,GAAG;QAChD;QACA,YAAY,YAAY,KAAK,cAAc;QAC3C,QAAQ,KAAK,GAAG,KAAK,GAAG,CAAC,QAAQ,KAAK,IAAI,KAAK,KAAK,EAAE,WAAW,KAAK,KAAK;QAC3E,QAAQ,MAAM,GAAG,KAAK,GAAG,CAAC,QAAQ,MAAM,IAAI,KAAK,MAAM,EAAE,YAAY,KAAK,MAAM;QAChF,QAAQ,MAAM,GAAG,QAAQ,MAAM,IAAI,KAAK,aAAa;QACrD,cAAc,MAAM,CAAC;QACrB,QAAQ,WAAW,CAAC,UAAU,SAAS;IACzC;IACA,cAAc,cAAc,CAAC,KAAK,aAAa;AACjD;AACA,MAAM;IACJ,YAAY,CAAC,EAAE,CAAC,CAAE;QAChB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;IACX;AACF;AACA,IAAI,oBAAoB,SAAS,QAAQ,EAAE,QAAQ;IACjD,IAAI,KAAK,SAAS,CAAC;IACnB,IAAI,KAAK,SAAS,CAAC;IACnB,IAAI,KAAK,SAAS,CAAC;IACnB,IAAI,KAAK,SAAS,CAAC;IACnB,IAAI,cAAc,KAAK,SAAS,KAAK,GAAG;IACxC,IAAI,cAAc,KAAK,SAAS,MAAM,GAAG;IACzC,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK;IACvB,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK;IACvB,IAAI,SAAS,KAAK;IAClB,IAAI,UAAU,SAAS,MAAM,GAAG,SAAS,KAAK;IAC9C,IAAI,cAAc;IAClB,IAAI,MAAM,MAAM,KAAK,IAAI;QACvB,cAAc,IAAI,MAAM,KAAK,SAAS,KAAK,EAAE;IAC/C,OAAO,IAAI,MAAM,MAAM,KAAK,IAAI;QAC9B,cAAc,IAAI,MAAM,IAAI;IAC9B,OAAO,IAAI,MAAM,MAAM,KAAK,IAAI;QAC9B,cAAc,IAAI,MAAM,aAAa,KAAK,SAAS,MAAM;IAC3D,OAAO,IAAI,MAAM,MAAM,KAAK,IAAI;QAC9B,cAAc,IAAI,MAAM,aAAa;IACvC;IACA,IAAI,KAAK,MAAM,KAAK,IAAI;QACtB,IAAI,WAAW,QAAQ;YACrB,cAAc,IAAI,MAAM,IAAI,cAAc,SAAS,SAAS,KAAK,GAAG;QACtE,OAAO;YACL,cAAc,IAAI,MAChB,cAAc,KAAK,KAAK,SAAS,MAAM,GAAG,GAC1C,KAAK,SAAS,MAAM;QAExB;IACF,OAAO,IAAI,KAAK,MAAM,KAAK,IAAI;QAC7B,IAAI,WAAW,QAAQ;YACrB,cAAc,IAAI,MAAM,KAAK,SAAS,KAAK,EAAE,cAAc,SAAS,SAAS,KAAK,GAAG;QACvF,OAAO;YACL,cAAc,IAAI,MAChB,cAAc,KAAK,KAAK,SAAS,MAAM,GAAG,GAC1C,KAAK,SAAS,MAAM;QAExB;IACF,OAAO,IAAI,KAAK,MAAM,KAAK,IAAI;QAC7B,IAAI,WAAW,QAAQ;YACrB,cAAc,IAAI,MAAM,KAAK,SAAS,KAAK,EAAE,cAAc,SAAS,SAAS,KAAK,GAAG;QACvF,OAAO;YACL,cAAc,IAAI,MAAM,cAAc,SAAS,MAAM,GAAG,IAAI,KAAK,IAAI;QACvE;IACF,OAAO,IAAI,KAAK,MAAM,KAAK,IAAI;QAC7B,IAAI,WAAW,QAAQ;YACrB,cAAc,IAAI,MAAM,IAAI,cAAc,SAAS,KAAK,GAAG,IAAI;QACjE,OAAO;YACL,cAAc,IAAI,MAAM,cAAc,SAAS,MAAM,GAAG,IAAI,KAAK,IAAI;QACvE;IACF;IACA,OAAO;AACT;AACA,IAAI,qBAAqB,SAAS,QAAQ,EAAE,OAAO;IACjD,IAAI,oBAAoB;QAAE,GAAG;QAAG,GAAG;IAAE;IACrC,kBAAkB,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,KAAK,GAAG;IAClD,kBAAkB,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,MAAM,GAAG;IACnD,IAAI,aAAa,kBAAkB,UAAU;IAC7C,kBAAkB,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,KAAK,GAAG;IACpD,kBAAkB,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG;IACrD,IAAI,WAAW,kBAAkB,SAAS;IAC1C,OAAO;QAAE;QAAY;IAAS;AAChC;AACA,MAAM,WAAW,SAAS,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO;IAC/D,IAAI,IAAI;IACR,KAAK,IAAI,OAAO,MAAO;QACrB,IAAI,IAAI;QACR,IAAI,cAAc,IAAI,IAAI,IAAI,KAAK,IAAI;QACvC,IAAI,UAAU,YAAY;QAC1B,IAAI,cAAc,QAAQ,EAAE,CAAC,SAAS;QACtC,IAAI,gBAAgB,aAAa;YAC/B,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI;QAC5C;QACA,IAAI,iBAAiB,CAAA,GAAA,yJAAA,CAAA,IAAkB,AAAD,EAAE,IAAI,KAAK,CAAC,IAAI,EAAE;QACxD,kBAAkB,SAAS,KAAK,aAAa,SAAS;QACtD,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI;YACtC,iBAAiB,CAAA,GAAA,yJAAA,CAAA,IAAkB,AAAD,EAAE,IAAI,KAAK,CAAC,IAAI,EAAE;YACpD,kBAAkB,SAAS,KAAK,aAAa,SAAS;QACxD;QACA,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI;YACtC,iBAAiB,CAAA,GAAA,yJAAA,CAAA,IAAkB,AAAD,EAAE,IAAI,KAAK,CAAC,IAAI,EAAE;YACpD,kBAAkB,SAAS,KAAK,aAAa,SAAS;QACxD;QACA,IAAI,WAAW,cAAc,IAAI,IAAI;QACrC,IAAI,UAAU,cAAc,IAAI,EAAE;QAClC,IAAI,SAAS,mBAAmB,UAAU;QAC1C,IAAI,UAAU,GAAG,OAAO,UAAU;QAClC,IAAI,QAAQ,GAAG,OAAO,QAAQ;IAChC;IACA,QAAQ,QAAQ,CAAC,UAAU,OAAO;AACpC;AACA,SAAS,mBAAmB,QAAQ,EAAE,mBAAmB,EAAE,YAAY,EAAE,iBAAiB,EAAE,OAAO;IACjG,IAAI,gBAAgB,IAAI,OAAO;IAC/B,cAAc,IAAI,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,iBAAiB,kBAAkB,MAAM;IACjH,KAAK,IAAI,CAAC,GAAG,gBAAgB,IAAI,kBAAkB,OAAO,GAAI;QAC5D,IAAI,IAAI;QACR,gBAAgB,KAAK,GAAG;YAAE,OAAO;YAAG,QAAQ;YAAG,GAAG;QAAE;QACpD,IAAI,gBAAgB,MAAM,EAAE;YAC1B,gBAAgB,KAAK,CAAC,KAAK,GAAG;YAC9B,gBAAgB,KAAK,CAAC,MAAM,GAAG;YAC/B,gBAAgB,KAAK,CAAC,CAAC,GAAG;YAC1B,IAAI,gBAAgB,KAAK,CAAC,CAAC,GAAG,gBAAgB,KAAK,CAAC,MAAM;QAC5D;QACA,IAAI,0BAA0B,gBAAgB,IAAI,IAAI,KAAK,IAAI;QAC/D,IAAI,2BAA2B,aAAa;QAC5C,yBAAyB,QAAQ,GAAG,yBAAyB,QAAQ,GAAG;QACxE,yBAAyB,UAAU,GAAG;QACtC,kBACE,SACA,iBACA,yBACA,0BACA,cAAc,IAAI,CAAC,UAAU;QAE/B,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI;QACjC,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM;QAChE,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,IAAI,CAAC,IAAI,KAAK,IAAI;YAC5D,gBAAgB,IAAI,CAAC,IAAI,GAAG,MAAM,gBAAgB,IAAI,CAAC,IAAI,GAAG;YAC9D,IAAI,0BAA0B,aAAa;YAC3C,kBACE,QACA,iBACA,yBACA,yBACA,cAAc,IAAI,CAAC,UAAU;YAE/B,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI;YAChC,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM;QAChE;QACA,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,KAAK,CAAC,IAAI,KAAK,IAAI;YAC9D,IAAI,2BAA2B,aAAa;YAC5C,yBAAyB,QAAQ,GAAG,yBAAyB,QAAQ,GAAG;YACxE,kBACE,SACA,iBACA,yBACA,0BACA,cAAc,IAAI,CAAC,UAAU;YAE/B,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI;YACjC,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM;QAClE;QACA,IAAI,KAAK,KAAK,IAAI,oBAAoB,GAAG;YACvC,IAAI,KAAK,aAAa,IAAI,CAAC,MAAM,GAAG,KAAK,cAAc;YACvD,IAAI,KAAK,aAAa,IAAI,CAAC,KAAK,GAAG,KAAK,cAAc,GAAG;YACzD,cAAc,OAAO,CAAC,IAAI,IAAI,IAAI;QACpC,OAAO;YACL,IAAI,KAAK,cAAc,IAAI,CAAC,KAAK,KAAK,cAAc,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,CAAC,KAAK,GAAG,KAAK,cAAc,GAAG,cAAc,IAAI,CAAC,MAAM;YAC5I,IAAI,KAAK,cAAc,IAAI,CAAC,MAAM;YAClC,cAAc,OAAO,CAAC,IAAI,IAAI,IAAI;QACpC;QACA,cAAc,IAAI,GAAG,gBAAgB,KAAK;QAC1C,IAAI,6BAA6B,QAAQ,EAAE,CAAC,eAAe,CAAC,gBAAgB,KAAK;QACjF,IAAI,4BAA4B,QAAQ,EAAE,CAAC,cAAc,CAAC,gBAAgB,KAAK;QAC/E,IAAI,0BAA0B,MAAM,GAAG,GAAG;YACxC,iBACE,eACA,UACA,4BACA;QAEJ;QACA,sBAAsB,gBAAgB,KAAK;QAC3C,IAAI,wBAAwB,QAAQ,EAAE,CAAC,YAAY,CAAC;QACpD,IAAI,sBAAsB,MAAM,GAAG,GAAG;YACpC,mBACE,UACA,qBACA,eACA,uBACA;QAEJ;QACA,IAAI,gBAAgB,KAAK,KAAK,UAAU;YACtC,aAAa,UAAU,iBAAiB;QAC1C;QACA,aAAa,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,CAChC,cAAc,IAAI,CAAC,KAAK,GAAG,KAAK,aAAa,EAC7C,aAAa,IAAI,CAAC,KAAK;QAEzB,aAAa,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,CAChC,cAAc,IAAI,CAAC,KAAK,GAAG,KAAK,aAAa,EAC7C,aAAa,IAAI,CAAC,KAAK;QAEzB,qBAAqB,KAAK,GAAG,CAAC,oBAAoB,aAAa,IAAI,CAAC,KAAK;QACzE,qBAAqB,KAAK,GAAG,CAAC,oBAAoB,aAAa,IAAI,CAAC,KAAK;IAC3E;AACF;AACA,MAAM,OAAO,SAAS,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO;IAChD,OAAO,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD,IAAI,EAAE;IACrB,MAAM,gBAAgB,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD,IAAI,aAAa;IAC/C,IAAI;IACJ,IAAI,kBAAkB,WAAW;QAC/B,iBAAiB,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACjC;IACA,MAAM,OAAO,kBAAkB,YAAY,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,eAAe,KAAK,EAAE,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,IAAI,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE;IAC3G,IAAI,MAAM,QAAQ,EAAE;IACpB,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI;IAC5B,eAAe,IAAI,eAAe;IAClC,kBAAkB,IAAI,kBAAkB;IACxC,yJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC9C,MAAM,WAAW,kBAAkB,YAAY,KAAK,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;IAClG,QAAQ,kBAAkB,CAAC;IAC3B,QAAQ,kBAAkB,CAAC;IAC3B,QAAQ,eAAe,CAAC;IACxB,IAAI,eAAe,IAAI,OAAO;IAC9B,aAAa,OAAO,CAClB,KAAK,cAAc,EACnB,KAAK,cAAc,EACnB,KAAK,cAAc,EACnB,KAAK,cAAc;IAErB,aAAa,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU;IAChD,qBAAqB,KAAK,cAAc;IACxC,qBAAqB,KAAK,cAAc;IACxC,MAAM,SAAS,QAAQ,EAAE,CAAC,QAAQ;IAClC,IAAI,oBAAoB,QAAQ,EAAE,CAAC,YAAY,CAAC;IAChD,mBAAmB,UAAU,IAAI,cAAc,mBAAmB;IAClE,QAAQ,eAAe,CAAC;IACxB,QAAQ,cAAc,CAAC;IACvB,QAAQ,oBAAoB,CAAC;IAC7B,QAAQ,qBAAqB,CAAC;IAC9B,SAAS,UAAU,QAAQ,EAAE,CAAC,OAAO,IAAI,QAAQ,EAAE,CAAC,UAAU,EAAE;IAChE,aAAa,IAAI,CAAC,KAAK,GAAG;IAC1B,aAAa,IAAI,CAAC,KAAK,GAAG;IAC1B,MAAM,MAAM,aAAa,IAAI;IAC7B,IAAI,YAAY,IAAI,KAAK,GAAG,IAAI,MAAM;IACtC,IAAI,SAAS,YAAY,IAAI,KAAK,cAAc;IAChD,IAAI,WAAW,IAAI,KAAK,GAAG,IAAI,MAAM;IACrC,MAAM,QAAQ,WAAW,IAAI,KAAK,cAAc;IAChD,IAAI,QAAQ;QACV,SAAS,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,cAAc,EAAE,IAAI,CAAC,KAAK,IAAI,MAAM,GAAG,KAAK,cAAc;IACnJ;IACA,CAAA,GAAA,yJAAA,CAAA,IAAgB,AAAD,EAAE,UAAU,QAAQ,OAAO,KAAK,WAAW;IAC1D,MAAM,oBAAoB,SAAS,KAAK;IACxC,SAAS,IAAI,CACX,WACA,IAAI,MAAM,GAAG,KAAK,cAAc,GAAG,OAAO,CAAC,KAAK,cAAc,GAAG,iBAAiB,IAAI,MAAM,QAAQ,MAAM,CAAC,SAAS,iBAAiB;IAEvI,yJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE;AACvB;AACA,MAAM,WAAW;IACf,yBAAyB;IACzB;IACA;IACA;AACF;AACA,MAAM,YAAY,CAAC,UAAY,CAAC;YACpB,EAAE,QAAQ,YAAY,CAAC;UACzB,EAAE,QAAQ,SAAS,CAAC;;AAE9B,CAAC;AACD,MAAM,SAAS;AACf,MAAM,UAAU;IACd,QAAQ;IACR;IACA;IACA;IACA,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;QACjB,SAAS,OAAO,CAAC;QACjB,GAAG,OAAO,CAAC;IACb;AACF", "ignoreList": [0], "debugId": null}}]}