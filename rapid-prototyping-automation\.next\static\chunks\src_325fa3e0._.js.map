{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  gradient?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n}\n\nexport const Card: React.FC<CardProps> = ({\n  children,\n  className,\n  gradient = false,\n  padding = 'md'\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  return (\n    <div\n      className={cn(\n        'card',\n        gradient && 'card-gradient',\n        paddingClasses[padding],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardTitle: React.FC<CardTitleProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <h3 className={cn('text-xl font-semibold gradient-text', className)}>\n      {children}\n    </h3>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContent: React.FC<CardContentProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('text-gray-600', className)}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AACA;;;AASO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,IAAI,EACf;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,YAAY,iBACZ,cAAc,CAAC,QAAQ,EACvB;kBAGD;;;;;;AAGP;KAxBa;AA+BN,MAAM,aAAwC,CAAC,EACpD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;MATa;AAgBN,MAAM,YAAsC,CAAC,EAClD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBACtD;;;;;;AAGP;MATa;AAgBN,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBACjC;;;;;;AAGP;MATa", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'success';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  disabled,\n  children,\n  ...props\n}) => {\n  return (\n    <button\n      className={cn(\n        'btn',\n        `btn-${variant}`,\n        `btn-${size}`,\n        loading && 'opacity-75 cursor-not-allowed',\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-3 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AASO,MAAM,SAAgC,CAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,CAAC,IAAI,EAAE,SAAS,EAChB,CAAC,IAAI,EAAE,MAAM,EACb,WAAW,iCACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA9Ca", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ProgressProps {\n  value: number;\n  max?: number;\n  className?: string;\n  showLabel?: boolean;\n  label?: string;\n}\n\nexport const Progress: React.FC<ProgressProps> = ({\n  value,\n  max = 100,\n  className,\n  showLabel = false,\n  label\n}) => {\n  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);\n\n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between items-center mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">\n            {label || 'Progress'}\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            {Math.round(percentage)}%\n          </span>\n        </div>\n      )}\n      <div className=\"progress-bar\">\n        <div\n          className=\"progress-fill\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,MAAM,GAAG,EACT,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACN;IACC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,SAAS;;;;;;kCAEZ,6LAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAI9B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C;KA7Ba", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/FileUpload.tsx"], "sourcesContent": ["import React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, File, X } from 'lucide-react';\nimport { cn, formatFileSize } from '@/lib/utils';\nimport { Button } from './Button';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  accept?: Record<string, string[]>;\n  maxSize?: number;\n  className?: string;\n}\n\nexport const FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  accept = {\n    'text/*': ['.txt', '.md', '.doc', '.docx'],\n    'application/pdf': ['.pdf']\n  },\n  maxSize = 10 * 1024 * 1024, // 10MB\n  className\n}) => {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [error, setError] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError('');\n    \n    if (rejectedFiles.length > 0) {\n      const rejection = rejectedFiles[0];\n      if (rejection.errors[0]?.code === 'file-too-large') {\n        setError(`File is too large. Maximum size is ${formatFileSize(maxSize)}`);\n      } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n        setError('Invalid file type. Please upload a text document or PDF.');\n      } else {\n        setError('File upload failed. Please try again.');\n      }\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      setSelectedFile(file);\n      onFileSelect(file);\n    }\n  }, [onFileSelect, maxSize]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept,\n    maxSize,\n    multiple: false\n  });\n\n  const removeFile = () => {\n    setSelectedFile(null);\n    setError('');\n  };\n\n  return (\n    <div className={cn('w-full', className)}>\n      {!selectedFile ? (\n        <div\n          {...getRootProps()}\n          className={cn(\n            'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200',\n            isDragActive\n              ? 'border-blue-400 bg-blue-50'\n              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n          )}\n        >\n          <input {...getInputProps()} />\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <p className=\"text-lg font-medium text-gray-700 mb-2\">\n            {isDragActive ? 'Drop the file here' : 'Upload transcript file'}\n          </p>\n          <p className=\"text-sm text-gray-500 mb-4\">\n            Drag and drop your file here, or click to browse\n          </p>\n          <p className=\"text-xs text-gray-400\">\n            Supports: TXT, MD, DOC, DOCX, PDF (max {formatFileSize(maxSize)})\n          </p>\n        </div>\n      ) : (\n        <div className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <File className=\"h-8 w-8 text-blue-500\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">{selectedFile.name}</p>\n                <p className=\"text-sm text-gray-500\">\n                  {formatFileSize(selectedFile.size)}\n                </p>\n              </div>\n            </div>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={removeFile}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      )}\n      \n      {error && (\n        <div className=\"mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2\">\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;;AASO,MAAM,aAAwC,CAAC,EACpD,YAAY,EACZ,SAAS;IACP,UAAU;QAAC;QAAQ;QAAO;QAAQ;KAAQ;IAC1C,mBAAmB;QAAC;KAAO;AAC7B,CAAC,EACD,UAAU,KAAK,OAAO,IAAI,EAC1B,SAAS,EACV;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC,eAAuB;YACjD,SAAS;YAET,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,YAAY,aAAa,CAAC,EAAE;gBAClC,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,kBAAkB;oBAClD,SAAS,CAAC,mCAAmC,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAC1E,OAAO,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,qBAAqB;oBAC5D,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;gBACA;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,gBAAgB;gBAChB,aAAa;YACf;QACF;yCAAG;QAAC;QAAc;KAAQ;IAE1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA;QACA;QACA,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,CAAC,6BACA,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,eACI,+BACA;;kCAGN,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAE,WAAU;kCACV,eAAe,uBAAuB;;;;;;kCAEzC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAE,WAAU;;4BAAwB;4BACK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;4BAAS;;;;;;;;;;;;qCAIpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA6B,aAAa,IAAI;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;GArGa;;QAkC2C,2KAAA,CAAA,cAAW;;;KAlCtD", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ChatInterface.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Button } from './Button';\nimport { Send, Bot, User } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: 'user' | 'bot';\n  timestamp: Date;\n}\n\ninterface ChatInterfaceProps {\n  onFeedback: (feedback: string) => void;\n  context?: string;\n  diagramCode?: string;\n  onDiagramUpdate?: (updatedCode: any) => void;\n  className?: string;\n}\n\nexport const ChatInterface: React.FC<ChatInterfaceProps> = ({\n  onFeedback,\n  context = '',\n  diagramCode = '',\n  onDiagramUpdate,\n  className\n}) => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: 'I\\'ve generated the architecture diagram based on your technical requirements. What do you think about the current design? You can ask me to modify components, add security layers, improve performance, or make any other architectural changes.',\n      sender: 'bot',\n      timestamp: new Date()\n    }\n  ]);\n  const [inputValue, setInputValue] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: inputValue,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    onFeedback(inputValue);\n\n    const currentInput = inputValue;\n    setInputValue('');\n    setIsLoading(true);\n\n    try {\n      // Call the chat API\n      const response = await fetch('/api/chat-architecture', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: currentInput,\n          context: context,\n          diagramCode: diagramCode\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get chat response');\n      }\n\n      const data = await response.json();\n\n      const botResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        content: data.response || 'I understand your feedback. Let me help you improve the architecture.',\n        sender: 'bot',\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, botResponse]);\n\n      // If there's an updated diagram code, notify the parent component\n      if (data.updatedDiagramCode && onDiagramUpdate) {\n        onDiagramUpdate(data.updatedDiagramCode);\n      }\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      const errorResponse: Message = {\n        id: (Date.now() + 2).toString(),\n        content: 'I apologize, but I\\'m having trouble processing your request right now. Please try again or check if the Claude API is properly configured.',\n        sender: 'bot',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className={cn('bg-white border border-gray-200 rounded-lg', className)}>\n      <div className=\"p-4 border-b border-gray-200\">\n        <h4 className=\"font-semibold text-gray-900\">Architecture Feedback</h4>\n        <p className=\"text-sm text-gray-600\">Share your thoughts on the generated architecture</p>\n      </div>\n      \n      <div className=\"h-64 overflow-y-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={cn(\n              'flex items-start space-x-3',\n              message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n            )}\n          >\n            <div className={cn(\n              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',\n              message.sender === 'user' \n                ? 'bg-blue-500 text-white' \n                : 'bg-gray-200 text-gray-600'\n            )}>\n              {message.sender === 'user' ? (\n                <User className=\"w-4 h-4\" />\n              ) : (\n                <Bot className=\"w-4 h-4\" />\n              )}\n            </div>\n            <div className={cn(\n              'flex-1 max-w-xs lg:max-w-md px-4 py-2 rounded-lg',\n              message.sender === 'user'\n                ? 'bg-blue-500 text-white ml-auto'\n                : 'bg-gray-100 text-gray-900'\n            )}>\n              <p className=\"text-sm\">{message.content}</p>\n              <p className={cn(\n                'text-xs mt-1',\n                message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'\n              )}>\n                {message.timestamp.toLocaleTimeString([], { \n                  hour: '2-digit', \n                  minute: '2-digit' \n                })}\n              </p>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex space-x-2\">\n          <textarea\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Share your feedback on the architecture...\"\n            className=\"flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows={2}\n          />\n          <Button\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim() || isLoading}\n            loading={isLoading}\n            size=\"sm\"\n            className=\"self-end\"\n          >\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;AAiBO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,EACV,UAAU,EAAE,EACZ,cAAc,EAAE,EAChB,eAAe,EACf,SAAS,EACV;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,WAAW;QAErC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,WAAW;QAEX,MAAM,eAAe;QACrB,cAAc;QACd,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,cAAuB;gBAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS,KAAK,QAAQ,IAAI;gBAC1B,QAAQ;gBACR,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAE1C,kEAAkE;YAClE,IAAI,KAAK,kBAAkB,IAAI,iBAAiB;gBAC9C,gBAAgB,KAAK,kBAAkB;YACzC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,gBAAyB;gBAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;;0BAC/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,QAAQ,MAAM,KAAK,SAAS,qCAAqC;;0CAGnE,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uEACA,QAAQ,MAAM,KAAK,SACf,2BACA;0CAEH,QAAQ,MAAM,KAAK,uBAClB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oDACA,QAAQ,MAAM,KAAK,SACf,mCACA;;kDAEJ,6LAAC;wCAAE,WAAU;kDAAW,QAAQ,OAAO;;;;;;kDACvC,6LAAC;wCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,gBACA,QAAQ,MAAM,KAAK,SAAS,kBAAkB;kDAE7C,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4CACxC,MAAM;4CACN,QAAQ;wCACV;;;;;;;;;;;;;uBAhCC,QAAQ,EAAE;;;;;;;;;;0BAuCrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,YAAY;4BACZ,aAAY;4BACZ,WAAU;4BACV,MAAM;;;;;;sCAER,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI,MAAM;4BAChC,SAAS;4BACT,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAlKa;KAAA", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Toast.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { CheckCircle, X, AlertCircle, Info } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport type ToastType = 'success' | 'error' | 'info' | 'warning';\n\ninterface Toast {\n  id: string;\n  message: string;\n  type: ToastType;\n  duration?: number;\n}\n\ninterface ToastProps {\n  toast: Toast;\n  onRemove: (id: string) => void;\n}\n\nconst ToastComponent: React.FC<ToastProps> = ({ toast, onRemove }) => {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id);\n    }, toast.duration || 3000);\n\n    return () => clearTimeout(timer);\n  }, [toast.id, toast.duration, onRemove]);\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5\" />;\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'warning':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5\" />;\n      default:\n        return <Info className=\"h-5 w-5\" />;\n    }\n  };\n\n  const getStyles = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800';\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800';\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800';\n    }\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex items-center justify-between p-4 rounded-lg border shadow-lg animate-slide-up',\n        getStyles()\n      )}\n    >\n      <div className=\"flex items-center space-x-3\">\n        {getIcon()}\n        <span className=\"text-sm font-medium\">{toast.message}</span>\n      </div>\n      <button\n        onClick={() => onRemove(toast.id)}\n        className=\"ml-4 text-current hover:opacity-70 transition-opacity\"\n      >\n        <X className=\"h-4 w-4\" />\n      </button>\n    </div>\n  );\n};\n\ninterface ToastContainerProps {\n  toasts: Toast[];\n  onRemove: (id: string) => void;\n}\n\nexport const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  );\n};\n\n// Hook for managing toasts\nexport const useToast = () => {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n\n  const addToast = (message: string, type: ToastType = 'info', duration?: number) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const newToast: Toast = { id, message, type, duration };\n    setToasts(prev => [...prev, newToast]);\n  };\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success: (message: string, duration?: number) => addToast(message, 'success', duration),\n    error: (message: string, duration?: number) => addToast(message, 'error', duration),\n    info: (message: string, duration?: number) => addToast(message, 'info', duration),\n    warning: (message: string, duration?: number) => addToast(message, 'warning', duration),\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAgBA,MAAM,iBAAuC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,SAAS,MAAM,EAAE;gBACnB;iDAAG,MAAM,QAAQ,IAAI;YAErB;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,YAAY;QAChB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA;;0BAGF,6LAAC;gBAAI,WAAU;;oBACZ;kCACD,6LAAC;wBAAK,WAAU;kCAAuB,MAAM,OAAO;;;;;;;;;;;;0BAEtD,6LAAC;gBACC,SAAS,IAAM,SAAS,MAAM,EAAE;gBAChC,WAAU;0BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GA1DM;KAAA;AAiEC,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChF,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;MARa;AAWN,MAAM,WAAW;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC,SAAiB,OAAkB,MAAM,EAAE;QAC3D,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAkB;YAAE;YAAI;YAAS;YAAM;QAAS;QACtD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;QAC9E,OAAO,CAAC,SAAiB,WAAsB,SAAS,SAAS,SAAS;QAC1E,MAAM,CAAC,SAAiB,WAAsB,SAAS,SAAS,QAAQ;QACxE,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;IAChF;AACF;IAtBa", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ThinkingIndicator.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Brain, Lightbulb, Zap, Cpu, Sparkles } from 'lucide-react';\n\ninterface ThinkingIndicatorProps {\n  step: 'transcript' | 'technical' | 'architecture' | 'prompts';\n  className?: string;\n}\n\nconst THINKING_MESSAGES = {\n  transcript: [\n    \"🧠 Reading your conversation transcript...\",\n    \"🔍 Identifying key stakeholders and pain points...\",\n    \"💡 Extracting business challenges...\",\n    \"📝 Crafting executive summary...\",\n    \"🎯 Formulating 'How Might We' statements...\",\n    \"✨ Polishing the problem statement...\",\n    \"🚀 Almost done with the analysis...\"\n  ],\n  technical: [\n    \"🏗️ Analyzing problem statement architecture needs...\",\n    \"⚡ Designing cloud-native solution components...\",\n    \"🔧 Selecting optimal technology stack...\",\n    \"🌐 Mapping data flow and integration patterns...\",\n    \"🔒 Defining security and compliance framework...\",\n    \"📊 Calculating scalability requirements...\",\n    \"🎯 Scoping MVP features and timeline...\",\n    \"📋 Generating implementation roadmap...\"\n  ],\n  architecture: [\n    \"🎨 Analyzing technical requirements for architecture...\",\n    \"🏗️ Sketching system architecture diagrams...\",\n    \"🔗 Connecting microservices and APIs...\",\n    \"☁️ Optimizing cloud infrastructure layout...\",\n    \"🔄 Designing data pipelines and flows...\",\n    \"🛡️ Adding security layers and compliance...\",\n    \"📈 Planning for scale and performance...\",\n    \"🎯 Generating Mermaid and PlantUML code...\",\n    \"✨ Finalizing architecture visualization...\"\n  ],\n  prompts: [\n    \"💻 Crafting development prompts...\",\n    \"🔨 Preparing implementation guides...\",\n    \"📚 Organizing code templates...\",\n    \"🎯 Finalizing copy-paste instructions...\"\n  ]\n};\n\nconst THINKING_ICONS = [Brain, Lightbulb, Zap, Cpu, Sparkles];\n\nexport const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ \n  step, \n  className = '' \n}) => {\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\n  const [currentIconIndex, setCurrentIconIndex] = useState(0);\n  const [dots, setDots] = useState('');\n\n  const messages = THINKING_MESSAGES[step];\n  const CurrentIcon = THINKING_ICONS[currentIconIndex];\n\n  useEffect(() => {\n    // Rotate messages every 2.5 seconds\n    const messageInterval = setInterval(() => {\n      setCurrentMessageIndex((prev) => (prev + 1) % messages.length);\n    }, 2500);\n\n    // Rotate icons every 1.5 seconds\n    const iconInterval = setInterval(() => {\n      setCurrentIconIndex((prev) => (prev + 1) % THINKING_ICONS.length);\n    }, 1500);\n\n    // Animate dots every 500ms\n    const dotsInterval = setInterval(() => {\n      setDots((prev) => {\n        if (prev === '...') return '';\n        return prev + '.';\n      });\n    }, 500);\n\n    return () => {\n      clearInterval(messageInterval);\n      clearInterval(iconInterval);\n      clearInterval(dotsInterval);\n    };\n  }, [messages.length]);\n\n  return (\n    <div className={`text-center py-8 ${className}`}>\n      {/* Animated Icon */}\n      <div className=\"relative mb-6\">\n        <div className=\"animate-pulse-slow mb-4\">\n          <CurrentIcon className=\"h-16 w-16 text-blue-500 mx-auto animate-bounce\" />\n        </div>\n        \n        {/* Thinking bubbles animation */}\n        <div className=\"absolute -top-2 -right-2\">\n          <div className=\"flex space-x-1\">\n            <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-ping\" style={{ animationDelay: '0ms' }}></div>\n            <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-ping\" style={{ animationDelay: '200ms' }}></div>\n            <div className=\"w-2 h-2 bg-pink-400 rounded-full animate-ping\" style={{ animationDelay: '400ms' }}></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main thinking message */}\n      <h3 className=\"text-xl font-semibold mb-3 text-gray-800\">\n        Claude is thinking{dots}\n      </h3>\n\n      {/* Dynamic message */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        <p className=\"text-gray-600 text-lg animate-fade-in max-w-md mx-auto\">\n          {messages[currentMessageIndex]}\n        </p>\n      </div>\n\n      {/* Progress indicator */}\n      <div className=\"mt-6 max-w-md mx-auto\">\n        <div className=\"flex justify-between text-xs text-gray-500 mb-2\">\n          <span>Processing...</span>\n          <span>{Math.round(((currentMessageIndex + 1) / messages.length) * 100)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div \n            className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000 ease-out\"\n            style={{ width: `${((currentMessageIndex + 1) / messages.length) * 100}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* Fun fact */}\n      <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-lg mx-auto\">\n        <p className=\"text-blue-800 text-sm\">\n          <Sparkles className=\"inline h-4 w-4 mr-1\" />\n          <strong>Did you know?</strong> Claude processes thousands of tokens per second to understand context and generate human-like responses!\n        </p>\n      </div>\n\n      {/* Estimated time */}\n      <p className=\"text-sm text-gray-500 mt-4\">\n        ⏱️ This usually takes 30-60 seconds depending on content complexity\n      </p>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAOA,MAAM,oBAAoB;IACxB,YAAY;QACV;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QACP;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,iBAAiB;IAAC,uMAAA,CAAA,QAAK;IAAE,+MAAA,CAAA,YAAS;IAAE,mMAAA,CAAA,MAAG;IAAE,mMAAA,CAAA,MAAG;IAAE,6MAAA,CAAA,WAAQ;CAAC;AAEtD,MAAM,oBAAsD,CAAC,EAClE,IAAI,EACJ,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,WAAW,iBAAiB,CAAC,KAAK;IACxC,MAAM,cAAc,cAAc,CAAC,iBAAiB;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,oCAAoC;YACpC,MAAM,kBAAkB;+DAAY;oBAClC;uEAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,SAAS,MAAM;;gBAC/D;8DAAG;YAEH,iCAAiC;YACjC,MAAM,eAAe;4DAAY;oBAC/B;oEAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,eAAe,MAAM;;gBAClE;2DAAG;YAEH,2BAA2B;YAC3B,MAAM,eAAe;4DAAY;oBAC/B;oEAAQ,CAAC;4BACP,IAAI,SAAS,OAAO,OAAO;4BAC3B,OAAO,OAAO;wBAChB;;gBACF;2DAAG;YAEH;+CAAO;oBACL,cAAc;oBACd,cAAc;oBACd,cAAc;gBAChB;;QACF;sCAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,qBACE,6LAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;;0BAE7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAY,WAAU;;;;;;;;;;;kCAIzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAgD,OAAO;wCAAE,gBAAgB;oCAAM;;;;;;8CAC9F,6LAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;8CAClG,6LAAC;oCAAI,WAAU;oCAAgD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAMtG,6LAAC;gBAAG,WAAU;;oBAA2C;oBACpC;;;;;;;0BAIrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,QAAQ,CAAC,oBAAoB;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM,KAAK,KAAK,CAAC,AAAC,CAAC,sBAAsB,CAAC,IAAI,SAAS,MAAM,GAAI;oCAAK;;;;;;;;;;;;;kCAEzE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,AAAC,CAAC,sBAAsB,CAAC,IAAI,SAAS,MAAM,GAAI,IAAI,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAMhF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;sCAAO;;;;;;wBAAsB;;;;;;;;;;;;0BAKlC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAKhD;GA/Fa;KAAA", "debugId": null}}, {"offset": {"line": 1253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ArchitectureDiagram.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { But<PERSON> } from './Button';\nimport { Copy, Download, Code, RefreshCw } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface ArchitectureDiagramProps {\n  mermaidCode?: string;\n  plantUMLCode?: string;\n  legend?: string[];\n  fullResponse?: string;\n  debug?: any;\n  onCopyCode?: (code: string, type: 'mermaid' | 'plantuml') => void;\n  className?: string;\n}\n\nexport const ArchitectureDiagram: React.FC<ArchitectureDiagramProps> = ({\n  mermaidCode,\n  plantUMLCode,\n  legend = [],\n  fullResponse,\n  debug,\n  onCopyCode,\n  className\n}) => {\n  const mermaidRef = useRef<HTMLDivElement>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [mermaidInstance, setMermaidInstance] = useState<any>(null);\n\n  useEffect(() => {\n    // Load Mermaid library\n    const loadMermaid = async () => {\n      try {\n        const mermaid = await import('mermaid');\n        mermaid.default.initialize({\n          startOnLoad: false,\n          theme: 'default',\n          securityLevel: 'loose',\n          themeVariables: {\n            primaryColor: '#3B82F6',\n            primaryTextColor: '#1F2937',\n            primaryBorderColor: '#2563EB',\n            lineColor: '#6B7280',\n            secondaryColor: '#F3F4F6',\n            tertiaryColor: '#F9FAFB'\n          }\n        });\n        setMermaidInstance(mermaid.default);\n      } catch (error) {\n        console.error('Failed to load Mermaid:', error);\n        setError('Failed to load Mermaid library');\n      }\n    };\n\n    loadMermaid();\n  }, []);\n\n  useEffect(() => {\n    if (mermaidCode && mermaidInstance && mermaidRef.current) {\n      renderMermaidDiagram();\n    }\n  }, [mermaidCode, mermaidInstance]);\n\n  const cleanMermaidCode = (code: string): string => {\n    // Remove any markdown code block markers that might have slipped through\n    let cleaned = code\n      .replace(/^```mermaid\\s*/i, '')\n      .replace(/\\s*```$/i, '')\n      .replace(/^```\\s*/i, '')\n      .trim();\n\n    // Remove any extra whitespace and normalize line endings\n    cleaned = cleaned.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n\n    // Log the cleaning process\n    if (code !== cleaned) {\n      console.log('Cleaned Mermaid code:', { original: code.substring(0, 50), cleaned: cleaned.substring(0, 50) });\n    }\n\n    return cleaned;\n  };\n\n  const renderMermaidDiagram = async () => {\n    if (!mermaidCode || !mermaidInstance || !mermaidRef.current) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Clear previous content\n      mermaidRef.current.innerHTML = '';\n\n      // Create a unique ID for this diagram\n      const diagramId = `mermaid-diagram-${Date.now()}`;\n\n      // Clean the mermaid code thoroughly\n      const cleanCode = cleanMermaidCode(mermaidCode);\n\n      console.log('Rendering Mermaid diagram:', cleanCode);\n\n      // Render the diagram\n      const { svg } = await mermaidInstance.render(diagramId, cleanCode);\n\n      if (mermaidRef.current) {\n        mermaidRef.current.innerHTML = svg;\n      }\n\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Mermaid rendering error:', error);\n      setError(`Diagram rendering failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      setIsLoading(false);\n\n      if (mermaidRef.current) {\n        mermaidRef.current.innerHTML = `\n          <div class=\"text-red-600 p-4 border border-red-200 rounded-lg bg-red-50\">\n            <p class=\"font-semibold\">Diagram Rendering Error</p>\n            <p class=\"text-sm mt-1\">${error instanceof Error ? error.message : 'Please check the diagram code syntax.'}</p>\n            <details class=\"mt-2\">\n              <summary class=\"cursor-pointer text-xs\">Show raw code</summary>\n              <pre class=\"text-xs mt-1 p-2 bg-gray-100 rounded overflow-auto\">${mermaidCode}</pre>\n            </details>\n          </div>\n        `;\n      }\n    }\n  };\n\n  const handleCopyCode = (code: string, type: 'mermaid' | 'plantuml') => {\n    navigator.clipboard.writeText(code);\n    if (onCopyCode) {\n      onCopyCode(code, type);\n    }\n  };\n\n  const handleDownloadDiagram = () => {\n    try {\n      const svgElement = mermaidRef.current?.querySelector('svg');\n      if (!svgElement) {\n        alert('No diagram available to download. Please ensure the diagram has rendered successfully.');\n        return;\n      }\n\n      // Clone the SVG to avoid modifying the original\n      const clonedSvg = svgElement.cloneNode(true) as SVGElement;\n\n      // Ensure the SVG has proper dimensions\n      if (!clonedSvg.getAttribute('width')) {\n        clonedSvg.setAttribute('width', '800');\n      }\n      if (!clonedSvg.getAttribute('height')) {\n        clonedSvg.setAttribute('height', '600');\n      }\n\n      // Add XML declaration and DOCTYPE\n      const svgData = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n${new XMLSerializer().serializeToString(clonedSvg)}`;\n\n      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });\n      const svgUrl = URL.createObjectURL(svgBlob);\n\n      const downloadLink = document.createElement('a');\n      downloadLink.href = svgUrl;\n      downloadLink.download = `architecture-diagram-${new Date().toISOString().split('T')[0]}.svg`;\n      downloadLink.style.display = 'none';\n\n      document.body.appendChild(downloadLink);\n      downloadLink.click();\n      document.body.removeChild(downloadLink);\n\n      // Clean up the URL object\n      setTimeout(() => URL.revokeObjectURL(svgUrl), 100);\n\n      console.log('SVG download initiated successfully');\n    } catch (error) {\n      console.error('Download failed:', error);\n      alert('Failed to download diagram. Please try again.');\n    }\n  };\n\n  const handleRetryRender = () => {\n    if (mermaidCode) {\n      renderMermaidDiagram();\n    }\n  };\n\n  const generatePlantUMLUrl = (code: string) => {\n    // Simple PlantUML online renderer (for fallback)\n    const encoded = btoa(code);\n    return `http://www.plantuml.com/plantuml/svg/${encoded}`;\n  };\n\n  return (\n    <div className={cn('space-y-6', className)}>\n      {/* Diagram Display */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h4 className=\"font-semibold text-lg text-gray-900\">System Architecture Diagram</h4>\n          <div className=\"flex gap-2\">\n            {error && (\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={handleRetryRender}\n                disabled={isLoading}\n              >\n                <RefreshCw className={cn(\"w-4 h-4 mr-1\", isLoading && \"animate-spin\")} />\n                Retry\n              </Button>\n            )}\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={handleDownloadDiagram}\n              disabled={!mermaidCode || error !== null || isLoading}\n            >\n              <Download className=\"w-4 h-4 mr-1\" />\n              Download SVG\n            </Button>\n          </div>\n        </div>\n\n        {/* Status Messages */}\n        {isLoading && (\n          <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n            <p className=\"text-blue-800 text-sm\">\n              <RefreshCw className=\"inline w-4 h-4 mr-1 animate-spin\" />\n              Rendering diagram...\n            </p>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800 text-sm font-medium\">Mermaid Rendering Error</p>\n            <p className=\"text-red-700 text-sm mt-1\">{error}</p>\n            {plantUMLCode && (\n              <div className=\"mt-3\">\n                <p className=\"text-red-700 text-sm mb-2\">Fallback: Showing PlantUML diagram instead</p>\n                <img\n                  src={generatePlantUMLUrl(plantUMLCode)}\n                  alt=\"PlantUML Architecture Diagram\"\n                  className=\"max-w-full h-auto border rounded\"\n                  onError={(e) => {\n                    (e.target as HTMLImageElement).style.display = 'none';\n                  }}\n                />\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Mermaid Diagram */}\n        <div className=\"border border-gray-100 rounded-lg p-4 bg-gray-50 min-h-[400px] flex items-center justify-center\">\n          <div ref={mermaidRef} className=\"w-full max-w-full overflow-auto\">\n            {!mermaidCode && !isLoading && (\n              <div className=\"text-center text-gray-500\">\n                <Code className=\"w-16 h-16 mx-auto mb-4 text-gray-300\" />\n                <p>Architecture diagram will appear here</p>\n                <p className=\"text-sm mt-1\">Generate from technical requirements to see the diagram</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Legend */}\n      {legend.length > 0 && (\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <h5 className=\"font-semibold text-blue-900 mb-2\">Legend & Assumptions</h5>\n          <ul className=\"space-y-1\">\n            {legend.map((item, index) => (\n              <li key={index} className=\"text-blue-800 text-sm flex items-start\">\n                <span className=\"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0\"></span>\n                {item}\n              </li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      {/* Code Blocks */}\n      <div className=\"grid md:grid-cols-2 gap-4\">\n        {/* Mermaid Code */}\n        {mermaidCode && (\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <h5 className=\"font-semibold text-gray-900\">Mermaid Code</h5>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={() => handleCopyCode(cleanMermaidCode(mermaidCode), 'mermaid')}\n                >\n                  <Copy className=\"w-4 h-4 mr-1\" />\n                  Copy Clean\n                </Button>\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={() => handleCopyCode(mermaidCode, 'mermaid')}\n                >\n                  <Copy className=\"w-4 h-4 mr-1\" />\n                  Copy Raw\n                </Button>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <div>\n                <p className=\"text-xs text-gray-600 mb-1\">Cleaned Code (for rendering):</p>\n                <pre className=\"text-xs bg-white border rounded p-3 overflow-x-auto max-h-32\">\n                  <code>{cleanMermaidCode(mermaidCode)}</code>\n                </pre>\n              </div>\n              <details>\n                <summary className=\"text-xs text-gray-600 cursor-pointer\">Show raw code</summary>\n                <pre className=\"text-xs bg-gray-100 border rounded p-3 overflow-x-auto max-h-32 mt-1\">\n                  <code>{mermaidCode}</code>\n                </pre>\n              </details>\n            </div>\n          </div>\n        )}\n\n        {/* PlantUML Code */}\n        {plantUMLCode && (\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <h5 className=\"font-semibold text-gray-900\">PlantUML Code</h5>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => handleCopyCode(plantUMLCode, 'plantuml')}\n              >\n                <Copy className=\"w-4 h-4 mr-1\" />\n                Copy\n              </Button>\n            </div>\n            <pre className=\"text-xs bg-white border rounded p-3 overflow-x-auto max-h-40\">\n              <code>{plantUMLCode}</code>\n            </pre>\n          </div>\n        )}\n      </div>\n\n      {/* Debug Section */}\n      {(fullResponse || debug) && (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <details>\n            <summary className=\"font-semibold text-yellow-800 cursor-pointer\">\n              🐛 Debug Information (Click to expand)\n            </summary>\n            <div className=\"mt-3 space-y-3\">\n              {debug && (\n                <div>\n                  <h6 className=\"font-medium text-yellow-800 mb-2\">Parsing Debug:</h6>\n                  <div className=\"text-sm text-yellow-700 space-y-1\">\n                    <p>Response Length: {debug.responseLength}</p>\n                    <p>Contains \"mermaid\": {debug.containsMermaid ? '✅' : '❌'}</p>\n                    <p>Contains \"plantuml\": {debug.containsPlantUML ? '✅' : '❌'}</p>\n                    <p>Contains \"graph\": {debug.containsGraph ? '✅' : '❌'}</p>\n                  </div>\n                </div>\n              )}\n\n              {fullResponse && (\n                <div>\n                  <h6 className=\"font-medium text-yellow-800 mb-2\">Full Claude Response:</h6>\n                  <pre className=\"text-xs bg-white border rounded p-3 overflow-auto max-h-60 text-gray-700\">\n                    {fullResponse}\n                  </pre>\n                  <Button\n                    variant=\"secondary\"\n                    size=\"sm\"\n                    className=\"mt-2\"\n                    onClick={() => {\n                      navigator.clipboard.writeText(fullResponse);\n                      if (onCopyCode) onCopyCode(fullResponse, 'mermaid');\n                    }}\n                  >\n                    <Copy className=\"w-4 h-4 mr-1\" />\n                    Copy Full Response\n                  </Button>\n                </div>\n              )}\n            </div>\n          </details>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;AAYO,MAAM,sBAA0D,CAAC,EACtE,WAAW,EACX,YAAY,EACZ,SAAS,EAAE,EACX,YAAY,EACZ,KAAK,EACL,UAAU,EACV,SAAS,EACV;;IACC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,uBAAuB;YACvB,MAAM;6DAAc;oBAClB,IAAI;wBACF,MAAM,UAAU;wBAChB,QAAQ,OAAO,CAAC,UAAU,CAAC;4BACzB,aAAa;4BACb,OAAO;4BACP,eAAe;4BACf,gBAAgB;gCACd,cAAc;gCACd,kBAAkB;gCAClB,oBAAoB;gCACpB,WAAW;gCACX,gBAAgB;gCAChB,eAAe;4BACjB;wBACF;wBACA,mBAAmB,QAAQ,OAAO;oBACpC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,SAAS;oBACX;gBACF;;YAEA;QACF;wCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,eAAe,mBAAmB,WAAW,OAAO,EAAE;gBACxD;YACF;QACF;wCAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,mBAAmB,CAAC;QACxB,yEAAyE;QACzE,IAAI,UAAU,KACX,OAAO,CAAC,mBAAmB,IAC3B,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,YAAY,IACpB,IAAI;QAEP,yDAAyD;QACzD,UAAU,QAAQ,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,OAAO;QAExD,2BAA2B;QAC3B,IAAI,SAAS,SAAS;YACpB,QAAQ,GAAG,CAAC,yBAAyB;gBAAE,UAAU,KAAK,SAAS,CAAC,GAAG;gBAAK,SAAS,QAAQ,SAAS,CAAC,GAAG;YAAI;QAC5G;QAEA,OAAO;IACT;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,WAAW,OAAO,EAAE;QAE7D,aAAa;QACb,SAAS;QAET,IAAI;YACF,yBAAyB;YACzB,WAAW,OAAO,CAAC,SAAS,GAAG;YAE/B,sCAAsC;YACtC,MAAM,YAAY,CAAC,gBAAgB,EAAE,KAAK,GAAG,IAAI;YAEjD,oCAAoC;YACpC,MAAM,YAAY,iBAAiB;YAEnC,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,qBAAqB;YACrB,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,gBAAgB,MAAM,CAAC,WAAW;YAExD,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC,SAAS,GAAG;YACjC;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,0BAA0B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAChG,aAAa;YAEb,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC,SAAS,GAAG,CAAC;;;oCAGJ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,wCAAwC;;;8EAGzC,EAAE,YAAY;;;QAGpF,CAAC;YACH;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC,MAAc;QACpC,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,IAAI,YAAY;YACd,WAAW,MAAM;QACnB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,aAAa,WAAW,OAAO,EAAE,cAAc;YACrD,IAAI,CAAC,YAAY;gBACf,MAAM;gBACN;YACF;YAEA,gDAAgD;YAChD,MAAM,YAAY,WAAW,SAAS,CAAC;YAEvC,uCAAuC;YACvC,IAAI,CAAC,UAAU,YAAY,CAAC,UAAU;gBACpC,UAAU,YAAY,CAAC,SAAS;YAClC;YACA,IAAI,CAAC,UAAU,YAAY,CAAC,WAAW;gBACrC,UAAU,YAAY,CAAC,UAAU;YACnC;YAEA,kCAAkC;YAClC,MAAM,UAAU,CAAC;;AAEvB,EAAE,IAAI,gBAAgB,iBAAiB,CAAC,YAAY;YAE9C,MAAM,UAAU,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAAE,MAAM;YAA8B;YAC1E,MAAM,SAAS,IAAI,eAAe,CAAC;YAEnC,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,IAAI,GAAG;YACpB,aAAa,QAAQ,GAAG,CAAC,qBAAqB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC5F,aAAa,KAAK,CAAC,OAAO,GAAG;YAE7B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,aAAa,KAAK;YAClB,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,0BAA0B;YAC1B,WAAW,IAAM,IAAI,eAAe,CAAC,SAAS;YAE9C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,aAAa;YACf;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,iDAAiD;QACjD,MAAM,UAAU,KAAK;QACrB,OAAO,CAAC,qCAAqC,EAAE,SAAS;IAC1D;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;;oCACZ,uBACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,aAAa;;;;;;4CAAmB;;;;;;;kDAI7E,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,eAAe,UAAU,QAAQ;;0DAE5C,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAO1C,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;8CACX,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAqC;;;;;;;;;;;;oBAM/D,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;4BACzC,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,6LAAC;wCACC,KAAK,oBAAoB;wCACzB,KAAI;wCACJ,WAAU;wCACV,SAAS,CAAC;4CACP,EAAE,MAAM,CAAsB,KAAK,CAAC,OAAO,GAAG;wCACjD;;;;;;;;;;;;;;;;;;kCAQV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,KAAK;4BAAY,WAAU;sCAC7B,CAAC,eAAe,CAAC,2BAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQrC,OAAO,MAAM,GAAG,mBACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAG,WAAU;kCACX,OAAO,GAAG,CAAC,CAAC,MAAM,sBACjB,6LAAC;gCAAe,WAAU;;kDACxB,6LAAC;wCAAK,WAAU;;;;;;oCACf;;+BAFM;;;;;;;;;;;;;;;;0BAUjB,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,iBAAiB,cAAc;;kEAE7D,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,aAAa;;kEAE3C,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAM,iBAAiB;;;;;;;;;;;;;;;;;kDAG5B,6LAAC;;0DACC,6LAAC;gDAAQ,WAAU;0DAAuC;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQhB,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,cAAc;;0DAE5C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAIrC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAM;;;;;;;;;;;;;;;;;;;;;;;YAOd,CAAC,gBAAgB,KAAK,mBACrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BAAQ,WAAU;sCAA+C;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;;gCACZ,uBACC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;wDAAkB,MAAM,cAAc;;;;;;;8DACzC,6LAAC;;wDAAE;wDAAqB,MAAM,eAAe,GAAG,MAAM;;;;;;;8DACtD,6LAAC;;wDAAE;wDAAsB,MAAM,gBAAgB,GAAG,MAAM;;;;;;;8DACxD,6LAAC;;wDAAE;wDAAmB,MAAM,aAAa,GAAG,MAAM;;;;;;;;;;;;;;;;;;;gCAKvD,8BACC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;sDACZ;;;;;;sDAEH,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;gDACP,UAAU,SAAS,CAAC,SAAS,CAAC;gDAC9B,IAAI,YAAY,WAAW,cAAc;4CAC3C;;8DAEA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GAzXa;KAAA", "debugId": null}}, {"offset": {"line": 2040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/RapidPrototypingApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { Progress } from '@/components/ui/Progress';\nimport { FileUpload } from '@/components/ui/FileUpload';\nimport { ChatInterface } from '@/components/ui/ChatInterface';\nimport { ToastContainer, useToast } from '@/components/ui/Toast';\nimport { ThinkingIndicator } from '@/components/ui/ThinkingIndicator';\nimport { ArchitectureDiagram } from '@/components/ui/ArchitectureDiagram';\nimport { ImplementationPrompts } from '@/components/ui/ImplementationPrompts';\nimport { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';\n\ntype Step = 1 | 2 | 3 | 4 | 5;\n\ninterface AppState {\n  currentStep: Step;\n  uploadedFile: File | null;\n  isProcessing: boolean;\n  processingStep: string;\n  error: string | null;\n  isTransitioning: boolean;\n  transitionType: 'transcript' | 'technical' | 'architecture' | 'prompts' | null;\n  documents: {\n    problemStatement: string | null;\n    technicalRequirements: string | null;\n    architectureDiagram: {\n      mermaidCode?: string;\n      plantUMLCode?: string;\n      legend?: string[];\n      fullResponse?: string;\n      debug?: any;\n    } | null;\n    prompts: {\n      frontend: string;\n      backend: string;\n      database: string;\n      devops: string | any;\n    } | null;\n  };\n  validations: {\n    problemStatement: boolean | null;\n    technicalRequirements: boolean | null;\n    architectureDiagram: boolean | null;\n  };\n}\n\nconst STEPS = [\n  { id: 1, title: 'Upload Transcript', icon: FileText, description: 'Upload your conversation transcript' },\n  { id: 2, title: 'Problem Statement', icon: CheckCircle, description: 'Review and validate problem statement' },\n  { id: 3, title: 'Technical Requirements', icon: Settings, description: 'Review technical requirements document' },\n  { id: 4, title: 'Architecture Diagram', icon: Image, description: 'Review system architecture' },\n  { id: 5, title: 'Generated Prompts', icon: Code, description: 'Copy implementation prompts' }\n];\n\nexport const RapidPrototypingApp: React.FC = () => {\n  const { toasts, removeToast, success, error, info } = useToast();\n  const [state, setState] = useState<AppState>({\n    currentStep: 1,\n    uploadedFile: null,\n    isProcessing: false,\n    processingStep: '',\n    error: null,\n    isTransitioning: false,\n    transitionType: null,\n    documents: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null,\n      prompts: null\n    },\n    validations: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null\n    }\n  });\n\n  const handleFileUpload = async (file: File) => {\n    setState(prev => ({\n      ...prev,\n      uploadedFile: file,\n      isProcessing: true,\n      processingStep: 'Uploading transcript...',\n      error: null\n    }));\n\n    info('Processing your transcript with Claude AI...', 5000);\n\n    try {\n      setState(prev => ({ ...prev, processingStep: 'Analyzing transcript with Claude AI...' }));\n\n      // Create FormData to send file to API\n      const formData = new FormData();\n      formData.append('file', file);\n\n      setState(prev => ({ ...prev, processingStep: 'Generating problem statement document...' }));\n\n      // Call the API to generate problem statement\n      const response = await fetch('/api/generate-problem-statement', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate problem statement`;\n        throw new Error(errorMessage);\n      }\n\n      setState(prev => ({ ...prev, processingStep: 'Finalizing document...' }));\n\n      // Get the generated document as blob\n      const blob = await response.blob();\n      const documentUrl = URL.createObjectURL(blob);\n\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        currentStep: 2,\n        error: null,\n        documents: {\n          ...prev.documents,\n          problemStatement: documentUrl\n        }\n      }));\n\n      success('Problem statement document generated successfully with Claude AI!');\n    } catch (err) {\n      console.error('Error generating problem statement:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate problem statement. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        error: errorMessage\n      }));\n    }\n  };\n\n  const handleValidation = async (documentType: keyof AppState['validations'], isValid: boolean) => {\n    setState(prev => ({\n      ...prev,\n      validations: {\n        ...prev.validations,\n        [documentType]: isValid\n      }\n    }));\n\n    if (isValid) {\n      const nextStep = state.currentStep + 1;\n\n      if (nextStep === 3 && documentType === 'problemStatement') {\n        // Show thinking indicator for technical requirements generation\n        setState(prev => ({\n          ...prev,\n          isTransitioning: true,\n          transitionType: 'technical',\n          error: null\n        }));\n\n        // Small delay to show the thinking indicator\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Generate technical requirements from problem statement\n        await generateTechnicalRequirements();\n      } else if (nextStep === 4 && documentType === 'technicalRequirements') {\n        // Generate architecture diagram from technical requirements\n        setState(prev => ({\n          ...prev,\n          isTransitioning: true,\n          transitionType: 'architecture',\n          error: null\n        }));\n\n        // Small delay to show the thinking indicator\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Generate architecture diagram\n        await generateArchitectureDiagram();\n      } else if (nextStep === 5 && documentType === 'architectureDiagram') {\n        // Generate implementation prompts from technical requirements\n        setState(prev => ({\n          ...prev,\n          isTransitioning: true,\n          transitionType: 'prompts',\n          error: null\n        }));\n\n        // Small delay to show the thinking indicator\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Generate implementation prompts\n        await generateImplementationPrompts();\n      }\n    }\n  };\n\n  const generateTechnicalRequirements = async () => {\n    try {\n      // Extract content from the problem statement document\n      let problemStatementContent = '';\n\n      if (state.documents.problemStatement) {\n        // For now, we'll use a placeholder since we can't easily extract from the blob\n        // In a real implementation, you might store the text content separately\n        problemStatementContent = `Problem Statement Document Analysis:\n\nThis document contains the comprehensive problem statement analysis including:\n- Executive Summary of the business challenges\n- Background and context of the workshop\n- Key business challenges identified\n- Core user needs and pain points\n- How Might We problem statement\n- Constraints and success criteria\n- Next steps and recommendations\n- Key insights and quotes from stakeholders\n\nThe technical solution should address the scalability, performance, and integration challenges identified in the problem statement while providing a modern, cloud-native architecture that can handle the specified requirements.`;\n      }\n\n      // Call the technical requirements API\n      const response = await fetch('/api/generate-technical-requirements', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          problemStatementContent: problemStatementContent\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate technical requirements`;\n        throw new Error(errorMessage);\n      }\n\n      // Get the generated document as blob\n      const blob = await response.blob();\n      const documentUrl = URL.createObjectURL(blob);\n\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        currentStep: 3,\n        error: null,\n        documents: {\n          ...prev.documents,\n          technicalRequirements: documentUrl\n        }\n      }));\n\n      success('Technical requirements document generated successfully with Claude AI!');\n    } catch (err) {\n      console.error('Error generating technical requirements:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate technical requirements. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        error: errorMessage\n      }));\n    }\n  };\n\n  const generateArchitectureDiagram = async () => {\n    try {\n      // Extract content from the technical requirements document\n      let technicalRequirementsContent = '';\n\n      if (state.documents.technicalRequirements) {\n        // For now, we'll use a placeholder since we can't easily extract from the blob\n        // In a real implementation, you might store the text content separately\n        technicalRequirementsContent = `Technical Solution Document Analysis:\n\nThis document contains the comprehensive technical solution including:\n- Solution Architecture Overview with cloud-native design\n- Component Design with technology choices and interfaces\n- Data Flow & Integration patterns and real-time processing\n- Security & Compliance framework and requirements\n- Non-Functional Requirements for scalability and performance\n- Prototype Scope & MVP Features for validation\n- Implementation Roadmap with phases and milestones\n- Next Steps & Recommendations for moving to prototype\n\nThe architecture diagram should visualize the cloud-native solution with microservices, data flows, security layers, and integration points as specified in the technical requirements.`;\n      }\n\n      // Call the architecture diagram API\n      const response = await fetch('/api/generate-architecture-diagram', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          technicalRequirementsContent: technicalRequirementsContent\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate architecture diagram`;\n        throw new Error(errorMessage);\n      }\n\n      const data = await response.json();\n\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        currentStep: 4,\n        error: null,\n        documents: {\n          ...prev.documents,\n          architectureDiagram: {\n            mermaidCode: data.mermaid,\n            plantUMLCode: data.plantUML,\n            legend: data.legend,\n            fullResponse: data.fullResponse,\n            debug: data.debug\n          }\n        }\n      }));\n\n      success('Architecture diagram generated successfully with Claude Sonnet 4!');\n    } catch (err) {\n      console.error('Error generating architecture diagram:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate architecture diagram. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        error: errorMessage\n      }));\n    }\n  };\n\n  const currentStepData = STEPS.find(step => step.id === state.currentStep);\n  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <ToastContainer toasts={toasts} onRemove={removeToast} />\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold gradient-text mb-4\">\n            Rapid Prototyping Automation\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Transform your conversation transcripts into comprehensive technical documentation and implementation guides\n          </p>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <Progress \n            value={progress} \n            showLabel \n            label={`Step ${state.currentStep} of ${STEPS.length}: ${currentStepData?.title}`}\n            className=\"max-w-2xl mx-auto\"\n          />\n        </div>\n\n        {/* Steps Navigation */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"flex space-x-4 overflow-x-auto pb-2\">\n            {STEPS.map((step) => {\n              const Icon = step.icon;\n              const isActive = step.id === state.currentStep;\n              const isCompleted = step.id < state.currentStep;\n              \n              return (\n                <div\n                  key={step.id}\n                  className={`flex flex-col items-center min-w-[120px] p-3 rounded-lg transition-all ${\n                    isActive \n                      ? 'bg-blue-100 border-2 border-blue-300' \n                      : isCompleted \n                        ? 'bg-green-100 border-2 border-green-300'\n                        : 'bg-gray-100 border-2 border-gray-200'\n                  }`}\n                >\n                  <Icon \n                    className={`h-6 w-6 mb-2 ${\n                      isActive \n                        ? 'text-blue-600' \n                        : isCompleted \n                          ? 'text-green-600'\n                          : 'text-gray-400'\n                    }`} \n                  />\n                  <span className={`text-sm font-medium text-center ${\n                    isActive \n                      ? 'text-blue-800' \n                      : isCompleted \n                        ? 'text-green-800'\n                        : 'text-gray-600'\n                  }`}>\n                    {step.title}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Show thinking indicator when transitioning between steps */}\n          {state.isTransitioning && state.transitionType && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardContent>\n                <ThinkingIndicator step={state.transitionType} />\n                {state.error && (\n                  <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg max-w-md mx-auto\">\n                    <p className=\"text-red-700 text-sm\">{state.error}</p>\n                    <Button\n                      variant=\"secondary\"\n                      size=\"sm\"\n                      className=\"mt-2\"\n                      onClick={() => setState(prev => ({\n                        ...prev,\n                        error: null,\n                        isTransitioning: false,\n                        transitionType: null\n                      }))}\n                    >\n                      Try Again\n                    </Button>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 1 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Upload Your Conversation Transcript</CardTitle>\n              </CardHeader>\n              <CardContent>\n                {!state.isProcessing ? (\n                  <FileUpload onFileSelect={handleFileUpload} />\n                ) : (\n                  <div>\n                    <ThinkingIndicator step=\"transcript\" />\n                    {state.error && (\n                      <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg max-w-md mx-auto\">\n                        <p className=\"text-red-700 text-sm\">{state.error}</p>\n                        <Button\n                          variant=\"secondary\"\n                          size=\"sm\"\n                          className=\"mt-2\"\n                          onClick={() => setState(prev => ({ ...prev, error: null, isProcessing: false }))}\n                        >\n                          Try Again\n                        </Button>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 2 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Problem Statement Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">AI-Generated Problem Statement</h4>\n                    <div className=\"prose max-w-none\">\n                      <p className=\"text-gray-700 leading-relaxed\">\n                        Claude AI has analyzed your conversation transcript and generated a comprehensive problem statement document.\n                        This professional document includes executive summary, business challenges, user needs, and actionable recommendations.\n                      </p>\n                      <div className=\"mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\">\n                        <h5 className=\"font-medium text-blue-900 mb-2\">Document Includes:</h5>\n                        <ul className=\"list-disc list-inside text-blue-800 space-y-1\">\n                          <li>Executive Summary & Background Context</li>\n                          <li>Key Business Challenges with Supporting Quotes</li>\n                          <li>Core User Needs & Pain Points Analysis</li>\n                          <li>\"How Might We\" Problem Statement</li>\n                          <li>Constraints & Success Criteria</li>\n                          <li>Next Steps & Recommendations</li>\n                          <li>Key Insights & Critical Quotes</li>\n                        </ul>\n                      </div>\n                      <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                        <p className=\"text-green-800 text-sm\">\n                          ✅ Generated using Claude Sonnet 4 - Latest AI model for professional document creation\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\n                    <Button\n                      variant=\"success\"\n                      size=\"lg\"\n                      onClick={() => {\n                        if (state.documents.problemStatement) {\n                          // Download the actual generated document\n                          const element = document.createElement('a');\n                          element.href = state.documents.problemStatement;\n                          element.download = 'Problem_Statement.docx';\n                          document.body.appendChild(element);\n                          element.click();\n                          document.body.removeChild(element);\n                          success('Problem Statement document downloaded!');\n                        } else {\n                          error('Document not available. Please try uploading the transcript again.');\n                        }\n                      }}\n                      className=\"flex-1\"\n                      disabled={!state.documents.problemStatement}\n                    >\n                      Download Problem Statement (.docx)\n                    </Button>\n                  </div>\n\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                    <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                    <p className=\"text-yellow-700 mb-4\">\n                      Please review the problem statement document and confirm if it accurately captures your requirements.\n                    </p>\n                    <div className=\"flex gap-3\">\n                      <Button\n                        variant=\"success\"\n                        onClick={() => handleValidation('problemStatement', true)}\n                      >\n                        YES - Continue\n                      </Button>\n                      <Button\n                        variant=\"secondary\"\n                        onClick={() => handleValidation('problemStatement', false)}\n                      >\n                        NO - Needs Feedback\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 3 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Technical Requirements Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">AI-Generated Technical Solution Document</h4>\n                    <div className=\"prose max-w-none\">\n                      <p className=\"text-gray-700 leading-relaxed mb-4\">\n                        Claude AI has analyzed your problem statement and generated a comprehensive technical solution document.\n                        This professional document includes solution architecture, component design, and implementation roadmap.\n                      </p>\n                      <div className=\"grid md:grid-cols-2 gap-4\">\n                        <div className=\"p-4 bg-blue-50 rounded-lg\">\n                          <h5 className=\"font-medium text-blue-900 mb-2\">Architecture & Design</h5>\n                          <ul className=\"list-disc list-inside text-blue-800 space-y-1 text-sm\">\n                            <li>Solution Architecture Overview</li>\n                            <li>Component Design & Technology Choices</li>\n                            <li>Data Flow & Integration Patterns</li>\n                            <li>Cloud-Native Implementation Strategy</li>\n                          </ul>\n                        </div>\n                        <div className=\"p-4 bg-purple-50 rounded-lg\">\n                          <h5 className=\"font-medium text-purple-900 mb-2\">Requirements & Implementation</h5>\n                          <ul className=\"list-disc list-inside text-purple-800 space-y-1 text-sm\">\n                            <li>Security & Compliance Framework</li>\n                            <li>Non-Functional Requirements</li>\n                            <li>Prototype Scope & MVP Features</li>\n                            <li>Implementation Roadmap</li>\n                          </ul>\n                        </div>\n                      </div>\n                      <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                        <p className=\"text-green-800 text-sm\">\n                          ✅ Generated using Claude Sonnet 4 - Based on your problem statement analysis\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\n                    <Button\n                      variant=\"success\"\n                      size=\"lg\"\n                      onClick={() => {\n                        if (state.documents.technicalRequirements) {\n                          // Download the actual generated document\n                          const element = document.createElement('a');\n                          element.href = state.documents.technicalRequirements;\n                          element.download = 'Technical_Solution_Document.docx';\n                          document.body.appendChild(element);\n                          element.click();\n                          document.body.removeChild(element);\n                          success('Technical Solution Document downloaded!');\n                        } else {\n                          error('Document not available. Please try regenerating from the problem statement.');\n                        }\n                      }}\n                      className=\"flex-1\"\n                      disabled={!state.documents.technicalRequirements}\n                    >\n                      Download Technical Solution Document (.docx)\n                    </Button>\n                  </div>\n\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                    <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                    <p className=\"text-yellow-700 mb-4\">\n                      Please review the technical requirements document and confirm if it meets your technical specifications.\n                    </p>\n                    <div className=\"flex gap-3\">\n                      <Button\n                        variant=\"success\"\n                        onClick={() => handleValidation('technicalRequirements', true)}\n                      >\n                        YES - Continue\n                      </Button>\n                      <Button\n                        variant=\"secondary\"\n                        onClick={() => handleValidation('technicalRequirements', false)}\n                      >\n                        NO - Needs Feedback\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 4 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>AI-Generated System Architecture</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Claude Sonnet 4 Generated Architecture</h4>\n                    <div className=\"prose max-w-none mb-4\">\n                      <p className=\"text-gray-700 leading-relaxed\">\n                        Claude Sonnet 4 has analyzed your technical requirements and generated a comprehensive system architecture diagram.\n                        This includes component relationships, data flows, and integration patterns.\n                      </p>\n                      <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                        <p className=\"text-green-800 text-sm\">\n                          ✅ Generated using Claude Sonnet 4 - Based on your technical solution document\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Architecture Diagram Component */}\n                  <ArchitectureDiagram\n                    mermaidCode={state.documents.architectureDiagram?.mermaidCode}\n                    plantUMLCode={state.documents.architectureDiagram?.plantUMLCode}\n                    legend={state.documents.architectureDiagram?.legend}\n                    fullResponse={state.documents.architectureDiagram?.fullResponse}\n                    debug={state.documents.architectureDiagram?.debug}\n                    onCopyCode={(code, type) => {\n                      success(`${type === 'mermaid' ? 'Mermaid' : 'PlantUML'} code copied to clipboard!`);\n                    }}\n                  />\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-blue-800 mb-3\">Quick Review</h4>\n                      <p className=\"text-blue-700 mb-4\">\n                        Does the architecture diagram accurately represent your system design requirements?\n                      </p>\n                      <div className=\"flex gap-3\">\n                        <Button\n                          variant=\"success\"\n                          onClick={() => handleValidation('architectureDiagram', true)}\n                        >\n                          YES - Looks Good\n                        </Button>\n                        <Button\n                          variant=\"secondary\"\n                          onClick={() => handleValidation('architectureDiagram', false)}\n                        >\n                          NO - Needs Changes\n                        </Button>\n                      </div>\n                    </div>\n\n                    <ChatInterface\n                      context=\"System architecture diagram generated from technical requirements\"\n                      diagramCode={state.documents.architectureDiagram?.mermaidCode || ''}\n                      onFeedback={(feedback) => {\n                        console.log('Architecture feedback:', feedback);\n                      }}\n                      onDiagramUpdate={(updatedCode) => {\n                        console.log('Diagram update:', updatedCode);\n                        // Update the diagram with new code\n                        setState(prev => ({\n                          ...prev,\n                          documents: {\n                            ...prev.documents,\n                            architectureDiagram: {\n                              ...prev.documents.architectureDiagram,\n                              [updatedCode.type === 'mermaid' ? 'mermaidCode' : 'plantUMLCode']: updatedCode.code\n                            }\n                          }\n                        }));\n                        success('Architecture diagram updated based on your feedback!');\n                      }}\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 5 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Implementation Prompts</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Copy-Paste Ready Prompts</h4>\n                    <p className=\"text-gray-600 mb-6\">\n                      Here are the generated prompts for implementing your solution. Click to copy each prompt.\n                    </p>\n\n                    <div className=\"space-y-4\">\n                      {[\n                        {\n                          title: \"Frontend Development Prompt\",\n                          content: \"Create a React TypeScript application with the following components: user authentication, dashboard interface, real-time data visualization, and responsive design using Tailwind CSS...\"\n                        },\n                        {\n                          title: \"Backend API Development Prompt\",\n                          content: \"Build a Node.js Express API with the following endpoints: user management, data processing, real-time WebSocket connections, and PostgreSQL database integration...\"\n                        },\n                        {\n                          title: \"Database Schema Prompt\",\n                          content: \"Design a PostgreSQL database schema with the following tables: users, projects, data_points, and audit_logs. Include proper indexing and relationships...\"\n                        },\n                        {\n                          title: \"DevOps & Deployment Prompt\",\n                          content: \"Create Docker containers and Kubernetes deployment configurations for a scalable application with load balancing, auto-scaling, and monitoring...\"\n                        }\n                      ].map((prompt, index) => (\n                        <div key={index} className=\"border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors\">\n                          <div className=\"flex justify-between items-start mb-2\">\n                            <h5 className=\"font-medium text-gray-900\">{prompt.title}</h5>\n                            <Button\n                              size=\"sm\"\n                              variant=\"secondary\"\n                              onClick={() => {\n                                navigator.clipboard.writeText(prompt.content);\n                                success('Prompt copied to clipboard!');\n                              }}\n                            >\n                              Copy\n                            </Button>\n                          </div>\n                          <p className=\"text-sm text-gray-600 line-clamp-3\">\n                            {prompt.content}\n                          </p>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 text-center\">\n                    <CheckCircle className=\"h-12 w-12 text-green-500 mx-auto mb-3\" />\n                    <h4 className=\"font-semibold text-green-800 mb-2\">Process Complete!</h4>\n                    <p className=\"text-green-700\">\n                      Your rapid prototyping automation is complete. Use the prompts above to implement your solution.\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AAZA;;;;;;;;;;;AAgDA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,iNAAA,CAAA,WAAQ;QAAE,aAAa;IAAsC;IACxG;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,8NAAA,CAAA,cAAW;QAAE,aAAa;IAAwC;IAC7G;QAAE,IAAI;QAAG,OAAO;QAA0B,MAAM,6MAAA,CAAA,WAAQ;QAAE,aAAa;IAAyC;IAChH;QAAE,IAAI;QAAG,OAAO;QAAwB,MAAM,uMAAA,CAAA,QAAK;QAAE,aAAa;IAA6B;IAC/F;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,qMAAA,CAAA,OAAI;QAAE,aAAa;IAA8B;CAC7F;AAEM,MAAM,sBAAgC;;IAC3C,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC3C,aAAa;QACb,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,OAAO;QACP,iBAAiB;QACjB,gBAAgB;QAChB,WAAW;YACT,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;YACrB,SAAS;QACX;QACA,aAAa;YACX,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;YACT,CAAC;QAED,KAAK,gDAAgD;QAErD,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAyC,CAAC;YAEvF,sCAAsC;YACtC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAA2C,CAAC;YAEzF,6CAA6C;YAC7C,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,uCAAuC,CAAC;gBACjH,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAyB,CAAC;YAEvE,qCAAqC;YACrC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,cAAc,IAAI,eAAe,CAAC;YAExC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,kBAAkB;oBACpB;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,OAAO,cAA6C;QAC3E,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;oBACX,GAAG,KAAK,WAAW;oBACnB,CAAC,aAAa,EAAE;gBAClB;YACF,CAAC;QAED,IAAI,SAAS;YACX,MAAM,WAAW,MAAM,WAAW,GAAG;YAErC,IAAI,aAAa,KAAK,iBAAiB,oBAAoB;gBACzD,gEAAgE;gBAChE,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;wBACjB,gBAAgB;wBAChB,OAAO;oBACT,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,yDAAyD;gBACzD,MAAM;YACR,OAAO,IAAI,aAAa,KAAK,iBAAiB,yBAAyB;gBACrE,4DAA4D;gBAC5D,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;wBACjB,gBAAgB;wBAChB,OAAO;oBACT,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,gCAAgC;gBAChC,MAAM;YACR,OAAO,IAAI,aAAa,KAAK,iBAAiB,uBAAuB;gBACnE,8DAA8D;gBAC9D,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;wBACjB,gBAAgB;wBAChB,OAAO;oBACT,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,kCAAkC;gBAClC,MAAM;YACR;QACF;IACF;IAEA,MAAM,gCAAgC;QACpC,IAAI;YACF,sDAAsD;YACtD,IAAI,0BAA0B;YAE9B,IAAI,MAAM,SAAS,CAAC,gBAAgB,EAAE;gBACpC,+EAA+E;gBAC/E,wEAAwE;gBACxE,0BAA0B,CAAC;;;;;;;;;;;;kOAY+L,CAAC;YAC7N;YAEA,sCAAsC;YACtC,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,yBAAyB;gBAC3B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,4CAA4C,CAAC;gBACtH,MAAM,IAAI,MAAM;YAClB;YAEA,qCAAqC;YACrC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,cAAc,IAAI,eAAe,CAAC;YAExC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,uBAAuB;oBACzB;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,8BAA8B;QAClC,IAAI;YACF,2DAA2D;YAC3D,IAAI,+BAA+B;YAEnC,IAAI,MAAM,SAAS,CAAC,qBAAqB,EAAE;gBACzC,+EAA+E;gBAC/E,wEAAwE;gBACxE,+BAA+B,CAAC;;;;;;;;;;;;uLAY+I,CAAC;YAClL;YAEA,oCAAoC;YACpC,MAAM,WAAW,MAAM,MAAM,sCAAsC;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,8BAA8B;gBAChC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,0CAA0C,CAAC;gBACpH,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,qBAAqB;4BACnB,aAAa,KAAK,OAAO;4BACzB,cAAc,KAAK,QAAQ;4BAC3B,QAAQ,KAAK,MAAM;4BACnB,cAAc,KAAK,YAAY;4BAC/B,OAAO,KAAK,KAAK;wBACnB;oBACF;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,kBAAkB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,WAAW;IACxE,MAAM,WAAW,AAAC,CAAC,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK;IAElE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,UAAU;;;;;;0BAC1C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,SAAS;4BACT,OAAO,CAAC,KAAK,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,iBAAiB,OAAO;4BAChF,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC;gCACV,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,KAAK,EAAE,KAAK,MAAM,WAAW;gCAC9C,MAAM,cAAc,KAAK,EAAE,GAAG,MAAM,WAAW;gCAE/C,qBACE,6LAAC;oCAEC,WAAW,CAAC,uEAAuE,EACjF,WACI,yCACA,cACE,2CACA,wCACN;;sDAEF,6LAAC;4CACC,WAAW,CAAC,aAAa,EACvB,WACI,kBACA,cACE,mBACA,iBACN;;;;;;sDAEJ,6LAAC;4CAAK,WAAW,CAAC,gCAAgC,EAChD,WACI,kBACA,cACE,mBACA,iBACN;sDACC,KAAK,KAAK;;;;;;;mCAzBR,KAAK,EAAE;;;;;4BA6BlB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;4BAEZ,MAAM,eAAe,IAAI,MAAM,cAAc,kBAC5C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;0CACpC,cAAA,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC,gJAAA,CAAA,oBAAiB;4CAAC,MAAM,MAAM,cAAc;;;;;;wCAC5C,MAAM,KAAK,kBACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAwB,MAAM,KAAK;;;;;;8DAChD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,SAAS,CAAA,OAAQ,CAAC;gEAC/B,GAAG,IAAI;gEACP,OAAO;gEACP,iBAAiB;gEACjB,gBAAgB;4DAClB,CAAC;8DACF;;;;;;;;;;;;;;;;;;;;;;;4BASV,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACT,CAAC,MAAM,YAAY,iBAClB,6LAAC,yIAAA,CAAA,aAAU;4CAAC,cAAc;;;;;iEAE1B,6LAAC;;8DACC,6LAAC,gJAAA,CAAA,oBAAiB;oDAAC,MAAK;;;;;;gDACvB,MAAM,KAAK,kBACV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB,MAAM,KAAK;;;;;;sEAChD,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,SAAS,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO;wEAAM,cAAc;oEAAM,CAAC;sEAC/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAWd,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAI7C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,6LAAC;4EAAG,WAAU;;8FACZ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;;;;;;;;;;;;;8EAGR,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;8DAO5C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,IAAI,MAAM,SAAS,CAAC,gBAAgB,EAAE;gEACpC,yCAAyC;gEACzC,MAAM,UAAU,SAAS,aAAa,CAAC;gEACvC,QAAQ,IAAI,GAAG,MAAM,SAAS,CAAC,gBAAgB;gEAC/C,QAAQ,QAAQ,GAAG;gEACnB,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ,KAAK;gEACb,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ;4DACV,OAAO;gEACL,MAAM;4DACR;wDACF;wDACA,WAAU;wDACV,UAAU,CAAC,MAAM,SAAS,CAAC,gBAAgB;kEAC5C;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;8EAGD,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUZ,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAqC;;;;;;8EAIlD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAiC;;;;;;8FAC/C,6LAAC;oFAAG,WAAU;;sGACZ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;;;;;;;;;;;;;sFAGR,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAmC;;;;;;8FACjD,6LAAC;oFAAG,WAAU;;sGACZ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;;;;;;;;;;;;;;;;;;;8EAIV,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;8DAO5C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,IAAI,MAAM,SAAS,CAAC,qBAAqB,EAAE;gEACzC,yCAAyC;gEACzC,MAAM,UAAU,SAAS,aAAa,CAAC;gEACvC,QAAQ,IAAI,GAAG,MAAM,SAAS,CAAC,qBAAqB;gEACpD,QAAQ,QAAQ,GAAG;gEACnB,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ,KAAK;gEACb,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,QAAQ;4DACV,OAAO;gEACL,MAAM;4DACR;wDACF;wDACA,WAAU;wDACV,UAAU,CAAC,MAAM,SAAS,CAAC,qBAAqB;kEACjD;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;8EAGD,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUZ,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAI7C,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;8DAQ5C,6LAAC,kJAAA,CAAA,sBAAmB;oDAClB,aAAa,MAAM,SAAS,CAAC,mBAAmB,EAAE;oDAClD,cAAc,MAAM,SAAS,CAAC,mBAAmB,EAAE;oDACnD,QAAQ,MAAM,SAAS,CAAC,mBAAmB,EAAE;oDAC7C,cAAc,MAAM,SAAS,CAAC,mBAAmB,EAAE;oDACnD,OAAO,MAAM,SAAS,CAAC,mBAAmB,EAAE;oDAC5C,YAAY,CAAC,MAAM;wDACjB,QAAQ,GAAG,SAAS,YAAY,YAAY,WAAW,0BAA0B,CAAC;oDACpF;;;;;;8DAGF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAGlC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;sFAGD,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;;;;;;;;;;;;;sEAML,6LAAC,4IAAA,CAAA,gBAAa;4DACZ,SAAQ;4DACR,aAAa,MAAM,SAAS,CAAC,mBAAmB,EAAE,eAAe;4DACjE,YAAY,CAAC;gEACX,QAAQ,GAAG,CAAC,0BAA0B;4DACxC;4DACA,iBAAiB,CAAC;gEAChB,QAAQ,GAAG,CAAC,mBAAmB;gEAC/B,mCAAmC;gEACnC,SAAS,CAAA,OAAQ,CAAC;wEAChB,GAAG,IAAI;wEACP,WAAW;4EACT,GAAG,KAAK,SAAS;4EACjB,qBAAqB;gFACnB,GAAG,KAAK,SAAS,CAAC,mBAAmB;gFACrC,CAAC,YAAY,IAAI,KAAK,YAAY,gBAAgB,eAAe,EAAE,YAAY,IAAI;4EACrF;wEACF;oEACF,CAAC;gEACD,QAAQ;4DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQX,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAE,WAAU;sEAAqB;;;;;;sEAIlC,6LAAC;4DAAI,WAAU;sEACZ;gEACC;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;6DACD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,6LAAC;oEAAgB,WAAU;;sFACzB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAA6B,OAAO,KAAK;;;;;;8FACvD,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS;wFACP,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;wFAC5C,QAAQ;oFACV;8FACD;;;;;;;;;;;;sFAIH,6LAAC;4EAAE,WAAU;sFACV,OAAO,OAAO;;;;;;;mEAfT;;;;;;;;;;;;;;;;8DAsBhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlD;GA3uBa;;QAC2C,oIAAA,CAAA,WAAQ;;;KADnD", "debugId": null}}, {"offset": {"line": 3539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { RapidPrototypingApp } from '@/components/RapidPrototypingApp';\n\nexport default function Home() {\n  return <RapidPrototypingApp />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,6LAAC,4IAAA,CAAA,sBAAmB;;;;;AAC7B;KAFwB", "debugId": null}}]}