{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/test-file-upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport mammoth from 'mammoth';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n    \n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    // Read file content\n    let transcriptContent = '';\n    \n    console.log(`Test: Processing file: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);\n    \n    try {\n      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n        console.log('Test: Reading as plain text file');\n        transcriptContent = await file.text();\n      } else if (file.name.endsWith('.docx')) {\n        console.log('Test: Reading as DOCX file');\n        const arrayBuffer = await file.arrayBuffer();\n        const result = await mammoth.extractRawText({ buffer: Buffer.from(arrayBuffer) });\n        transcriptContent = result.value;\n      } else {\n        console.log('Test: Reading as generic text file');\n        transcriptContent = await file.text();\n      }\n    } catch (fileError) {\n      console.error('Test: Error reading file content:', fileError);\n      return NextResponse.json({ error: 'Failed to read file content' }, { status: 400 });\n    }\n\n    console.log(`Test: Extracted content length: ${transcriptContent.length} characters`);\n    console.log(`Test: Content preview: ${transcriptContent.substring(0, 200)}...`);\n\n    return NextResponse.json({\n      success: true,\n      fileName: file.name,\n      fileType: file.type,\n      fileSize: file.size,\n      contentLength: transcriptContent.length,\n      contentPreview: transcriptContent.substring(0, 500),\n      message: 'File processed successfully'\n    });\n\n  } catch (error) {\n    console.error('Test: Error processing file:', error);\n    return NextResponse.json(\n      { error: 'Failed to process file' }, \n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,oBAAoB;QACpB,IAAI,oBAAoB;QAExB,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC;QAE/F,IAAI;YACF,IAAI,KAAK,IAAI,KAAK,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAC5D,QAAQ,GAAG,CAAC;gBACZ,oBAAoB,MAAM,KAAK,IAAI;YACrC,OAAO,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU;gBACtC,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc,MAAM,KAAK,WAAW;gBAC1C,MAAM,SAAS,MAAM,yIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;oBAAE,QAAQ,OAAO,IAAI,CAAC;gBAAa;gBAC/E,oBAAoB,OAAO,KAAK;YAClC,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,oBAAoB,MAAM,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,WAAW;YAClB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8B,GAAG;gBAAE,QAAQ;YAAI;QACnF;QAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,kBAAkB,MAAM,CAAC,WAAW,CAAC;QACpF,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,kBAAkB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAE9E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;YACnB,eAAe,kBAAkB,MAAM;YACvC,gBAAgB,kBAAkB,SAAS,CAAC,GAAG;YAC/C,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}