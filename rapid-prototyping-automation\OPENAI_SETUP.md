# OpenAI API Integration Setup

This guide will help you set up OpenAI API integration for architecture diagram generation and chat functionality in Step 4.

## Prerequisites

1. **OpenAI Account**: You need an account with OpenAI to access GPT-4o
2. **API Key**: You'll need a valid OpenAI API key with GPT-4o access
3. **Credits**: Ensure you have sufficient API credits for diagram generation

## Step 1: Get Your OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign up or log in to your account
3. Navigate to the API Keys section
4. Create a new API key
5. Copy the API key (it starts with `sk-`)

## Step 2: Configure Environment Variables

1. Edit your `.env.local` file and add your OpenAI API key:
```env
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```

2. **Important**: Never commit your `.env.local` file to version control!

## Step 3: Verify Integration

1. Start your development server:
```bash
npm run dev
```

2. Complete Steps 1-3 (transcript upload, problem statement, technical requirements)
3. In Step 3, click "YES - Continue" to proceed to Step 4
4. The system should:
   - Send the technical requirements to OpenAI GPT-4o
   - Generate architecture diagrams (Mermaid and PlantUML)
   - Display an interactive diagram with chat functionality

## API Usage & Costs

- **Model Used**: GPT-4o (latest OpenAI model)
- **Typical Cost**: ~$0.20-1.00 per architecture diagram generation
- **Chat Cost**: ~$0.05-0.20 per chat interaction
- **Processing Time**: 15-45 seconds depending on complexity
- **Token Limits**: Up to 4000 tokens for response

## Features

### Architecture Diagram Generation
- **Input**: Technical requirements document content
- **Output**: 
  - Mermaid diagram code
  - PlantUML diagram code
  - Legend and assumptions
  - Visual diagram rendering

### Interactive Chat
- **Real-time feedback**: Discuss diagram improvements
- **Diagram updates**: AI can modify diagrams based on feedback
- **Context-aware**: Understands the current architecture
- **Professional advice**: Solutions architect-level recommendations

## Troubleshooting

### Common Issues

1. **"API Key not found" error**
   - Ensure `.env.local` file exists in project root
   - Verify the API key is correctly formatted
   - Restart your development server

2. **"Model not available" error**
   - Your API key may not have GPT-4o access
   - Check your OpenAI plan and model permissions
   - Try using GPT-4 as a fallback

3. **"Rate limit exceeded" error**
   - You've hit OpenAI's rate limits
   - Wait a few minutes before trying again
   - Consider upgrading your OpenAI plan

4. **Diagram rendering fails**
   - Check browser console for Mermaid errors
   - Verify the generated diagram code is valid
   - Try refreshing the page

5. **Chat not responding**
   - Check if OpenAI API is accessible
   - Verify API key has chat completion permissions
   - Check browser network tab for API errors

### Debug Mode

To enable detailed logging, add to your `.env.local`:
```env
NODE_ENV=development
DEBUG=true
```

## Architecture Diagram Features

The integration generates:

1. **Mermaid Diagrams**: Interactive, web-friendly diagrams
2. **PlantUML Code**: Industry-standard diagram format
3. **Legend**: Explanations of symbols and assumptions
4. **Downloadable SVG**: High-quality vector graphics

## Chat Functionality

The AI chat assistant can help with:

- **Component modifications**: Add, remove, or change system components
- **Security improvements**: Add authentication, encryption, compliance layers
- **Performance optimization**: Suggest caching, load balancing, scaling strategies
- **Integration patterns**: Recommend API designs, message queues, data flows
- **Technology choices**: Advise on databases, frameworks, cloud services

## Example Interactions

**User**: "Can you add a Redis cache layer between the API and database?"

**AI**: "Absolutely! I'll add a Redis cache layer to improve performance. Here's the updated architecture..."

**User**: "What about security? How do we handle authentication?"

**AI**: "Great question! I recommend adding an OAuth 2.0 authentication service. Let me update the diagram to include..."

## Security Notes

1. **API Key Security**:
   - Never expose your API key in client-side code
   - Use environment variables only
   - Rotate keys regularly

2. **Data Privacy**:
   - Technical requirements are sent to OpenAI's servers
   - Review OpenAI's privacy policy
   - Consider data sensitivity before processing

3. **Rate Limiting**:
   - Implement client-side rate limiting for production
   - Monitor API usage in OpenAI dashboard
   - Set up billing alerts

## Production Deployment

For production deployment:

1. **Environment Variables**:
   - Set `OPENAI_API_KEY` in your hosting platform
   - Use secure secret management

2. **Error Handling**:
   - Implement proper error logging
   - Add retry mechanisms for failed requests
   - Set up monitoring and alerts

3. **Performance**:
   - Consider caching for repeated requests
   - Implement request queuing for high volume
   - Monitor response times

## Support

If you encounter issues:

1. Check the [OpenAI Documentation](https://platform.openai.com/docs)
2. Review the API status page
3. Contact OpenAI support for API-related issues
4. Check this project's GitHub issues for common problems

## Model Capabilities

GPT-4o excels at:

- **Architecture Analysis**: Understanding complex technical requirements
- **Visual Design**: Creating clear, professional diagrams
- **Best Practices**: Recommending industry-standard patterns
- **Technology Selection**: Suggesting appropriate tools and frameworks
- **Scalability Planning**: Designing for growth and performance
- **Security Integration**: Including compliance and protection measures

The integration provides enterprise-grade architecture documentation and interactive improvement capabilities powered by the latest AI technology.
