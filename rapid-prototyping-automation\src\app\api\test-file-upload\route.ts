import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Read file content
    let transcriptContent = '';
    
    console.log(`Test: Processing file: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);
    
    try {
      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        console.log('Test: Reading as plain text file');
        transcriptContent = await file.text();
      } else if (file.name.endsWith('.docx')) {
        console.log('Test: Reading as DOCX file');
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.extractRawText({ buffer: Buffer.from(arrayBuffer) });
        transcriptContent = result.value;
      } else {
        console.log('Test: Reading as generic text file');
        transcriptContent = await file.text();
      }
    } catch (fileError) {
      console.error('Test: Error reading file content:', fileError);
      return NextResponse.json({ error: 'Failed to read file content' }, { status: 400 });
    }

    console.log(`Test: Extracted content length: ${transcriptContent.length} characters`);
    console.log(`Test: Content preview: ${transcriptContent.substring(0, 200)}...`);

    return NextResponse.json({
      success: true,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      contentLength: transcriptContent.length,
      contentPreview: transcriptContent.substring(0, 500),
      message: 'File processed successfully'
    });

  } catch (error) {
    console.error('Test: Error processing file:', error);
    return NextResponse.json(
      { error: 'Failed to process file' }, 
      { status: 500 }
    );
  }
}
