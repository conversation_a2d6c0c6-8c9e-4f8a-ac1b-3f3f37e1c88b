import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
  console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

const TECHNICAL_REQUIREMENTS_PROMPT = `Role:
You are a seasoned Solution Architect and technical writer.

Action:
1. Load and parse the content attached.   
2. Extract its sections (Executive Summary, Background & Context, Key Challenges, Core Needs, HMW Problem Statement, Constraints & Success Criteria, Next Steps).  
3. Translate those insights into a Technical Solution Document that describes what needs to be built—and how—to address the challenges and deliver a prototype.

Context:
The source document captures a Design Thinking workshop defining a problem statement. Your solution must align to that business context and success criteria.

Expectation:
Produce a polished technical analysis with these sections:

1. Title and Overview
   - Infer the title from the solution being proposed and write a catchy title that encompasses the solution.  
   - Executive Summary: Briefly restate the problem and summarize the proposed technical approach.

2. Solution Architecture Overview  
   - High-level system components and interactions
   - Cloud-native architecture approach
   - Integration strategy

3. Component Design  
   - For each major component:  
     - Purpose and functionality
     - Technology choices and rationale
     - Key interfaces and protocols  
     - Data schemas and formats
     - Scalability considerations

4. Data Flow & Integration  
   - Step-by-step flow from source systems → ingestion → processing → storage → visualization
   - Integration points with data sources based on the industry and client context
   - Real-time processing requirements

5. Security & Compliance  
   - Authentication and authorization model 
   - Data encryption at rest and in transit  
   - Audit logging and compliance requirements
   - Privacy and data protection measures

6. Non-Functional Requirements  
   - Scalability targets and auto-scaling strategy
   - Availability and resilience (SLA targets)
   - Performance benchmarks and optimization
   - Monitoring, logging, and supportability

7. Prototype Scope & MVP Features  
   - Minimum viable product capabilities to validate the concept
   - Core features for initial release
   - Success metrics and validation criteria
   - Timeline and development milestones

8. Implementation Roadmap
   - Phase 1: Foundation and core infrastructure
   - Phase 2: Core functionality and integrations
   - Phase 3: Advanced features and optimization
   - Risk mitigation strategies

9. Next Steps & Recommendations  
   - 3–5 action items to move from design → prototype build
   - Technology spike tasks or POC validations
   - Resource requirements and team structure

Please provide a comprehensive technical solution that addresses all the challenges identified in the problem statement while being practical and implementable.`;

export async function POST(request: NextRequest) {
  try {
    const { problemStatementContent } = await request.json();
    
    if (!problemStatementContent) {
      return NextResponse.json({ error: 'No problem statement content provided' }, { status: 400 });
    }

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo document');
      
      // For development, create a mock document
      const mockDoc = await createMockTechnicalDocument();
      const buffer = await Packer.toBuffer(mockDoc);
      
      // Return JSON with both document URL and content for next steps
      const base64Document = buffer.toString('base64');
      const documentUrl = `data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,${base64Document}`;

      return NextResponse.json({
        success: true,
        documentUrl: documentUrl,
        content: 'Demo technical requirements content for development mode.',
        message: 'Demo technical requirements document generated. Configure Claude API key for real document generation.'
      });
    }

    console.log('Generating technical requirements from problem statement...');
    console.log(`Problem statement content length: ${problemStatementContent.length} characters`);

    // Call Claude API
    console.log('Calling Claude API with Sonnet 4 for technical requirements...');
    const fullPrompt = `${TECHNICAL_REQUIREMENTS_PROMPT}\n\nProblem Statement Document Content:\n${problemStatementContent}`;
    console.log(`Full prompt length: ${fullPrompt.length} characters`);
    
    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 10000,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude API call successful for technical requirements');
    } catch (apiError: any) {
      console.error('Claude API Error:', apiError);
      
      // Handle specific API errors
      if (apiError.status === 401) {
        return NextResponse.json({ 
          error: 'Invalid API key. Please check your Anthropic API key configuration.' 
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({ 
          error: 'Rate limit exceeded. Please try again in a few minutes.' 
        }, { status: 429 });
      } else if (apiError.message?.includes('model')) {
        return NextResponse.json({ 
          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.' 
        }, { status: 400 });
      } else {
        return NextResponse.json({ 
          error: `Claude API error: ${apiError.message || 'Unknown error'}` 
        }, { status: 500 });
      }
    }

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Parse the analysis and create Word document
    const doc = await createTechnicalRequirementsDocument(analysisText);
    
    // Generate the document buffer
    const buffer = await Packer.toBuffer(doc);
    
    // Return JSON with both document URL and content for next steps
    const base64Document = buffer.toString('base64');
    const documentUrl = `data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,${base64Document}`;

    return NextResponse.json({
      success: true,
      documentUrl: documentUrl,
      content: analysisText, // Include the actual content for next steps
      message: 'Technical requirements document generated successfully with Claude Sonnet 4!'
    });

  } catch (error) {
    console.error('Error generating technical requirements:', error);
    return NextResponse.json(
      { error: 'Failed to generate technical requirements document' }, 
      { status: 500 }
    );
  }
}

async function createTechnicalRequirementsDocument(analysisText: string): Promise<Document> {
  // Parse the analysis text to extract structured information
  const sections = parseTechnicalAnalysisText(analysisText);
  
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        // Title Page
        new Paragraph({
          children: [
            new TextRun({
              text: sections.title || "Technical Solution Document",
              bold: true,
              size: 32,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.TITLE,
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: `Date: ${new Date().toLocaleDateString()}`,
              size: 24
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: "Prepared by: Solution Architect",
              size: 20,
              italics: true
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 800 }
        }),

        // Executive Summary
        new Paragraph({
          children: [
            new TextRun({
              text: "Executive Summary",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.executiveSummary || "Technical solution summary will be generated based on problem statement analysis.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Solution Architecture Overview
        new Paragraph({
          children: [
            new TextRun({
              text: "Solution Architecture Overview",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.architecture || "Cloud-native architecture overview and system components will be detailed here.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Component Design
        new Paragraph({
          children: [
            new TextRun({
              text: "Component Design",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.components || "Detailed component design including technology choices and interfaces.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Data Flow & Integration
        new Paragraph({
          children: [
            new TextRun({
              text: "Data Flow & Integration",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.dataFlow || "Data flow and integration patterns for the solution.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Security & Compliance
        new Paragraph({
          children: [
            new TextRun({
              text: "Security & Compliance",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.security || "Security architecture and compliance requirements.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Non-Functional Requirements
        new Paragraph({
          children: [
            new TextRun({
              text: "Non-Functional Requirements",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.nonFunctional || "Performance, scalability, and reliability requirements.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Prototype Scope & MVP Features
        new Paragraph({
          children: [
            new TextRun({
              text: "Prototype Scope & MVP Features",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.mvp || "Minimum viable product scope and key features for validation.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Next Steps & Recommendations
        new Paragraph({
          children: [
            new TextRun({
              text: "Next Steps & Recommendations",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: sections.nextSteps || "Implementation roadmap and recommended next steps.",
              size: 22
            })
          ],
          spacing: { after: 400 }
        }),

        // Full Analysis
        new Paragraph({
          children: [
            new TextRun({
              text: "Detailed Technical Analysis",
              bold: true,
              size: 28,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 400, after: 200 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: analysisText,
              size: 20
            })
          ],
          spacing: { after: 400 }
        })
      ]
    }]
  });

  return doc;
}

function parseTechnicalAnalysisText(text: string) {
  const sections: any = {};
  
  // Try to extract key sections from the Claude response
  const titleMatch = text.match(/title[:\s]*([^\n]+)/i);
  sections.title = titleMatch ? titleMatch[1].trim() : null;
  
  const summaryMatch = text.match(/executive summary[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.executiveSummary = summaryMatch ? summaryMatch[1].trim() : null;
  
  const architectureMatch = text.match(/architecture[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.architecture = architectureMatch ? architectureMatch[1].trim() : null;
  
  const componentsMatch = text.match(/component[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.components = componentsMatch ? componentsMatch[1].trim() : null;
  
  const dataFlowMatch = text.match(/data flow[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.dataFlow = dataFlowMatch ? dataFlowMatch[1].trim() : null;
  
  const securityMatch = text.match(/security[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.security = securityMatch ? securityMatch[1].trim() : null;
  
  const nonFunctionalMatch = text.match(/non-functional[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.nonFunctional = nonFunctionalMatch ? nonFunctionalMatch[1].trim() : null;
  
  const mvpMatch = text.match(/mvp|prototype[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.mvp = mvpMatch ? mvpMatch[1].trim() : null;
  
  const stepsMatch = text.match(/next steps[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
  sections.nextSteps = stepsMatch ? stepsMatch[1].trim() : null;
  
  return sections;
}

async function createMockTechnicalDocument(): Promise<Document> {
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        new Paragraph({
          children: [
            new TextRun({
              text: "Technical Solution Document - Demo",
              bold: true,
              size: 32,
              color: "2563EB"
            })
          ],
          heading: HeadingLevel.TITLE,
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 }
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: "⚠️ Demo Mode - Claude API Not Configured",
              bold: true,
              size: 24,
              color: "DC2626"
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 }
        }),

        new Paragraph({
          children: [
            new TextRun({
              text: "This is a demonstration document. Configure your Anthropic API key to generate real technical requirements.",
              size: 22,
              color: "DC2626"
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 600 }
        })
      ]
    }]
  });

  return doc;
}
