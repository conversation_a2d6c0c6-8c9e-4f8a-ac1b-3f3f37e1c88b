{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/test/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function TestPage() {\n  const [result, setResult] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    setLoading(true);\n    setResult(null);\n\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await fetch('/api/test-file-upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      const data = await response.json();\n      setResult(data);\n    } catch (error) {\n      setResult({ error: 'Failed to upload file' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-8\">\n      <div className=\"max-w-2xl mx-auto\">\n        <h1 className=\"text-3xl font-bold mb-8\">File Upload Test</h1>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow\">\n          <label className=\"block mb-4\">\n            <span className=\"text-gray-700\">Upload a transcript file:</span>\n            <input\n              type=\"file\"\n              onChange={handleFileUpload}\n              accept=\".txt,.docx,.pdf,.md\"\n              className=\"mt-1 block w-full text-sm text-gray-500\n                file:mr-4 file:py-2 file:px-4\n                file:rounded-full file:border-0\n                file:text-sm file:font-semibold\n                file:bg-blue-50 file:text-blue-700\n                hover:file:bg-blue-100\"\n            />\n          </label>\n\n          {loading && (\n            <div className=\"text-blue-600\">Processing file...</div>\n          )}\n\n          {result && (\n            <div className=\"mt-6\">\n              <h3 className=\"text-lg font-semibold mb-3\">Result:</h3>\n              <pre className=\"bg-gray-100 p-4 rounded text-sm overflow-auto\">\n                {JSON.stringify(result, null, 2)}\n              </pre>\n            </div>\n          )}\n        </div>\n\n        <div className=\"mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <h3 className=\"font-semibold text-yellow-800 mb-2\">Test Instructions:</h3>\n          <ol className=\"list-decimal list-inside text-yellow-700 space-y-1\">\n            <li>Upload the sample-transcript.txt file from the project root</li>\n            <li>Check if the file content is extracted correctly</li>\n            <li>Verify the content preview shows the meeting transcript</li>\n            <li>If successful, the main Claude integration should work</li>\n          </ol>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,UAAU;gBAAE,OAAO;YAAwB;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,QAAO;oCACP,WAAU;;;;;;;;;;;;wBASb,yBACC,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;wBAGhC,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACZ,KAAK,SAAS,CAAC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;8BAMtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}