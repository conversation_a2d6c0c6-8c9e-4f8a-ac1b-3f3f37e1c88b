{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dingbat-to-unicode/dist/dingbats.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar dingbats = [\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"32\", \"Dingbat hex\": \"20\", \"Unicode dec\": \"32\", \"Unicode hex\": \"20\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"33\", \"Dingbat hex\": \"21\", \"Unicode dec\": \"33\", \"Unicode hex\": \"21\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"34\", \"Dingbat hex\": \"22\", \"Unicode dec\": \"8704\", \"Unicode hex\": \"2200\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"35\", \"Dingbat hex\": \"23\", \"Unicode dec\": \"35\", \"Unicode hex\": \"23\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"36\", \"Dingbat hex\": \"24\", \"Unicode dec\": \"8707\", \"Unicode hex\": \"2203\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"37\", \"Dingbat hex\": \"25\", \"Unicode dec\": \"37\", \"Unicode hex\": \"25\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"38\", \"Dingbat hex\": \"26\", \"Unicode dec\": \"38\", \"Unicode hex\": \"26\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"39\", \"Dingbat hex\": \"27\", \"Unicode dec\": \"8717\", \"Unicode hex\": \"220D\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"40\", \"Dingbat hex\": \"28\", \"Unicode dec\": \"40\", \"Unicode hex\": \"28\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"41\", \"Dingbat hex\": \"29\", \"Unicode dec\": \"41\", \"Unicode hex\": \"29\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"42\", \"Dingbat hex\": \"2A\", \"Unicode dec\": \"42\", \"Unicode hex\": \"2A\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"43\", \"Dingbat hex\": \"2B\", \"Unicode dec\": \"43\", \"Unicode hex\": \"2B\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"44\", \"Dingbat hex\": \"2C\", \"Unicode dec\": \"44\", \"Unicode hex\": \"2C\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"45\", \"Dingbat hex\": \"2D\", \"Unicode dec\": \"8722\", \"Unicode hex\": \"2212\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"46\", \"Dingbat hex\": \"2E\", \"Unicode dec\": \"46\", \"Unicode hex\": \"2E\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"47\", \"Dingbat hex\": \"2F\", \"Unicode dec\": \"47\", \"Unicode hex\": \"2F\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"48\", \"Dingbat hex\": \"30\", \"Unicode dec\": \"48\", \"Unicode hex\": \"30\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"49\", \"Dingbat hex\": \"31\", \"Unicode dec\": \"49\", \"Unicode hex\": \"31\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"50\", \"Dingbat hex\": \"32\", \"Unicode dec\": \"50\", \"Unicode hex\": \"32\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"51\", \"Dingbat hex\": \"33\", \"Unicode dec\": \"51\", \"Unicode hex\": \"33\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"52\", \"Dingbat hex\": \"34\", \"Unicode dec\": \"52\", \"Unicode hex\": \"34\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"53\", \"Dingbat hex\": \"35\", \"Unicode dec\": \"53\", \"Unicode hex\": \"35\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"54\", \"Dingbat hex\": \"36\", \"Unicode dec\": \"54\", \"Unicode hex\": \"36\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"55\", \"Dingbat hex\": \"37\", \"Unicode dec\": \"55\", \"Unicode hex\": \"37\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"56\", \"Dingbat hex\": \"38\", \"Unicode dec\": \"56\", \"Unicode hex\": \"38\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"57\", \"Dingbat hex\": \"39\", \"Unicode dec\": \"57\", \"Unicode hex\": \"39\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"58\", \"Dingbat hex\": \"3A\", \"Unicode dec\": \"58\", \"Unicode hex\": \"3A\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"59\", \"Dingbat hex\": \"3B\", \"Unicode dec\": \"59\", \"Unicode hex\": \"3B\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"60\", \"Dingbat hex\": \"3C\", \"Unicode dec\": \"60\", \"Unicode hex\": \"3C\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"61\", \"Dingbat hex\": \"3D\", \"Unicode dec\": \"61\", \"Unicode hex\": \"3D\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"62\", \"Dingbat hex\": \"3E\", \"Unicode dec\": \"62\", \"Unicode hex\": \"3E\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"63\", \"Dingbat hex\": \"3F\", \"Unicode dec\": \"63\", \"Unicode hex\": \"3F\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"64\", \"Dingbat hex\": \"40\", \"Unicode dec\": \"8773\", \"Unicode hex\": \"2245\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"65\", \"Dingbat hex\": \"41\", \"Unicode dec\": \"913\", \"Unicode hex\": \"391\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"66\", \"Dingbat hex\": \"42\", \"Unicode dec\": \"914\", \"Unicode hex\": \"392\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"67\", \"Dingbat hex\": \"43\", \"Unicode dec\": \"935\", \"Unicode hex\": \"3A7\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"68\", \"Dingbat hex\": \"44\", \"Unicode dec\": \"916\", \"Unicode hex\": \"394\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"69\", \"Dingbat hex\": \"45\", \"Unicode dec\": \"917\", \"Unicode hex\": \"395\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"70\", \"Dingbat hex\": \"46\", \"Unicode dec\": \"934\", \"Unicode hex\": \"3A6\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"71\", \"Dingbat hex\": \"47\", \"Unicode dec\": \"915\", \"Unicode hex\": \"393\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"72\", \"Dingbat hex\": \"48\", \"Unicode dec\": \"919\", \"Unicode hex\": \"397\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"73\", \"Dingbat hex\": \"49\", \"Unicode dec\": \"921\", \"Unicode hex\": \"399\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"74\", \"Dingbat hex\": \"4A\", \"Unicode dec\": \"977\", \"Unicode hex\": \"3D1\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"75\", \"Dingbat hex\": \"4B\", \"Unicode dec\": \"922\", \"Unicode hex\": \"39A\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"76\", \"Dingbat hex\": \"4C\", \"Unicode dec\": \"923\", \"Unicode hex\": \"39B\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"77\", \"Dingbat hex\": \"4D\", \"Unicode dec\": \"924\", \"Unicode hex\": \"39C\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"78\", \"Dingbat hex\": \"4E\", \"Unicode dec\": \"925\", \"Unicode hex\": \"39D\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"79\", \"Dingbat hex\": \"4F\", \"Unicode dec\": \"927\", \"Unicode hex\": \"39F\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"80\", \"Dingbat hex\": \"50\", \"Unicode dec\": \"928\", \"Unicode hex\": \"3A0\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"81\", \"Dingbat hex\": \"51\", \"Unicode dec\": \"920\", \"Unicode hex\": \"398\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"82\", \"Dingbat hex\": \"52\", \"Unicode dec\": \"929\", \"Unicode hex\": \"3A1\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"83\", \"Dingbat hex\": \"53\", \"Unicode dec\": \"931\", \"Unicode hex\": \"3A3\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"84\", \"Dingbat hex\": \"54\", \"Unicode dec\": \"932\", \"Unicode hex\": \"3A4\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"85\", \"Dingbat hex\": \"55\", \"Unicode dec\": \"933\", \"Unicode hex\": \"3A5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"86\", \"Dingbat hex\": \"56\", \"Unicode dec\": \"962\", \"Unicode hex\": \"3C2\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"87\", \"Dingbat hex\": \"57\", \"Unicode dec\": \"937\", \"Unicode hex\": \"3A9\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"88\", \"Dingbat hex\": \"58\", \"Unicode dec\": \"926\", \"Unicode hex\": \"39E\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"89\", \"Dingbat hex\": \"59\", \"Unicode dec\": \"936\", \"Unicode hex\": \"3A8\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"90\", \"Dingbat hex\": \"5A\", \"Unicode dec\": \"918\", \"Unicode hex\": \"396\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"91\", \"Dingbat hex\": \"5B\", \"Unicode dec\": \"91\", \"Unicode hex\": \"5B\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"92\", \"Dingbat hex\": \"5C\", \"Unicode dec\": \"8756\", \"Unicode hex\": \"2234\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"93\", \"Dingbat hex\": \"5D\", \"Unicode dec\": \"93\", \"Unicode hex\": \"5D\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"94\", \"Dingbat hex\": \"5E\", \"Unicode dec\": \"8869\", \"Unicode hex\": \"22A5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"95\", \"Dingbat hex\": \"5F\", \"Unicode dec\": \"95\", \"Unicode hex\": \"5F\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"96\", \"Dingbat hex\": \"60\", \"Unicode dec\": \"8254\", \"Unicode hex\": \"203E\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"97\", \"Dingbat hex\": \"61\", \"Unicode dec\": \"945\", \"Unicode hex\": \"3B1\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"98\", \"Dingbat hex\": \"62\", \"Unicode dec\": \"946\", \"Unicode hex\": \"3B2\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"99\", \"Dingbat hex\": \"63\", \"Unicode dec\": \"967\", \"Unicode hex\": \"3C7\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"100\", \"Dingbat hex\": \"64\", \"Unicode dec\": \"948\", \"Unicode hex\": \"3B4\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"101\", \"Dingbat hex\": \"65\", \"Unicode dec\": \"949\", \"Unicode hex\": \"3B5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"102\", \"Dingbat hex\": \"66\", \"Unicode dec\": \"966\", \"Unicode hex\": \"3C6\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"103\", \"Dingbat hex\": \"67\", \"Unicode dec\": \"947\", \"Unicode hex\": \"3B3\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"104\", \"Dingbat hex\": \"68\", \"Unicode dec\": \"951\", \"Unicode hex\": \"3B7\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"105\", \"Dingbat hex\": \"69\", \"Unicode dec\": \"953\", \"Unicode hex\": \"3B9\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"106\", \"Dingbat hex\": \"6A\", \"Unicode dec\": \"981\", \"Unicode hex\": \"3D5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"107\", \"Dingbat hex\": \"6B\", \"Unicode dec\": \"954\", \"Unicode hex\": \"3BA\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"108\", \"Dingbat hex\": \"6C\", \"Unicode dec\": \"955\", \"Unicode hex\": \"3BB\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"109\", \"Dingbat hex\": \"6D\", \"Unicode dec\": \"956\", \"Unicode hex\": \"3BC\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"110\", \"Dingbat hex\": \"6E\", \"Unicode dec\": \"957\", \"Unicode hex\": \"3BD\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"111\", \"Dingbat hex\": \"6F\", \"Unicode dec\": \"959\", \"Unicode hex\": \"3BF\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"112\", \"Dingbat hex\": \"70\", \"Unicode dec\": \"960\", \"Unicode hex\": \"3C0\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"113\", \"Dingbat hex\": \"71\", \"Unicode dec\": \"952\", \"Unicode hex\": \"3B8\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"114\", \"Dingbat hex\": \"72\", \"Unicode dec\": \"961\", \"Unicode hex\": \"3C1\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"115\", \"Dingbat hex\": \"73\", \"Unicode dec\": \"963\", \"Unicode hex\": \"3C3\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"116\", \"Dingbat hex\": \"74\", \"Unicode dec\": \"964\", \"Unicode hex\": \"3C4\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"117\", \"Dingbat hex\": \"75\", \"Unicode dec\": \"965\", \"Unicode hex\": \"3C5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"118\", \"Dingbat hex\": \"76\", \"Unicode dec\": \"982\", \"Unicode hex\": \"3D6\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"119\", \"Dingbat hex\": \"77\", \"Unicode dec\": \"969\", \"Unicode hex\": \"3C9\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"120\", \"Dingbat hex\": \"78\", \"Unicode dec\": \"958\", \"Unicode hex\": \"3BE\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"121\", \"Dingbat hex\": \"79\", \"Unicode dec\": \"968\", \"Unicode hex\": \"3C8\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"122\", \"Dingbat hex\": \"7A\", \"Unicode dec\": \"950\", \"Unicode hex\": \"3B6\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"123\", \"Dingbat hex\": \"7B\", \"Unicode dec\": \"123\", \"Unicode hex\": \"7B\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"124\", \"Dingbat hex\": \"7C\", \"Unicode dec\": \"124\", \"Unicode hex\": \"7C\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"125\", \"Dingbat hex\": \"7D\", \"Unicode dec\": \"125\", \"Unicode hex\": \"7D\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"126\", \"Dingbat hex\": \"7E\", \"Unicode dec\": \"126\", \"Unicode hex\": \"7E\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"160\", \"Dingbat hex\": \"A0\", \"Unicode dec\": \"8364\", \"Unicode hex\": \"20AC\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"161\", \"Dingbat hex\": \"A1\", \"Unicode dec\": \"978\", \"Unicode hex\": \"3D2\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"162\", \"Dingbat hex\": \"A2\", \"Unicode dec\": \"8242\", \"Unicode hex\": \"2032\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"163\", \"Dingbat hex\": \"A3\", \"Unicode dec\": \"8804\", \"Unicode hex\": \"2264\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"164\", \"Dingbat hex\": \"A4\", \"Unicode dec\": \"8260\", \"Unicode hex\": \"2044\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"165\", \"Dingbat hex\": \"A5\", \"Unicode dec\": \"8734\", \"Unicode hex\": \"221E\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"166\", \"Dingbat hex\": \"A6\", \"Unicode dec\": \"402\", \"Unicode hex\": \"192\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"167\", \"Dingbat hex\": \"A7\", \"Unicode dec\": \"9827\", \"Unicode hex\": \"2663\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"168\", \"Dingbat hex\": \"A8\", \"Unicode dec\": \"9830\", \"Unicode hex\": \"2666\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"169\", \"Dingbat hex\": \"A9\", \"Unicode dec\": \"9829\", \"Unicode hex\": \"2665\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"170\", \"Dingbat hex\": \"AA\", \"Unicode dec\": \"9824\", \"Unicode hex\": \"2660\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"171\", \"Dingbat hex\": \"AB\", \"Unicode dec\": \"8596\", \"Unicode hex\": \"2194\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"172\", \"Dingbat hex\": \"AC\", \"Unicode dec\": \"8592\", \"Unicode hex\": \"2190\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"173\", \"Dingbat hex\": \"AD\", \"Unicode dec\": \"8593\", \"Unicode hex\": \"2191\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"174\", \"Dingbat hex\": \"AE\", \"Unicode dec\": \"8594\", \"Unicode hex\": \"2192\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"175\", \"Dingbat hex\": \"AF\", \"Unicode dec\": \"8595\", \"Unicode hex\": \"2193\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"176\", \"Dingbat hex\": \"B0\", \"Unicode dec\": \"176\", \"Unicode hex\": \"B0\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"177\", \"Dingbat hex\": \"B1\", \"Unicode dec\": \"177\", \"Unicode hex\": \"B1\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"178\", \"Dingbat hex\": \"B2\", \"Unicode dec\": \"8243\", \"Unicode hex\": \"2033\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"179\", \"Dingbat hex\": \"B3\", \"Unicode dec\": \"8805\", \"Unicode hex\": \"2265\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"180\", \"Dingbat hex\": \"B4\", \"Unicode dec\": \"215\", \"Unicode hex\": \"D7\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"181\", \"Dingbat hex\": \"B5\", \"Unicode dec\": \"8733\", \"Unicode hex\": \"221D\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"182\", \"Dingbat hex\": \"B6\", \"Unicode dec\": \"8706\", \"Unicode hex\": \"2202\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"183\", \"Dingbat hex\": \"B7\", \"Unicode dec\": \"8226\", \"Unicode hex\": \"2022\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"184\", \"Dingbat hex\": \"B8\", \"Unicode dec\": \"247\", \"Unicode hex\": \"F7\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"185\", \"Dingbat hex\": \"B9\", \"Unicode dec\": \"8800\", \"Unicode hex\": \"2260\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"186\", \"Dingbat hex\": \"BA\", \"Unicode dec\": \"8801\", \"Unicode hex\": \"2261\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"187\", \"Dingbat hex\": \"BB\", \"Unicode dec\": \"8776\", \"Unicode hex\": \"2248\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"188\", \"Dingbat hex\": \"BC\", \"Unicode dec\": \"8230\", \"Unicode hex\": \"2026\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"189\", \"Dingbat hex\": \"BD\", \"Unicode dec\": \"9168\", \"Unicode hex\": \"23D0\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"190\", \"Dingbat hex\": \"BE\", \"Unicode dec\": \"9135\", \"Unicode hex\": \"23AF\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"191\", \"Dingbat hex\": \"BF\", \"Unicode dec\": \"8629\", \"Unicode hex\": \"21B5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"192\", \"Dingbat hex\": \"C0\", \"Unicode dec\": \"8501\", \"Unicode hex\": \"2135\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"193\", \"Dingbat hex\": \"C1\", \"Unicode dec\": \"8465\", \"Unicode hex\": \"2111\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"194\", \"Dingbat hex\": \"C2\", \"Unicode dec\": \"8476\", \"Unicode hex\": \"211C\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"195\", \"Dingbat hex\": \"C3\", \"Unicode dec\": \"8472\", \"Unicode hex\": \"2118\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"196\", \"Dingbat hex\": \"C4\", \"Unicode dec\": \"8855\", \"Unicode hex\": \"2297\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"197\", \"Dingbat hex\": \"C5\", \"Unicode dec\": \"8853\", \"Unicode hex\": \"2295\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"198\", \"Dingbat hex\": \"C6\", \"Unicode dec\": \"8709\", \"Unicode hex\": \"2205\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"199\", \"Dingbat hex\": \"C7\", \"Unicode dec\": \"8745\", \"Unicode hex\": \"2229\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"200\", \"Dingbat hex\": \"C8\", \"Unicode dec\": \"8746\", \"Unicode hex\": \"222A\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"201\", \"Dingbat hex\": \"C9\", \"Unicode dec\": \"8835\", \"Unicode hex\": \"2283\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"202\", \"Dingbat hex\": \"CA\", \"Unicode dec\": \"8839\", \"Unicode hex\": \"2287\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"203\", \"Dingbat hex\": \"CB\", \"Unicode dec\": \"8836\", \"Unicode hex\": \"2284\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"204\", \"Dingbat hex\": \"CC\", \"Unicode dec\": \"8834\", \"Unicode hex\": \"2282\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"205\", \"Dingbat hex\": \"CD\", \"Unicode dec\": \"8838\", \"Unicode hex\": \"2286\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"206\", \"Dingbat hex\": \"CE\", \"Unicode dec\": \"8712\", \"Unicode hex\": \"2208\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"207\", \"Dingbat hex\": \"CF\", \"Unicode dec\": \"8713\", \"Unicode hex\": \"2209\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"208\", \"Dingbat hex\": \"D0\", \"Unicode dec\": \"8736\", \"Unicode hex\": \"2220\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"209\", \"Dingbat hex\": \"D1\", \"Unicode dec\": \"8711\", \"Unicode hex\": \"2207\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"210\", \"Dingbat hex\": \"D2\", \"Unicode dec\": \"174\", \"Unicode hex\": \"AE\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"211\", \"Dingbat hex\": \"D3\", \"Unicode dec\": \"169\", \"Unicode hex\": \"A9\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"212\", \"Dingbat hex\": \"D4\", \"Unicode dec\": \"8482\", \"Unicode hex\": \"2122\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"213\", \"Dingbat hex\": \"D5\", \"Unicode dec\": \"8719\", \"Unicode hex\": \"220F\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"214\", \"Dingbat hex\": \"D6\", \"Unicode dec\": \"8730\", \"Unicode hex\": \"221A\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"215\", \"Dingbat hex\": \"D7\", \"Unicode dec\": \"8901\", \"Unicode hex\": \"22C5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"216\", \"Dingbat hex\": \"D8\", \"Unicode dec\": \"172\", \"Unicode hex\": \"AC\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"217\", \"Dingbat hex\": \"D9\", \"Unicode dec\": \"8743\", \"Unicode hex\": \"2227\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"218\", \"Dingbat hex\": \"DA\", \"Unicode dec\": \"8744\", \"Unicode hex\": \"2228\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"219\", \"Dingbat hex\": \"DB\", \"Unicode dec\": \"8660\", \"Unicode hex\": \"21D4\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"220\", \"Dingbat hex\": \"DC\", \"Unicode dec\": \"8656\", \"Unicode hex\": \"21D0\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"221\", \"Dingbat hex\": \"DD\", \"Unicode dec\": \"8657\", \"Unicode hex\": \"21D1\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"222\", \"Dingbat hex\": \"DE\", \"Unicode dec\": \"8658\", \"Unicode hex\": \"21D2\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"223\", \"Dingbat hex\": \"DF\", \"Unicode dec\": \"8659\", \"Unicode hex\": \"21D3\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"224\", \"Dingbat hex\": \"E0\", \"Unicode dec\": \"9674\", \"Unicode hex\": \"25CA\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"225\", \"Dingbat hex\": \"E1\", \"Unicode dec\": \"12296\", \"Unicode hex\": \"3008\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"226\", \"Dingbat hex\": \"E2\", \"Unicode dec\": \"174\", \"Unicode hex\": \"AE\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"227\", \"Dingbat hex\": \"E3\", \"Unicode dec\": \"169\", \"Unicode hex\": \"A9\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"228\", \"Dingbat hex\": \"E4\", \"Unicode dec\": \"8482\", \"Unicode hex\": \"2122\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"229\", \"Dingbat hex\": \"E5\", \"Unicode dec\": \"8721\", \"Unicode hex\": \"2211\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"230\", \"Dingbat hex\": \"E6\", \"Unicode dec\": \"9115\", \"Unicode hex\": \"239B\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"231\", \"Dingbat hex\": \"E7\", \"Unicode dec\": \"9116\", \"Unicode hex\": \"239C\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"232\", \"Dingbat hex\": \"E8\", \"Unicode dec\": \"9117\", \"Unicode hex\": \"239D\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"233\", \"Dingbat hex\": \"E9\", \"Unicode dec\": \"9121\", \"Unicode hex\": \"23A1\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"234\", \"Dingbat hex\": \"EA\", \"Unicode dec\": \"9122\", \"Unicode hex\": \"23A2\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"235\", \"Dingbat hex\": \"EB\", \"Unicode dec\": \"9123\", \"Unicode hex\": \"23A3\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"236\", \"Dingbat hex\": \"EC\", \"Unicode dec\": \"9127\", \"Unicode hex\": \"23A7\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"237\", \"Dingbat hex\": \"ED\", \"Unicode dec\": \"9128\", \"Unicode hex\": \"23A8\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"238\", \"Dingbat hex\": \"EE\", \"Unicode dec\": \"9129\", \"Unicode hex\": \"23A9\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"239\", \"Dingbat hex\": \"EF\", \"Unicode dec\": \"9130\", \"Unicode hex\": \"23AA\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"240\", \"Dingbat hex\": \"F0\", \"Unicode dec\": \"63743\", \"Unicode hex\": \"F8FF\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"241\", \"Dingbat hex\": \"F1\", \"Unicode dec\": \"12297\", \"Unicode hex\": \"3009\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"242\", \"Dingbat hex\": \"F2\", \"Unicode dec\": \"8747\", \"Unicode hex\": \"222B\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"243\", \"Dingbat hex\": \"F3\", \"Unicode dec\": \"8992\", \"Unicode hex\": \"2320\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"244\", \"Dingbat hex\": \"F4\", \"Unicode dec\": \"9134\", \"Unicode hex\": \"23AE\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"245\", \"Dingbat hex\": \"F5\", \"Unicode dec\": \"8993\", \"Unicode hex\": \"2321\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"246\", \"Dingbat hex\": \"F6\", \"Unicode dec\": \"9118\", \"Unicode hex\": \"239E\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"247\", \"Dingbat hex\": \"F7\", \"Unicode dec\": \"9119\", \"Unicode hex\": \"239F\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"248\", \"Dingbat hex\": \"F8\", \"Unicode dec\": \"9120\", \"Unicode hex\": \"23A0\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"249\", \"Dingbat hex\": \"F9\", \"Unicode dec\": \"9124\", \"Unicode hex\": \"23A4\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"250\", \"Dingbat hex\": \"FA\", \"Unicode dec\": \"9125\", \"Unicode hex\": \"23A5\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"251\", \"Dingbat hex\": \"FB\", \"Unicode dec\": \"9126\", \"Unicode hex\": \"23A6\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"252\", \"Dingbat hex\": \"FC\", \"Unicode dec\": \"9131\", \"Unicode hex\": \"23AB\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"253\", \"Dingbat hex\": \"FD\", \"Unicode dec\": \"9132\", \"Unicode hex\": \"23AC\" },\n    { \"Typeface name\": \"Symbol\", \"Dingbat dec\": \"254\", \"Dingbat hex\": \"FE\", \"Unicode dec\": \"9133\", \"Unicode hex\": \"23AD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"32\", \"Dingbat hex\": \"20\", \"Unicode dec\": \"32\", \"Unicode hex\": \"20\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"33\", \"Dingbat hex\": \"21\", \"Unicode dec\": \"128375\", \"Unicode hex\": \"1F577\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"34\", \"Dingbat hex\": \"22\", \"Unicode dec\": \"128376\", \"Unicode hex\": \"1F578\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"35\", \"Dingbat hex\": \"23\", \"Unicode dec\": \"128370\", \"Unicode hex\": \"1F572\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"36\", \"Dingbat hex\": \"24\", \"Unicode dec\": \"128374\", \"Unicode hex\": \"1F576\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"37\", \"Dingbat hex\": \"25\", \"Unicode dec\": \"127942\", \"Unicode hex\": \"1F3C6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"38\", \"Dingbat hex\": \"26\", \"Unicode dec\": \"127894\", \"Unicode hex\": \"1F396\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"39\", \"Dingbat hex\": \"27\", \"Unicode dec\": \"128391\", \"Unicode hex\": \"1F587\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"40\", \"Dingbat hex\": \"28\", \"Unicode dec\": \"128488\", \"Unicode hex\": \"1F5E8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"41\", \"Dingbat hex\": \"29\", \"Unicode dec\": \"128489\", \"Unicode hex\": \"1F5E9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"42\", \"Dingbat hex\": \"2A\", \"Unicode dec\": \"128496\", \"Unicode hex\": \"1F5F0\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"43\", \"Dingbat hex\": \"2B\", \"Unicode dec\": \"128497\", \"Unicode hex\": \"1F5F1\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"44\", \"Dingbat hex\": \"2C\", \"Unicode dec\": \"127798\", \"Unicode hex\": \"1F336\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"45\", \"Dingbat hex\": \"2D\", \"Unicode dec\": \"127895\", \"Unicode hex\": \"1F397\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"46\", \"Dingbat hex\": \"2E\", \"Unicode dec\": \"128638\", \"Unicode hex\": \"1F67E\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"47\", \"Dingbat hex\": \"2F\", \"Unicode dec\": \"128636\", \"Unicode hex\": \"1F67C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"48\", \"Dingbat hex\": \"30\", \"Unicode dec\": \"128469\", \"Unicode hex\": \"1F5D5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"49\", \"Dingbat hex\": \"31\", \"Unicode dec\": \"128470\", \"Unicode hex\": \"1F5D6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"50\", \"Dingbat hex\": \"32\", \"Unicode dec\": \"128471\", \"Unicode hex\": \"1F5D7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"51\", \"Dingbat hex\": \"33\", \"Unicode dec\": \"9204\", \"Unicode hex\": \"23F4\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"52\", \"Dingbat hex\": \"34\", \"Unicode dec\": \"9205\", \"Unicode hex\": \"23F5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"53\", \"Dingbat hex\": \"35\", \"Unicode dec\": \"9206\", \"Unicode hex\": \"23F6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"54\", \"Dingbat hex\": \"36\", \"Unicode dec\": \"9207\", \"Unicode hex\": \"23F7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"55\", \"Dingbat hex\": \"37\", \"Unicode dec\": \"9194\", \"Unicode hex\": \"23EA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"56\", \"Dingbat hex\": \"38\", \"Unicode dec\": \"9193\", \"Unicode hex\": \"23E9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"57\", \"Dingbat hex\": \"39\", \"Unicode dec\": \"9198\", \"Unicode hex\": \"23EE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"58\", \"Dingbat hex\": \"3A\", \"Unicode dec\": \"9197\", \"Unicode hex\": \"23ED\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"59\", \"Dingbat hex\": \"3B\", \"Unicode dec\": \"9208\", \"Unicode hex\": \"23F8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"60\", \"Dingbat hex\": \"3C\", \"Unicode dec\": \"9209\", \"Unicode hex\": \"23F9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"61\", \"Dingbat hex\": \"3D\", \"Unicode dec\": \"9210\", \"Unicode hex\": \"23FA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"62\", \"Dingbat hex\": \"3E\", \"Unicode dec\": \"128474\", \"Unicode hex\": \"1F5DA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"63\", \"Dingbat hex\": \"3F\", \"Unicode dec\": \"128499\", \"Unicode hex\": \"1F5F3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"64\", \"Dingbat hex\": \"40\", \"Unicode dec\": \"128736\", \"Unicode hex\": \"1F6E0\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"65\", \"Dingbat hex\": \"41\", \"Unicode dec\": \"127959\", \"Unicode hex\": \"1F3D7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"66\", \"Dingbat hex\": \"42\", \"Unicode dec\": \"127960\", \"Unicode hex\": \"1F3D8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"67\", \"Dingbat hex\": \"43\", \"Unicode dec\": \"127961\", \"Unicode hex\": \"1F3D9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"68\", \"Dingbat hex\": \"44\", \"Unicode dec\": \"127962\", \"Unicode hex\": \"1F3DA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"69\", \"Dingbat hex\": \"45\", \"Unicode dec\": \"127964\", \"Unicode hex\": \"1F3DC\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"70\", \"Dingbat hex\": \"46\", \"Unicode dec\": \"127981\", \"Unicode hex\": \"1F3ED\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"71\", \"Dingbat hex\": \"47\", \"Unicode dec\": \"127963\", \"Unicode hex\": \"1F3DB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"72\", \"Dingbat hex\": \"48\", \"Unicode dec\": \"127968\", \"Unicode hex\": \"1F3E0\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"73\", \"Dingbat hex\": \"49\", \"Unicode dec\": \"127958\", \"Unicode hex\": \"1F3D6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"74\", \"Dingbat hex\": \"4A\", \"Unicode dec\": \"127965\", \"Unicode hex\": \"1F3DD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"75\", \"Dingbat hex\": \"4B\", \"Unicode dec\": \"128739\", \"Unicode hex\": \"1F6E3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"76\", \"Dingbat hex\": \"4C\", \"Unicode dec\": \"128269\", \"Unicode hex\": \"1F50D\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"77\", \"Dingbat hex\": \"4D\", \"Unicode dec\": \"127956\", \"Unicode hex\": \"1F3D4\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"78\", \"Dingbat hex\": \"4E\", \"Unicode dec\": \"128065\", \"Unicode hex\": \"1F441\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"79\", \"Dingbat hex\": \"4F\", \"Unicode dec\": \"128066\", \"Unicode hex\": \"1F442\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"80\", \"Dingbat hex\": \"50\", \"Unicode dec\": \"127966\", \"Unicode hex\": \"1F3DE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"81\", \"Dingbat hex\": \"51\", \"Unicode dec\": \"127957\", \"Unicode hex\": \"1F3D5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"82\", \"Dingbat hex\": \"52\", \"Unicode dec\": \"128740\", \"Unicode hex\": \"1F6E4\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"83\", \"Dingbat hex\": \"53\", \"Unicode dec\": \"127967\", \"Unicode hex\": \"1F3DF\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"84\", \"Dingbat hex\": \"54\", \"Unicode dec\": \"128755\", \"Unicode hex\": \"1F6F3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"85\", \"Dingbat hex\": \"55\", \"Unicode dec\": \"128364\", \"Unicode hex\": \"1F56C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"86\", \"Dingbat hex\": \"56\", \"Unicode dec\": \"128363\", \"Unicode hex\": \"1F56B\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"87\", \"Dingbat hex\": \"57\", \"Unicode dec\": \"128360\", \"Unicode hex\": \"1F568\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"88\", \"Dingbat hex\": \"58\", \"Unicode dec\": \"128264\", \"Unicode hex\": \"1F508\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"89\", \"Dingbat hex\": \"59\", \"Unicode dec\": \"127892\", \"Unicode hex\": \"1F394\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"90\", \"Dingbat hex\": \"5A\", \"Unicode dec\": \"127893\", \"Unicode hex\": \"1F395\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"91\", \"Dingbat hex\": \"5B\", \"Unicode dec\": \"128492\", \"Unicode hex\": \"1F5EC\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"92\", \"Dingbat hex\": \"5C\", \"Unicode dec\": \"128637\", \"Unicode hex\": \"1F67D\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"93\", \"Dingbat hex\": \"5D\", \"Unicode dec\": \"128493\", \"Unicode hex\": \"1F5ED\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"94\", \"Dingbat hex\": \"5E\", \"Unicode dec\": \"128490\", \"Unicode hex\": \"1F5EA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"95\", \"Dingbat hex\": \"5F\", \"Unicode dec\": \"128491\", \"Unicode hex\": \"1F5EB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"96\", \"Dingbat hex\": \"60\", \"Unicode dec\": \"11156\", \"Unicode hex\": \"2B94\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"97\", \"Dingbat hex\": \"61\", \"Unicode dec\": \"10004\", \"Unicode hex\": \"2714\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"98\", \"Dingbat hex\": \"62\", \"Unicode dec\": \"128690\", \"Unicode hex\": \"1F6B2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"99\", \"Dingbat hex\": \"63\", \"Unicode dec\": \"11036\", \"Unicode hex\": \"2B1C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"100\", \"Dingbat hex\": \"64\", \"Unicode dec\": \"128737\", \"Unicode hex\": \"1F6E1\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"101\", \"Dingbat hex\": \"65\", \"Unicode dec\": \"128230\", \"Unicode hex\": \"1F4E6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"102\", \"Dingbat hex\": \"66\", \"Unicode dec\": \"128753\", \"Unicode hex\": \"1F6F1\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"103\", \"Dingbat hex\": \"67\", \"Unicode dec\": \"11035\", \"Unicode hex\": \"2B1B\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"104\", \"Dingbat hex\": \"68\", \"Unicode dec\": \"128657\", \"Unicode hex\": \"1F691\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"105\", \"Dingbat hex\": \"69\", \"Unicode dec\": \"128712\", \"Unicode hex\": \"1F6C8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"106\", \"Dingbat hex\": \"6A\", \"Unicode dec\": \"128745\", \"Unicode hex\": \"1F6E9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"107\", \"Dingbat hex\": \"6B\", \"Unicode dec\": \"128752\", \"Unicode hex\": \"1F6F0\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"108\", \"Dingbat hex\": \"6C\", \"Unicode dec\": \"128968\", \"Unicode hex\": \"1F7C8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"109\", \"Dingbat hex\": \"6D\", \"Unicode dec\": \"128372\", \"Unicode hex\": \"1F574\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"110\", \"Dingbat hex\": \"6E\", \"Unicode dec\": \"11044\", \"Unicode hex\": \"2B24\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"111\", \"Dingbat hex\": \"6F\", \"Unicode dec\": \"128741\", \"Unicode hex\": \"1F6E5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"112\", \"Dingbat hex\": \"70\", \"Unicode dec\": \"128660\", \"Unicode hex\": \"1F694\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"113\", \"Dingbat hex\": \"71\", \"Unicode dec\": \"128472\", \"Unicode hex\": \"1F5D8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"114\", \"Dingbat hex\": \"72\", \"Unicode dec\": \"128473\", \"Unicode hex\": \"1F5D9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"115\", \"Dingbat hex\": \"73\", \"Unicode dec\": \"10067\", \"Unicode hex\": \"2753\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"116\", \"Dingbat hex\": \"74\", \"Unicode dec\": \"128754\", \"Unicode hex\": \"1F6F2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"117\", \"Dingbat hex\": \"75\", \"Unicode dec\": \"128647\", \"Unicode hex\": \"1F687\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"118\", \"Dingbat hex\": \"76\", \"Unicode dec\": \"128653\", \"Unicode hex\": \"1F68D\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"119\", \"Dingbat hex\": \"77\", \"Unicode dec\": \"9971\", \"Unicode hex\": \"26F3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"120\", \"Dingbat hex\": \"78\", \"Unicode dec\": \"10680\", \"Unicode hex\": \"29B8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"121\", \"Dingbat hex\": \"79\", \"Unicode dec\": \"8854\", \"Unicode hex\": \"2296\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"122\", \"Dingbat hex\": \"7A\", \"Unicode dec\": \"128685\", \"Unicode hex\": \"1F6AD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"123\", \"Dingbat hex\": \"7B\", \"Unicode dec\": \"128494\", \"Unicode hex\": \"1F5EE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"124\", \"Dingbat hex\": \"7C\", \"Unicode dec\": \"9168\", \"Unicode hex\": \"23D0\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"125\", \"Dingbat hex\": \"7D\", \"Unicode dec\": \"128495\", \"Unicode hex\": \"1F5EF\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"126\", \"Dingbat hex\": \"7E\", \"Unicode dec\": \"128498\", \"Unicode hex\": \"1F5F2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"128\", \"Dingbat hex\": \"80\", \"Unicode dec\": \"128697\", \"Unicode hex\": \"1F6B9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"129\", \"Dingbat hex\": \"81\", \"Unicode dec\": \"128698\", \"Unicode hex\": \"1F6BA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"130\", \"Dingbat hex\": \"82\", \"Unicode dec\": \"128713\", \"Unicode hex\": \"1F6C9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"131\", \"Dingbat hex\": \"83\", \"Unicode dec\": \"128714\", \"Unicode hex\": \"1F6CA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"132\", \"Dingbat hex\": \"84\", \"Unicode dec\": \"128700\", \"Unicode hex\": \"1F6BC\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"133\", \"Dingbat hex\": \"85\", \"Unicode dec\": \"128125\", \"Unicode hex\": \"1F47D\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"134\", \"Dingbat hex\": \"86\", \"Unicode dec\": \"127947\", \"Unicode hex\": \"1F3CB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"135\", \"Dingbat hex\": \"87\", \"Unicode dec\": \"9975\", \"Unicode hex\": \"26F7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"136\", \"Dingbat hex\": \"88\", \"Unicode dec\": \"127938\", \"Unicode hex\": \"1F3C2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"137\", \"Dingbat hex\": \"89\", \"Unicode dec\": \"127948\", \"Unicode hex\": \"1F3CC\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"138\", \"Dingbat hex\": \"8A\", \"Unicode dec\": \"127946\", \"Unicode hex\": \"1F3CA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"139\", \"Dingbat hex\": \"8B\", \"Unicode dec\": \"127940\", \"Unicode hex\": \"1F3C4\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"140\", \"Dingbat hex\": \"8C\", \"Unicode dec\": \"127949\", \"Unicode hex\": \"1F3CD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"141\", \"Dingbat hex\": \"8D\", \"Unicode dec\": \"127950\", \"Unicode hex\": \"1F3CE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"142\", \"Dingbat hex\": \"8E\", \"Unicode dec\": \"128664\", \"Unicode hex\": \"1F698\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"143\", \"Dingbat hex\": \"8F\", \"Unicode dec\": \"128480\", \"Unicode hex\": \"1F5E0\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"144\", \"Dingbat hex\": \"90\", \"Unicode dec\": \"128738\", \"Unicode hex\": \"1F6E2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"145\", \"Dingbat hex\": \"91\", \"Unicode dec\": \"128176\", \"Unicode hex\": \"1F4B0\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"146\", \"Dingbat hex\": \"92\", \"Unicode dec\": \"127991\", \"Unicode hex\": \"1F3F7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"147\", \"Dingbat hex\": \"93\", \"Unicode dec\": \"128179\", \"Unicode hex\": \"1F4B3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"148\", \"Dingbat hex\": \"94\", \"Unicode dec\": \"128106\", \"Unicode hex\": \"1F46A\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"149\", \"Dingbat hex\": \"95\", \"Unicode dec\": \"128481\", \"Unicode hex\": \"1F5E1\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"150\", \"Dingbat hex\": \"96\", \"Unicode dec\": \"128482\", \"Unicode hex\": \"1F5E2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"151\", \"Dingbat hex\": \"97\", \"Unicode dec\": \"128483\", \"Unicode hex\": \"1F5E3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"152\", \"Dingbat hex\": \"98\", \"Unicode dec\": \"10031\", \"Unicode hex\": \"272F\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"153\", \"Dingbat hex\": \"99\", \"Unicode dec\": \"128388\", \"Unicode hex\": \"1F584\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"154\", \"Dingbat hex\": \"9A\", \"Unicode dec\": \"128389\", \"Unicode hex\": \"1F585\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"155\", \"Dingbat hex\": \"9B\", \"Unicode dec\": \"128387\", \"Unicode hex\": \"1F583\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"156\", \"Dingbat hex\": \"9C\", \"Unicode dec\": \"128390\", \"Unicode hex\": \"1F586\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"157\", \"Dingbat hex\": \"9D\", \"Unicode dec\": \"128441\", \"Unicode hex\": \"1F5B9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"158\", \"Dingbat hex\": \"9E\", \"Unicode dec\": \"128442\", \"Unicode hex\": \"1F5BA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"159\", \"Dingbat hex\": \"9F\", \"Unicode dec\": \"128443\", \"Unicode hex\": \"1F5BB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"160\", \"Dingbat hex\": \"A0\", \"Unicode dec\": \"128373\", \"Unicode hex\": \"1F575\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"161\", \"Dingbat hex\": \"A1\", \"Unicode dec\": \"128368\", \"Unicode hex\": \"1F570\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"162\", \"Dingbat hex\": \"A2\", \"Unicode dec\": \"128445\", \"Unicode hex\": \"1F5BD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"163\", \"Dingbat hex\": \"A3\", \"Unicode dec\": \"128446\", \"Unicode hex\": \"1F5BE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"164\", \"Dingbat hex\": \"A4\", \"Unicode dec\": \"128203\", \"Unicode hex\": \"1F4CB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"165\", \"Dingbat hex\": \"A5\", \"Unicode dec\": \"128466\", \"Unicode hex\": \"1F5D2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"166\", \"Dingbat hex\": \"A6\", \"Unicode dec\": \"128467\", \"Unicode hex\": \"1F5D3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"167\", \"Dingbat hex\": \"A7\", \"Unicode dec\": \"128366\", \"Unicode hex\": \"1F56E\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"168\", \"Dingbat hex\": \"A8\", \"Unicode dec\": \"128218\", \"Unicode hex\": \"1F4DA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"169\", \"Dingbat hex\": \"A9\", \"Unicode dec\": \"128478\", \"Unicode hex\": \"1F5DE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"170\", \"Dingbat hex\": \"AA\", \"Unicode dec\": \"128479\", \"Unicode hex\": \"1F5DF\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"171\", \"Dingbat hex\": \"AB\", \"Unicode dec\": \"128451\", \"Unicode hex\": \"1F5C3\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"172\", \"Dingbat hex\": \"AC\", \"Unicode dec\": \"128450\", \"Unicode hex\": \"1F5C2\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"173\", \"Dingbat hex\": \"AD\", \"Unicode dec\": \"128444\", \"Unicode hex\": \"1F5BC\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"174\", \"Dingbat hex\": \"AE\", \"Unicode dec\": \"127917\", \"Unicode hex\": \"1F3AD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"175\", \"Dingbat hex\": \"AF\", \"Unicode dec\": \"127900\", \"Unicode hex\": \"1F39C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"176\", \"Dingbat hex\": \"B0\", \"Unicode dec\": \"127896\", \"Unicode hex\": \"1F398\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"177\", \"Dingbat hex\": \"B1\", \"Unicode dec\": \"127897\", \"Unicode hex\": \"1F399\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"178\", \"Dingbat hex\": \"B2\", \"Unicode dec\": \"127911\", \"Unicode hex\": \"1F3A7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"179\", \"Dingbat hex\": \"B3\", \"Unicode dec\": \"128191\", \"Unicode hex\": \"1F4BF\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"180\", \"Dingbat hex\": \"B4\", \"Unicode dec\": \"127902\", \"Unicode hex\": \"1F39E\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"181\", \"Dingbat hex\": \"B5\", \"Unicode dec\": \"128247\", \"Unicode hex\": \"1F4F7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"182\", \"Dingbat hex\": \"B6\", \"Unicode dec\": \"127903\", \"Unicode hex\": \"1F39F\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"183\", \"Dingbat hex\": \"B7\", \"Unicode dec\": \"127916\", \"Unicode hex\": \"1F3AC\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"184\", \"Dingbat hex\": \"B8\", \"Unicode dec\": \"128253\", \"Unicode hex\": \"1F4FD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"185\", \"Dingbat hex\": \"B9\", \"Unicode dec\": \"128249\", \"Unicode hex\": \"1F4F9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"186\", \"Dingbat hex\": \"BA\", \"Unicode dec\": \"128254\", \"Unicode hex\": \"1F4FE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"187\", \"Dingbat hex\": \"BB\", \"Unicode dec\": \"128251\", \"Unicode hex\": \"1F4FB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"188\", \"Dingbat hex\": \"BC\", \"Unicode dec\": \"127898\", \"Unicode hex\": \"1F39A\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"189\", \"Dingbat hex\": \"BD\", \"Unicode dec\": \"127899\", \"Unicode hex\": \"1F39B\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"190\", \"Dingbat hex\": \"BE\", \"Unicode dec\": \"128250\", \"Unicode hex\": \"1F4FA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"191\", \"Dingbat hex\": \"BF\", \"Unicode dec\": \"128187\", \"Unicode hex\": \"1F4BB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"192\", \"Dingbat hex\": \"C0\", \"Unicode dec\": \"128421\", \"Unicode hex\": \"1F5A5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"193\", \"Dingbat hex\": \"C1\", \"Unicode dec\": \"128422\", \"Unicode hex\": \"1F5A6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"194\", \"Dingbat hex\": \"C2\", \"Unicode dec\": \"128423\", \"Unicode hex\": \"1F5A7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"195\", \"Dingbat hex\": \"C3\", \"Unicode dec\": \"128377\", \"Unicode hex\": \"1F579\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"196\", \"Dingbat hex\": \"C4\", \"Unicode dec\": \"127918\", \"Unicode hex\": \"1F3AE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"197\", \"Dingbat hex\": \"C5\", \"Unicode dec\": \"128379\", \"Unicode hex\": \"1F57B\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"198\", \"Dingbat hex\": \"C6\", \"Unicode dec\": \"128380\", \"Unicode hex\": \"1F57C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"199\", \"Dingbat hex\": \"C7\", \"Unicode dec\": \"128223\", \"Unicode hex\": \"1F4DF\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"200\", \"Dingbat hex\": \"C8\", \"Unicode dec\": \"128385\", \"Unicode hex\": \"1F581\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"201\", \"Dingbat hex\": \"C9\", \"Unicode dec\": \"128384\", \"Unicode hex\": \"1F580\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"202\", \"Dingbat hex\": \"CA\", \"Unicode dec\": \"128424\", \"Unicode hex\": \"1F5A8\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"203\", \"Dingbat hex\": \"CB\", \"Unicode dec\": \"128425\", \"Unicode hex\": \"1F5A9\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"204\", \"Dingbat hex\": \"CC\", \"Unicode dec\": \"128447\", \"Unicode hex\": \"1F5BF\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"205\", \"Dingbat hex\": \"CD\", \"Unicode dec\": \"128426\", \"Unicode hex\": \"1F5AA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"206\", \"Dingbat hex\": \"CE\", \"Unicode dec\": \"128476\", \"Unicode hex\": \"1F5DC\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"207\", \"Dingbat hex\": \"CF\", \"Unicode dec\": \"128274\", \"Unicode hex\": \"1F512\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"208\", \"Dingbat hex\": \"D0\", \"Unicode dec\": \"128275\", \"Unicode hex\": \"1F513\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"209\", \"Dingbat hex\": \"D1\", \"Unicode dec\": \"128477\", \"Unicode hex\": \"1F5DD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"210\", \"Dingbat hex\": \"D2\", \"Unicode dec\": \"128229\", \"Unicode hex\": \"1F4E5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"211\", \"Dingbat hex\": \"D3\", \"Unicode dec\": \"128228\", \"Unicode hex\": \"1F4E4\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"212\", \"Dingbat hex\": \"D4\", \"Unicode dec\": \"128371\", \"Unicode hex\": \"1F573\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"213\", \"Dingbat hex\": \"D5\", \"Unicode dec\": \"127779\", \"Unicode hex\": \"1F323\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"214\", \"Dingbat hex\": \"D6\", \"Unicode dec\": \"127780\", \"Unicode hex\": \"1F324\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"215\", \"Dingbat hex\": \"D7\", \"Unicode dec\": \"127781\", \"Unicode hex\": \"1F325\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"216\", \"Dingbat hex\": \"D8\", \"Unicode dec\": \"127782\", \"Unicode hex\": \"1F326\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"217\", \"Dingbat hex\": \"D9\", \"Unicode dec\": \"9729\", \"Unicode hex\": \"2601\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"218\", \"Dingbat hex\": \"DA\", \"Unicode dec\": \"127784\", \"Unicode hex\": \"1F328\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"219\", \"Dingbat hex\": \"DB\", \"Unicode dec\": \"127783\", \"Unicode hex\": \"1F327\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"220\", \"Dingbat hex\": \"DC\", \"Unicode dec\": \"127785\", \"Unicode hex\": \"1F329\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"221\", \"Dingbat hex\": \"DD\", \"Unicode dec\": \"127786\", \"Unicode hex\": \"1F32A\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"222\", \"Dingbat hex\": \"DE\", \"Unicode dec\": \"127788\", \"Unicode hex\": \"1F32C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"223\", \"Dingbat hex\": \"DF\", \"Unicode dec\": \"127787\", \"Unicode hex\": \"1F32B\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"224\", \"Dingbat hex\": \"E0\", \"Unicode dec\": \"127772\", \"Unicode hex\": \"1F31C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"225\", \"Dingbat hex\": \"E1\", \"Unicode dec\": \"127777\", \"Unicode hex\": \"1F321\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"226\", \"Dingbat hex\": \"E2\", \"Unicode dec\": \"128715\", \"Unicode hex\": \"1F6CB\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"227\", \"Dingbat hex\": \"E3\", \"Unicode dec\": \"128719\", \"Unicode hex\": \"1F6CF\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"228\", \"Dingbat hex\": \"E4\", \"Unicode dec\": \"127869\", \"Unicode hex\": \"1F37D\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"229\", \"Dingbat hex\": \"E5\", \"Unicode dec\": \"127864\", \"Unicode hex\": \"1F378\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"230\", \"Dingbat hex\": \"E6\", \"Unicode dec\": \"128718\", \"Unicode hex\": \"1F6CE\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"231\", \"Dingbat hex\": \"E7\", \"Unicode dec\": \"128717\", \"Unicode hex\": \"1F6CD\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"232\", \"Dingbat hex\": \"E8\", \"Unicode dec\": \"9413\", \"Unicode hex\": \"24C5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"233\", \"Dingbat hex\": \"E9\", \"Unicode dec\": \"9855\", \"Unicode hex\": \"267F\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"234\", \"Dingbat hex\": \"EA\", \"Unicode dec\": \"128710\", \"Unicode hex\": \"1F6C6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"235\", \"Dingbat hex\": \"EB\", \"Unicode dec\": \"128392\", \"Unicode hex\": \"1F588\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"236\", \"Dingbat hex\": \"EC\", \"Unicode dec\": \"127891\", \"Unicode hex\": \"1F393\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"237\", \"Dingbat hex\": \"ED\", \"Unicode dec\": \"128484\", \"Unicode hex\": \"1F5E4\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"238\", \"Dingbat hex\": \"EE\", \"Unicode dec\": \"128485\", \"Unicode hex\": \"1F5E5\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"239\", \"Dingbat hex\": \"EF\", \"Unicode dec\": \"128486\", \"Unicode hex\": \"1F5E6\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"240\", \"Dingbat hex\": \"F0\", \"Unicode dec\": \"128487\", \"Unicode hex\": \"1F5E7\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"241\", \"Dingbat hex\": \"F1\", \"Unicode dec\": \"128746\", \"Unicode hex\": \"1F6EA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"242\", \"Dingbat hex\": \"F2\", \"Unicode dec\": \"128063\", \"Unicode hex\": \"1F43F\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"243\", \"Dingbat hex\": \"F3\", \"Unicode dec\": \"128038\", \"Unicode hex\": \"1F426\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"244\", \"Dingbat hex\": \"F4\", \"Unicode dec\": \"128031\", \"Unicode hex\": \"1F41F\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"245\", \"Dingbat hex\": \"F5\", \"Unicode dec\": \"128021\", \"Unicode hex\": \"1F415\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"246\", \"Dingbat hex\": \"F6\", \"Unicode dec\": \"128008\", \"Unicode hex\": \"1F408\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"247\", \"Dingbat hex\": \"F7\", \"Unicode dec\": \"128620\", \"Unicode hex\": \"1F66C\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"248\", \"Dingbat hex\": \"F8\", \"Unicode dec\": \"128622\", \"Unicode hex\": \"1F66E\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"249\", \"Dingbat hex\": \"F9\", \"Unicode dec\": \"128621\", \"Unicode hex\": \"1F66D\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"250\", \"Dingbat hex\": \"FA\", \"Unicode dec\": \"128623\", \"Unicode hex\": \"1F66F\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"251\", \"Dingbat hex\": \"FB\", \"Unicode dec\": \"128506\", \"Unicode hex\": \"1F5FA\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"252\", \"Dingbat hex\": \"FC\", \"Unicode dec\": \"127757\", \"Unicode hex\": \"1F30D\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"253\", \"Dingbat hex\": \"FD\", \"Unicode dec\": \"127759\", \"Unicode hex\": \"1F30F\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"254\", \"Dingbat hex\": \"FE\", \"Unicode dec\": \"127758\", \"Unicode hex\": \"1F30E\" },\n    { \"Typeface name\": \"Webdings\", \"Dingbat dec\": \"255\", \"Dingbat hex\": \"FF\", \"Unicode dec\": \"128330\", \"Unicode hex\": \"1F54A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"32\", \"Dingbat hex\": \"20\", \"Unicode dec\": \"32\", \"Unicode hex\": \"20\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"33\", \"Dingbat hex\": \"21\", \"Unicode dec\": \"128393\", \"Unicode hex\": \"1F589\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"34\", \"Dingbat hex\": \"22\", \"Unicode dec\": \"9986\", \"Unicode hex\": \"2702\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"35\", \"Dingbat hex\": \"23\", \"Unicode dec\": \"9985\", \"Unicode hex\": \"2701\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"36\", \"Dingbat hex\": \"24\", \"Unicode dec\": \"128083\", \"Unicode hex\": \"1F453\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"37\", \"Dingbat hex\": \"25\", \"Unicode dec\": \"128365\", \"Unicode hex\": \"1F56D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"38\", \"Dingbat hex\": \"26\", \"Unicode dec\": \"128366\", \"Unicode hex\": \"1F56E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"39\", \"Dingbat hex\": \"27\", \"Unicode dec\": \"128367\", \"Unicode hex\": \"1F56F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"40\", \"Dingbat hex\": \"28\", \"Unicode dec\": \"128383\", \"Unicode hex\": \"1F57F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"41\", \"Dingbat hex\": \"29\", \"Unicode dec\": \"9990\", \"Unicode hex\": \"2706\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"42\", \"Dingbat hex\": \"2A\", \"Unicode dec\": \"128386\", \"Unicode hex\": \"1F582\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"43\", \"Dingbat hex\": \"2B\", \"Unicode dec\": \"128387\", \"Unicode hex\": \"1F583\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"44\", \"Dingbat hex\": \"2C\", \"Unicode dec\": \"128234\", \"Unicode hex\": \"1F4EA\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"45\", \"Dingbat hex\": \"2D\", \"Unicode dec\": \"128235\", \"Unicode hex\": \"1F4EB\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"46\", \"Dingbat hex\": \"2E\", \"Unicode dec\": \"128236\", \"Unicode hex\": \"1F4EC\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"47\", \"Dingbat hex\": \"2F\", \"Unicode dec\": \"128237\", \"Unicode hex\": \"1F4ED\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"48\", \"Dingbat hex\": \"30\", \"Unicode dec\": \"128448\", \"Unicode hex\": \"1F5C0\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"49\", \"Dingbat hex\": \"31\", \"Unicode dec\": \"128449\", \"Unicode hex\": \"1F5C1\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"50\", \"Dingbat hex\": \"32\", \"Unicode dec\": \"128462\", \"Unicode hex\": \"1F5CE\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"51\", \"Dingbat hex\": \"33\", \"Unicode dec\": \"128463\", \"Unicode hex\": \"1F5CF\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"52\", \"Dingbat hex\": \"34\", \"Unicode dec\": \"128464\", \"Unicode hex\": \"1F5D0\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"53\", \"Dingbat hex\": \"35\", \"Unicode dec\": \"128452\", \"Unicode hex\": \"1F5C4\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"54\", \"Dingbat hex\": \"36\", \"Unicode dec\": \"8987\", \"Unicode hex\": \"231B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"55\", \"Dingbat hex\": \"37\", \"Unicode dec\": \"128430\", \"Unicode hex\": \"1F5AE\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"56\", \"Dingbat hex\": \"38\", \"Unicode dec\": \"128432\", \"Unicode hex\": \"1F5B0\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"57\", \"Dingbat hex\": \"39\", \"Unicode dec\": \"128434\", \"Unicode hex\": \"1F5B2\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"58\", \"Dingbat hex\": \"3A\", \"Unicode dec\": \"128435\", \"Unicode hex\": \"1F5B3\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"59\", \"Dingbat hex\": \"3B\", \"Unicode dec\": \"128436\", \"Unicode hex\": \"1F5B4\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"60\", \"Dingbat hex\": \"3C\", \"Unicode dec\": \"128427\", \"Unicode hex\": \"1F5AB\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"61\", \"Dingbat hex\": \"3D\", \"Unicode dec\": \"128428\", \"Unicode hex\": \"1F5AC\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"62\", \"Dingbat hex\": \"3E\", \"Unicode dec\": \"9991\", \"Unicode hex\": \"2707\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"63\", \"Dingbat hex\": \"3F\", \"Unicode dec\": \"9997\", \"Unicode hex\": \"270D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"64\", \"Dingbat hex\": \"40\", \"Unicode dec\": \"128398\", \"Unicode hex\": \"1F58E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"65\", \"Dingbat hex\": \"41\", \"Unicode dec\": \"9996\", \"Unicode hex\": \"270C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"66\", \"Dingbat hex\": \"42\", \"Unicode dec\": \"128399\", \"Unicode hex\": \"1F58F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"67\", \"Dingbat hex\": \"43\", \"Unicode dec\": \"128077\", \"Unicode hex\": \"1F44D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"68\", \"Dingbat hex\": \"44\", \"Unicode dec\": \"128078\", \"Unicode hex\": \"1F44E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"69\", \"Dingbat hex\": \"45\", \"Unicode dec\": \"9756\", \"Unicode hex\": \"261C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"70\", \"Dingbat hex\": \"46\", \"Unicode dec\": \"9758\", \"Unicode hex\": \"261E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"71\", \"Dingbat hex\": \"47\", \"Unicode dec\": \"9757\", \"Unicode hex\": \"261D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"72\", \"Dingbat hex\": \"48\", \"Unicode dec\": \"9759\", \"Unicode hex\": \"261F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"73\", \"Dingbat hex\": \"49\", \"Unicode dec\": \"128400\", \"Unicode hex\": \"1F590\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"74\", \"Dingbat hex\": \"4A\", \"Unicode dec\": \"9786\", \"Unicode hex\": \"263A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"75\", \"Dingbat hex\": \"4B\", \"Unicode dec\": \"128528\", \"Unicode hex\": \"1F610\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"76\", \"Dingbat hex\": \"4C\", \"Unicode dec\": \"9785\", \"Unicode hex\": \"2639\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"77\", \"Dingbat hex\": \"4D\", \"Unicode dec\": \"128163\", \"Unicode hex\": \"1F4A3\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"78\", \"Dingbat hex\": \"4E\", \"Unicode dec\": \"128369\", \"Unicode hex\": \"1F571\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"79\", \"Dingbat hex\": \"4F\", \"Unicode dec\": \"127987\", \"Unicode hex\": \"1F3F3\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"80\", \"Dingbat hex\": \"50\", \"Unicode dec\": \"127985\", \"Unicode hex\": \"1F3F1\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"81\", \"Dingbat hex\": \"51\", \"Unicode dec\": \"9992\", \"Unicode hex\": \"2708\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"82\", \"Dingbat hex\": \"52\", \"Unicode dec\": \"9788\", \"Unicode hex\": \"263C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"83\", \"Dingbat hex\": \"53\", \"Unicode dec\": \"127778\", \"Unicode hex\": \"1F322\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"84\", \"Dingbat hex\": \"54\", \"Unicode dec\": \"10052\", \"Unicode hex\": \"2744\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"85\", \"Dingbat hex\": \"55\", \"Unicode dec\": \"128326\", \"Unicode hex\": \"1F546\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"86\", \"Dingbat hex\": \"56\", \"Unicode dec\": \"10014\", \"Unicode hex\": \"271E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"87\", \"Dingbat hex\": \"57\", \"Unicode dec\": \"128328\", \"Unicode hex\": \"1F548\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"88\", \"Dingbat hex\": \"58\", \"Unicode dec\": \"10016\", \"Unicode hex\": \"2720\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"89\", \"Dingbat hex\": \"59\", \"Unicode dec\": \"10017\", \"Unicode hex\": \"2721\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"90\", \"Dingbat hex\": \"5A\", \"Unicode dec\": \"9770\", \"Unicode hex\": \"262A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"91\", \"Dingbat hex\": \"5B\", \"Unicode dec\": \"9775\", \"Unicode hex\": \"262F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"92\", \"Dingbat hex\": \"5C\", \"Unicode dec\": \"128329\", \"Unicode hex\": \"1F549\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"93\", \"Dingbat hex\": \"5D\", \"Unicode dec\": \"9784\", \"Unicode hex\": \"2638\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"94\", \"Dingbat hex\": \"5E\", \"Unicode dec\": \"9800\", \"Unicode hex\": \"2648\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"95\", \"Dingbat hex\": \"5F\", \"Unicode dec\": \"9801\", \"Unicode hex\": \"2649\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"96\", \"Dingbat hex\": \"60\", \"Unicode dec\": \"9802\", \"Unicode hex\": \"264A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"97\", \"Dingbat hex\": \"61\", \"Unicode dec\": \"9803\", \"Unicode hex\": \"264B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"98\", \"Dingbat hex\": \"62\", \"Unicode dec\": \"9804\", \"Unicode hex\": \"264C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"99\", \"Dingbat hex\": \"63\", \"Unicode dec\": \"9805\", \"Unicode hex\": \"264D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"100\", \"Dingbat hex\": \"64\", \"Unicode dec\": \"9806\", \"Unicode hex\": \"264E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"101\", \"Dingbat hex\": \"65\", \"Unicode dec\": \"9807\", \"Unicode hex\": \"264F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"102\", \"Dingbat hex\": \"66\", \"Unicode dec\": \"9808\", \"Unicode hex\": \"2650\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"103\", \"Dingbat hex\": \"67\", \"Unicode dec\": \"9809\", \"Unicode hex\": \"2651\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"104\", \"Dingbat hex\": \"68\", \"Unicode dec\": \"9810\", \"Unicode hex\": \"2652\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"105\", \"Dingbat hex\": \"69\", \"Unicode dec\": \"9811\", \"Unicode hex\": \"2653\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"106\", \"Dingbat hex\": \"6A\", \"Unicode dec\": \"128624\", \"Unicode hex\": \"1F670\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"107\", \"Dingbat hex\": \"6B\", \"Unicode dec\": \"128629\", \"Unicode hex\": \"1F675\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"108\", \"Dingbat hex\": \"6C\", \"Unicode dec\": \"9899\", \"Unicode hex\": \"26AB\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"109\", \"Dingbat hex\": \"6D\", \"Unicode dec\": \"128318\", \"Unicode hex\": \"1F53E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"110\", \"Dingbat hex\": \"6E\", \"Unicode dec\": \"9724\", \"Unicode hex\": \"25FC\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"111\", \"Dingbat hex\": \"6F\", \"Unicode dec\": \"128911\", \"Unicode hex\": \"1F78F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"112\", \"Dingbat hex\": \"70\", \"Unicode dec\": \"128912\", \"Unicode hex\": \"1F790\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"113\", \"Dingbat hex\": \"71\", \"Unicode dec\": \"10065\", \"Unicode hex\": \"2751\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"114\", \"Dingbat hex\": \"72\", \"Unicode dec\": \"10066\", \"Unicode hex\": \"2752\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"115\", \"Dingbat hex\": \"73\", \"Unicode dec\": \"128927\", \"Unicode hex\": \"1F79F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"116\", \"Dingbat hex\": \"74\", \"Unicode dec\": \"10731\", \"Unicode hex\": \"29EB\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"117\", \"Dingbat hex\": \"75\", \"Unicode dec\": \"9670\", \"Unicode hex\": \"25C6\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"118\", \"Dingbat hex\": \"76\", \"Unicode dec\": \"10070\", \"Unicode hex\": \"2756\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"119\", \"Dingbat hex\": \"77\", \"Unicode dec\": \"11049\", \"Unicode hex\": \"2B29\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"120\", \"Dingbat hex\": \"78\", \"Unicode dec\": \"8999\", \"Unicode hex\": \"2327\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"121\", \"Dingbat hex\": \"79\", \"Unicode dec\": \"11193\", \"Unicode hex\": \"2BB9\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"122\", \"Dingbat hex\": \"7A\", \"Unicode dec\": \"8984\", \"Unicode hex\": \"2318\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"123\", \"Dingbat hex\": \"7B\", \"Unicode dec\": \"127989\", \"Unicode hex\": \"1F3F5\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"124\", \"Dingbat hex\": \"7C\", \"Unicode dec\": \"127990\", \"Unicode hex\": \"1F3F6\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"125\", \"Dingbat hex\": \"7D\", \"Unicode dec\": \"128630\", \"Unicode hex\": \"1F676\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"126\", \"Dingbat hex\": \"7E\", \"Unicode dec\": \"128631\", \"Unicode hex\": \"1F677\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"127\", \"Dingbat hex\": \"7F\", \"Unicode dec\": \"9647\", \"Unicode hex\": \"25AF\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"128\", \"Dingbat hex\": \"80\", \"Unicode dec\": \"127243\", \"Unicode hex\": \"1F10B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"129\", \"Dingbat hex\": \"81\", \"Unicode dec\": \"10112\", \"Unicode hex\": \"2780\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"130\", \"Dingbat hex\": \"82\", \"Unicode dec\": \"10113\", \"Unicode hex\": \"2781\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"131\", \"Dingbat hex\": \"83\", \"Unicode dec\": \"10114\", \"Unicode hex\": \"2782\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"132\", \"Dingbat hex\": \"84\", \"Unicode dec\": \"10115\", \"Unicode hex\": \"2783\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"133\", \"Dingbat hex\": \"85\", \"Unicode dec\": \"10116\", \"Unicode hex\": \"2784\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"134\", \"Dingbat hex\": \"86\", \"Unicode dec\": \"10117\", \"Unicode hex\": \"2785\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"135\", \"Dingbat hex\": \"87\", \"Unicode dec\": \"10118\", \"Unicode hex\": \"2786\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"136\", \"Dingbat hex\": \"88\", \"Unicode dec\": \"10119\", \"Unicode hex\": \"2787\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"137\", \"Dingbat hex\": \"89\", \"Unicode dec\": \"10120\", \"Unicode hex\": \"2788\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"138\", \"Dingbat hex\": \"8A\", \"Unicode dec\": \"10121\", \"Unicode hex\": \"2789\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"139\", \"Dingbat hex\": \"8B\", \"Unicode dec\": \"127244\", \"Unicode hex\": \"1F10C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"140\", \"Dingbat hex\": \"8C\", \"Unicode dec\": \"10122\", \"Unicode hex\": \"278A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"141\", \"Dingbat hex\": \"8D\", \"Unicode dec\": \"10123\", \"Unicode hex\": \"278B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"142\", \"Dingbat hex\": \"8E\", \"Unicode dec\": \"10124\", \"Unicode hex\": \"278C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"143\", \"Dingbat hex\": \"8F\", \"Unicode dec\": \"10125\", \"Unicode hex\": \"278D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"144\", \"Dingbat hex\": \"90\", \"Unicode dec\": \"10126\", \"Unicode hex\": \"278E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"145\", \"Dingbat hex\": \"91\", \"Unicode dec\": \"10127\", \"Unicode hex\": \"278F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"146\", \"Dingbat hex\": \"92\", \"Unicode dec\": \"10128\", \"Unicode hex\": \"2790\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"147\", \"Dingbat hex\": \"93\", \"Unicode dec\": \"10129\", \"Unicode hex\": \"2791\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"148\", \"Dingbat hex\": \"94\", \"Unicode dec\": \"10130\", \"Unicode hex\": \"2792\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"149\", \"Dingbat hex\": \"95\", \"Unicode dec\": \"10131\", \"Unicode hex\": \"2793\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"150\", \"Dingbat hex\": \"96\", \"Unicode dec\": \"128610\", \"Unicode hex\": \"1F662\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"151\", \"Dingbat hex\": \"97\", \"Unicode dec\": \"128608\", \"Unicode hex\": \"1F660\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"152\", \"Dingbat hex\": \"98\", \"Unicode dec\": \"128609\", \"Unicode hex\": \"1F661\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"153\", \"Dingbat hex\": \"99\", \"Unicode dec\": \"128611\", \"Unicode hex\": \"1F663\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"154\", \"Dingbat hex\": \"9A\", \"Unicode dec\": \"128606\", \"Unicode hex\": \"1F65E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"155\", \"Dingbat hex\": \"9B\", \"Unicode dec\": \"128604\", \"Unicode hex\": \"1F65C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"156\", \"Dingbat hex\": \"9C\", \"Unicode dec\": \"128605\", \"Unicode hex\": \"1F65D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"157\", \"Dingbat hex\": \"9D\", \"Unicode dec\": \"128607\", \"Unicode hex\": \"1F65F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"158\", \"Dingbat hex\": \"9E\", \"Unicode dec\": \"8729\", \"Unicode hex\": \"2219\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"159\", \"Dingbat hex\": \"9F\", \"Unicode dec\": \"8226\", \"Unicode hex\": \"2022\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"160\", \"Dingbat hex\": \"A0\", \"Unicode dec\": \"11037\", \"Unicode hex\": \"2B1D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"161\", \"Dingbat hex\": \"A1\", \"Unicode dec\": \"11096\", \"Unicode hex\": \"2B58\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"162\", \"Dingbat hex\": \"A2\", \"Unicode dec\": \"128902\", \"Unicode hex\": \"1F786\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"163\", \"Dingbat hex\": \"A3\", \"Unicode dec\": \"128904\", \"Unicode hex\": \"1F788\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"164\", \"Dingbat hex\": \"A4\", \"Unicode dec\": \"128906\", \"Unicode hex\": \"1F78A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"165\", \"Dingbat hex\": \"A5\", \"Unicode dec\": \"128907\", \"Unicode hex\": \"1F78B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"166\", \"Dingbat hex\": \"A6\", \"Unicode dec\": \"128319\", \"Unicode hex\": \"1F53F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"167\", \"Dingbat hex\": \"A7\", \"Unicode dec\": \"9642\", \"Unicode hex\": \"25AA\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"168\", \"Dingbat hex\": \"A8\", \"Unicode dec\": \"128910\", \"Unicode hex\": \"1F78E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"169\", \"Dingbat hex\": \"A9\", \"Unicode dec\": \"128961\", \"Unicode hex\": \"1F7C1\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"170\", \"Dingbat hex\": \"AA\", \"Unicode dec\": \"128965\", \"Unicode hex\": \"1F7C5\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"171\", \"Dingbat hex\": \"AB\", \"Unicode dec\": \"9733\", \"Unicode hex\": \"2605\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"172\", \"Dingbat hex\": \"AC\", \"Unicode dec\": \"128971\", \"Unicode hex\": \"1F7CB\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"173\", \"Dingbat hex\": \"AD\", \"Unicode dec\": \"128975\", \"Unicode hex\": \"1F7CF\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"174\", \"Dingbat hex\": \"AE\", \"Unicode dec\": \"128979\", \"Unicode hex\": \"1F7D3\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"175\", \"Dingbat hex\": \"AF\", \"Unicode dec\": \"128977\", \"Unicode hex\": \"1F7D1\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"176\", \"Dingbat hex\": \"B0\", \"Unicode dec\": \"11216\", \"Unicode hex\": \"2BD0\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"177\", \"Dingbat hex\": \"B1\", \"Unicode dec\": \"8982\", \"Unicode hex\": \"2316\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"178\", \"Dingbat hex\": \"B2\", \"Unicode dec\": \"11214\", \"Unicode hex\": \"2BCE\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"179\", \"Dingbat hex\": \"B3\", \"Unicode dec\": \"11215\", \"Unicode hex\": \"2BCF\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"180\", \"Dingbat hex\": \"B4\", \"Unicode dec\": \"11217\", \"Unicode hex\": \"2BD1\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"181\", \"Dingbat hex\": \"B5\", \"Unicode dec\": \"10026\", \"Unicode hex\": \"272A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"182\", \"Dingbat hex\": \"B6\", \"Unicode dec\": \"10032\", \"Unicode hex\": \"2730\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"183\", \"Dingbat hex\": \"B7\", \"Unicode dec\": \"128336\", \"Unicode hex\": \"1F550\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"184\", \"Dingbat hex\": \"B8\", \"Unicode dec\": \"128337\", \"Unicode hex\": \"1F551\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"185\", \"Dingbat hex\": \"B9\", \"Unicode dec\": \"128338\", \"Unicode hex\": \"1F552\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"186\", \"Dingbat hex\": \"BA\", \"Unicode dec\": \"128339\", \"Unicode hex\": \"1F553\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"187\", \"Dingbat hex\": \"BB\", \"Unicode dec\": \"128340\", \"Unicode hex\": \"1F554\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"188\", \"Dingbat hex\": \"BC\", \"Unicode dec\": \"128341\", \"Unicode hex\": \"1F555\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"189\", \"Dingbat hex\": \"BD\", \"Unicode dec\": \"128342\", \"Unicode hex\": \"1F556\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"190\", \"Dingbat hex\": \"BE\", \"Unicode dec\": \"128343\", \"Unicode hex\": \"1F557\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"191\", \"Dingbat hex\": \"BF\", \"Unicode dec\": \"128344\", \"Unicode hex\": \"1F558\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"192\", \"Dingbat hex\": \"C0\", \"Unicode dec\": \"128345\", \"Unicode hex\": \"1F559\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"193\", \"Dingbat hex\": \"C1\", \"Unicode dec\": \"128346\", \"Unicode hex\": \"1F55A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"194\", \"Dingbat hex\": \"C2\", \"Unicode dec\": \"128347\", \"Unicode hex\": \"1F55B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"195\", \"Dingbat hex\": \"C3\", \"Unicode dec\": \"11184\", \"Unicode hex\": \"2BB0\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"196\", \"Dingbat hex\": \"C4\", \"Unicode dec\": \"11185\", \"Unicode hex\": \"2BB1\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"197\", \"Dingbat hex\": \"C5\", \"Unicode dec\": \"11186\", \"Unicode hex\": \"2BB2\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"198\", \"Dingbat hex\": \"C6\", \"Unicode dec\": \"11187\", \"Unicode hex\": \"2BB3\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"199\", \"Dingbat hex\": \"C7\", \"Unicode dec\": \"11188\", \"Unicode hex\": \"2BB4\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"200\", \"Dingbat hex\": \"C8\", \"Unicode dec\": \"11189\", \"Unicode hex\": \"2BB5\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"201\", \"Dingbat hex\": \"C9\", \"Unicode dec\": \"11190\", \"Unicode hex\": \"2BB6\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"202\", \"Dingbat hex\": \"CA\", \"Unicode dec\": \"11191\", \"Unicode hex\": \"2BB7\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"203\", \"Dingbat hex\": \"CB\", \"Unicode dec\": \"128618\", \"Unicode hex\": \"1F66A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"204\", \"Dingbat hex\": \"CC\", \"Unicode dec\": \"128619\", \"Unicode hex\": \"1F66B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"205\", \"Dingbat hex\": \"CD\", \"Unicode dec\": \"128597\", \"Unicode hex\": \"1F655\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"206\", \"Dingbat hex\": \"CE\", \"Unicode dec\": \"128596\", \"Unicode hex\": \"1F654\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"207\", \"Dingbat hex\": \"CF\", \"Unicode dec\": \"128599\", \"Unicode hex\": \"1F657\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"208\", \"Dingbat hex\": \"D0\", \"Unicode dec\": \"128598\", \"Unicode hex\": \"1F656\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"209\", \"Dingbat hex\": \"D1\", \"Unicode dec\": \"128592\", \"Unicode hex\": \"1F650\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"210\", \"Dingbat hex\": \"D2\", \"Unicode dec\": \"128593\", \"Unicode hex\": \"1F651\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"211\", \"Dingbat hex\": \"D3\", \"Unicode dec\": \"128594\", \"Unicode hex\": \"1F652\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"212\", \"Dingbat hex\": \"D4\", \"Unicode dec\": \"128595\", \"Unicode hex\": \"1F653\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"213\", \"Dingbat hex\": \"D5\", \"Unicode dec\": \"9003\", \"Unicode hex\": \"232B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"214\", \"Dingbat hex\": \"D6\", \"Unicode dec\": \"8998\", \"Unicode hex\": \"2326\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"215\", \"Dingbat hex\": \"D7\", \"Unicode dec\": \"11160\", \"Unicode hex\": \"2B98\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"216\", \"Dingbat hex\": \"D8\", \"Unicode dec\": \"11162\", \"Unicode hex\": \"2B9A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"217\", \"Dingbat hex\": \"D9\", \"Unicode dec\": \"11161\", \"Unicode hex\": \"2B99\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"218\", \"Dingbat hex\": \"DA\", \"Unicode dec\": \"11163\", \"Unicode hex\": \"2B9B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"219\", \"Dingbat hex\": \"DB\", \"Unicode dec\": \"11144\", \"Unicode hex\": \"2B88\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"220\", \"Dingbat hex\": \"DC\", \"Unicode dec\": \"11146\", \"Unicode hex\": \"2B8A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"221\", \"Dingbat hex\": \"DD\", \"Unicode dec\": \"11145\", \"Unicode hex\": \"2B89\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"222\", \"Dingbat hex\": \"DE\", \"Unicode dec\": \"11147\", \"Unicode hex\": \"2B8B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"223\", \"Dingbat hex\": \"DF\", \"Unicode dec\": \"129128\", \"Unicode hex\": \"1F868\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"224\", \"Dingbat hex\": \"E0\", \"Unicode dec\": \"129130\", \"Unicode hex\": \"1F86A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"225\", \"Dingbat hex\": \"E1\", \"Unicode dec\": \"129129\", \"Unicode hex\": \"1F869\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"226\", \"Dingbat hex\": \"E2\", \"Unicode dec\": \"129131\", \"Unicode hex\": \"1F86B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"227\", \"Dingbat hex\": \"E3\", \"Unicode dec\": \"129132\", \"Unicode hex\": \"1F86C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"228\", \"Dingbat hex\": \"E4\", \"Unicode dec\": \"129133\", \"Unicode hex\": \"1F86D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"229\", \"Dingbat hex\": \"E5\", \"Unicode dec\": \"129135\", \"Unicode hex\": \"1F86F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"230\", \"Dingbat hex\": \"E6\", \"Unicode dec\": \"129134\", \"Unicode hex\": \"1F86E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"231\", \"Dingbat hex\": \"E7\", \"Unicode dec\": \"129144\", \"Unicode hex\": \"1F878\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"232\", \"Dingbat hex\": \"E8\", \"Unicode dec\": \"129146\", \"Unicode hex\": \"1F87A\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"233\", \"Dingbat hex\": \"E9\", \"Unicode dec\": \"129145\", \"Unicode hex\": \"1F879\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"234\", \"Dingbat hex\": \"EA\", \"Unicode dec\": \"129147\", \"Unicode hex\": \"1F87B\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"235\", \"Dingbat hex\": \"EB\", \"Unicode dec\": \"129148\", \"Unicode hex\": \"1F87C\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"236\", \"Dingbat hex\": \"EC\", \"Unicode dec\": \"129149\", \"Unicode hex\": \"1F87D\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"237\", \"Dingbat hex\": \"ED\", \"Unicode dec\": \"129151\", \"Unicode hex\": \"1F87F\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"238\", \"Dingbat hex\": \"EE\", \"Unicode dec\": \"129150\", \"Unicode hex\": \"1F87E\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"239\", \"Dingbat hex\": \"EF\", \"Unicode dec\": \"8678\", \"Unicode hex\": \"21E6\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"240\", \"Dingbat hex\": \"F0\", \"Unicode dec\": \"8680\", \"Unicode hex\": \"21E8\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"241\", \"Dingbat hex\": \"F1\", \"Unicode dec\": \"8679\", \"Unicode hex\": \"21E7\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"242\", \"Dingbat hex\": \"F2\", \"Unicode dec\": \"8681\", \"Unicode hex\": \"21E9\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"243\", \"Dingbat hex\": \"F3\", \"Unicode dec\": \"11012\", \"Unicode hex\": \"2B04\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"244\", \"Dingbat hex\": \"F4\", \"Unicode dec\": \"8691\", \"Unicode hex\": \"21F3\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"245\", \"Dingbat hex\": \"F5\", \"Unicode dec\": \"11009\", \"Unicode hex\": \"2B01\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"246\", \"Dingbat hex\": \"F6\", \"Unicode dec\": \"11008\", \"Unicode hex\": \"2B00\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"247\", \"Dingbat hex\": \"F7\", \"Unicode dec\": \"11011\", \"Unicode hex\": \"2B03\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"248\", \"Dingbat hex\": \"F8\", \"Unicode dec\": \"11010\", \"Unicode hex\": \"2B02\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"249\", \"Dingbat hex\": \"F9\", \"Unicode dec\": \"129196\", \"Unicode hex\": \"1F8AC\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"250\", \"Dingbat hex\": \"FA\", \"Unicode dec\": \"129197\", \"Unicode hex\": \"1F8AD\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"251\", \"Dingbat hex\": \"FB\", \"Unicode dec\": \"128502\", \"Unicode hex\": \"1F5F6\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"252\", \"Dingbat hex\": \"FC\", \"Unicode dec\": \"10003\", \"Unicode hex\": \"2713\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"253\", \"Dingbat hex\": \"FD\", \"Unicode dec\": \"128503\", \"Unicode hex\": \"1F5F7\" },\n    { \"Typeface name\": \"Wingdings\", \"Dingbat dec\": \"254\", \"Dingbat hex\": \"FE\", \"Unicode dec\": \"128505\", \"Unicode hex\": \"1F5F9\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"32\", \"Dingbat hex\": \"20\", \"Unicode dec\": \"32\", \"Unicode hex\": \"20\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"33\", \"Dingbat hex\": \"21\", \"Unicode dec\": \"128394\", \"Unicode hex\": \"1F58A\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"34\", \"Dingbat hex\": \"22\", \"Unicode dec\": \"128395\", \"Unicode hex\": \"1F58B\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"35\", \"Dingbat hex\": \"23\", \"Unicode dec\": \"128396\", \"Unicode hex\": \"1F58C\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"36\", \"Dingbat hex\": \"24\", \"Unicode dec\": \"128397\", \"Unicode hex\": \"1F58D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"37\", \"Dingbat hex\": \"25\", \"Unicode dec\": \"9988\", \"Unicode hex\": \"2704\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"38\", \"Dingbat hex\": \"26\", \"Unicode dec\": \"9984\", \"Unicode hex\": \"2700\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"39\", \"Dingbat hex\": \"27\", \"Unicode dec\": \"128382\", \"Unicode hex\": \"1F57E\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"40\", \"Dingbat hex\": \"28\", \"Unicode dec\": \"128381\", \"Unicode hex\": \"1F57D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"41\", \"Dingbat hex\": \"29\", \"Unicode dec\": \"128453\", \"Unicode hex\": \"1F5C5\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"42\", \"Dingbat hex\": \"2A\", \"Unicode dec\": \"128454\", \"Unicode hex\": \"1F5C6\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"43\", \"Dingbat hex\": \"2B\", \"Unicode dec\": \"128455\", \"Unicode hex\": \"1F5C7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"44\", \"Dingbat hex\": \"2C\", \"Unicode dec\": \"128456\", \"Unicode hex\": \"1F5C8\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"45\", \"Dingbat hex\": \"2D\", \"Unicode dec\": \"128457\", \"Unicode hex\": \"1F5C9\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"46\", \"Dingbat hex\": \"2E\", \"Unicode dec\": \"128458\", \"Unicode hex\": \"1F5CA\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"47\", \"Dingbat hex\": \"2F\", \"Unicode dec\": \"128459\", \"Unicode hex\": \"1F5CB\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"48\", \"Dingbat hex\": \"30\", \"Unicode dec\": \"128460\", \"Unicode hex\": \"1F5CC\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"49\", \"Dingbat hex\": \"31\", \"Unicode dec\": \"128461\", \"Unicode hex\": \"1F5CD\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"50\", \"Dingbat hex\": \"32\", \"Unicode dec\": \"128203\", \"Unicode hex\": \"1F4CB\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"51\", \"Dingbat hex\": \"33\", \"Unicode dec\": \"128465\", \"Unicode hex\": \"1F5D1\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"52\", \"Dingbat hex\": \"34\", \"Unicode dec\": \"128468\", \"Unicode hex\": \"1F5D4\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"53\", \"Dingbat hex\": \"35\", \"Unicode dec\": \"128437\", \"Unicode hex\": \"1F5B5\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"54\", \"Dingbat hex\": \"36\", \"Unicode dec\": \"128438\", \"Unicode hex\": \"1F5B6\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"55\", \"Dingbat hex\": \"37\", \"Unicode dec\": \"128439\", \"Unicode hex\": \"1F5B7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"56\", \"Dingbat hex\": \"38\", \"Unicode dec\": \"128440\", \"Unicode hex\": \"1F5B8\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"57\", \"Dingbat hex\": \"39\", \"Unicode dec\": \"128429\", \"Unicode hex\": \"1F5AD\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"58\", \"Dingbat hex\": \"3A\", \"Unicode dec\": \"128431\", \"Unicode hex\": \"1F5AF\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"59\", \"Dingbat hex\": \"3B\", \"Unicode dec\": \"128433\", \"Unicode hex\": \"1F5B1\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"60\", \"Dingbat hex\": \"3C\", \"Unicode dec\": \"128402\", \"Unicode hex\": \"1F592\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"61\", \"Dingbat hex\": \"3D\", \"Unicode dec\": \"128403\", \"Unicode hex\": \"1F593\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"62\", \"Dingbat hex\": \"3E\", \"Unicode dec\": \"128408\", \"Unicode hex\": \"1F598\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"63\", \"Dingbat hex\": \"3F\", \"Unicode dec\": \"128409\", \"Unicode hex\": \"1F599\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"64\", \"Dingbat hex\": \"40\", \"Unicode dec\": \"128410\", \"Unicode hex\": \"1F59A\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"65\", \"Dingbat hex\": \"41\", \"Unicode dec\": \"128411\", \"Unicode hex\": \"1F59B\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"66\", \"Dingbat hex\": \"42\", \"Unicode dec\": \"128072\", \"Unicode hex\": \"1F448\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"67\", \"Dingbat hex\": \"43\", \"Unicode dec\": \"128073\", \"Unicode hex\": \"1F449\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"68\", \"Dingbat hex\": \"44\", \"Unicode dec\": \"128412\", \"Unicode hex\": \"1F59C\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"69\", \"Dingbat hex\": \"45\", \"Unicode dec\": \"128413\", \"Unicode hex\": \"1F59D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"70\", \"Dingbat hex\": \"46\", \"Unicode dec\": \"128414\", \"Unicode hex\": \"1F59E\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"71\", \"Dingbat hex\": \"47\", \"Unicode dec\": \"128415\", \"Unicode hex\": \"1F59F\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"72\", \"Dingbat hex\": \"48\", \"Unicode dec\": \"128416\", \"Unicode hex\": \"1F5A0\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"73\", \"Dingbat hex\": \"49\", \"Unicode dec\": \"128417\", \"Unicode hex\": \"1F5A1\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"74\", \"Dingbat hex\": \"4A\", \"Unicode dec\": \"128070\", \"Unicode hex\": \"1F446\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"75\", \"Dingbat hex\": \"4B\", \"Unicode dec\": \"128071\", \"Unicode hex\": \"1F447\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"76\", \"Dingbat hex\": \"4C\", \"Unicode dec\": \"128418\", \"Unicode hex\": \"1F5A2\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"77\", \"Dingbat hex\": \"4D\", \"Unicode dec\": \"128419\", \"Unicode hex\": \"1F5A3\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"78\", \"Dingbat hex\": \"4E\", \"Unicode dec\": \"128401\", \"Unicode hex\": \"1F591\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"79\", \"Dingbat hex\": \"4F\", \"Unicode dec\": \"128500\", \"Unicode hex\": \"1F5F4\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"80\", \"Dingbat hex\": \"50\", \"Unicode dec\": \"128504\", \"Unicode hex\": \"1F5F8\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"81\", \"Dingbat hex\": \"51\", \"Unicode dec\": \"128501\", \"Unicode hex\": \"1F5F5\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"82\", \"Dingbat hex\": \"52\", \"Unicode dec\": \"9745\", \"Unicode hex\": \"2611\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"83\", \"Dingbat hex\": \"53\", \"Unicode dec\": \"11197\", \"Unicode hex\": \"2BBD\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"84\", \"Dingbat hex\": \"54\", \"Unicode dec\": \"9746\", \"Unicode hex\": \"2612\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"85\", \"Dingbat hex\": \"55\", \"Unicode dec\": \"11198\", \"Unicode hex\": \"2BBE\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"86\", \"Dingbat hex\": \"56\", \"Unicode dec\": \"11199\", \"Unicode hex\": \"2BBF\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"87\", \"Dingbat hex\": \"57\", \"Unicode dec\": \"128711\", \"Unicode hex\": \"1F6C7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"88\", \"Dingbat hex\": \"58\", \"Unicode dec\": \"10680\", \"Unicode hex\": \"29B8\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"89\", \"Dingbat hex\": \"59\", \"Unicode dec\": \"128625\", \"Unicode hex\": \"1F671\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"90\", \"Dingbat hex\": \"5A\", \"Unicode dec\": \"128628\", \"Unicode hex\": \"1F674\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"91\", \"Dingbat hex\": \"5B\", \"Unicode dec\": \"128626\", \"Unicode hex\": \"1F672\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"92\", \"Dingbat hex\": \"5C\", \"Unicode dec\": \"128627\", \"Unicode hex\": \"1F673\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"93\", \"Dingbat hex\": \"5D\", \"Unicode dec\": \"8253\", \"Unicode hex\": \"203D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"94\", \"Dingbat hex\": \"5E\", \"Unicode dec\": \"128633\", \"Unicode hex\": \"1F679\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"95\", \"Dingbat hex\": \"5F\", \"Unicode dec\": \"128634\", \"Unicode hex\": \"1F67A\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"96\", \"Dingbat hex\": \"60\", \"Unicode dec\": \"128635\", \"Unicode hex\": \"1F67B\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"97\", \"Dingbat hex\": \"61\", \"Unicode dec\": \"128614\", \"Unicode hex\": \"1F666\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"98\", \"Dingbat hex\": \"62\", \"Unicode dec\": \"128612\", \"Unicode hex\": \"1F664\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"99\", \"Dingbat hex\": \"63\", \"Unicode dec\": \"128613\", \"Unicode hex\": \"1F665\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"100\", \"Dingbat hex\": \"64\", \"Unicode dec\": \"128615\", \"Unicode hex\": \"1F667\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"101\", \"Dingbat hex\": \"65\", \"Unicode dec\": \"128602\", \"Unicode hex\": \"1F65A\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"102\", \"Dingbat hex\": \"66\", \"Unicode dec\": \"128600\", \"Unicode hex\": \"1F658\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"103\", \"Dingbat hex\": \"67\", \"Unicode dec\": \"128601\", \"Unicode hex\": \"1F659\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"104\", \"Dingbat hex\": \"68\", \"Unicode dec\": \"128603\", \"Unicode hex\": \"1F65B\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"105\", \"Dingbat hex\": \"69\", \"Unicode dec\": \"9450\", \"Unicode hex\": \"24EA\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"106\", \"Dingbat hex\": \"6A\", \"Unicode dec\": \"9312\", \"Unicode hex\": \"2460\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"107\", \"Dingbat hex\": \"6B\", \"Unicode dec\": \"9313\", \"Unicode hex\": \"2461\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"108\", \"Dingbat hex\": \"6C\", \"Unicode dec\": \"9314\", \"Unicode hex\": \"2462\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"109\", \"Dingbat hex\": \"6D\", \"Unicode dec\": \"9315\", \"Unicode hex\": \"2463\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"110\", \"Dingbat hex\": \"6E\", \"Unicode dec\": \"9316\", \"Unicode hex\": \"2464\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"111\", \"Dingbat hex\": \"6F\", \"Unicode dec\": \"9317\", \"Unicode hex\": \"2465\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"112\", \"Dingbat hex\": \"70\", \"Unicode dec\": \"9318\", \"Unicode hex\": \"2466\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"113\", \"Dingbat hex\": \"71\", \"Unicode dec\": \"9319\", \"Unicode hex\": \"2467\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"114\", \"Dingbat hex\": \"72\", \"Unicode dec\": \"9320\", \"Unicode hex\": \"2468\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"115\", \"Dingbat hex\": \"73\", \"Unicode dec\": \"9321\", \"Unicode hex\": \"2469\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"116\", \"Dingbat hex\": \"74\", \"Unicode dec\": \"9471\", \"Unicode hex\": \"24FF\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"117\", \"Dingbat hex\": \"75\", \"Unicode dec\": \"10102\", \"Unicode hex\": \"2776\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"118\", \"Dingbat hex\": \"76\", \"Unicode dec\": \"10103\", \"Unicode hex\": \"2777\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"119\", \"Dingbat hex\": \"77\", \"Unicode dec\": \"10104\", \"Unicode hex\": \"2778\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"120\", \"Dingbat hex\": \"78\", \"Unicode dec\": \"10105\", \"Unicode hex\": \"2779\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"121\", \"Dingbat hex\": \"79\", \"Unicode dec\": \"10106\", \"Unicode hex\": \"277A\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"122\", \"Dingbat hex\": \"7A\", \"Unicode dec\": \"10107\", \"Unicode hex\": \"277B\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"123\", \"Dingbat hex\": \"7B\", \"Unicode dec\": \"10108\", \"Unicode hex\": \"277C\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"124\", \"Dingbat hex\": \"7C\", \"Unicode dec\": \"10109\", \"Unicode hex\": \"277D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"125\", \"Dingbat hex\": \"7D\", \"Unicode dec\": \"10110\", \"Unicode hex\": \"277E\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"126\", \"Dingbat hex\": \"7E\", \"Unicode dec\": \"10111\", \"Unicode hex\": \"277F\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"128\", \"Dingbat hex\": \"80\", \"Unicode dec\": \"9737\", \"Unicode hex\": \"2609\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"129\", \"Dingbat hex\": \"81\", \"Unicode dec\": \"127765\", \"Unicode hex\": \"1F315\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"130\", \"Dingbat hex\": \"82\", \"Unicode dec\": \"9789\", \"Unicode hex\": \"263D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"131\", \"Dingbat hex\": \"83\", \"Unicode dec\": \"9790\", \"Unicode hex\": \"263E\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"132\", \"Dingbat hex\": \"84\", \"Unicode dec\": \"11839\", \"Unicode hex\": \"2E3F\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"133\", \"Dingbat hex\": \"85\", \"Unicode dec\": \"10013\", \"Unicode hex\": \"271D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"134\", \"Dingbat hex\": \"86\", \"Unicode dec\": \"128327\", \"Unicode hex\": \"1F547\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"135\", \"Dingbat hex\": \"87\", \"Unicode dec\": \"128348\", \"Unicode hex\": \"1F55C\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"136\", \"Dingbat hex\": \"88\", \"Unicode dec\": \"128349\", \"Unicode hex\": \"1F55D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"137\", \"Dingbat hex\": \"89\", \"Unicode dec\": \"128350\", \"Unicode hex\": \"1F55E\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"138\", \"Dingbat hex\": \"8A\", \"Unicode dec\": \"128351\", \"Unicode hex\": \"1F55F\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"139\", \"Dingbat hex\": \"8B\", \"Unicode dec\": \"128352\", \"Unicode hex\": \"1F560\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"140\", \"Dingbat hex\": \"8C\", \"Unicode dec\": \"128353\", \"Unicode hex\": \"1F561\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"141\", \"Dingbat hex\": \"8D\", \"Unicode dec\": \"128354\", \"Unicode hex\": \"1F562\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"142\", \"Dingbat hex\": \"8E\", \"Unicode dec\": \"128355\", \"Unicode hex\": \"1F563\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"143\", \"Dingbat hex\": \"8F\", \"Unicode dec\": \"128356\", \"Unicode hex\": \"1F564\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"144\", \"Dingbat hex\": \"90\", \"Unicode dec\": \"128357\", \"Unicode hex\": \"1F565\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"145\", \"Dingbat hex\": \"91\", \"Unicode dec\": \"128358\", \"Unicode hex\": \"1F566\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"146\", \"Dingbat hex\": \"92\", \"Unicode dec\": \"128359\", \"Unicode hex\": \"1F567\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"147\", \"Dingbat hex\": \"93\", \"Unicode dec\": \"128616\", \"Unicode hex\": \"1F668\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"148\", \"Dingbat hex\": \"94\", \"Unicode dec\": \"128617\", \"Unicode hex\": \"1F669\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"149\", \"Dingbat hex\": \"95\", \"Unicode dec\": \"8901\", \"Unicode hex\": \"22C5\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"150\", \"Dingbat hex\": \"96\", \"Unicode dec\": \"128900\", \"Unicode hex\": \"1F784\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"151\", \"Dingbat hex\": \"97\", \"Unicode dec\": \"10625\", \"Unicode hex\": \"2981\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"152\", \"Dingbat hex\": \"98\", \"Unicode dec\": \"9679\", \"Unicode hex\": \"25CF\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"153\", \"Dingbat hex\": \"99\", \"Unicode dec\": \"9675\", \"Unicode hex\": \"25CB\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"154\", \"Dingbat hex\": \"9A\", \"Unicode dec\": \"128901\", \"Unicode hex\": \"1F785\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"155\", \"Dingbat hex\": \"9B\", \"Unicode dec\": \"128903\", \"Unicode hex\": \"1F787\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"156\", \"Dingbat hex\": \"9C\", \"Unicode dec\": \"128905\", \"Unicode hex\": \"1F789\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"157\", \"Dingbat hex\": \"9D\", \"Unicode dec\": \"8857\", \"Unicode hex\": \"2299\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"158\", \"Dingbat hex\": \"9E\", \"Unicode dec\": \"10687\", \"Unicode hex\": \"29BF\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"159\", \"Dingbat hex\": \"9F\", \"Unicode dec\": \"128908\", \"Unicode hex\": \"1F78C\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"160\", \"Dingbat hex\": \"A0\", \"Unicode dec\": \"128909\", \"Unicode hex\": \"1F78D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"161\", \"Dingbat hex\": \"A1\", \"Unicode dec\": \"9726\", \"Unicode hex\": \"25FE\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"162\", \"Dingbat hex\": \"A2\", \"Unicode dec\": \"9632\", \"Unicode hex\": \"25A0\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"163\", \"Dingbat hex\": \"A3\", \"Unicode dec\": \"9633\", \"Unicode hex\": \"25A1\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"164\", \"Dingbat hex\": \"A4\", \"Unicode dec\": \"128913\", \"Unicode hex\": \"1F791\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"165\", \"Dingbat hex\": \"A5\", \"Unicode dec\": \"128914\", \"Unicode hex\": \"1F792\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"166\", \"Dingbat hex\": \"A6\", \"Unicode dec\": \"128915\", \"Unicode hex\": \"1F793\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"167\", \"Dingbat hex\": \"A7\", \"Unicode dec\": \"128916\", \"Unicode hex\": \"1F794\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"168\", \"Dingbat hex\": \"A8\", \"Unicode dec\": \"9635\", \"Unicode hex\": \"25A3\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"169\", \"Dingbat hex\": \"A9\", \"Unicode dec\": \"128917\", \"Unicode hex\": \"1F795\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"170\", \"Dingbat hex\": \"AA\", \"Unicode dec\": \"128918\", \"Unicode hex\": \"1F796\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"171\", \"Dingbat hex\": \"AB\", \"Unicode dec\": \"128919\", \"Unicode hex\": \"1F797\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"172\", \"Dingbat hex\": \"AC\", \"Unicode dec\": \"128920\", \"Unicode hex\": \"1F798\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"173\", \"Dingbat hex\": \"AD\", \"Unicode dec\": \"11049\", \"Unicode hex\": \"2B29\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"174\", \"Dingbat hex\": \"AE\", \"Unicode dec\": \"11045\", \"Unicode hex\": \"2B25\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"175\", \"Dingbat hex\": \"AF\", \"Unicode dec\": \"9671\", \"Unicode hex\": \"25C7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"176\", \"Dingbat hex\": \"B0\", \"Unicode dec\": \"128922\", \"Unicode hex\": \"1F79A\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"177\", \"Dingbat hex\": \"B1\", \"Unicode dec\": \"9672\", \"Unicode hex\": \"25C8\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"178\", \"Dingbat hex\": \"B2\", \"Unicode dec\": \"128923\", \"Unicode hex\": \"1F79B\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"179\", \"Dingbat hex\": \"B3\", \"Unicode dec\": \"128924\", \"Unicode hex\": \"1F79C\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"180\", \"Dingbat hex\": \"B4\", \"Unicode dec\": \"128925\", \"Unicode hex\": \"1F79D\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"181\", \"Dingbat hex\": \"B5\", \"Unicode dec\": \"128926\", \"Unicode hex\": \"1F79E\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"182\", \"Dingbat hex\": \"B6\", \"Unicode dec\": \"11050\", \"Unicode hex\": \"2B2A\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"183\", \"Dingbat hex\": \"B7\", \"Unicode dec\": \"11047\", \"Unicode hex\": \"2B27\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"184\", \"Dingbat hex\": \"B8\", \"Unicode dec\": \"9674\", \"Unicode hex\": \"25CA\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"185\", \"Dingbat hex\": \"B9\", \"Unicode dec\": \"128928\", \"Unicode hex\": \"1F7A0\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"186\", \"Dingbat hex\": \"BA\", \"Unicode dec\": \"9686\", \"Unicode hex\": \"25D6\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"187\", \"Dingbat hex\": \"BB\", \"Unicode dec\": \"9687\", \"Unicode hex\": \"25D7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"188\", \"Dingbat hex\": \"BC\", \"Unicode dec\": \"11210\", \"Unicode hex\": \"2BCA\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"189\", \"Dingbat hex\": \"BD\", \"Unicode dec\": \"11211\", \"Unicode hex\": \"2BCB\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"190\", \"Dingbat hex\": \"BE\", \"Unicode dec\": \"11200\", \"Unicode hex\": \"2BC0\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"191\", \"Dingbat hex\": \"BF\", \"Unicode dec\": \"11201\", \"Unicode hex\": \"2BC1\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"192\", \"Dingbat hex\": \"C0\", \"Unicode dec\": \"11039\", \"Unicode hex\": \"2B1F\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"193\", \"Dingbat hex\": \"C1\", \"Unicode dec\": \"11202\", \"Unicode hex\": \"2BC2\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"194\", \"Dingbat hex\": \"C2\", \"Unicode dec\": \"11043\", \"Unicode hex\": \"2B23\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"195\", \"Dingbat hex\": \"C3\", \"Unicode dec\": \"11042\", \"Unicode hex\": \"2B22\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"196\", \"Dingbat hex\": \"C4\", \"Unicode dec\": \"11203\", \"Unicode hex\": \"2BC3\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"197\", \"Dingbat hex\": \"C5\", \"Unicode dec\": \"11204\", \"Unicode hex\": \"2BC4\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"198\", \"Dingbat hex\": \"C6\", \"Unicode dec\": \"128929\", \"Unicode hex\": \"1F7A1\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"199\", \"Dingbat hex\": \"C7\", \"Unicode dec\": \"128930\", \"Unicode hex\": \"1F7A2\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"200\", \"Dingbat hex\": \"C8\", \"Unicode dec\": \"128931\", \"Unicode hex\": \"1F7A3\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"201\", \"Dingbat hex\": \"C9\", \"Unicode dec\": \"128932\", \"Unicode hex\": \"1F7A4\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"202\", \"Dingbat hex\": \"CA\", \"Unicode dec\": \"128933\", \"Unicode hex\": \"1F7A5\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"203\", \"Dingbat hex\": \"CB\", \"Unicode dec\": \"128934\", \"Unicode hex\": \"1F7A6\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"204\", \"Dingbat hex\": \"CC\", \"Unicode dec\": \"128935\", \"Unicode hex\": \"1F7A7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"205\", \"Dingbat hex\": \"CD\", \"Unicode dec\": \"128936\", \"Unicode hex\": \"1F7A8\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"206\", \"Dingbat hex\": \"CE\", \"Unicode dec\": \"128937\", \"Unicode hex\": \"1F7A9\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"207\", \"Dingbat hex\": \"CF\", \"Unicode dec\": \"128938\", \"Unicode hex\": \"1F7AA\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"208\", \"Dingbat hex\": \"D0\", \"Unicode dec\": \"128939\", \"Unicode hex\": \"1F7AB\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"209\", \"Dingbat hex\": \"D1\", \"Unicode dec\": \"128940\", \"Unicode hex\": \"1F7AC\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"210\", \"Dingbat hex\": \"D2\", \"Unicode dec\": \"128941\", \"Unicode hex\": \"1F7AD\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"211\", \"Dingbat hex\": \"D3\", \"Unicode dec\": \"128942\", \"Unicode hex\": \"1F7AE\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"212\", \"Dingbat hex\": \"D4\", \"Unicode dec\": \"128943\", \"Unicode hex\": \"1F7AF\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"213\", \"Dingbat hex\": \"D5\", \"Unicode dec\": \"128944\", \"Unicode hex\": \"1F7B0\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"214\", \"Dingbat hex\": \"D6\", \"Unicode dec\": \"128945\", \"Unicode hex\": \"1F7B1\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"215\", \"Dingbat hex\": \"D7\", \"Unicode dec\": \"128946\", \"Unicode hex\": \"1F7B2\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"216\", \"Dingbat hex\": \"D8\", \"Unicode dec\": \"128947\", \"Unicode hex\": \"1F7B3\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"217\", \"Dingbat hex\": \"D9\", \"Unicode dec\": \"128948\", \"Unicode hex\": \"1F7B4\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"218\", \"Dingbat hex\": \"DA\", \"Unicode dec\": \"128949\", \"Unicode hex\": \"1F7B5\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"219\", \"Dingbat hex\": \"DB\", \"Unicode dec\": \"128950\", \"Unicode hex\": \"1F7B6\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"220\", \"Dingbat hex\": \"DC\", \"Unicode dec\": \"128951\", \"Unicode hex\": \"1F7B7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"221\", \"Dingbat hex\": \"DD\", \"Unicode dec\": \"128952\", \"Unicode hex\": \"1F7B8\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"222\", \"Dingbat hex\": \"DE\", \"Unicode dec\": \"128953\", \"Unicode hex\": \"1F7B9\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"223\", \"Dingbat hex\": \"DF\", \"Unicode dec\": \"128954\", \"Unicode hex\": \"1F7BA\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"224\", \"Dingbat hex\": \"E0\", \"Unicode dec\": \"128955\", \"Unicode hex\": \"1F7BB\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"225\", \"Dingbat hex\": \"E1\", \"Unicode dec\": \"128956\", \"Unicode hex\": \"1F7BC\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"226\", \"Dingbat hex\": \"E2\", \"Unicode dec\": \"128957\", \"Unicode hex\": \"1F7BD\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"227\", \"Dingbat hex\": \"E3\", \"Unicode dec\": \"128958\", \"Unicode hex\": \"1F7BE\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"228\", \"Dingbat hex\": \"E4\", \"Unicode dec\": \"128959\", \"Unicode hex\": \"1F7BF\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"229\", \"Dingbat hex\": \"E5\", \"Unicode dec\": \"128960\", \"Unicode hex\": \"1F7C0\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"230\", \"Dingbat hex\": \"E6\", \"Unicode dec\": \"128962\", \"Unicode hex\": \"1F7C2\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"231\", \"Dingbat hex\": \"E7\", \"Unicode dec\": \"128964\", \"Unicode hex\": \"1F7C4\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"232\", \"Dingbat hex\": \"E8\", \"Unicode dec\": \"128966\", \"Unicode hex\": \"1F7C6\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"233\", \"Dingbat hex\": \"E9\", \"Unicode dec\": \"128969\", \"Unicode hex\": \"1F7C9\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"234\", \"Dingbat hex\": \"EA\", \"Unicode dec\": \"128970\", \"Unicode hex\": \"1F7CA\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"235\", \"Dingbat hex\": \"EB\", \"Unicode dec\": \"10038\", \"Unicode hex\": \"2736\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"236\", \"Dingbat hex\": \"EC\", \"Unicode dec\": \"128972\", \"Unicode hex\": \"1F7CC\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"237\", \"Dingbat hex\": \"ED\", \"Unicode dec\": \"128974\", \"Unicode hex\": \"1F7CE\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"238\", \"Dingbat hex\": \"EE\", \"Unicode dec\": \"128976\", \"Unicode hex\": \"1F7D0\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"239\", \"Dingbat hex\": \"EF\", \"Unicode dec\": \"128978\", \"Unicode hex\": \"1F7D2\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"240\", \"Dingbat hex\": \"F0\", \"Unicode dec\": \"10041\", \"Unicode hex\": \"2739\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"241\", \"Dingbat hex\": \"F1\", \"Unicode dec\": \"128963\", \"Unicode hex\": \"1F7C3\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"242\", \"Dingbat hex\": \"F2\", \"Unicode dec\": \"128967\", \"Unicode hex\": \"1F7C7\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"243\", \"Dingbat hex\": \"F3\", \"Unicode dec\": \"10031\", \"Unicode hex\": \"272F\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"244\", \"Dingbat hex\": \"F4\", \"Unicode dec\": \"128973\", \"Unicode hex\": \"1F7CD\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"245\", \"Dingbat hex\": \"F5\", \"Unicode dec\": \"128980\", \"Unicode hex\": \"1F7D4\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"246\", \"Dingbat hex\": \"F6\", \"Unicode dec\": \"11212\", \"Unicode hex\": \"2BCC\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"247\", \"Dingbat hex\": \"F7\", \"Unicode dec\": \"11213\", \"Unicode hex\": \"2BCD\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"248\", \"Dingbat hex\": \"F8\", \"Unicode dec\": \"8251\", \"Unicode hex\": \"203B\" },\n    { \"Typeface name\": \"Wingdings 2\", \"Dingbat dec\": \"249\", \"Dingbat hex\": \"F9\", \"Unicode dec\": \"8258\", \"Unicode hex\": \"2042\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"32\", \"Dingbat hex\": \"20\", \"Unicode dec\": \"32\", \"Unicode hex\": \"20\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"33\", \"Dingbat hex\": \"21\", \"Unicode dec\": \"11104\", \"Unicode hex\": \"2B60\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"34\", \"Dingbat hex\": \"22\", \"Unicode dec\": \"11106\", \"Unicode hex\": \"2B62\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"35\", \"Dingbat hex\": \"23\", \"Unicode dec\": \"11105\", \"Unicode hex\": \"2B61\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"36\", \"Dingbat hex\": \"24\", \"Unicode dec\": \"11107\", \"Unicode hex\": \"2B63\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"37\", \"Dingbat hex\": \"25\", \"Unicode dec\": \"11110\", \"Unicode hex\": \"2B66\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"38\", \"Dingbat hex\": \"26\", \"Unicode dec\": \"11111\", \"Unicode hex\": \"2B67\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"39\", \"Dingbat hex\": \"27\", \"Unicode dec\": \"11113\", \"Unicode hex\": \"2B69\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"40\", \"Dingbat hex\": \"28\", \"Unicode dec\": \"11112\", \"Unicode hex\": \"2B68\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"41\", \"Dingbat hex\": \"29\", \"Unicode dec\": \"11120\", \"Unicode hex\": \"2B70\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"42\", \"Dingbat hex\": \"2A\", \"Unicode dec\": \"11122\", \"Unicode hex\": \"2B72\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"43\", \"Dingbat hex\": \"2B\", \"Unicode dec\": \"11121\", \"Unicode hex\": \"2B71\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"44\", \"Dingbat hex\": \"2C\", \"Unicode dec\": \"11123\", \"Unicode hex\": \"2B73\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"45\", \"Dingbat hex\": \"2D\", \"Unicode dec\": \"11126\", \"Unicode hex\": \"2B76\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"46\", \"Dingbat hex\": \"2E\", \"Unicode dec\": \"11128\", \"Unicode hex\": \"2B78\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"47\", \"Dingbat hex\": \"2F\", \"Unicode dec\": \"11131\", \"Unicode hex\": \"2B7B\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"48\", \"Dingbat hex\": \"30\", \"Unicode dec\": \"11133\", \"Unicode hex\": \"2B7D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"49\", \"Dingbat hex\": \"31\", \"Unicode dec\": \"11108\", \"Unicode hex\": \"2B64\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"50\", \"Dingbat hex\": \"32\", \"Unicode dec\": \"11109\", \"Unicode hex\": \"2B65\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"51\", \"Dingbat hex\": \"33\", \"Unicode dec\": \"11114\", \"Unicode hex\": \"2B6A\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"52\", \"Dingbat hex\": \"34\", \"Unicode dec\": \"11116\", \"Unicode hex\": \"2B6C\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"53\", \"Dingbat hex\": \"35\", \"Unicode dec\": \"11115\", \"Unicode hex\": \"2B6B\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"54\", \"Dingbat hex\": \"36\", \"Unicode dec\": \"11117\", \"Unicode hex\": \"2B6D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"55\", \"Dingbat hex\": \"37\", \"Unicode dec\": \"11085\", \"Unicode hex\": \"2B4D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"56\", \"Dingbat hex\": \"38\", \"Unicode dec\": \"11168\", \"Unicode hex\": \"2BA0\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"57\", \"Dingbat hex\": \"39\", \"Unicode dec\": \"11169\", \"Unicode hex\": \"2BA1\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"58\", \"Dingbat hex\": \"3A\", \"Unicode dec\": \"11170\", \"Unicode hex\": \"2BA2\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"59\", \"Dingbat hex\": \"3B\", \"Unicode dec\": \"11171\", \"Unicode hex\": \"2BA3\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"60\", \"Dingbat hex\": \"3C\", \"Unicode dec\": \"11172\", \"Unicode hex\": \"2BA4\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"61\", \"Dingbat hex\": \"3D\", \"Unicode dec\": \"11173\", \"Unicode hex\": \"2BA5\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"62\", \"Dingbat hex\": \"3E\", \"Unicode dec\": \"11174\", \"Unicode hex\": \"2BA6\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"63\", \"Dingbat hex\": \"3F\", \"Unicode dec\": \"11175\", \"Unicode hex\": \"2BA7\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"64\", \"Dingbat hex\": \"40\", \"Unicode dec\": \"11152\", \"Unicode hex\": \"2B90\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"65\", \"Dingbat hex\": \"41\", \"Unicode dec\": \"11153\", \"Unicode hex\": \"2B91\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"66\", \"Dingbat hex\": \"42\", \"Unicode dec\": \"11154\", \"Unicode hex\": \"2B92\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"67\", \"Dingbat hex\": \"43\", \"Unicode dec\": \"11155\", \"Unicode hex\": \"2B93\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"68\", \"Dingbat hex\": \"44\", \"Unicode dec\": \"11136\", \"Unicode hex\": \"2B80\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"69\", \"Dingbat hex\": \"45\", \"Unicode dec\": \"11139\", \"Unicode hex\": \"2B83\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"70\", \"Dingbat hex\": \"46\", \"Unicode dec\": \"11134\", \"Unicode hex\": \"2B7E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"71\", \"Dingbat hex\": \"47\", \"Unicode dec\": \"11135\", \"Unicode hex\": \"2B7F\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"72\", \"Dingbat hex\": \"48\", \"Unicode dec\": \"11140\", \"Unicode hex\": \"2B84\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"73\", \"Dingbat hex\": \"49\", \"Unicode dec\": \"11142\", \"Unicode hex\": \"2B86\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"74\", \"Dingbat hex\": \"4A\", \"Unicode dec\": \"11141\", \"Unicode hex\": \"2B85\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"75\", \"Dingbat hex\": \"4B\", \"Unicode dec\": \"11143\", \"Unicode hex\": \"2B87\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"76\", \"Dingbat hex\": \"4C\", \"Unicode dec\": \"11151\", \"Unicode hex\": \"2B8F\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"77\", \"Dingbat hex\": \"4D\", \"Unicode dec\": \"11149\", \"Unicode hex\": \"2B8D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"78\", \"Dingbat hex\": \"4E\", \"Unicode dec\": \"11150\", \"Unicode hex\": \"2B8E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"79\", \"Dingbat hex\": \"4F\", \"Unicode dec\": \"11148\", \"Unicode hex\": \"2B8C\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"80\", \"Dingbat hex\": \"50\", \"Unicode dec\": \"11118\", \"Unicode hex\": \"2B6E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"81\", \"Dingbat hex\": \"51\", \"Unicode dec\": \"11119\", \"Unicode hex\": \"2B6F\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"82\", \"Dingbat hex\": \"52\", \"Unicode dec\": \"9099\", \"Unicode hex\": \"238B\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"83\", \"Dingbat hex\": \"53\", \"Unicode dec\": \"8996\", \"Unicode hex\": \"2324\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"84\", \"Dingbat hex\": \"54\", \"Unicode dec\": \"8963\", \"Unicode hex\": \"2303\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"85\", \"Dingbat hex\": \"55\", \"Unicode dec\": \"8997\", \"Unicode hex\": \"2325\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"86\", \"Dingbat hex\": \"56\", \"Unicode dec\": \"9251\", \"Unicode hex\": \"2423\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"87\", \"Dingbat hex\": \"57\", \"Unicode dec\": \"9085\", \"Unicode hex\": \"237D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"88\", \"Dingbat hex\": \"58\", \"Unicode dec\": \"8682\", \"Unicode hex\": \"21EA\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"89\", \"Dingbat hex\": \"59\", \"Unicode dec\": \"11192\", \"Unicode hex\": \"2BB8\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"90\", \"Dingbat hex\": \"5A\", \"Unicode dec\": \"129184\", \"Unicode hex\": \"1F8A0\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"91\", \"Dingbat hex\": \"5B\", \"Unicode dec\": \"129185\", \"Unicode hex\": \"1F8A1\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"92\", \"Dingbat hex\": \"5C\", \"Unicode dec\": \"129186\", \"Unicode hex\": \"1F8A2\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"93\", \"Dingbat hex\": \"5D\", \"Unicode dec\": \"129187\", \"Unicode hex\": \"1F8A3\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"94\", \"Dingbat hex\": \"5E\", \"Unicode dec\": \"129188\", \"Unicode hex\": \"1F8A4\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"95\", \"Dingbat hex\": \"5F\", \"Unicode dec\": \"129189\", \"Unicode hex\": \"1F8A5\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"96\", \"Dingbat hex\": \"60\", \"Unicode dec\": \"129190\", \"Unicode hex\": \"1F8A6\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"97\", \"Dingbat hex\": \"61\", \"Unicode dec\": \"129191\", \"Unicode hex\": \"1F8A7\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"98\", \"Dingbat hex\": \"62\", \"Unicode dec\": \"129192\", \"Unicode hex\": \"1F8A8\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"99\", \"Dingbat hex\": \"63\", \"Unicode dec\": \"129193\", \"Unicode hex\": \"1F8A9\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"100\", \"Dingbat hex\": \"64\", \"Unicode dec\": \"129194\", \"Unicode hex\": \"1F8AA\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"101\", \"Dingbat hex\": \"65\", \"Unicode dec\": \"129195\", \"Unicode hex\": \"1F8AB\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"102\", \"Dingbat hex\": \"66\", \"Unicode dec\": \"129104\", \"Unicode hex\": \"1F850\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"103\", \"Dingbat hex\": \"67\", \"Unicode dec\": \"129106\", \"Unicode hex\": \"1F852\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"104\", \"Dingbat hex\": \"68\", \"Unicode dec\": \"129105\", \"Unicode hex\": \"1F851\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"105\", \"Dingbat hex\": \"69\", \"Unicode dec\": \"129107\", \"Unicode hex\": \"1F853\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"106\", \"Dingbat hex\": \"6A\", \"Unicode dec\": \"129108\", \"Unicode hex\": \"1F854\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"107\", \"Dingbat hex\": \"6B\", \"Unicode dec\": \"129109\", \"Unicode hex\": \"1F855\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"108\", \"Dingbat hex\": \"6C\", \"Unicode dec\": \"129111\", \"Unicode hex\": \"1F857\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"109\", \"Dingbat hex\": \"6D\", \"Unicode dec\": \"129110\", \"Unicode hex\": \"1F856\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"110\", \"Dingbat hex\": \"6E\", \"Unicode dec\": \"129112\", \"Unicode hex\": \"1F858\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"111\", \"Dingbat hex\": \"6F\", \"Unicode dec\": \"129113\", \"Unicode hex\": \"1F859\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"112\", \"Dingbat hex\": \"70\", \"Unicode dec\": \"9650\", \"Unicode hex\": \"25B2\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"113\", \"Dingbat hex\": \"71\", \"Unicode dec\": \"9660\", \"Unicode hex\": \"25BC\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"114\", \"Dingbat hex\": \"72\", \"Unicode dec\": \"9651\", \"Unicode hex\": \"25B3\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"115\", \"Dingbat hex\": \"73\", \"Unicode dec\": \"9661\", \"Unicode hex\": \"25BD\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"116\", \"Dingbat hex\": \"74\", \"Unicode dec\": \"9664\", \"Unicode hex\": \"25C0\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"117\", \"Dingbat hex\": \"75\", \"Unicode dec\": \"9654\", \"Unicode hex\": \"25B6\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"118\", \"Dingbat hex\": \"76\", \"Unicode dec\": \"9665\", \"Unicode hex\": \"25C1\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"119\", \"Dingbat hex\": \"77\", \"Unicode dec\": \"9655\", \"Unicode hex\": \"25B7\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"120\", \"Dingbat hex\": \"78\", \"Unicode dec\": \"9699\", \"Unicode hex\": \"25E3\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"121\", \"Dingbat hex\": \"79\", \"Unicode dec\": \"9698\", \"Unicode hex\": \"25E2\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"122\", \"Dingbat hex\": \"7A\", \"Unicode dec\": \"9700\", \"Unicode hex\": \"25E4\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"123\", \"Dingbat hex\": \"7B\", \"Unicode dec\": \"9701\", \"Unicode hex\": \"25E5\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"124\", \"Dingbat hex\": \"7C\", \"Unicode dec\": \"128896\", \"Unicode hex\": \"1F780\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"125\", \"Dingbat hex\": \"7D\", \"Unicode dec\": \"128898\", \"Unicode hex\": \"1F782\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"126\", \"Dingbat hex\": \"7E\", \"Unicode dec\": \"128897\", \"Unicode hex\": \"1F781\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"128\", \"Dingbat hex\": \"80\", \"Unicode dec\": \"128899\", \"Unicode hex\": \"1F783\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"129\", \"Dingbat hex\": \"81\", \"Unicode dec\": \"11205\", \"Unicode hex\": \"2BC5\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"130\", \"Dingbat hex\": \"82\", \"Unicode dec\": \"11206\", \"Unicode hex\": \"2BC6\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"131\", \"Dingbat hex\": \"83\", \"Unicode dec\": \"11207\", \"Unicode hex\": \"2BC7\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"132\", \"Dingbat hex\": \"84\", \"Unicode dec\": \"11208\", \"Unicode hex\": \"2BC8\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"133\", \"Dingbat hex\": \"85\", \"Unicode dec\": \"11164\", \"Unicode hex\": \"2B9C\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"134\", \"Dingbat hex\": \"86\", \"Unicode dec\": \"11166\", \"Unicode hex\": \"2B9E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"135\", \"Dingbat hex\": \"87\", \"Unicode dec\": \"11165\", \"Unicode hex\": \"2B9D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"136\", \"Dingbat hex\": \"88\", \"Unicode dec\": \"11167\", \"Unicode hex\": \"2B9F\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"137\", \"Dingbat hex\": \"89\", \"Unicode dec\": \"129040\", \"Unicode hex\": \"1F810\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"138\", \"Dingbat hex\": \"8A\", \"Unicode dec\": \"129042\", \"Unicode hex\": \"1F812\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"139\", \"Dingbat hex\": \"8B\", \"Unicode dec\": \"129041\", \"Unicode hex\": \"1F811\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"140\", \"Dingbat hex\": \"8C\", \"Unicode dec\": \"129043\", \"Unicode hex\": \"1F813\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"141\", \"Dingbat hex\": \"8D\", \"Unicode dec\": \"129044\", \"Unicode hex\": \"1F814\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"142\", \"Dingbat hex\": \"8E\", \"Unicode dec\": \"129046\", \"Unicode hex\": \"1F816\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"143\", \"Dingbat hex\": \"8F\", \"Unicode dec\": \"129045\", \"Unicode hex\": \"1F815\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"144\", \"Dingbat hex\": \"90\", \"Unicode dec\": \"129047\", \"Unicode hex\": \"1F817\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"145\", \"Dingbat hex\": \"91\", \"Unicode dec\": \"129048\", \"Unicode hex\": \"1F818\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"146\", \"Dingbat hex\": \"92\", \"Unicode dec\": \"129050\", \"Unicode hex\": \"1F81A\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"147\", \"Dingbat hex\": \"93\", \"Unicode dec\": \"129049\", \"Unicode hex\": \"1F819\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"148\", \"Dingbat hex\": \"94\", \"Unicode dec\": \"129051\", \"Unicode hex\": \"1F81B\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"149\", \"Dingbat hex\": \"95\", \"Unicode dec\": \"129052\", \"Unicode hex\": \"1F81C\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"150\", \"Dingbat hex\": \"96\", \"Unicode dec\": \"129054\", \"Unicode hex\": \"1F81E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"151\", \"Dingbat hex\": \"97\", \"Unicode dec\": \"129053\", \"Unicode hex\": \"1F81D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"152\", \"Dingbat hex\": \"98\", \"Unicode dec\": \"129055\", \"Unicode hex\": \"1F81F\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"153\", \"Dingbat hex\": \"99\", \"Unicode dec\": \"129024\", \"Unicode hex\": \"1F800\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"154\", \"Dingbat hex\": \"9A\", \"Unicode dec\": \"129026\", \"Unicode hex\": \"1F802\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"155\", \"Dingbat hex\": \"9B\", \"Unicode dec\": \"129025\", \"Unicode hex\": \"1F801\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"156\", \"Dingbat hex\": \"9C\", \"Unicode dec\": \"129027\", \"Unicode hex\": \"1F803\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"157\", \"Dingbat hex\": \"9D\", \"Unicode dec\": \"129028\", \"Unicode hex\": \"1F804\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"158\", \"Dingbat hex\": \"9E\", \"Unicode dec\": \"129030\", \"Unicode hex\": \"1F806\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"159\", \"Dingbat hex\": \"9F\", \"Unicode dec\": \"129029\", \"Unicode hex\": \"1F805\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"160\", \"Dingbat hex\": \"A0\", \"Unicode dec\": \"129031\", \"Unicode hex\": \"1F807\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"161\", \"Dingbat hex\": \"A1\", \"Unicode dec\": \"129032\", \"Unicode hex\": \"1F808\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"162\", \"Dingbat hex\": \"A2\", \"Unicode dec\": \"129034\", \"Unicode hex\": \"1F80A\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"163\", \"Dingbat hex\": \"A3\", \"Unicode dec\": \"129033\", \"Unicode hex\": \"1F809\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"164\", \"Dingbat hex\": \"A4\", \"Unicode dec\": \"129035\", \"Unicode hex\": \"1F80B\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"165\", \"Dingbat hex\": \"A5\", \"Unicode dec\": \"129056\", \"Unicode hex\": \"1F820\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"166\", \"Dingbat hex\": \"A6\", \"Unicode dec\": \"129058\", \"Unicode hex\": \"1F822\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"167\", \"Dingbat hex\": \"A7\", \"Unicode dec\": \"129060\", \"Unicode hex\": \"1F824\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"168\", \"Dingbat hex\": \"A8\", \"Unicode dec\": \"129062\", \"Unicode hex\": \"1F826\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"169\", \"Dingbat hex\": \"A9\", \"Unicode dec\": \"129064\", \"Unicode hex\": \"1F828\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"170\", \"Dingbat hex\": \"AA\", \"Unicode dec\": \"129066\", \"Unicode hex\": \"1F82A\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"171\", \"Dingbat hex\": \"AB\", \"Unicode dec\": \"129068\", \"Unicode hex\": \"1F82C\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"172\", \"Dingbat hex\": \"AC\", \"Unicode dec\": \"129180\", \"Unicode hex\": \"1F89C\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"173\", \"Dingbat hex\": \"AD\", \"Unicode dec\": \"129181\", \"Unicode hex\": \"1F89D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"174\", \"Dingbat hex\": \"AE\", \"Unicode dec\": \"129182\", \"Unicode hex\": \"1F89E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"175\", \"Dingbat hex\": \"AF\", \"Unicode dec\": \"129183\", \"Unicode hex\": \"1F89F\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"176\", \"Dingbat hex\": \"B0\", \"Unicode dec\": \"129070\", \"Unicode hex\": \"1F82E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"177\", \"Dingbat hex\": \"B1\", \"Unicode dec\": \"129072\", \"Unicode hex\": \"1F830\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"178\", \"Dingbat hex\": \"B2\", \"Unicode dec\": \"129074\", \"Unicode hex\": \"1F832\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"179\", \"Dingbat hex\": \"B3\", \"Unicode dec\": \"129076\", \"Unicode hex\": \"1F834\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"180\", \"Dingbat hex\": \"B4\", \"Unicode dec\": \"129078\", \"Unicode hex\": \"1F836\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"181\", \"Dingbat hex\": \"B5\", \"Unicode dec\": \"129080\", \"Unicode hex\": \"1F838\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"182\", \"Dingbat hex\": \"B6\", \"Unicode dec\": \"129082\", \"Unicode hex\": \"1F83A\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"183\", \"Dingbat hex\": \"B7\", \"Unicode dec\": \"129081\", \"Unicode hex\": \"1F839\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"184\", \"Dingbat hex\": \"B8\", \"Unicode dec\": \"129083\", \"Unicode hex\": \"1F83B\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"185\", \"Dingbat hex\": \"B9\", \"Unicode dec\": \"129176\", \"Unicode hex\": \"1F898\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"186\", \"Dingbat hex\": \"BA\", \"Unicode dec\": \"129178\", \"Unicode hex\": \"1F89A\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"187\", \"Dingbat hex\": \"BB\", \"Unicode dec\": \"129177\", \"Unicode hex\": \"1F899\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"188\", \"Dingbat hex\": \"BC\", \"Unicode dec\": \"129179\", \"Unicode hex\": \"1F89B\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"189\", \"Dingbat hex\": \"BD\", \"Unicode dec\": \"129084\", \"Unicode hex\": \"1F83C\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"190\", \"Dingbat hex\": \"BE\", \"Unicode dec\": \"129086\", \"Unicode hex\": \"1F83E\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"191\", \"Dingbat hex\": \"BF\", \"Unicode dec\": \"129085\", \"Unicode hex\": \"1F83D\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"192\", \"Dingbat hex\": \"C0\", \"Unicode dec\": \"129087\", \"Unicode hex\": \"1F83F\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"193\", \"Dingbat hex\": \"C1\", \"Unicode dec\": \"129088\", \"Unicode hex\": \"1F840\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"194\", \"Dingbat hex\": \"C2\", \"Unicode dec\": \"129090\", \"Unicode hex\": \"1F842\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"195\", \"Dingbat hex\": \"C3\", \"Unicode dec\": \"129089\", \"Unicode hex\": \"1F841\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"196\", \"Dingbat hex\": \"C4\", \"Unicode dec\": \"129091\", \"Unicode hex\": \"1F843\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"197\", \"Dingbat hex\": \"C5\", \"Unicode dec\": \"129092\", \"Unicode hex\": \"1F844\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"198\", \"Dingbat hex\": \"C6\", \"Unicode dec\": \"129094\", \"Unicode hex\": \"1F846\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"199\", \"Dingbat hex\": \"C7\", \"Unicode dec\": \"129093\", \"Unicode hex\": \"1F845\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"200\", \"Dingbat hex\": \"C8\", \"Unicode dec\": \"129095\", \"Unicode hex\": \"1F847\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"201\", \"Dingbat hex\": \"C9\", \"Unicode dec\": \"11176\", \"Unicode hex\": \"2BA8\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"202\", \"Dingbat hex\": \"CA\", \"Unicode dec\": \"11177\", \"Unicode hex\": \"2BA9\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"203\", \"Dingbat hex\": \"CB\", \"Unicode dec\": \"11178\", \"Unicode hex\": \"2BAA\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"204\", \"Dingbat hex\": \"CC\", \"Unicode dec\": \"11179\", \"Unicode hex\": \"2BAB\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"205\", \"Dingbat hex\": \"CD\", \"Unicode dec\": \"11180\", \"Unicode hex\": \"2BAC\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"206\", \"Dingbat hex\": \"CE\", \"Unicode dec\": \"11181\", \"Unicode hex\": \"2BAD\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"207\", \"Dingbat hex\": \"CF\", \"Unicode dec\": \"11182\", \"Unicode hex\": \"2BAE\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"208\", \"Dingbat hex\": \"D0\", \"Unicode dec\": \"11183\", \"Unicode hex\": \"2BAF\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"209\", \"Dingbat hex\": \"D1\", \"Unicode dec\": \"129120\", \"Unicode hex\": \"1F860\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"210\", \"Dingbat hex\": \"D2\", \"Unicode dec\": \"129122\", \"Unicode hex\": \"1F862\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"211\", \"Dingbat hex\": \"D3\", \"Unicode dec\": \"129121\", \"Unicode hex\": \"1F861\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"212\", \"Dingbat hex\": \"D4\", \"Unicode dec\": \"129123\", \"Unicode hex\": \"1F863\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"213\", \"Dingbat hex\": \"D5\", \"Unicode dec\": \"129124\", \"Unicode hex\": \"1F864\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"214\", \"Dingbat hex\": \"D6\", \"Unicode dec\": \"129125\", \"Unicode hex\": \"1F865\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"215\", \"Dingbat hex\": \"D7\", \"Unicode dec\": \"129127\", \"Unicode hex\": \"1F867\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"216\", \"Dingbat hex\": \"D8\", \"Unicode dec\": \"129126\", \"Unicode hex\": \"1F866\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"217\", \"Dingbat hex\": \"D9\", \"Unicode dec\": \"129136\", \"Unicode hex\": \"1F870\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"218\", \"Dingbat hex\": \"DA\", \"Unicode dec\": \"129138\", \"Unicode hex\": \"1F872\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"219\", \"Dingbat hex\": \"DB\", \"Unicode dec\": \"129137\", \"Unicode hex\": \"1F871\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"220\", \"Dingbat hex\": \"DC\", \"Unicode dec\": \"129139\", \"Unicode hex\": \"1F873\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"221\", \"Dingbat hex\": \"DD\", \"Unicode dec\": \"129140\", \"Unicode hex\": \"1F874\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"222\", \"Dingbat hex\": \"DE\", \"Unicode dec\": \"129141\", \"Unicode hex\": \"1F875\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"223\", \"Dingbat hex\": \"DF\", \"Unicode dec\": \"129143\", \"Unicode hex\": \"1F877\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"224\", \"Dingbat hex\": \"E0\", \"Unicode dec\": \"129142\", \"Unicode hex\": \"1F876\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"225\", \"Dingbat hex\": \"E1\", \"Unicode dec\": \"129152\", \"Unicode hex\": \"1F880\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"226\", \"Dingbat hex\": \"E2\", \"Unicode dec\": \"129154\", \"Unicode hex\": \"1F882\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"227\", \"Dingbat hex\": \"E3\", \"Unicode dec\": \"129153\", \"Unicode hex\": \"1F881\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"228\", \"Dingbat hex\": \"E4\", \"Unicode dec\": \"129155\", \"Unicode hex\": \"1F883\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"229\", \"Dingbat hex\": \"E5\", \"Unicode dec\": \"129156\", \"Unicode hex\": \"1F884\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"230\", \"Dingbat hex\": \"E6\", \"Unicode dec\": \"129157\", \"Unicode hex\": \"1F885\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"231\", \"Dingbat hex\": \"E7\", \"Unicode dec\": \"129159\", \"Unicode hex\": \"1F887\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"232\", \"Dingbat hex\": \"E8\", \"Unicode dec\": \"129158\", \"Unicode hex\": \"1F886\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"233\", \"Dingbat hex\": \"E9\", \"Unicode dec\": \"129168\", \"Unicode hex\": \"1F890\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"234\", \"Dingbat hex\": \"EA\", \"Unicode dec\": \"129170\", \"Unicode hex\": \"1F892\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"235\", \"Dingbat hex\": \"EB\", \"Unicode dec\": \"129169\", \"Unicode hex\": \"1F891\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"236\", \"Dingbat hex\": \"EC\", \"Unicode dec\": \"129171\", \"Unicode hex\": \"1F893\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"237\", \"Dingbat hex\": \"ED\", \"Unicode dec\": \"129172\", \"Unicode hex\": \"1F894\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"238\", \"Dingbat hex\": \"EE\", \"Unicode dec\": \"129174\", \"Unicode hex\": \"1F896\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"239\", \"Dingbat hex\": \"EF\", \"Unicode dec\": \"129173\", \"Unicode hex\": \"1F895\" },\n    { \"Typeface name\": \"Wingdings 3\", \"Dingbat dec\": \"240\", \"Dingbat hex\": \"F0\", \"Unicode dec\": \"129175\", \"Unicode hex\": \"1F897\" },\n];\nexports.default = dingbats;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,IAAI,WAAW;IACX;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAChH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACpH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAM,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAM;IACnH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAO,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAU,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACrH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IAClH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACtH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IACzH;QAAE,iBAAiB;QAAY,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAY,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IACnH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC1H;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACvH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACxH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAa,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IACrH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAM,eAAe;IAAK;IACrH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IACzH;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAM,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC5H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAQ,eAAe;IAAO;IAC1H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAS,eAAe;IAAO;IAC3H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;IAC7H;QAAE,iBAAiB;QAAe,eAAe;QAAO,eAAe;QAAM,eAAe;QAAU,eAAe;IAAQ;CAChI;AACD,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dingbat-to-unicode/dist/index.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hex = exports.dec = exports.codePoint = void 0;\nvar dingbats_1 = __importDefault(require(\"./dingbats\"));\nvar dingbatsByCodePoint = {};\nvar fromCodePoint = String.fromCodePoint ? String.fromCodePoint : fromCodePointPolyfill;\nfor (var _i = 0, dingbats_2 = dingbats_1.default; _i < dingbats_2.length; _i++) {\n    var dingbat = dingbats_2[_i];\n    var codePoint_1 = parseInt(dingbat[\"Unicode dec\"], 10);\n    var scalarValue = {\n        codePoint: codePoint_1,\n        string: fromCodePoint(codePoint_1),\n    };\n    dingbatsByCodePoint[dingbat[\"Typeface name\"].toUpperCase() + \"_\" + dingbat[\"Dingbat dec\"]] = scalarValue;\n}\nfunction codePoint(typeface, codePoint) {\n    return dingbatsByCodePoint[typeface.toUpperCase() + \"_\" + codePoint];\n}\nexports.codePoint = codePoint;\nfunction dec(typeface, dec) {\n    return codePoint(typeface, parseInt(dec, 10));\n}\nexports.dec = dec;\nfunction hex(typeface, hex) {\n    return codePoint(typeface, parseInt(hex, 16));\n}\nexports.hex = hex;\nfunction fromCodePointPolyfill(codePoint) {\n    if (codePoint <= 0xFFFF) {\n        // BMP\n        return String.fromCharCode(codePoint);\n    }\n    else {\n        // Astral\n        // https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n        var highSurrogate = Math.floor((codePoint - 0x10000) / 0x400) + 0xD800;\n        var lowSurrogate = (codePoint - 0x10000) % 0x400 + 0xDC00;\n        return String.fromCharCode(highSurrogate, lowSurrogate);\n    }\n}\n;\n"], "names": [], "mappings": "AAAA;AACA,IAAI,kBAAkB,AAAC,IAAI,IAAI,IAAI,CAAC,eAAe,IAAK,SAAU,GAAG;IACjE,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,WAAW;IAAI;AAC5D;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,QAAQ,SAAS,GAAG,KAAK;AACrD,IAAI,aAAa;AACjB,IAAI,sBAAsB,CAAC;AAC3B,IAAI,gBAAgB,OAAO,aAAa,GAAG,OAAO,aAAa,GAAG;AAClE,IAAK,IAAI,KAAK,GAAG,aAAa,WAAW,OAAO,EAAE,KAAK,WAAW,MAAM,EAAE,KAAM;IAC5E,IAAI,UAAU,UAAU,CAAC,GAAG;IAC5B,IAAI,cAAc,SAAS,OAAO,CAAC,cAAc,EAAE;IACnD,IAAI,cAAc;QACd,WAAW;QACX,QAAQ,cAAc;IAC1B;IACA,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,KAAK,MAAM,OAAO,CAAC,cAAc,CAAC,GAAG;AACjG;AACA,SAAS,UAAU,QAAQ,EAAE,SAAS;IAClC,OAAO,mBAAmB,CAAC,SAAS,WAAW,KAAK,MAAM,UAAU;AACxE;AACA,QAAQ,SAAS,GAAG;AACpB,SAAS,IAAI,QAAQ,EAAE,GAAG;IACtB,OAAO,UAAU,UAAU,SAAS,KAAK;AAC7C;AACA,QAAQ,GAAG,GAAG;AACd,SAAS,IAAI,QAAQ,EAAE,GAAG;IACtB,OAAO,UAAU,UAAU,SAAS,KAAK;AAC7C;AACA,QAAQ,GAAG,GAAG;AACd,SAAS,sBAAsB,SAAS;IACpC,IAAI,aAAa,QAAQ;QACrB,MAAM;QACN,OAAO,OAAO,YAAY,CAAC;IAC/B,OACK;QACD,SAAS;QACT,wEAAwE;QACxE,IAAI,gBAAgB,KAAK,KAAK,CAAC,CAAC,YAAY,OAAO,IAAI,SAAS;QAChE,IAAI,eAAe,CAAC,YAAY,OAAO,IAAI,QAAQ;QACnD,OAAO,OAAO,YAAY,CAAC,eAAe;IAC9C;AACJ", "ignoreList": [0], "debugId": null}}]}