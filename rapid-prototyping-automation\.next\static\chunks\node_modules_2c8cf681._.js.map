{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-selection/src/selectAll.js"], "sourcesContent": ["import array from \"./array.js\";\nimport {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([document.querySelectorAll(selector)], [document.documentElement])\n      : new Selection([array(selector)], root);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEe,wCAAS,QAAQ;IAC9B,OAAO,OAAO,aAAa,WACrB,IAAI,+JAAA,CAAA,YAAS,CAAC;QAAC,SAAS,gBAAgB,CAAC;KAAU,EAAE;QAAC,SAAS,eAAe;KAAC,IAC/E,IAAI,+JAAA,CAAA,YAAS,CAAC;QAAC,CAAA,GAAA,kJAAA,CAAA,UAAK,AAAD,EAAE;KAAU,EAAE,+JAAA,CAAA,OAAI;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/array.js"], "sourcesContent": ["export var slice = Array.prototype.slice;\n\nexport default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAI,QAAQ,MAAM,SAAS,CAAC,KAAK;AAEzB,wCAAS,CAAC;IACvB,OAAO,OAAO,MAAM,YAAY,YAAY,IACxC,EAAE,0CAA0C;OAC5C,MAAM,IAAI,CAAC,IAAI,+CAA+C;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/constant.js"], "sourcesContent": ["export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC;IACvB,OAAO,SAAS;QACd,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/path.js"], "sourcesContent": ["import {Path} from \"d3-path\";\n\nexport function withPath(shape) {\n  let digits = 3;\n\n  shape.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n\n  return () => new Path(digits);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,SAAS,KAAK;IAC5B,IAAI,SAAS;IAEb,MAAM,MAAM,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO;QAC9B,IAAI,KAAK,MAAM;YACb,SAAS;QACX,OAAO;YACL,MAAM,IAAI,KAAK,KAAK,CAAC;YACrB,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,GAAG;YAC1D,SAAS;QACX;QACA,OAAO;IACT;IAEA,OAAO,IAAM,IAAI,4IAAA,CAAA,OAAI,CAAC;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/point.js"], "sourcesContent": ["export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,EAAE,CAAC;IACjB,OAAO,CAAC,CAAC,EAAE;AACb;AAEO,SAAS,EAAE,CAAC;IACjB,OAAO,CAAC,CAAC,EAAE;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/line.js"], "sourcesContent": ["import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x, y) {\n  var defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(line);\n\n  x = typeof x === \"function\" ? x : (x === undefined) ? pointX : constant(x);\n  y = typeof y === \"function\" ? y : (y === undefined) ? pointY : constant(y);\n\n  function line(data) {\n    var i,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer;\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) output.lineStart();\n        else output.lineEnd();\n      }\n      if (defined0) output.point(+x(d, i, data), +y(d, i, data));\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  line.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), line) : x;\n  };\n\n  line.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), line) : y;\n  };\n\n  line.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), line) : defined;\n  };\n\n  line.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), line) : curve;\n  };\n\n  line.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), line) : context;\n  };\n\n  return line;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,OACnB,UAAU,MACV,QAAQ,wJAAA,CAAA,UAAW,EACnB,SAAS,MACT,OAAO,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE;IAEpB,IAAI,OAAO,MAAM,aAAa,IAAI,AAAC,MAAM,YAAa,8IAAA,CAAA,IAAM,GAAG,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE;IACxE,IAAI,OAAO,MAAM,aAAa,IAAI,AAAC,MAAM,YAAa,8IAAA,CAAA,IAAM,GAAG,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE;IAExE,SAAS,KAAK,IAAI;QAChB,IAAI,GACA,IAAI,CAAC,OAAO,CAAA,GAAA,8IAAA,CAAA,UAAK,AAAD,EAAE,KAAK,EAAE,MAAM,EAC/B,GACA,WAAW,OACX;QAEJ,IAAI,WAAW,MAAM,SAAS,MAAM,SAAS;QAE7C,IAAK,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;YACvB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,EAAE,EAAE,GAAG,KAAK,MAAM,UAAU;gBAC1D,IAAI,WAAW,CAAC,UAAU,OAAO,SAAS;qBACrC,OAAO,OAAO;YACrB;YACA,IAAI,UAAU,OAAO,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG;QACtD;QAEA,IAAI,QAAQ,OAAO,SAAS,MAAM,SAAS,MAAM;IACnD;IAEA,KAAK,CAAC,GAAG,SAAS,CAAC;QACjB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,IAAI,IAAI;IACrF;IAEA,KAAK,CAAC,GAAG,SAAS,CAAC;QACjB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,IAAI,IAAI;IACrF;IAEA,KAAK,OAAO,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,UAAU,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI;IAC5F;IAEA,KAAK,KAAK,GAAG,SAAS,CAAC;QACrB,OAAO,UAAU,MAAM,GAAG,CAAC,QAAQ,GAAG,WAAW,QAAQ,CAAC,SAAS,MAAM,QAAQ,GAAG,IAAI,IAAI;IAC9F;IAEA,KAAK,OAAO,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,KAAK,OAAO,UAAU,SAAS,OAAO,SAAS,MAAM,UAAU,IAAI,IAAI,IAAI;IACxG;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-path/src/path.js"], "sourcesContent": ["const pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction append(strings) {\n  this._ += strings[0];\n  for (let i = 1, n = strings.length; i < n; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  let d = Math.floor(digits);\n  if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  const k = 10 ** d;\n  return function(strings) {\n    this._ += strings[0];\n    for (let i = 1, n = strings.length; i < n; ++i) {\n      this._ += Math.round(arguments[i] * k) / k + strings[i];\n    }\n  };\n}\n\nexport class Path {\n  constructor(digits) {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n    this._append = digits == null ? append : appendRound(digits);\n  }\n  moveTo(x, y) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._append`Z`;\n    }\n  }\n  lineTo(x, y) {\n    this._append`L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arcTo(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._append`M${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._append`L${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      let x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;\n      }\n\n      this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;\n    }\n  }\n  arc(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._append`M${x0},${y0}`;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._append`L${x0},${y0}`;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;\n    }\n  }\n  rect(x, y, w, h) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;\n  }\n  toString() {\n    return this._;\n  }\n}\n\nexport function path() {\n  return new Path;\n}\n\n// Allow instanceof d3.path\npath.prototype = Path.prototype;\n\nexport function pathRound(digits = 3) {\n  return new Path(+digits);\n}\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,KAAK,KAAK,EAAE,EACd,MAAM,IAAI,IACV,UAAU,MACV,aAAa,MAAM;AAEvB,SAAS,OAAO,OAAO;IACrB,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,EAAE;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;QAC9C,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IACrC;AACF;AAEA,SAAS,YAAY,MAAM;IACzB,IAAI,IAAI,KAAK,KAAK,CAAC;IACnB,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;IAC1D,IAAI,IAAI,IAAI,OAAO;IACnB,MAAM,IAAI,MAAM;IAChB,OAAO,SAAS,OAAO;QACrB,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,EAAE;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;YAC9C,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,KAAK,IAAI,OAAO,CAAC,EAAE;QACzD;IACF;AACF;AAEO,MAAM;IACX,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GACnB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,yBAAyB;QACrD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,OAAO,GAAG,UAAU,OAAO,SAAS,YAAY;IACvD;IACA,OAAO,CAAC,EAAE,CAAC,EAAE;QACX,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IACxE;IACA,YAAY;QACV,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM;YACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;YACxC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QACjB;IACF;IACA,OAAO,CAAC,EAAE,CAAC,EAAE;QACX,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IAClD;IACA,iBAAiB,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC7B,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IAChE;IACA,cAAc,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAClC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IAC9E;IACA,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;QACvB,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;QAE7C,iCAAiC;QACjC,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,GAAG;QAElD,IAAI,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;QAE9B,uCAAuC;QACvC,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM;YACrB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAClD,OAGK,IAAI,CAAC,CAAC,QAAQ,OAAO;aAKrB,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK,CAAC,GAAG;YAC3D,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAClD,OAGK;YACH,IAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,IAAI,CAAC,QAChB,MAAM,KAAK,IAAI,CAAC,QAChB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,QAAQ,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,GAAG,EAAE,IAAI,IAC/E,MAAM,IAAI,KACV,MAAM,IAAI;YAEd,gEAAgE;YAChE,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,SAAS;gBAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC;YACpD;YAEA,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,MAAM,IAAI,CAAC;QACpH;IACF;IACA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;QACxB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;QAEhC,iCAAiC;QACjC,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,GAAG;QAElD,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,KAClB,KAAK,IAAI,KAAK,GAAG,CAAC,KAClB,KAAK,IAAI,IACT,KAAK,IAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;QAE9B,uCAAuC;QACvC,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM;YACrB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;QAC5B,OAGK,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,SAAS;YAC/E,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;QAC5B;QAEA,iCAAiC;QACjC,IAAI,CAAC,GAAG;QAER,uDAAuD;QACvD,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM;QAE5B,mEAAmE;QACnE,IAAI,KAAK,YAAY;YACnB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAC9G,OAGK,IAAI,KAAK,SAAS;YACrB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC;QACvH;IACF;IACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACf,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/F;IACA,WAAW;QACT,OAAO,IAAI,CAAC,CAAC;IACf;AACF;AAEO,SAAS;IACd,OAAO,IAAI;AACb;AAEA,2BAA2B;AAC3B,KAAK,SAAS,GAAG,KAAK,SAAS;AAExB,SAAS,UAAU,SAAS,CAAC;IAClC,OAAO,IAAI,KAAK,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-fetch/src/text.js"], "sourcesContent": ["function responseText(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.text();\n}\n\nexport default function(input, init) {\n  return fetch(input, init).then(responseText);\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,aAAa,QAAQ;IAC5B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG,MAAM,SAAS,UAAU;IAC7E,OAAO,SAAS,IAAI;AACtB;AAEe,wCAAS,KAAK,EAAE,IAAI;IACjC,OAAO,MAAM,OAAO,MAAM,IAAI,CAAC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-fetch/src/xml.js"], "sourcesContent": ["import text from \"./text.js\";\n\nfunction parser(type) {\n  return (input, init) => text(input, init)\n    .then(text => (new DOMParser).parseFromString(text, type));\n}\n\nexport default parser(\"application/xml\");\n\nexport var html = parser(\"text/html\");\n\nexport var svg = parser(\"image/svg+xml\");\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,SAAS,OAAO,IAAI;IAClB,OAAO,CAAC,OAAO,OAAS,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,OAAO,MACjC,IAAI,CAAC,CAAA,OAAQ,CAAC,IAAI,SAAS,EAAE,eAAe,CAAC,MAAM;AACxD;uCAEe,OAAO;AAEf,IAAI,OAAO,OAAO;AAElB,IAAI,MAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/kleur/index.mjs"], "sourcesContent": ["'use strict';\n\nlet FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM, isTTY=true;\nif (typeof process !== 'undefined') {\n\t({ FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM } = process.env || {});\n\tisTTY = process.stdout && process.stdout.isTTY;\n}\n\nconst $ = {\n\tenabled: !NODE_DISABLE_COLORS && NO_COLOR == null && TERM !== 'dumb' && (\n\t\tFORCE_COLOR != null && FORCE_COLOR !== '0' || isTTY\n\t),\n\n\t// modifiers\n\treset: init(0, 0),\n\tbold: init(1, 22),\n\tdim: init(2, 22),\n\titalic: init(3, 23),\n\tunderline: init(4, 24),\n\tinverse: init(7, 27),\n\thidden: init(8, 28),\n\tstrikethrough: init(9, 29),\n\n\t// colors\n\tblack: init(30, 39),\n\tred: init(31, 39),\n\tgreen: init(32, 39),\n\tyellow: init(33, 39),\n\tblue: init(34, 39),\n\tmagenta: init(35, 39),\n\tcyan: init(36, 39),\n\twhite: init(37, 39),\n\tgray: init(90, 39),\n\tgrey: init(90, 39),\n\n\t// background colors\n\tbgBlack: init(40, 49),\n\tbgRed: init(41, 49),\n\tbgGreen: init(42, 49),\n\tbgYellow: init(43, 49),\n\tbgBlue: init(44, 49),\n\tbgMagenta: init(45, 49),\n\tbgCyan: init(46, 49),\n\tbgWhite: init(47, 49)\n};\n\nfunction run(arr, str) {\n\tlet i=0, tmp, beg='', end='';\n\tfor (; i < arr.length; i++) {\n\t\ttmp = arr[i];\n\t\tbeg += tmp.open;\n\t\tend += tmp.close;\n\t\tif (!!~str.indexOf(tmp.close)) {\n\t\t\tstr = str.replace(tmp.rgx, tmp.close + tmp.open);\n\t\t}\n\t}\n\treturn beg + str + end;\n}\n\nfunction chain(has, keys) {\n\tlet ctx = { has, keys };\n\n\tctx.reset = $.reset.bind(ctx);\n\tctx.bold = $.bold.bind(ctx);\n\tctx.dim = $.dim.bind(ctx);\n\tctx.italic = $.italic.bind(ctx);\n\tctx.underline = $.underline.bind(ctx);\n\tctx.inverse = $.inverse.bind(ctx);\n\tctx.hidden = $.hidden.bind(ctx);\n\tctx.strikethrough = $.strikethrough.bind(ctx);\n\n\tctx.black = $.black.bind(ctx);\n\tctx.red = $.red.bind(ctx);\n\tctx.green = $.green.bind(ctx);\n\tctx.yellow = $.yellow.bind(ctx);\n\tctx.blue = $.blue.bind(ctx);\n\tctx.magenta = $.magenta.bind(ctx);\n\tctx.cyan = $.cyan.bind(ctx);\n\tctx.white = $.white.bind(ctx);\n\tctx.gray = $.gray.bind(ctx);\n\tctx.grey = $.grey.bind(ctx);\n\n\tctx.bgBlack = $.bgBlack.bind(ctx);\n\tctx.bgRed = $.bgRed.bind(ctx);\n\tctx.bgGreen = $.bgGreen.bind(ctx);\n\tctx.bgYellow = $.bgYellow.bind(ctx);\n\tctx.bgBlue = $.bgBlue.bind(ctx);\n\tctx.bgMagenta = $.bgMagenta.bind(ctx);\n\tctx.bgCyan = $.bgCyan.bind(ctx);\n\tctx.bgWhite = $.bgWhite.bind(ctx);\n\n\treturn ctx;\n}\n\nfunction init(open, close) {\n\tlet blk = {\n\t\topen: `\\x1b[${open}m`,\n\t\tclose: `\\x1b[${close}m`,\n\t\trgx: new RegExp(`\\\\x1b\\\\[${close}m`, 'g')\n\t};\n\treturn function (txt) {\n\t\tif (this !== void 0 && this.has !== void 0) {\n\t\t\t!!~this.has.indexOf(open) || (this.has.push(open),this.keys.push(blk));\n\t\t\treturn txt === void 0 ? this : $.enabled ? run(this.keys, txt+'') : txt+'';\n\t\t}\n\t\treturn txt === void 0 ? chain([open], [blk]) : $.enabled ? run([blk], txt+'') : txt+'';\n\t};\n}\n\nexport default $;\n"], "names": [], "mappings": ";;;AAGW;AAHX;AAEA,IAAI,aAAa,qBAAqB,UAAU,MAAM,QAAM;AAC5D,IAAI,OAAO,gKAAA,CAAA,UAAO,KAAK,aAAa;IACnC,CAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,IAAI,CAAC,CAAC;IACzE,QAAQ,gKAAA,CAAA,UAAO,CAAC,MAAM,IAAI,gKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,KAAK;AAC/C;AAEA,MAAM,IAAI;IACT,SAAS,CAAC,uBAAuB,YAAY,QAAQ,SAAS,UAAU,CACvE,eAAe,QAAQ,gBAAgB,OAAO,KAC/C;IAEA,YAAY;IACZ,OAAO,KAAK,GAAG;IACf,MAAM,KAAK,GAAG;IACd,KAAK,KAAK,GAAG;IACb,QAAQ,KAAK,GAAG;IAChB,WAAW,KAAK,GAAG;IACnB,SAAS,KAAK,GAAG;IACjB,QAAQ,KAAK,GAAG;IAChB,eAAe,KAAK,GAAG;IAEvB,SAAS;IACT,OAAO,KAAK,IAAI;IAChB,KAAK,KAAK,IAAI;IACd,OAAO,KAAK,IAAI;IAChB,QAAQ,KAAK,IAAI;IACjB,MAAM,KAAK,IAAI;IACf,SAAS,KAAK,IAAI;IAClB,MAAM,KAAK,IAAI;IACf,OAAO,KAAK,IAAI;IAChB,MAAM,KAAK,IAAI;IACf,MAAM,KAAK,IAAI;IAEf,oBAAoB;IACpB,SAAS,KAAK,IAAI;IAClB,OAAO,KAAK,IAAI;IAChB,SAAS,KAAK,IAAI;IAClB,UAAU,KAAK,IAAI;IACnB,QAAQ,KAAK,IAAI;IACjB,WAAW,KAAK,IAAI;IACpB,QAAQ,KAAK,IAAI;IACjB,SAAS,KAAK,IAAI;AACnB;AAEA,SAAS,IAAI,GAAG,EAAE,GAAG;IACpB,IAAI,IAAE,GAAG,KAAK,MAAI,IAAI,MAAI;IAC1B,MAAO,IAAI,IAAI,MAAM,EAAE,IAAK;QAC3B,MAAM,GAAG,CAAC,EAAE;QACZ,OAAO,IAAI,IAAI;QACf,OAAO,IAAI,KAAK;QAChB,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,GAAG;YAC9B,MAAM,IAAI,OAAO,CAAC,IAAI,GAAG,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI;QAChD;IACD;IACA,OAAO,MAAM,MAAM;AACpB;AAEA,SAAS,MAAM,GAAG,EAAE,IAAI;IACvB,IAAI,MAAM;QAAE;QAAK;IAAK;IAEtB,IAAI,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC;IACzB,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;IACvB,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC;IACrB,IAAI,MAAM,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,SAAS,GAAG,EAAE,SAAS,CAAC,IAAI,CAAC;IACjC,IAAI,OAAO,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC;IAC7B,IAAI,MAAM,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,aAAa,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC;IAEzC,IAAI,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC;IACzB,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC;IACrB,IAAI,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC;IACzB,IAAI,MAAM,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;IACvB,IAAI,OAAO,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC;IAC7B,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;IACvB,IAAI,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC;IACzB,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;IACvB,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;IAEvB,IAAI,OAAO,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC;IAC7B,IAAI,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC;IACzB,IAAI,OAAO,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC;IAC7B,IAAI,QAAQ,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC;IAC/B,IAAI,MAAM,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,SAAS,GAAG,EAAE,SAAS,CAAC,IAAI,CAAC;IACjC,IAAI,MAAM,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;IAC3B,IAAI,OAAO,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC;IAE7B,OAAO;AACR;AAEA,SAAS,KAAK,IAAI,EAAE,KAAK;IACxB,IAAI,MAAM;QACT,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACvB,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,EAAE;IACtC;IACA,OAAO,SAAU,GAAG;QACnB,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG;YAC3C,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YACrE,OAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,EAAE,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,MAAI,MAAM,MAAI;QACzE;QACA,OAAO,QAAQ,KAAK,IAAI,MAAM;YAAC;SAAK,EAAE;YAAC;SAAI,IAAI,EAAE,OAAO,GAAG,IAAI;YAAC;SAAI,EAAE,MAAI,MAAM,MAAI;IACrF;AACD;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/diff/lib/index.mjs"], "sourcesContent": ["function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var _options$timeout;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    this.options = options;\n    var self = this;\n\n    function done(value) {\n      if (callback) {\n        setTimeout(function () {\n          callback(undefined, value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    } // Allow subclasses to massage the input prior to running\n\n\n    oldString = this.castInput(oldString);\n    newString = this.castInput(newString);\n    oldString = this.removeEmpty(this.tokenize(oldString));\n    newString = this.removeEmpty(this.tokenize(newString));\n    var newLen = newString.length,\n        oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n\n    if (options.maxEditLength) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n\n    var maxExecutionTime = (_options$timeout = options.timeout) !== null && _options$timeout !== void 0 ? _options$timeout : Infinity;\n    var abortAfterTimestamp = Date.now() + maxExecutionTime;\n    var bestPath = [{\n      oldPos: -1,\n      lastComponent: undefined\n    }]; // Seed editLength = 0, i.e. the content starts with the same values\n\n    var newPos = this.extractCommon(bestPath[0], newString, oldString, 0);\n\n    if (bestPath[0].oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n      // Identity per the equality and tokenizer\n      return done([{\n        value: this.join(newString),\n        count: newString.length\n      }]);\n    } // Once we hit the right edge of the edit graph on some diagonal k, we can\n    // definitely reach the end of the edit graph in no more than k edits, so\n    // there's no point in considering any moves to diagonal k+1 any more (from\n    // which we're guaranteed to need at least k+1 more edits).\n    // Similarly, once we've reached the bottom of the edit graph, there's no\n    // point considering moves to lower diagonals.\n    // We record this fact by setting minDiagonalToConsider and\n    // maxDiagonalToConsider to some finite value once we've hit the edge of\n    // the edit graph.\n    // This optimization is not faithful to the original algorithm presented in\n    // Myers's paper, which instead pointlessly extends D-paths off the end of\n    // the edit graph - see page 7 of Myers's paper which notes this point\n    // explicitly and illustrates it with a diagram. This has major performance\n    // implications for some common scenarios. For instance, to compute a diff\n    // where the new text simply appends d characters on the end of the\n    // original text of length n, the true Myers algorithm will take O(n+d^2)\n    // time while this optimization needs only O(n+d) time.\n\n\n    var minDiagonalToConsider = -Infinity,\n        maxDiagonalToConsider = Infinity; // Main worker method. checks all permutations of a given edit length for acceptance.\n\n    function execEditLength() {\n      for (var diagonalPath = Math.max(minDiagonalToConsider, -editLength); diagonalPath <= Math.min(maxDiagonalToConsider, editLength); diagonalPath += 2) {\n        var basePath = void 0;\n        var removePath = bestPath[diagonalPath - 1],\n            addPath = bestPath[diagonalPath + 1];\n\n        if (removePath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n\n        var canAdd = false;\n\n        if (addPath) {\n          // what newPos will be after we do an insertion:\n          var addPathNewPos = addPath.oldPos - diagonalPath;\n          canAdd = addPath && 0 <= addPathNewPos && addPathNewPos < newLen;\n        }\n\n        var canRemove = removePath && removePath.oldPos + 1 < oldLen;\n\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        } // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the old string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n        // TODO: Remove the `+ 1` here to make behavior match Myers algorithm\n        //       and prefer to order removals before insertions.\n\n\n        if (!canRemove || canAdd && removePath.oldPos + 1 < addPath.oldPos) {\n          basePath = self.addToPath(addPath, true, undefined, 0);\n        } else {\n          basePath = self.addToPath(removePath, undefined, true, 1);\n        }\n\n        newPos = self.extractCommon(basePath, newString, oldString, diagonalPath);\n\n        if (basePath.oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n          // If we have hit the end of both strings, then we are done\n          return done(buildValues(self, basePath.lastComponent, newString, oldString, self.useLongestToken));\n        } else {\n          bestPath[diagonalPath] = basePath;\n\n          if (basePath.oldPos + 1 >= oldLen) {\n            maxDiagonalToConsider = Math.min(maxDiagonalToConsider, diagonalPath - 1);\n          }\n\n          if (newPos + 1 >= newLen) {\n            minDiagonalToConsider = Math.max(minDiagonalToConsider, diagonalPath + 1);\n          }\n        }\n      }\n\n      editLength++;\n    } // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n\n\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength || Date.now() > abortAfterTimestamp) {\n            return callback();\n          }\n\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength && Date.now() <= abortAfterTimestamp) {\n        var ret = execEditLength();\n\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  addToPath: function addToPath(path, added, removed, oldPosInc) {\n    var last = path.lastComponent;\n\n    if (last && last.added === added && last.removed === removed) {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: last.count + 1,\n          added: added,\n          removed: removed,\n          previousComponent: last.previousComponent\n        }\n      };\n    } else {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: 1,\n          added: added,\n          removed: removed,\n          previousComponent: last\n        }\n      };\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {\n    var newLen = newString.length,\n        oldLen = oldString.length,\n        oldPos = basePath.oldPos,\n        newPos = oldPos - diagonalPath,\n        commonCount = 0;\n\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n    }\n\n    if (commonCount) {\n      basePath.lastComponent = {\n        count: commonCount,\n        previousComponent: basePath.lastComponent\n      };\n    }\n\n    basePath.oldPos = oldPos;\n    return newPos;\n  },\n  equals: function equals(left, right) {\n    if (this.options.comparator) {\n      return this.options.comparator(left, right);\n    } else {\n      return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return value.split('');\n  },\n  join: function join(chars) {\n    return chars.join('');\n  }\n};\n\nfunction buildValues(diff, lastComponent, newString, oldString, useLongestToken) {\n  // First we convert our linked list of components in reverse order to an\n  // array in the right order:\n  var components = [];\n  var nextComponent;\n\n  while (lastComponent) {\n    components.push(lastComponent);\n    nextComponent = lastComponent.previousComponent;\n    delete lastComponent.previousComponent;\n    lastComponent = nextComponent;\n  }\n\n  components.reverse();\n  var componentPos = 0,\n      componentLen = components.length,\n      newPos = 0,\n      oldPos = 0;\n\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n\n      newPos += component.count; // Common case\n\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count; // Reverse add and remove so removes are output first to match common convention\n      // The diffing algorithm is tied to add then remove output and this is the simplest\n      // route to get the desired output with minimal overhead.\n\n      if (componentPos && components[componentPos - 1].added) {\n        var tmp = components[componentPos - 1];\n        components[componentPos - 1] = components[componentPos];\n        components[componentPos] = tmp;\n      }\n    }\n  } // Special case handle for when one terminal is ignored (i.e. whitespace).\n  // For this case we merge the terminal into the prior string and drop the change.\n  // This is only available for string mode.\n\n\n  var finalComponent = components[componentLen - 1];\n\n  if (componentLen > 1 && typeof finalComponent.value === 'string' && (finalComponent.added || finalComponent.removed) && diff.equals('', finalComponent.value)) {\n    components[componentLen - 2].value += finalComponent.value;\n    components.pop();\n  }\n\n  return components;\n}\n\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\n\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n\n  return defaults;\n}\n\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\n\nvar extendedWordChars = /^[A-Za-z\\xC0-\\u02C6\\u02C8-\\u02D7\\u02DE-\\u02FF\\u1E00-\\u1EFF]+$/;\nvar reWhitespace = /\\S/;\nvar wordDiff = new Diff();\n\nwordDiff.equals = function (left, right) {\n  if (this.options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n\n  return left === right || this.options.ignoreWhitespace && !reWhitespace.test(left) && !reWhitespace.test(right);\n};\n\nwordDiff.tokenize = function (value) {\n  // All whitespace symbols except newline group into one token, each newline - in separate token\n  var tokens = value.split(/([^\\S\\r\\n]+|[()[\\]{}'\"\\r\\n]|\\b)/); // Join the boundary splits that we do not consider to be boundaries. This is primarily the extended Latin character set.\n\n  for (var i = 0; i < tokens.length - 1; i++) {\n    // If we have an empty string in the next field and we have only word chars before and after, merge\n    if (!tokens[i + 1] && tokens[i + 2] && extendedWordChars.test(tokens[i]) && extendedWordChars.test(tokens[i + 2])) {\n      tokens[i] += tokens[i + 2];\n      tokens.splice(i + 1, 2);\n      i--;\n    }\n  }\n\n  return tokens;\n};\n\nfunction diffWords(oldStr, newStr, options) {\n  options = generateOptions(options, {\n    ignoreWhitespace: true\n  });\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordDiff.diff(oldStr, newStr, options);\n}\n\nvar lineDiff = new Diff();\n\nlineDiff.tokenize = function (value) {\n  if (this.options.stripTrailingCr) {\n    // remove one \\r before \\n to match GNU diff's --strip-trailing-cr behavior\n    value = value.replace(/\\r\\n/g, '\\n');\n  }\n\n  var retLines = [],\n      linesAndNewlines = value.split(/(\\n|\\r\\n)/); // Ignore the final empty token that occurs if the string ends with a new line\n\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  } // Merge the content and line separators into single tokens\n\n\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n\n    if (i % 2 && !this.options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      if (this.options.ignoreWhitespace) {\n        line = line.trim();\n      }\n\n      retLines.push(line);\n    }\n  }\n\n  return retLines;\n};\n\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\n\nvar sentenceDiff = new Diff();\n\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\n\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\n\nvar cssDiff = new Diff();\n\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\n\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar objectPrototypeToString = Object.prototype.toString;\nvar jsonDiff = new Diff(); // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\n\njsonDiff.castInput = function (value) {\n  var _this$options = this.options,\n      undefinedReplacement = _this$options.undefinedReplacement,\n      _this$options$stringi = _this$options.stringifyReplacer,\n      stringifyReplacer = _this$options$stringi === void 0 ? function (k, v) {\n    return typeof v === 'undefined' ? undefinedReplacement : v;\n  } : _this$options$stringi;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\n\njsonDiff.equals = function (left, right) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'));\n};\n\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n} // This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\n\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n\n  var i;\n\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n\n  var canonicalizedObj;\n\n  if ('[object Array]' === objectPrototypeToString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n\n    var sortedKeys = [],\n        _key;\n\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (obj.hasOwnProperty(_key)) {\n        sortedKeys.push(_key);\n      }\n    }\n\n    sortedKeys.sort();\n\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n\n  return canonicalizedObj;\n}\n\nvar arrayDiff = new Diff();\n\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\n\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\n\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\n\nfunction parsePatch(uniDiff) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var diffstr = uniDiff.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = uniDiff.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      list = [],\n      i = 0;\n\n  function parseIndex() {\n    var index = {};\n    list.push(index); // Parse diff metadata\n\n    while (i < diffstr.length) {\n      var line = diffstr[i]; // File header found, end parsing diff metadata\n\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      } // Diff index\n\n\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n\n      if (header) {\n        index.index = header[1];\n      }\n\n      i++;\n    } // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n\n\n    parseFileHeader(index);\n    parseFileHeader(index); // Parse hunks\n\n    index.hunks = [];\n\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n\n      if (/^(Index:|diff|\\-\\-\\-|\\+\\+\\+)\\s/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line && options.strict) {\n        // Ignore unexpected content unless in strict mode\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  } // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n\n\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)$/.exec(diffstr[i]);\n\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  } // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n\n\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n        chunkHeaderLine = diffstr[i++],\n        chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: [],\n      linedelimiters: []\n    }; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n\n    var addCount = 0,\n        removeCount = 0;\n\n    for (; i < diffstr.length; i++) {\n      // Lines starting with '---' could be mistaken for the \"remove line\" operation\n      // But they could be the header for the next file. Therefore prune such cases out.\n      if (diffstr[i].indexOf('--- ') === 0 && i + 2 < diffstr.length && diffstr[i + 1].indexOf('+++ ') === 0 && diffstr[i + 2].indexOf('@@') === 0) {\n        break;\n      }\n\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        hunk.linedelimiters.push(delimiters[i] || '\\n');\n\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        break;\n      }\n    } // Handle the empty block count case\n\n\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    } // Perform optional sanity checking\n\n\n    if (options.strict) {\n      if (addCount !== hunk.newLines) {\n        throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n\n      if (removeCount !== hunk.oldLines) {\n        throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n    }\n\n    return hunk;\n  }\n\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator (start, minLine, maxLine) {\n  var wantForward = true,\n      backwardExhausted = false,\n      forwardExhausted = false,\n      localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      } // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n\n\n      if (start + localOffset <= maxLine) {\n        return localOffset;\n      }\n\n      forwardExhausted = true;\n    }\n\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      } // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n\n\n      if (minLine <= start - localOffset) {\n        return -localOffset++;\n      }\n\n      backwardExhausted = true;\n      return iterator();\n    } // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n\n    uniDiff = uniDiff[0];\n  } // Apply the diff to the input\n\n\n  var lines = source.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = source.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      hunks = uniDiff.hunks,\n      compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n    return line === patchContent;\n  },\n      errorCount = 0,\n      fuzzFactor = options.fuzzFactor || 0,\n      minLine = 0,\n      offset = 0,\n      removeEOFNL,\n      addEOFNL;\n  /**\n   * Checks if the hunk exactly fits on the provided location\n   */\n\n\n  function hunkFits(hunk, toPos) {\n    for (var j = 0; j < hunk.lines.length; j++) {\n      var line = hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line;\n\n      if (operation === ' ' || operation === '-') {\n        // Context sanity check\n        if (!compareLine(toPos + 1, lines[toPos], operation, content)) {\n          errorCount++;\n\n          if (errorCount > fuzzFactor) {\n            return false;\n          }\n        }\n\n        toPos++;\n      }\n    }\n\n    return true;\n  } // Search best fit offsets for each hunk based on the previous ones\n\n\n  for (var i = 0; i < hunks.length; i++) {\n    var hunk = hunks[i],\n        maxLine = lines.length - hunk.oldLines,\n        localOffset = 0,\n        toPos = offset + hunk.oldStart - 1;\n    var iterator = distanceIterator(toPos, minLine, maxLine);\n\n    for (; localOffset !== undefined; localOffset = iterator()) {\n      if (hunkFits(hunk, toPos + localOffset)) {\n        hunk.offset = offset += localOffset;\n        break;\n      }\n    }\n\n    if (localOffset === undefined) {\n      return false;\n    } // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n\n\n    minLine = hunk.offset + hunk.oldStart + hunk.oldLines;\n  } // Apply patch hunks\n\n\n  var diffOffset = 0;\n\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var _hunk = hunks[_i],\n        _toPos = _hunk.oldStart + _hunk.offset + diffOffset - 1;\n\n    diffOffset += _hunk.newLines - _hunk.oldLines;\n\n    for (var j = 0; j < _hunk.lines.length; j++) {\n      var line = _hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line,\n          delimiter = _hunk.linedelimiters && _hunk.linedelimiters[j] || '\\n';\n\n      if (operation === ' ') {\n        _toPos++;\n      } else if (operation === '-') {\n        lines.splice(_toPos, 1);\n        delimiters.splice(_toPos, 1);\n        /* istanbul ignore else */\n      } else if (operation === '+') {\n        lines.splice(_toPos, 0, content);\n        delimiters.splice(_toPos, 0, delimiter);\n        _toPos++;\n      } else if (operation === '\\\\') {\n        var previousOperation = _hunk.lines[j - 1] ? _hunk.lines[j - 1][0] : null;\n\n        if (previousOperation === '+') {\n          removeEOFNL = true;\n        } else if (previousOperation === '-') {\n          addEOFNL = true;\n        }\n      }\n    }\n  } // Handle EOFNL insertion/removal\n\n\n  if (removeEOFNL) {\n    while (!lines[lines.length - 1]) {\n      lines.pop();\n      delimiters.pop();\n    }\n  } else if (addEOFNL) {\n    lines.push('');\n    delimiters.push('\\n');\n  }\n\n  for (var _k = 0; _k < lines.length - 1; _k++) {\n    lines[_k] = lines[_k] + delimiters[_k];\n  }\n\n  return lines.join('');\n} // Wrapper that supports multiple file patches via callbacks.\n\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  var currentIndex = 0;\n\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n\n    if (!index) {\n      return options.complete();\n    }\n\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n\n        processIndex();\n      });\n    });\n  }\n\n  processIndex();\n}\n\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n\n  var diff = diffLines(oldStr, newStr, options);\n\n  if (!diff) {\n    return;\n  }\n\n  diff.push({\n    value: '',\n    lines: []\n  }); // Append an empty value to make cleanup easier\n\n  function contextLines(lines) {\n    return lines.map(function (entry) {\n      return ' ' + entry;\n    });\n  }\n\n  var hunks = [];\n  var oldRangeStart = 0,\n      newRangeStart = 0,\n      curRange = [],\n      oldLine = 1,\n      newLine = 1;\n\n  var _loop = function _loop(i) {\n    var current = diff[i],\n        lines = current.lines || current.value.replace(/\\n$/, '').split('\\n');\n    current.lines = lines;\n\n    if (current.added || current.removed) {\n      var _curRange;\n\n      // If we have previous context, start with that\n      if (!oldRangeStart) {\n        var prev = diff[i - 1];\n        oldRangeStart = oldLine;\n        newRangeStart = newLine;\n\n        if (prev) {\n          curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n          oldRangeStart -= curRange.length;\n          newRangeStart -= curRange.length;\n        }\n      } // Output our changes\n\n\n      (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n        return (current.added ? '+' : '-') + entry;\n      }))); // Track the updated file position\n\n\n      if (current.added) {\n        newLine += lines.length;\n      } else {\n        oldLine += lines.length;\n      }\n    } else {\n      // Identical context lines. Track line changes\n      if (oldRangeStart) {\n        // Close out any changes that have been output (or join overlapping)\n        if (lines.length <= options.context * 2 && i < diff.length - 2) {\n          var _curRange2;\n\n          // Overlapping\n          (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n        } else {\n          var _curRange3;\n\n          // end the range and output\n          var contextSize = Math.min(lines.length, options.context);\n\n          (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n\n          var hunk = {\n            oldStart: oldRangeStart,\n            oldLines: oldLine - oldRangeStart + contextSize,\n            newStart: newRangeStart,\n            newLines: newLine - newRangeStart + contextSize,\n            lines: curRange\n          };\n\n          if (i >= diff.length - 2 && lines.length <= options.context) {\n            // EOF is inside this hunk\n            var oldEOFNewline = /\\n$/.test(oldStr);\n            var newEOFNewline = /\\n$/.test(newStr);\n            var noNlBeforeAdds = lines.length == 0 && curRange.length > hunk.oldLines;\n\n            if (!oldEOFNewline && noNlBeforeAdds && oldStr.length > 0) {\n              // special case: old has no eol and no trailing context; no-nl can end up before adds\n              // however, if the old file is empty, do not output the no-nl line\n              curRange.splice(hunk.oldLines, 0, '\\\\ No newline at end of file');\n            }\n\n            if (!oldEOFNewline && !noNlBeforeAdds || !newEOFNewline) {\n              curRange.push('\\\\ No newline at end of file');\n            }\n          }\n\n          hunks.push(hunk);\n          oldRangeStart = 0;\n          newRangeStart = 0;\n          curRange = [];\n        }\n      }\n\n      oldLine += lines.length;\n      newLine += lines.length;\n    }\n  };\n\n  for (var i = 0; i < diff.length; i++) {\n    _loop(i);\n  }\n\n  return {\n    oldFileName: oldFileName,\n    newFileName: newFileName,\n    oldHeader: oldHeader,\n    newHeader: newHeader,\n    hunks: hunks\n  };\n}\nfunction formatPatch(diff) {\n  if (Array.isArray(diff)) {\n    return diff.map(formatPatch).join('\\n');\n  }\n\n  var ret = [];\n\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i]; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return formatPatch(structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options));\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n      oldLines = _calcOldNewLineCount.oldLines,\n      newLines = _calcOldNewLineCount.newLines;\n\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {}; // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n\n  ret.hunks = [];\n  var mineIndex = 0,\n      theirsIndex = 0,\n      mineOffset = 0,\n      theirsOffset = 0;\n\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n      oldStart: Infinity\n    },\n        theirsCurrent = theirs.hunks[theirsIndex] || {\n      oldStart: Infinity\n    };\n\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n\n  return ret;\n}\n\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n\n    return structuredPatch(undefined, undefined, base, param);\n  }\n\n  return param;\n}\n\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\n\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\n\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\n\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\n\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n    offset: mineOffset,\n    lines: mineLines,\n    index: 0\n  },\n      their = {\n    offset: theirOffset,\n    lines: theirLines,\n    index: 0\n  }; // Handle any leading content\n\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine); // Now in the overlap content. Scan through and select the best changes from each.\n\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n        theirCurrent = their.lines[their.index];\n\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  } // Now push anything that may be remaining\n\n\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\n\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectChange(their);\n\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n\n    return;\n  }\n\n  conflict(hunk, myChanges, theirChanges);\n}\n\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectContext(their, myChanges);\n\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\n\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\n\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\n\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\n\nfunction collectChange(state) {\n  var ret = [],\n      operation = state.lines[state.index][0];\n\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index]; // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n\n  return ret;\n}\n\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n      merged = [],\n      matchIndex = 0,\n      contextChanges = false,\n      conflicted = false;\n\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n        match = matchChanges[matchIndex]; // Once we've hit our add, then we are done\n\n    if (match[0] === '+') {\n      break;\n    }\n\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++; // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n\n    if (change[0] === '+') {\n      conflicted = true;\n\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n\n  if (conflicted) {\n    return changes;\n  }\n\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\n\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\n\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n\n  state.index += delta;\n  return true;\n}\n\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\nfunction reversePatch(structuredPatch) {\n  if (Array.isArray(structuredPatch)) {\n    return structuredPatch.map(reversePatch).reverse();\n  }\n\n  return _objectSpread2(_objectSpread2({}, structuredPatch), {}, {\n    oldFileName: structuredPatch.newFileName,\n    oldHeader: structuredPatch.newHeader,\n    newFileName: structuredPatch.oldFileName,\n    newHeader: structuredPatch.oldHeader,\n    hunks: structuredPatch.hunks.map(function (hunk) {\n      return {\n        oldLines: hunk.newLines,\n        oldStart: hunk.newStart,\n        newLines: hunk.oldLines,\n        newStart: hunk.oldStart,\n        linedelimiters: hunk.linedelimiters,\n        lines: hunk.lines.map(function (l) {\n          if (l.startsWith('-')) {\n            return \"+\".concat(l.slice(1));\n          }\n\n          if (l.startsWith('+')) {\n            return \"-\".concat(l.slice(1));\n          }\n\n          return l;\n        })\n      };\n    })\n  });\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n      change,\n      operation;\n\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n\n    ret.push([operation, change.value]);\n  }\n\n  return ret;\n}\n\nfunction convertChangesToXML(changes) {\n  var ret = [];\n\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n\n    ret.push(escapeHTML(change.value));\n\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n\n  return ret.join('');\n}\n\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\n\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, formatPatch, merge, parsePatch, reversePatch, structuredPatch };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,QAAQ;AACjB,KAAK,SAAS,GAAG;IACf,MAAM,SAAS,KAAK,SAAS,EAAE,SAAS;QACtC,IAAI;QAEJ,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;QACnF,IAAI,WAAW,QAAQ,QAAQ;QAE/B,IAAI,OAAO,YAAY,YAAY;YACjC,WAAW;YACX,UAAU,CAAC;QACb;QAEA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,OAAO,IAAI;QAEf,SAAS,KAAK,KAAK;YACjB,IAAI,UAAU;gBACZ,WAAW;oBACT,SAAS,WAAW;gBACtB,GAAG;gBACH,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF,EAAE,yDAAyD;QAG3D,YAAY,IAAI,CAAC,SAAS,CAAC;QAC3B,YAAY,IAAI,CAAC,SAAS,CAAC;QAC3B,YAAY,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC3C,YAAY,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC3C,IAAI,SAAS,UAAU,MAAM,EACzB,SAAS,UAAU,MAAM;QAC7B,IAAI,aAAa;QACjB,IAAI,gBAAgB,SAAS;QAE7B,IAAI,QAAQ,aAAa,EAAE;YACzB,gBAAgB,KAAK,GAAG,CAAC,eAAe,QAAQ,aAAa;QAC/D;QAEA,IAAI,mBAAmB,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB;QACzH,IAAI,sBAAsB,KAAK,GAAG,KAAK;QACvC,IAAI,WAAW;YAAC;gBACd,QAAQ,CAAC;gBACT,eAAe;YACjB;SAAE,EAAE,oEAAoE;QAExE,IAAI,SAAS,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,EAAE,WAAW,WAAW;QAEnE,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,UAAU,SAAS,KAAK,QAAQ;YAC5D,0CAA0C;YAC1C,OAAO,KAAK;gBAAC;oBACX,OAAO,IAAI,CAAC,IAAI,CAAC;oBACjB,OAAO,UAAU,MAAM;gBACzB;aAAE;QACJ,EAAE,0EAA0E;QAC5E,yEAAyE;QACzE,2EAA2E;QAC3E,2DAA2D;QAC3D,yEAAyE;QACzE,8CAA8C;QAC9C,2DAA2D;QAC3D,wEAAwE;QACxE,kBAAkB;QAClB,2EAA2E;QAC3E,0EAA0E;QAC1E,sEAAsE;QACtE,2EAA2E;QAC3E,0EAA0E;QAC1E,mEAAmE;QACnE,yEAAyE;QACzE,uDAAuD;QAGvD,IAAI,wBAAwB,CAAC,UACzB,wBAAwB,UAAU,qFAAqF;QAE3H,SAAS;YACP,IAAK,IAAI,eAAe,KAAK,GAAG,CAAC,uBAAuB,CAAC,aAAa,gBAAgB,KAAK,GAAG,CAAC,uBAAuB,aAAa,gBAAgB,EAAG;gBACpJ,IAAI,WAAW,KAAK;gBACpB,IAAI,aAAa,QAAQ,CAAC,eAAe,EAAE,EACvC,UAAU,QAAQ,CAAC,eAAe,EAAE;gBAExC,IAAI,YAAY;oBACd,8DAA8D;oBAC9D,QAAQ,CAAC,eAAe,EAAE,GAAG;gBAC/B;gBAEA,IAAI,SAAS;gBAEb,IAAI,SAAS;oBACX,gDAAgD;oBAChD,IAAI,gBAAgB,QAAQ,MAAM,GAAG;oBACrC,SAAS,WAAW,KAAK,iBAAiB,gBAAgB;gBAC5D;gBAEA,IAAI,YAAY,cAAc,WAAW,MAAM,GAAG,IAAI;gBAEtD,IAAI,CAAC,UAAU,CAAC,WAAW;oBACzB,wCAAwC;oBACxC,QAAQ,CAAC,aAAa,GAAG;oBACzB;gBACF,EAAE,uEAAuE;gBACzE,wEAAwE;gBACxE,iDAAiD;gBACjD,qEAAqE;gBACrE,wDAAwD;gBAGxD,IAAI,CAAC,aAAa,UAAU,WAAW,MAAM,GAAG,IAAI,QAAQ,MAAM,EAAE;oBAClE,WAAW,KAAK,SAAS,CAAC,SAAS,MAAM,WAAW;gBACtD,OAAO;oBACL,WAAW,KAAK,SAAS,CAAC,YAAY,WAAW,MAAM;gBACzD;gBAEA,SAAS,KAAK,aAAa,CAAC,UAAU,WAAW,WAAW;gBAE5D,IAAI,SAAS,MAAM,GAAG,KAAK,UAAU,SAAS,KAAK,QAAQ;oBACzD,2DAA2D;oBAC3D,OAAO,KAAK,YAAY,MAAM,SAAS,aAAa,EAAE,WAAW,WAAW,KAAK,eAAe;gBAClG,OAAO;oBACL,QAAQ,CAAC,aAAa,GAAG;oBAEzB,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ;wBACjC,wBAAwB,KAAK,GAAG,CAAC,uBAAuB,eAAe;oBACzE;oBAEA,IAAI,SAAS,KAAK,QAAQ;wBACxB,wBAAwB,KAAK,GAAG,CAAC,uBAAuB,eAAe;oBACzE;gBACF;YACF;YAEA;QACF,EAAE,mFAAmF;QACrF,kFAAkF;QAClF,kFAAkF;QAClF,0CAA0C;QAG1C,IAAI,UAAU;YACZ,CAAC,SAAS;gBACR,WAAW;oBACT,IAAI,aAAa,iBAAiB,KAAK,GAAG,KAAK,qBAAqB;wBAClE,OAAO;oBACT;oBAEA,IAAI,CAAC,kBAAkB;wBACrB;oBACF;gBACF,GAAG;YACL,CAAC;QACH,OAAO;YACL,MAAO,cAAc,iBAAiB,KAAK,GAAG,MAAM,oBAAqB;gBACvE,IAAI,MAAM;gBAEV,IAAI,KAAK;oBACP,OAAO;gBACT;YACF;QACF;IACF;IACA,WAAW,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS;QAC3D,IAAI,OAAO,KAAK,aAAa;QAE7B,IAAI,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,OAAO,KAAK,SAAS;YAC5D,OAAO;gBACL,QAAQ,KAAK,MAAM,GAAG;gBACtB,eAAe;oBACb,OAAO,KAAK,KAAK,GAAG;oBACpB,OAAO;oBACP,SAAS;oBACT,mBAAmB,KAAK,iBAAiB;gBAC3C;YACF;QACF,OAAO;YACL,OAAO;gBACL,QAAQ,KAAK,MAAM,GAAG;gBACtB,eAAe;oBACb,OAAO;oBACP,OAAO;oBACP,SAAS;oBACT,mBAAmB;gBACrB;YACF;QACF;IACF;IACA,eAAe,SAAS,cAAc,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY;QAChF,IAAI,SAAS,UAAU,MAAM,EACzB,SAAS,UAAU,MAAM,EACzB,SAAS,SAAS,MAAM,EACxB,SAAS,SAAS,cAClB,cAAc;QAElB,MAAO,SAAS,IAAI,UAAU,SAAS,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC,SAAS,EAAE,EAAG;YAC9G;YACA;YACA;QACF;QAEA,IAAI,aAAa;YACf,SAAS,aAAa,GAAG;gBACvB,OAAO;gBACP,mBAAmB,SAAS,aAAa;YAC3C;QACF;QAEA,SAAS,MAAM,GAAG;QAClB,OAAO;IACT;IACA,QAAQ,SAAS,OAAO,IAAI,EAAE,KAAK;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM;QACvC,OAAO;YACL,OAAO,SAAS,SAAS,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,WAAW,OAAO,MAAM,WAAW;QAC9F;IACF;IACA,aAAa,SAAS,YAAY,KAAK;QACrC,IAAI,MAAM,EAAE;QAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,IAAI,KAAK,CAAC,EAAE,EAAE;gBACZ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACnB;QACF;QAEA,OAAO;IACT;IACA,WAAW,SAAS,UAAU,KAAK;QACjC,OAAO;IACT;IACA,UAAU,SAAS,SAAS,KAAK;QAC/B,OAAO,MAAM,KAAK,CAAC;IACrB;IACA,MAAM,SAAS,KAAK,KAAK;QACvB,OAAO,MAAM,IAAI,CAAC;IACpB;AACF;AAEA,SAAS,YAAY,IAAI,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe;IAC7E,wEAAwE;IACxE,4BAA4B;IAC5B,IAAI,aAAa,EAAE;IACnB,IAAI;IAEJ,MAAO,cAAe;QACpB,WAAW,IAAI,CAAC;QAChB,gBAAgB,cAAc,iBAAiB;QAC/C,OAAO,cAAc,iBAAiB;QACtC,gBAAgB;IAClB;IAEA,WAAW,OAAO;IAClB,IAAI,eAAe,GACf,eAAe,WAAW,MAAM,EAChC,SAAS,GACT,SAAS;IAEb,MAAO,eAAe,cAAc,eAAgB;QAClD,IAAI,YAAY,UAAU,CAAC,aAAa;QAExC,IAAI,CAAC,UAAU,OAAO,EAAE;YACtB,IAAI,CAAC,UAAU,KAAK,IAAI,iBAAiB;gBACvC,IAAI,QAAQ,UAAU,KAAK,CAAC,QAAQ,SAAS,UAAU,KAAK;gBAC5D,QAAQ,MAAM,GAAG,CAAC,SAAU,KAAK,EAAE,CAAC;oBAClC,IAAI,WAAW,SAAS,CAAC,SAAS,EAAE;oBACpC,OAAO,SAAS,MAAM,GAAG,MAAM,MAAM,GAAG,WAAW;gBACrD;gBACA,UAAU,KAAK,GAAG,KAAK,IAAI,CAAC;YAC9B,OAAO;gBACL,UAAU,KAAK,GAAG,KAAK,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,SAAS,UAAU,KAAK;YAC9E;YAEA,UAAU,UAAU,KAAK,EAAE,cAAc;YAEzC,IAAI,CAAC,UAAU,KAAK,EAAE;gBACpB,UAAU,UAAU,KAAK;YAC3B;QACF,OAAO;YACL,UAAU,KAAK,GAAG,KAAK,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,SAAS,UAAU,KAAK;YAC5E,UAAU,UAAU,KAAK,EAAE,gFAAgF;YAC3G,mFAAmF;YACnF,yDAAyD;YAEzD,IAAI,gBAAgB,UAAU,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE;gBACtD,IAAI,MAAM,UAAU,CAAC,eAAe,EAAE;gBACtC,UAAU,CAAC,eAAe,EAAE,GAAG,UAAU,CAAC,aAAa;gBACvD,UAAU,CAAC,aAAa,GAAG;YAC7B;QACF;IACF,EAAE,0EAA0E;IAC5E,iFAAiF;IACjF,0CAA0C;IAG1C,IAAI,iBAAiB,UAAU,CAAC,eAAe,EAAE;IAEjD,IAAI,eAAe,KAAK,OAAO,eAAe,KAAK,KAAK,YAAY,CAAC,eAAe,KAAK,IAAI,eAAe,OAAO,KAAK,KAAK,MAAM,CAAC,IAAI,eAAe,KAAK,GAAG;QAC7J,UAAU,CAAC,eAAe,EAAE,CAAC,KAAK,IAAI,eAAe,KAAK;QAC1D,WAAW,GAAG;IAChB;IAEA,OAAO;AACT;AAEA,IAAI,gBAAgB,IAAI;AACxB,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,OAAO;IACxC,OAAO,cAAc,IAAI,CAAC,QAAQ,QAAQ;AAC5C;AAEA,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACxC,IAAI,OAAO,YAAY,YAAY;QACjC,SAAS,QAAQ,GAAG;IACtB,OAAO,IAAI,SAAS;QAClB,IAAK,IAAI,QAAQ,QAAS;YACxB,wBAAwB,GACxB,IAAI,QAAQ,cAAc,CAAC,OAAO;gBAChC,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;YAChC;QACF;IACF;IAEA,OAAO;AACT;AAEA,EAAE;AACF,yBAAyB;AACzB,gCAAgC;AAChC,mCAAmC;AACnC,6BAA6B;AAC7B,8BAA8B;AAC9B,8BAA8B;AAC9B,4BAA4B;AAC5B,sCAAsC;AACtC,6BAA6B;AAC7B,6BAA6B;AAC7B,iCAAiC;AACjC,kCAAkC;AAClC,8BAA8B;AAC9B,mCAAmC;AACnC,2CAA2C;AAC3C,uCAAuC;AAEvC,IAAI,oBAAoB;AACxB,IAAI,eAAe;AACnB,IAAI,WAAW,IAAI;AAEnB,SAAS,MAAM,GAAG,SAAU,IAAI,EAAE,KAAK;IACrC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;QAC3B,OAAO,KAAK,WAAW;QACvB,QAAQ,MAAM,WAAW;IAC3B;IAEA,OAAO,SAAS,SAAS,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,CAAC;AAC3G;AAEA,SAAS,QAAQ,GAAG,SAAU,KAAK;IACjC,+FAA+F;IAC/F,IAAI,SAAS,MAAM,KAAK,CAAC,oCAAoC,yHAAyH;IAEtL,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,IAAK;QAC1C,mGAAmG;QACnG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,kBAAkB,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,kBAAkB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG;YACjH,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;YAC1B,OAAO,MAAM,CAAC,IAAI,GAAG;YACrB;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,OAAO;IACxC,UAAU,gBAAgB,SAAS;QACjC,kBAAkB;IACpB;IACA,OAAO,SAAS,IAAI,CAAC,QAAQ,QAAQ;AACvC;AACA,SAAS,mBAAmB,MAAM,EAAE,MAAM,EAAE,OAAO;IACjD,OAAO,SAAS,IAAI,CAAC,QAAQ,QAAQ;AACvC;AAEA,IAAI,WAAW,IAAI;AAEnB,SAAS,QAAQ,GAAG,SAAU,KAAK;IACjC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;QAChC,2EAA2E;QAC3E,QAAQ,MAAM,OAAO,CAAC,SAAS;IACjC;IAEA,IAAI,WAAW,EAAE,EACb,mBAAmB,MAAM,KAAK,CAAC,cAAc,8EAA8E;IAE/H,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,MAAM,GAAG,EAAE,EAAE;QAClD,iBAAiB,GAAG;IACtB,EAAE,2DAA2D;IAG7D,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;QAChD,IAAI,OAAO,gBAAgB,CAAC,EAAE;QAE9B,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YACzC,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,IAAI;QACnC,OAAO;YACL,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;gBACjC,OAAO,KAAK,IAAI;YAClB;YAEA,SAAS,IAAI,CAAC;QAChB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,QAAQ;IACzC,OAAO,SAAS,IAAI,CAAC,QAAQ,QAAQ;AACvC;AACA,SAAS,iBAAiB,MAAM,EAAE,MAAM,EAAE,QAAQ;IAChD,IAAI,UAAU,gBAAgB,UAAU;QACtC,kBAAkB;IACpB;IACA,OAAO,SAAS,IAAI,CAAC,QAAQ,QAAQ;AACvC;AAEA,IAAI,eAAe,IAAI;AAEvB,aAAa,QAAQ,GAAG,SAAU,KAAK;IACrC,OAAO,MAAM,KAAK,CAAC;AACrB;AAEA,SAAS,cAAc,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC7C,OAAO,aAAa,IAAI,CAAC,QAAQ,QAAQ;AAC3C;AAEA,IAAI,UAAU,IAAI;AAElB,QAAQ,QAAQ,GAAG,SAAU,KAAK;IAChC,OAAO,MAAM,KAAK,CAAC;AACrB;AAEA,SAAS,QAAQ,MAAM,EAAE,MAAM,EAAE,QAAQ;IACvC,OAAO,QAAQ,IAAI,CAAC,QAAQ,QAAQ;AACtC;AAEA,SAAS,QAAQ,GAAG;IAClB;IAEA,IAAI,OAAO,WAAW,cAAc,OAAO,OAAO,QAAQ,KAAK,UAAU;QACvE,UAAU,SAAU,GAAG;YACrB,OAAO,OAAO;QAChB;IACF,OAAO;QACL,UAAU,SAAU,GAAG;YACrB,OAAO,OAAO,OAAO,WAAW,cAAc,IAAI,WAAW,KAAK,UAAU,QAAQ,OAAO,SAAS,GAAG,WAAW,OAAO;QAC3H;IACF;IAEA,OAAO,QAAQ;AACjB;AAEA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IACtC,IAAI,OAAO,KAAK;QACd,OAAO,cAAc,CAAC,KAAK,KAAK;YAC9B,OAAO;YACP,YAAY;YACZ,cAAc;YACd,UAAU;QACZ;IACF,OAAO;QACL,GAAG,CAAC,IAAI,GAAG;IACb;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,MAAM,EAAE,cAAc;IACrC,IAAI,OAAO,OAAO,IAAI,CAAC;IAEvB,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,UAAU,OAAO,qBAAqB,CAAC;QAC3C,IAAI,gBAAgB,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG;YACxD,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;QAChE;QACA,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;IACxB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,MAAM;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,CAAC;QAEpD,IAAI,IAAI,GAAG;YACT,QAAQ,OAAO,SAAS,MAAM,OAAO,CAAC,SAAU,GAAG;gBACjD,gBAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;YAC1C;QACF,OAAO,IAAI,OAAO,yBAAyB,EAAE;YAC3C,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC;QACnE,OAAO;YACL,QAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG;gBAC3C,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;YAC7E;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,mBAAmB,GAAG;IAC7B,OAAO,mBAAmB,QAAQ,iBAAiB,QAAQ,4BAA4B,QAAQ;AACjG;AAEA,SAAS,mBAAmB,GAAG;IAC7B,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,kBAAkB;AACnD;AAEA,SAAS,iBAAiB,IAAI;IAC5B,IAAI,OAAO,WAAW,eAAe,OAAO,QAAQ,IAAI,OAAO,OAAO,OAAO,MAAM,IAAI,CAAC;AAC1F;AAEA,SAAS,4BAA4B,CAAC,EAAE,MAAM;IAC5C,IAAI,CAAC,GAAG;IACR,IAAI,OAAO,MAAM,UAAU,OAAO,kBAAkB,GAAG;IACvD,IAAI,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IACpD,IAAI,MAAM,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;IAC3D,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM,IAAI,CAAC;IAClD,IAAI,MAAM,eAAe,2CAA2C,IAAI,CAAC,IAAI,OAAO,kBAAkB,GAAG;AAC3G;AAEA,SAAS,kBAAkB,GAAG,EAAE,GAAG;IACjC,IAAI,OAAO,QAAQ,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,MAAM;IAErD,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;IAErE,OAAO;AACT;AAEA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB;AAEA,IAAI,0BAA0B,OAAO,SAAS,CAAC,QAAQ;AACvD,IAAI,WAAW,IAAI,QAAQ,4FAA4F;AACvH,yGAAyG;AAEzG,SAAS,eAAe,GAAG;AAC3B,SAAS,QAAQ,GAAG,SAAS,QAAQ;AAErC,SAAS,SAAS,GAAG,SAAU,KAAK;IAClC,IAAI,gBAAgB,IAAI,CAAC,OAAO,EAC5B,uBAAuB,cAAc,oBAAoB,EACzD,wBAAwB,cAAc,iBAAiB,EACvD,oBAAoB,0BAA0B,KAAK,IAAI,SAAU,CAAC,EAAE,CAAC;QACvE,OAAO,OAAO,MAAM,cAAc,uBAAuB;IAC3D,IAAI;IACJ,OAAO,OAAO,UAAU,WAAW,QAAQ,KAAK,SAAS,CAAC,aAAa,OAAO,MAAM,MAAM,oBAAoB,mBAAmB;AACnI;AAEA,SAAS,MAAM,GAAG,SAAU,IAAI,EAAE,KAAK;IACrC,OAAO,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,OAAO,CAAC,cAAc,OAAO,MAAM,OAAO,CAAC,cAAc;AAC5G;AAEA,SAAS,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO;IACvC,OAAO,SAAS,IAAI,CAAC,QAAQ,QAAQ;AACvC,EAAE,gGAAgG;AAClG,+FAA+F;AAE/F,SAAS,aAAa,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG;IAC/D,QAAQ,SAAS,EAAE;IACnB,mBAAmB,oBAAoB,EAAE;IAEzC,IAAI,UAAU;QACZ,MAAM,SAAS,KAAK;IACtB;IAEA,IAAI;IAEJ,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACpC,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,OAAO,gBAAgB,CAAC,EAAE;QAC5B;IACF;IAEA,IAAI;IAEJ,IAAI,qBAAqB,wBAAwB,IAAI,CAAC,MAAM;QAC1D,MAAM,IAAI,CAAC;QACX,mBAAmB,IAAI,MAAM,IAAI,MAAM;QACvC,iBAAiB,IAAI,CAAC;QAEtB,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,EAAG;YAClC,gBAAgB,CAAC,EAAE,GAAG,aAAa,GAAG,CAAC,EAAE,EAAE,OAAO,kBAAkB,UAAU;QAChF;QAEA,MAAM,GAAG;QACT,iBAAiB,GAAG;QACpB,OAAO;IACT;IAEA,IAAI,OAAO,IAAI,MAAM,EAAE;QACrB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,QAAQ,SAAS,YAAY,QAAQ,MAAM;QAC7C,MAAM,IAAI,CAAC;QACX,mBAAmB,CAAC;QACpB,iBAAiB,IAAI,CAAC;QAEtB,IAAI,aAAa,EAAE,EACf;QAEJ,IAAK,QAAQ,IAAK;YAChB,wBAAwB,GACxB,IAAI,IAAI,cAAc,CAAC,OAAO;gBAC5B,WAAW,IAAI,CAAC;YAClB;QACF;QAEA,WAAW,IAAI;QAEf,IAAK,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;YACzC,OAAO,UAAU,CAAC,EAAE;YACpB,gBAAgB,CAAC,KAAK,GAAG,aAAa,GAAG,CAAC,KAAK,EAAE,OAAO,kBAAkB,UAAU;QACtF;QAEA,MAAM,GAAG;QACT,iBAAiB,GAAG;IACtB,OAAO;QACL,mBAAmB;IACrB;IAEA,OAAO;AACT;AAEA,IAAI,YAAY,IAAI;AAEpB,UAAU,QAAQ,GAAG,SAAU,KAAK;IAClC,OAAO,MAAM,KAAK;AACpB;AAEA,UAAU,IAAI,GAAG,UAAU,WAAW,GAAG,SAAU,KAAK;IACtD,OAAO;AACT;AAEA,SAAS,WAAW,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC1C,OAAO,UAAU,IAAI,CAAC,QAAQ,QAAQ;AACxC;AAEA,SAAS,WAAW,OAAO;IACzB,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;IACnF,IAAI,UAAU,QAAQ,KAAK,CAAC,wBACxB,aAAa,QAAQ,KAAK,CAAC,2BAA2B,EAAE,EACxD,OAAO,EAAE,EACT,IAAI;IAER,SAAS;QACP,IAAI,QAAQ,CAAC;QACb,KAAK,IAAI,CAAC,QAAQ,sBAAsB;QAExC,MAAO,IAAI,QAAQ,MAAM,CAAE;YACzB,IAAI,OAAO,OAAO,CAAC,EAAE,EAAE,+CAA+C;YAEtE,IAAI,wBAAwB,IAAI,CAAC,OAAO;gBACtC;YACF,EAAE,aAAa;YAGf,IAAI,SAAS,2CAA2C,IAAI,CAAC;YAE7D,IAAI,QAAQ;gBACV,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE;YACzB;YAEA;QACF,EAAE,0EAA0E;QAC5E,2EAA2E;QAG3E,gBAAgB;QAChB,gBAAgB,QAAQ,cAAc;QAEtC,MAAM,KAAK,GAAG,EAAE;QAEhB,MAAO,IAAI,QAAQ,MAAM,CAAE;YACzB,IAAI,QAAQ,OAAO,CAAC,EAAE;YAEtB,IAAI,iCAAiC,IAAI,CAAC,QAAQ;gBAChD;YACF,OAAO,IAAI,MAAM,IAAI,CAAC,QAAQ;gBAC5B,MAAM,KAAK,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI,SAAS,QAAQ,MAAM,EAAE;gBAClC,kDAAkD;gBAClD,MAAM,IAAI,MAAM,kBAAkB,CAAC,IAAI,CAAC,IAAI,MAAM,KAAK,SAAS,CAAC;YACnE,OAAO;gBACL;YACF;QACF;IACF,EAAE,8DAA8D;IAChE,gBAAgB;IAGhB,SAAS,gBAAgB,KAAK;QAC5B,IAAI,aAAa,wBAAwB,IAAI,CAAC,OAAO,CAAC,EAAE;QAExD,IAAI,YAAY;YACd,IAAI,YAAY,UAAU,CAAC,EAAE,KAAK,QAAQ,QAAQ;YAClD,IAAI,OAAO,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM;YACrC,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS;YAExC,IAAI,SAAS,IAAI,CAAC,WAAW;gBAC3B,WAAW,SAAS,MAAM,CAAC,GAAG,SAAS,MAAM,GAAG;YAClD;YAEA,KAAK,CAAC,YAAY,WAAW,GAAG;YAChC,KAAK,CAAC,YAAY,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI;YAClD;QACF;IACF,EAAE,gBAAgB;IAClB,mDAAmD;IAGnD,SAAS;QACP,IAAI,mBAAmB,GACnB,kBAAkB,OAAO,CAAC,IAAI,EAC9B,cAAc,gBAAgB,KAAK,CAAC;QACxC,IAAI,OAAO;YACT,UAAU,CAAC,WAAW,CAAC,EAAE;YACzB,UAAU,OAAO,WAAW,CAAC,EAAE,KAAK,cAAc,IAAI,CAAC,WAAW,CAAC,EAAE;YACrE,UAAU,CAAC,WAAW,CAAC,EAAE;YACzB,UAAU,OAAO,WAAW,CAAC,EAAE,KAAK,cAAc,IAAI,CAAC,WAAW,CAAC,EAAE;YACrE,OAAO,EAAE;YACT,gBAAgB,EAAE;QACpB,GAAG,qDAAqD;QACxD,uDAAuD;QACvD,4DAA4D;QAE5D,IAAI,KAAK,QAAQ,KAAK,GAAG;YACvB,KAAK,QAAQ,IAAI;QACnB;QAEA,IAAI,KAAK,QAAQ,KAAK,GAAG;YACvB,KAAK,QAAQ,IAAI;QACnB;QAEA,IAAI,WAAW,GACX,cAAc;QAElB,MAAO,IAAI,QAAQ,MAAM,EAAE,IAAK;YAC9B,8EAA8E;YAC9E,kFAAkF;YAClF,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,UAAU,GAAG;gBAC5I;YACF;YAEA,IAAI,YAAY,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,KAAK,KAAK,QAAQ,MAAM,GAAG,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE;YAEvF,IAAI,cAAc,OAAO,cAAc,OAAO,cAAc,OAAO,cAAc,MAAM;gBACrF,KAAK,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC1B,KAAK,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI;gBAE1C,IAAI,cAAc,KAAK;oBACrB;gBACF,OAAO,IAAI,cAAc,KAAK;oBAC5B;gBACF,OAAO,IAAI,cAAc,KAAK;oBAC5B;oBACA;gBACF;YACF,OAAO;gBACL;YACF;QACF,EAAE,oCAAoC;QAGtC,IAAI,CAAC,YAAY,KAAK,QAAQ,KAAK,GAAG;YACpC,KAAK,QAAQ,GAAG;QAClB;QAEA,IAAI,CAAC,eAAe,KAAK,QAAQ,KAAK,GAAG;YACvC,KAAK,QAAQ,GAAG;QAClB,EAAE,mCAAmC;QAGrC,IAAI,QAAQ,MAAM,EAAE;YAClB,IAAI,aAAa,KAAK,QAAQ,EAAE;gBAC9B,MAAM,IAAI,MAAM,qDAAqD,CAAC,mBAAmB,CAAC;YAC5F;YAEA,IAAI,gBAAgB,KAAK,QAAQ,EAAE;gBACjC,MAAM,IAAI,MAAM,uDAAuD,CAAC,mBAAmB,CAAC;YAC9F;QACF;QAEA,OAAO;IACT;IAEA,MAAO,IAAI,QAAQ,MAAM,CAAE;QACzB;IACF;IAEA,OAAO;AACT;AAEA,+DAA+D;AAC/D,iEAAiE;AACjE,+CAA+C;AAC/C,SAAS,iBAAkB,KAAK,EAAE,OAAO,EAAE,OAAO;IAChD,IAAI,cAAc,MACd,oBAAoB,OACpB,mBAAmB,OACnB,cAAc;IAClB,OAAO,SAAS;QACd,IAAI,eAAe,CAAC,kBAAkB;YACpC,IAAI,mBAAmB;gBACrB;YACF,OAAO;gBACL,cAAc;YAChB,EAAE,uEAAuE;YACzE,iEAAiE;YAGjE,IAAI,QAAQ,eAAe,SAAS;gBAClC,OAAO;YACT;YAEA,mBAAmB;QACrB;QAEA,IAAI,CAAC,mBAAmB;YACtB,IAAI,CAAC,kBAAkB;gBACrB,cAAc;YAChB,EAAE,0EAA0E;YAC5E,yBAAyB;YAGzB,IAAI,WAAW,QAAQ,aAAa;gBAClC,OAAO,CAAC;YACV;YAEA,oBAAoB;YACpB,OAAO;QACT,EAAE,0EAA0E;IAC5E,+CAA+C;IAEjD;AACF;AAEA,SAAS,WAAW,MAAM,EAAE,OAAO;IACjC,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;IAEnF,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU,WAAW;IACvB;IAEA,IAAI,MAAM,OAAO,CAAC,UAAU;QAC1B,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,UAAU,OAAO,CAAC,EAAE;IACtB,EAAE,8BAA8B;IAGhC,IAAI,QAAQ,OAAO,KAAK,CAAC,wBACrB,aAAa,OAAO,KAAK,CAAC,2BAA2B,EAAE,EACvD,QAAQ,QAAQ,KAAK,EACrB,cAAc,QAAQ,WAAW,IAAI,SAAU,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY;QAC1F,OAAO,SAAS;IAClB,GACI,aAAa,GACb,aAAa,QAAQ,UAAU,IAAI,GACnC,UAAU,GACV,SAAS,GACT,aACA;IACJ;;GAEC,GAGD,SAAS,SAAS,IAAI,EAAE,KAAK;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EACpB,YAAY,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,EAAE,GAAG,KACxC,UAAU,KAAK,MAAM,GAAG,IAAI,KAAK,MAAM,CAAC,KAAK;YAEjD,IAAI,cAAc,OAAO,cAAc,KAAK;gBAC1C,uBAAuB;gBACvB,IAAI,CAAC,YAAY,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,WAAW,UAAU;oBAC7D;oBAEA,IAAI,aAAa,YAAY;wBAC3B,OAAO;oBACT;gBACF;gBAEA;YACF;QACF;QAEA,OAAO;IACT,EAAE,mEAAmE;IAGrE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,OAAO,KAAK,CAAC,EAAE,EACf,UAAU,MAAM,MAAM,GAAG,KAAK,QAAQ,EACtC,cAAc,GACd,QAAQ,SAAS,KAAK,QAAQ,GAAG;QACrC,IAAI,WAAW,iBAAiB,OAAO,SAAS;QAEhD,MAAO,gBAAgB,WAAW,cAAc,WAAY;YAC1D,IAAI,SAAS,MAAM,QAAQ,cAAc;gBACvC,KAAK,MAAM,GAAG,UAAU;gBACxB;YACF;QACF;QAEA,IAAI,gBAAgB,WAAW;YAC7B,OAAO;QACT,EAAE,0EAA0E;QAC5E,mCAAmC;QAGnC,UAAU,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ;IACvD,EAAE,oBAAoB;IAGtB,IAAI,aAAa;IAEjB,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,MAAM,EAAE,KAAM;QACxC,IAAI,QAAQ,KAAK,CAAC,GAAG,EACjB,SAAS,MAAM,QAAQ,GAAG,MAAM,MAAM,GAAG,aAAa;QAE1D,cAAc,MAAM,QAAQ,GAAG,MAAM,QAAQ;QAE7C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,IAAK;YAC3C,IAAI,OAAO,MAAM,KAAK,CAAC,EAAE,EACrB,YAAY,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,EAAE,GAAG,KACxC,UAAU,KAAK,MAAM,GAAG,IAAI,KAAK,MAAM,CAAC,KAAK,MAC7C,YAAY,MAAM,cAAc,IAAI,MAAM,cAAc,CAAC,EAAE,IAAI;YAEnE,IAAI,cAAc,KAAK;gBACrB;YACF,OAAO,IAAI,cAAc,KAAK;gBAC5B,MAAM,MAAM,CAAC,QAAQ;gBACrB,WAAW,MAAM,CAAC,QAAQ;YAC1B,wBAAwB,GAC1B,OAAO,IAAI,cAAc,KAAK;gBAC5B,MAAM,MAAM,CAAC,QAAQ,GAAG;gBACxB,WAAW,MAAM,CAAC,QAAQ,GAAG;gBAC7B;YACF,OAAO,IAAI,cAAc,MAAM;gBAC7B,IAAI,oBAAoB,MAAM,KAAK,CAAC,IAAI,EAAE,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;gBAErE,IAAI,sBAAsB,KAAK;oBAC7B,cAAc;gBAChB,OAAO,IAAI,sBAAsB,KAAK;oBACpC,WAAW;gBACb;YACF;QACF;IACF,EAAE,iCAAiC;IAGnC,IAAI,aAAa;QACf,MAAO,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAE;YAC/B,MAAM,GAAG;YACT,WAAW,GAAG;QAChB;IACF,OAAO,IAAI,UAAU;QACnB,MAAM,IAAI,CAAC;QACX,WAAW,IAAI,CAAC;IAClB;IAEA,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,MAAM,GAAG,GAAG,KAAM;QAC5C,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG;IACxC;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB,EAAE,6DAA6D;AAE/D,SAAS,aAAa,OAAO,EAAE,OAAO;IACpC,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU,WAAW;IACvB;IAEA,IAAI,eAAe;IAEnB,SAAS;QACP,IAAI,QAAQ,OAAO,CAAC,eAAe;QAEnC,IAAI,CAAC,OAAO;YACV,OAAO,QAAQ,QAAQ;QACzB;QAEA,QAAQ,QAAQ,CAAC,OAAO,SAAU,GAAG,EAAE,IAAI;YACzC,IAAI,KAAK;gBACP,OAAO,QAAQ,QAAQ,CAAC;YAC1B;YAEA,IAAI,iBAAiB,WAAW,MAAM,OAAO;YAC7C,QAAQ,OAAO,CAAC,OAAO,gBAAgB,SAAU,GAAG;gBAClD,IAAI,KAAK;oBACP,OAAO,QAAQ,QAAQ,CAAC;gBAC1B;gBAEA;YACF;QACF;IACF;IAEA;AACF;AAEA,SAAS,gBAAgB,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO;IAC9F,IAAI,CAAC,SAAS;QACZ,UAAU,CAAC;IACb;IAEA,IAAI,OAAO,QAAQ,OAAO,KAAK,aAAa;QAC1C,QAAQ,OAAO,GAAG;IACpB;IAEA,IAAI,OAAO,UAAU,QAAQ,QAAQ;IAErC,IAAI,CAAC,MAAM;QACT;IACF;IAEA,KAAK,IAAI,CAAC;QACR,OAAO;QACP,OAAO,EAAE;IACX,IAAI,+CAA+C;IAEnD,SAAS,aAAa,KAAK;QACzB,OAAO,MAAM,GAAG,CAAC,SAAU,KAAK;YAC9B,OAAO,MAAM;QACf;IACF;IAEA,IAAI,QAAQ,EAAE;IACd,IAAI,gBAAgB,GAChB,gBAAgB,GAChB,WAAW,EAAE,EACb,UAAU,GACV,UAAU;IAEd,IAAI,QAAQ,SAAS,MAAM,CAAC;QAC1B,IAAI,UAAU,IAAI,CAAC,EAAE,EACjB,QAAQ,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC;QACpE,QAAQ,KAAK,GAAG;QAEhB,IAAI,QAAQ,KAAK,IAAI,QAAQ,OAAO,EAAE;YACpC,IAAI;YAEJ,+CAA+C;YAC/C,IAAI,CAAC,eAAe;gBAClB,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE;gBACtB,gBAAgB;gBAChB,gBAAgB;gBAEhB,IAAI,MAAM;oBACR,WAAW,QAAQ,OAAO,GAAG,IAAI,aAAa,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,OAAO,KAAK,EAAE;oBACtF,iBAAiB,SAAS,MAAM;oBAChC,iBAAiB,SAAS,MAAM;gBAClC;YACF,EAAE,qBAAqB;YAGvB,CAAC,YAAY,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,mBAAmB,MAAM,GAAG,CAAC,SAAU,KAAK;gBACvF,OAAO,CAAC,QAAQ,KAAK,GAAG,MAAM,GAAG,IAAI;YACvC,MAAM,kCAAkC;YAGxC,IAAI,QAAQ,KAAK,EAAE;gBACjB,WAAW,MAAM,MAAM;YACzB,OAAO;gBACL,WAAW,MAAM,MAAM;YACzB;QACF,OAAO;YACL,8CAA8C;YAC9C,IAAI,eAAe;gBACjB,oEAAoE;gBACpE,IAAI,MAAM,MAAM,IAAI,QAAQ,OAAO,GAAG,KAAK,IAAI,KAAK,MAAM,GAAG,GAAG;oBAC9D,IAAI;oBAEJ,cAAc;oBACd,CAAC,aAAa,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,mBAAmB,aAAa;gBACjF,OAAO;oBACL,IAAI;oBAEJ,2BAA2B;oBAC3B,IAAI,cAAc,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,QAAQ,OAAO;oBAExD,CAAC,aAAa,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,mBAAmB,aAAa,MAAM,KAAK,CAAC,GAAG;oBAE9F,IAAI,OAAO;wBACT,UAAU;wBACV,UAAU,UAAU,gBAAgB;wBACpC,UAAU;wBACV,UAAU,UAAU,gBAAgB;wBACpC,OAAO;oBACT;oBAEA,IAAI,KAAK,KAAK,MAAM,GAAG,KAAK,MAAM,MAAM,IAAI,QAAQ,OAAO,EAAE;wBAC3D,0BAA0B;wBAC1B,IAAI,gBAAgB,MAAM,IAAI,CAAC;wBAC/B,IAAI,gBAAgB,MAAM,IAAI,CAAC;wBAC/B,IAAI,iBAAiB,MAAM,MAAM,IAAI,KAAK,SAAS,MAAM,GAAG,KAAK,QAAQ;wBAEzE,IAAI,CAAC,iBAAiB,kBAAkB,OAAO,MAAM,GAAG,GAAG;4BACzD,qFAAqF;4BACrF,kEAAkE;4BAClE,SAAS,MAAM,CAAC,KAAK,QAAQ,EAAE,GAAG;wBACpC;wBAEA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,eAAe;4BACvD,SAAS,IAAI,CAAC;wBAChB;oBACF;oBAEA,MAAM,IAAI,CAAC;oBACX,gBAAgB;oBAChB,gBAAgB;oBAChB,WAAW,EAAE;gBACf;YACF;YAEA,WAAW,MAAM,MAAM;YACvB,WAAW,MAAM,MAAM;QACzB;IACF;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM;IACR;IAEA,OAAO;QACL,aAAa;QACb,aAAa;QACb,WAAW;QACX,WAAW;QACX,OAAO;IACT;AACF;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,MAAM,OAAO,CAAC,OAAO;QACvB,OAAO,KAAK,GAAG,CAAC,aAAa,IAAI,CAAC;IACpC;IAEA,IAAI,MAAM,EAAE;IAEZ,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,EAAE;QACxC,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW;IACvC;IAEA,IAAI,IAAI,CAAC;IACT,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW,GAAG,CAAC,OAAO,KAAK,SAAS,KAAK,cAAc,KAAK,OAAO,KAAK,SAAS;IACxG,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW,GAAG,CAAC,OAAO,KAAK,SAAS,KAAK,cAAc,KAAK,OAAO,KAAK,SAAS;IAExG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,IAAK;QAC1C,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,qDAAqD;QAC/E,uDAAuD;QACvD,4DAA4D;QAE5D,IAAI,KAAK,QAAQ,KAAK,GAAG;YACvB,KAAK,QAAQ,IAAI;QACnB;QAEA,IAAI,KAAK,QAAQ,KAAK,GAAG;YACvB,KAAK,QAAQ,IAAI;QACnB;QAEA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,GAAG,MAAM,KAAK,QAAQ,GAAG,OAAO,KAAK,QAAQ,GAAG,MAAM,KAAK,QAAQ,GAAG;QACrG,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK;IAChC;IAEA,OAAO,IAAI,IAAI,CAAC,QAAQ;AAC1B;AACA,SAAS,oBAAoB,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO;IAClG,OAAO,YAAY,gBAAgB,aAAa,aAAa,QAAQ,QAAQ,WAAW,WAAW;AACrG;AACA,SAAS,YAAY,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO;IAC1E,OAAO,oBAAoB,UAAU,UAAU,QAAQ,QAAQ,WAAW,WAAW;AACvF;AAEA,SAAS,WAAW,CAAC,EAAE,CAAC;IACtB,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;QACzB,OAAO;IACT;IAEA,OAAO,gBAAgB,GAAG;AAC5B;AACA,SAAS,gBAAgB,KAAK,EAAE,KAAK;IACnC,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,EAAE;QAC/B,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE;YACzB,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,uBAAuB,oBAAoB,KAAK,KAAK,GACrD,WAAW,qBAAqB,QAAQ,EACxC,WAAW,qBAAqB,QAAQ;IAE5C,IAAI,aAAa,WAAW;QAC1B,KAAK,QAAQ,GAAG;IAClB,OAAO;QACL,OAAO,KAAK,QAAQ;IACtB;IAEA,IAAI,aAAa,WAAW;QAC1B,KAAK,QAAQ,GAAG;IAClB,OAAO;QACL,OAAO,KAAK,QAAQ;IACtB;AACF;AACA,SAAS,MAAM,IAAI,EAAE,MAAM,EAAE,IAAI;IAC/B,OAAO,UAAU,MAAM;IACvB,SAAS,UAAU,QAAQ;IAC3B,IAAI,MAAM,CAAC,GAAG,kFAAkF;IAChG,iFAAiF;IACjF,gCAAgC;IAEhC,IAAI,KAAK,KAAK,IAAI,OAAO,KAAK,EAAE;QAC9B,IAAI,KAAK,GAAG,KAAK,KAAK,IAAI,OAAO,KAAK;IACxC;IAEA,IAAI,KAAK,WAAW,IAAI,OAAO,WAAW,EAAE;QAC1C,IAAI,CAAC,gBAAgB,OAAO;YAC1B,iFAAiF;YACjF,IAAI,WAAW,GAAG,OAAO,WAAW,IAAI,KAAK,WAAW;YACxD,IAAI,WAAW,GAAG,OAAO,WAAW,IAAI,KAAK,WAAW;YACxD,IAAI,SAAS,GAAG,OAAO,SAAS,IAAI,KAAK,SAAS;YAClD,IAAI,SAAS,GAAG,OAAO,SAAS,IAAI,KAAK,SAAS;QACpD,OAAO,IAAI,CAAC,gBAAgB,SAAS;YACnC,6CAA6C;YAC7C,IAAI,WAAW,GAAG,KAAK,WAAW;YAClC,IAAI,WAAW,GAAG,KAAK,WAAW;YAClC,IAAI,SAAS,GAAG,KAAK,SAAS;YAC9B,IAAI,SAAS,GAAG,KAAK,SAAS;QAChC,OAAO;YACL,gCAAgC;YAChC,IAAI,WAAW,GAAG,YAAY,KAAK,KAAK,WAAW,EAAE,OAAO,WAAW;YACvE,IAAI,WAAW,GAAG,YAAY,KAAK,KAAK,WAAW,EAAE,OAAO,WAAW;YACvE,IAAI,SAAS,GAAG,YAAY,KAAK,KAAK,SAAS,EAAE,OAAO,SAAS;YACjE,IAAI,SAAS,GAAG,YAAY,KAAK,KAAK,SAAS,EAAE,OAAO,SAAS;QACnE;IACF;IAEA,IAAI,KAAK,GAAG,EAAE;IACd,IAAI,YAAY,GACZ,cAAc,GACd,aAAa,GACb,eAAe;IAEnB,MAAO,YAAY,KAAK,KAAK,CAAC,MAAM,IAAI,cAAc,OAAO,KAAK,CAAC,MAAM,CAAE;QACzE,IAAI,cAAc,KAAK,KAAK,CAAC,UAAU,IAAI;YACzC,UAAU;QACZ,GACI,gBAAgB,OAAO,KAAK,CAAC,YAAY,IAAI;YAC/C,UAAU;QACZ;QAEA,IAAI,WAAW,aAAa,gBAAgB;YAC1C,2DAA2D;YAC3D,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,aAAa;YACtC;YACA,gBAAgB,YAAY,QAAQ,GAAG,YAAY,QAAQ;QAC7D,OAAO,IAAI,WAAW,eAAe,cAAc;YACjD,2DAA2D;YAC3D,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,eAAe;YACxC;YACA,cAAc,cAAc,QAAQ,GAAG,cAAc,QAAQ;QAC/D,OAAO;YACL,gCAAgC;YAChC,IAAI,aAAa;gBACf,UAAU,KAAK,GAAG,CAAC,YAAY,QAAQ,EAAE,cAAc,QAAQ;gBAC/D,UAAU;gBACV,UAAU,KAAK,GAAG,CAAC,YAAY,QAAQ,GAAG,YAAY,cAAc,QAAQ,GAAG;gBAC/E,UAAU;gBACV,OAAO,EAAE;YACX;YACA,WAAW,YAAY,YAAY,QAAQ,EAAE,YAAY,KAAK,EAAE,cAAc,QAAQ,EAAE,cAAc,KAAK;YAC3G;YACA;YACA,IAAI,KAAK,CAAC,IAAI,CAAC;QACjB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,KAAK,EAAE,IAAI;IAC5B,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,OAAO,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC,QAAQ;YAChD,OAAO,WAAW,MAAM,CAAC,EAAE;QAC7B;QAEA,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,gBAAgB,WAAW,WAAW,MAAM;IACrD;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,KAAK;IAC5B,OAAO,MAAM,WAAW,IAAI,MAAM,WAAW,KAAK,MAAM,WAAW;AACrE;AAEA,SAAS,YAAY,KAAK,EAAE,IAAI,EAAE,MAAM;IACtC,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT,OAAO;QACL,MAAM,QAAQ,GAAG;QACjB,OAAO;YACL,MAAM;YACN,QAAQ;QACV;IACF;AACF;AAEA,SAAS,WAAW,IAAI,EAAE,KAAK;IAC7B,OAAO,KAAK,QAAQ,GAAG,MAAM,QAAQ,IAAI,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM,QAAQ;AACzF;AAEA,SAAS,UAAU,IAAI,EAAE,MAAM;IAC7B,OAAO;QACL,UAAU,KAAK,QAAQ;QACvB,UAAU,KAAK,QAAQ;QACvB,UAAU,KAAK,QAAQ,GAAG;QAC1B,UAAU,KAAK,QAAQ;QACvB,OAAO,KAAK,KAAK;IACnB;AACF;AAEA,SAAS,WAAW,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU;IACtE,yFAAyF;IACzF,wEAAwE;IACxE,IAAI,OAAO;QACT,QAAQ;QACR,OAAO;QACP,OAAO;IACT,GACI,QAAQ;QACV,QAAQ;QACR,OAAO;QACP,OAAO;IACT,GAAG,6BAA6B;IAEhC,cAAc,MAAM,MAAM;IAC1B,cAAc,MAAM,OAAO,OAAO,kFAAkF;IAEpH,MAAO,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAE;QACzE,IAAI,cAAc,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,EACpC,eAAe,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC;QAE3C,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,OAAO,WAAW,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,OAAO,YAAY,CAAC,EAAE,KAAK,GAAG,GAAG;YAC9G,oBAAoB;YACpB,aAAa,MAAM,MAAM;QAC3B,OAAO,IAAI,WAAW,CAAC,EAAE,KAAK,OAAO,YAAY,CAAC,EAAE,KAAK,KAAK;YAC5D,IAAI;YAEJ,gBAAgB;YAChB,CAAC,cAAc,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,mBAAmB,cAAc;QACtF,OAAO,IAAI,YAAY,CAAC,EAAE,KAAK,OAAO,WAAW,CAAC,EAAE,KAAK,KAAK;YAC5D,IAAI;YAEJ,kBAAkB;YAClB,CAAC,eAAe,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,mBAAmB,cAAc;QACxF,OAAO,IAAI,WAAW,CAAC,EAAE,KAAK,OAAO,YAAY,CAAC,EAAE,KAAK,KAAK;YAC5D,yBAAyB;YACzB,QAAQ,MAAM,MAAM;QACtB,OAAO,IAAI,YAAY,CAAC,EAAE,KAAK,OAAO,WAAW,CAAC,EAAE,KAAK,KAAK;YAC5D,0BAA0B;YAC1B,QAAQ,MAAM,OAAO,MAAM;QAC7B,OAAO,IAAI,gBAAgB,cAAc;YACvC,mBAAmB;YACnB,KAAK,KAAK,CAAC,IAAI,CAAC;YAChB,KAAK,KAAK;YACV,MAAM,KAAK;QACb,OAAO;YACL,mBAAmB;YACnB,SAAS,MAAM,cAAc,OAAO,cAAc;QACpD;IACF,EAAE,0CAA0C;IAG5C,eAAe,MAAM;IACrB,eAAe,MAAM;IACrB,cAAc;AAChB;AAEA,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,KAAK;IACrC,IAAI,YAAY,cAAc,OAC1B,eAAe,cAAc;IAEjC,IAAI,WAAW,cAAc,WAAW,eAAe;QACrD,oEAAoE;QACpE,IAAI,gBAAgB,WAAW,iBAAiB,mBAAmB,OAAO,WAAW,UAAU,MAAM,GAAG,aAAa,MAAM,GAAG;YAC5H,IAAI;YAEJ,CAAC,eAAe,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,mBAAmB;YAExE;QACF,OAAO,IAAI,gBAAgB,cAAc,cAAc,mBAAmB,MAAM,cAAc,aAAa,MAAM,GAAG,UAAU,MAAM,GAAG;YACrI,IAAI;YAEJ,CAAC,eAAe,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,mBAAmB;YAExE;QACF;IACF,OAAO,IAAI,WAAW,WAAW,eAAe;QAC9C,IAAI;QAEJ,CAAC,eAAe,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,mBAAmB;QAExE;IACF;IAEA,SAAS,MAAM,WAAW;AAC5B;AAEA,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IACtC,IAAI,YAAY,cAAc,OAC1B,eAAe,eAAe,OAAO;IAEzC,IAAI,aAAa,MAAM,EAAE;QACvB,IAAI;QAEJ,CAAC,eAAe,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,mBAAmB,aAAa,MAAM;IAC7F,OAAO;QACL,SAAS,MAAM,OAAO,eAAe,WAAW,OAAO,YAAY;IACrE;AACF;AAEA,SAAS,SAAS,IAAI,EAAE,IAAI,EAAE,KAAK;IACjC,KAAK,QAAQ,GAAG;IAChB,KAAK,KAAK,CAAC,IAAI,CAAC;QACd,UAAU;QACV,MAAM;QACN,QAAQ;IACV;AACF;AAEA,SAAS,cAAc,IAAI,EAAE,MAAM,EAAE,KAAK;IACxC,MAAO,OAAO,MAAM,GAAG,MAAM,MAAM,IAAI,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,MAAM,CAAE;QACzE,IAAI,OAAO,OAAO,KAAK,CAAC,OAAO,KAAK,GAAG;QACvC,KAAK,KAAK,CAAC,IAAI,CAAC;QAChB,OAAO,MAAM;IACf;AACF;AAEA,SAAS,eAAe,IAAI,EAAE,MAAM;IAClC,MAAO,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,MAAM,CAAE;QACzC,IAAI,OAAO,OAAO,KAAK,CAAC,OAAO,KAAK,GAAG;QACvC,KAAK,KAAK,CAAC,IAAI,CAAC;IAClB;AACF;AAEA,SAAS,cAAc,KAAK;IAC1B,IAAI,MAAM,EAAE,EACR,YAAY,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;IAE3C,MAAO,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAE;QACvC,IAAI,OAAO,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,wGAAwG;QAE7I,IAAI,cAAc,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK;YACxC,YAAY;QACd;QAEA,IAAI,cAAc,IAAI,CAAC,EAAE,EAAE;YACzB,IAAI,IAAI,CAAC;YACT,MAAM,KAAK;QACb,OAAO;YACL;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,KAAK,EAAE,YAAY;IACzC,IAAI,UAAU,EAAE,EACZ,SAAS,EAAE,EACX,aAAa,GACb,iBAAiB,OACjB,aAAa;IAEjB,MAAO,aAAa,aAAa,MAAM,IAAI,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAE;QAC3E,IAAI,SAAS,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,EACjC,QAAQ,YAAY,CAAC,WAAW,EAAE,2CAA2C;QAEjF,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB;QACF;QAEA,iBAAiB,kBAAkB,MAAM,CAAC,EAAE,KAAK;QACjD,OAAO,IAAI,CAAC;QACZ,cAAc,oEAAoE;QAClF,8CAA8C;QAE9C,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;YACrB,aAAa;YAEb,MAAO,MAAM,CAAC,EAAE,KAAK,IAAK;gBACxB,QAAQ,IAAI,CAAC;gBACb,SAAS,MAAM,KAAK,CAAC,EAAE,MAAM,KAAK,CAAC;YACrC;QACF;QAEA,IAAI,MAAM,MAAM,CAAC,OAAO,OAAO,MAAM,CAAC,IAAI;YACxC,QAAQ,IAAI,CAAC;YACb,MAAM,KAAK;QACb,OAAO;YACL,aAAa;QACf;IACF;IAEA,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,OAAO,gBAAgB;QACjE,aAAa;IACf;IAEA,IAAI,YAAY;QACd,OAAO;IACT;IAEA,MAAO,aAAa,aAAa,MAAM,CAAE;QACvC,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa;IACxC;IAEA,OAAO;QACL,QAAQ;QACR,SAAS;IACX;AACF;AAEA,SAAS,WAAW,OAAO;IACzB,OAAO,QAAQ,MAAM,CAAC,SAAU,IAAI,EAAE,MAAM;QAC1C,OAAO,QAAQ,MAAM,CAAC,EAAE,KAAK;IAC/B,GAAG;AACL;AAEA,SAAS,mBAAmB,KAAK,EAAE,aAAa,EAAE,KAAK;IACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,IAAI,gBAAgB,aAAa,CAAC,cAAc,MAAM,GAAG,QAAQ,EAAE,CAAC,MAAM,CAAC;QAE3E,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,KAAK,MAAM,eAAe;YACxD,OAAO;QACT;IACF;IAEA,MAAM,KAAK,IAAI;IACf,OAAO;AACT;AAEA,SAAS,oBAAoB,KAAK;IAChC,IAAI,WAAW;IACf,IAAI,WAAW;IACf,MAAM,OAAO,CAAC,SAAU,IAAI;QAC1B,IAAI,OAAO,SAAS,UAAU;YAC5B,IAAI,UAAU,oBAAoB,KAAK,IAAI;YAC3C,IAAI,aAAa,oBAAoB,KAAK,MAAM;YAEhD,IAAI,aAAa,WAAW;gBAC1B,IAAI,QAAQ,QAAQ,KAAK,WAAW,QAAQ,EAAE;oBAC5C,YAAY,QAAQ,QAAQ;gBAC9B,OAAO;oBACL,WAAW;gBACb;YACF;YAEA,IAAI,aAAa,WAAW;gBAC1B,IAAI,QAAQ,QAAQ,KAAK,WAAW,QAAQ,EAAE;oBAC5C,YAAY,QAAQ,QAAQ;gBAC9B,OAAO;oBACL,WAAW;gBACb;YACF;QACF,OAAO;YACL,IAAI,aAAa,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,GAAG,GAAG;gBAClE;YACF;YAEA,IAAI,aAAa,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,GAAG,GAAG;gBAClE;YACF;QACF;IACF;IACA,OAAO;QACL,UAAU;QACV,UAAU;IACZ;AACF;AAEA,SAAS,aAAa,eAAe;IACnC,IAAI,MAAM,OAAO,CAAC,kBAAkB;QAClC,OAAO,gBAAgB,GAAG,CAAC,cAAc,OAAO;IAClD;IAEA,OAAO,eAAe,eAAe,CAAC,GAAG,kBAAkB,CAAC,GAAG;QAC7D,aAAa,gBAAgB,WAAW;QACxC,WAAW,gBAAgB,SAAS;QACpC,aAAa,gBAAgB,WAAW;QACxC,WAAW,gBAAgB,SAAS;QACpC,OAAO,gBAAgB,KAAK,CAAC,GAAG,CAAC,SAAU,IAAI;YAC7C,OAAO;gBACL,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,cAAc;gBACnC,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,SAAU,CAAC;oBAC/B,IAAI,EAAE,UAAU,CAAC,MAAM;wBACrB,OAAO,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;oBAC5B;oBAEA,IAAI,EAAE,UAAU,CAAC,MAAM;wBACrB,OAAO,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;oBAC5B;oBAEA,OAAO;gBACT;YACF;QACF;IACF;AACF;AAEA,iEAAiE;AACjE,SAAS,oBAAoB,OAAO;IAClC,IAAI,MAAM,EAAE,EACR,QACA;IAEJ,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,SAAS,OAAO,CAAC,EAAE;QAEnB,IAAI,OAAO,KAAK,EAAE;YAChB,YAAY;QACd,OAAO,IAAI,OAAO,OAAO,EAAE;YACzB,YAAY,CAAC;QACf,OAAO;YACL,YAAY;QACd;QAEA,IAAI,IAAI,CAAC;YAAC;YAAW,OAAO,KAAK;SAAC;IACpC;IAEA,OAAO;AACT;AAEA,SAAS,oBAAoB,OAAO;IAClC,IAAI,MAAM,EAAE;IAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,SAAS,OAAO,CAAC,EAAE;QAEvB,IAAI,OAAO,KAAK,EAAE;YAChB,IAAI,IAAI,CAAC;QACX,OAAO,IAAI,OAAO,OAAO,EAAE;YACzB,IAAI,IAAI,CAAC;QACX;QAEA,IAAI,IAAI,CAAC,WAAW,OAAO,KAAK;QAEhC,IAAI,OAAO,KAAK,EAAE;YAChB,IAAI,IAAI,CAAC;QACX,OAAO,IAAI,OAAO,OAAO,EAAE;YACzB,IAAI,IAAI,CAAC;QACX;IACF;IAEA,OAAO,IAAI,IAAI,CAAC;AAClB;AAEA,SAAS,WAAW,CAAC;IACnB,IAAI,IAAI;IACR,IAAI,EAAE,OAAO,CAAC,MAAM;IACpB,IAAI,EAAE,OAAO,CAAC,MAAM;IACpB,IAAI,EAAE,OAAO,CAAC,MAAM;IACpB,IAAI,EAAE,OAAO,CAAC,MAAM;IACpB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/uvu/diff/index.mjs"], "sourcesContent": ["import kleur from 'kleur';\nimport * as diff from 'diff';\n\nconst colors = {\n\t'--': kleur.red,\n\t'··': kleur.grey,\n\t'++': kleur.green,\n};\n\nconst TITLE = kleur.dim().italic;\nconst TAB=kleur.dim('→'), SPACE=kleur.dim('·'), NL=kleur.dim('↵');\nconst LOG = (sym, str) => colors[sym](sym + PRETTY(str)) + '\\n';\nconst LINE = (num, x) => kleur.dim('L' + String(num).padStart(x, '0') + ' ');\nconst PRETTY = str => str.replace(/[ ]/g, SPACE).replace(/\\t/g, TAB).replace(/(\\r?\\n)/g, NL);\n\nfunction line(obj, prev, pad) {\n\tlet char = obj.removed ? '--' : obj.added ? '++' : '··';\n\tlet arr = obj.value.replace(/\\r?\\n$/, '').split('\\n');\n\tlet i=0, tmp, out='';\n\n\tif (obj.added) out += colors[char]().underline(TITLE('Expected:')) + '\\n';\n\telse if (obj.removed) out += colors[char]().underline(TITLE('Actual:')) + '\\n';\n\n\tfor (; i < arr.length; i++) {\n\t\ttmp = arr[i];\n\t\tif (tmp != null) {\n\t\t\tif (prev) out += LINE(prev + i, pad);\n\t\t\tout += LOG(char, tmp || '\\n');\n\t\t}\n\t}\n\n\treturn out;\n}\n\n// TODO: want better diffing\n//~> complex items bail outright\nexport function arrays(input, expect) {\n\tlet arr = diff.diffArrays(input, expect);\n\tlet i=0, j=0, k=0, tmp, val, char, isObj, str;\n\tlet out = LOG('··', '[');\n\n\tfor (; i < arr.length; i++) {\n\t\tchar = (tmp = arr[i]).removed ? '--' : tmp.added ? '++' : '··';\n\n\t\tif (tmp.added) {\n\t\t\tout += colors[char]().underline(TITLE('Expected:')) + '\\n';\n\t\t} else if (tmp.removed) {\n\t\t\tout += colors[char]().underline(TITLE('Actual:')) + '\\n';\n\t\t}\n\n\t\tfor (j=0; j < tmp.value.length; j++) {\n\t\t\tisObj = (tmp.value[j] && typeof tmp.value[j] === 'object');\n\t\t\tval = stringify(tmp.value[j]).split(/\\r?\\n/g);\n\t\t\tfor (k=0; k < val.length;) {\n\t\t\t\tstr = '  ' + val[k++] + (isObj ? '' : ',');\n\t\t\t\tif (isObj && k === val.length && (j + 1) < tmp.value.length) str += ',';\n\t\t\t\tout += LOG(char, str);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn out + LOG('··', ']');\n}\n\nexport function lines(input, expect, linenum = 0) {\n\tlet i=0, tmp, output='';\n\tlet arr = diff.diffLines(input, expect);\n\tlet pad = String(expect.split(/\\r?\\n/g).length - linenum).length;\n\n\tfor (; i < arr.length; i++) {\n\t\toutput += line(tmp = arr[i], linenum, pad);\n\t\tif (linenum && !tmp.removed) linenum += tmp.count;\n\t}\n\n\treturn output;\n}\n\nexport function chars(input, expect) {\n\tlet arr = diff.diffChars(input, expect);\n\tlet i=0, output='', tmp;\n\n\tlet l1 = input.length;\n\tlet l2 = expect.length;\n\n\tlet p1 = PRETTY(input);\n\tlet p2 = PRETTY(expect);\n\n\ttmp = arr[i];\n\n\tif (l1 === l2) {\n\t\t// no length offsets\n\t} else if (tmp.removed && arr[i + 1]) {\n\t\tlet del = tmp.count - arr[i + 1].count;\n\t\tif (del == 0) {\n\t\t\t// wash~\n\t\t} else if (del > 0) {\n\t\t\texpect = ' '.repeat(del) + expect;\n\t\t\tp2 = ' '.repeat(del) + p2;\n\t\t\tl2 += del;\n\t\t} else if (del < 0) {\n\t\t\tinput = ' '.repeat(-del) + input;\n\t\t\tp1 = ' '.repeat(-del) + p1;\n\t\t\tl1 += -del;\n\t\t}\n\t}\n\n\toutput += direct(p1, p2, l1, l2);\n\n\tif (l1 === l2) {\n\t\tfor (tmp='  '; i < l1; i++) {\n\t\t\ttmp += input[i] === expect[i] ? ' ' : '^';\n\t\t}\n\t} else {\n\t\tfor (tmp='  '; i < arr.length; i++) {\n\t\t\ttmp += ((arr[i].added || arr[i].removed) ? '^' : ' ').repeat(Math.max(arr[i].count, 0));\n\t\t\tif (i + 1 < arr.length && ((arr[i].added && arr[i+1].removed) || (arr[i].removed && arr[i+1].added))) {\n\t\t\t\tarr[i + 1].count -= arr[i].count;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn output + kleur.red(tmp);\n}\n\nexport function direct(input, expect, lenA = String(input).length, lenB = String(expect).length) {\n\tlet gutter = 4;\n\tlet lenC = Math.max(lenA, lenB);\n\tlet typeA=typeof input, typeB=typeof expect;\n\n\tif (typeA !== typeB) {\n\t\tgutter = 2;\n\n\t\tlet delA = gutter + lenC - lenA;\n\t\tlet delB = gutter + lenC - lenB;\n\n\t\tinput += ' '.repeat(delA) + kleur.dim(`[${typeA}]`);\n\t\texpect += ' '.repeat(delB) + kleur.dim(`[${typeB}]`);\n\n\t\tlenA += delA + typeA.length + 2;\n\t\tlenB += delB + typeB.length + 2;\n\t\tlenC = Math.max(lenA, lenB);\n\t}\n\n\tlet output = colors['++']('++' + expect + ' '.repeat(gutter + lenC - lenB) + TITLE('(Expected)')) + '\\n';\n\treturn output + colors['--']('--' + input + ' '.repeat(gutter + lenC - lenA) + TITLE('(Actual)')) + '\\n';\n}\n\nexport function sort(input, expect) {\n\tvar k, i=0, tmp, isArr = Array.isArray(input);\n\tvar keys=[], out=isArr ? Array(input.length) : {};\n\n\tif (isArr) {\n\t\tfor (i=0; i < out.length; i++) {\n\t\t\ttmp = input[i];\n\t\t\tif (!tmp || typeof tmp !== 'object') out[i] = tmp;\n\t\t\telse out[i] = sort(tmp, expect[i]); // might not be right\n\t\t}\n\t} else {\n\t\tfor (k in expect)\n\t\t\tkeys.push(k);\n\n\t\tfor (; i < keys.length; i++) {\n\t\t\tif (Object.prototype.hasOwnProperty.call(input, k = keys[i])) {\n\t\t\t\tif (!(tmp = input[k]) || typeof tmp !== 'object') out[k] = tmp;\n\t\t\t\telse out[k] = sort(tmp, expect[k]);\n\t\t\t}\n\t\t}\n\n\t\tfor (k in input) {\n\t\t\tif (!out.hasOwnProperty(k)) {\n\t\t\t\tout[k] = input[k]; // expect didnt have\n\t\t\t}\n\t\t}\n\t}\n\n\treturn out;\n}\n\nexport function circular() {\n\tvar cache = new Set;\n\treturn function print(key, val) {\n\t\tif (val === void 0) return '[__VOID__]';\n\t\tif (typeof val === 'number' && val !== val) return '[__NAN__]';\n\t\tif (typeof val === 'bigint') return val.toString();\n\t\tif (!val || typeof val !== 'object') return val;\n\t\tif (cache.has(val)) return '[Circular]';\n\t\tcache.add(val); return val;\n\t}\n}\n\nexport function stringify(input) {\n\treturn JSON.stringify(input, circular(), 2).replace(/\"\\[__NAN__\\]\"/g, 'NaN').replace(/\"\\[__VOID__\\]\"/g, 'undefined');\n}\n\nexport function compare(input, expect) {\n\tif (Array.isArray(expect) && Array.isArray(input)) return arrays(input, expect);\n\tif (expect instanceof RegExp) return chars(''+input, ''+expect);\n\n\tlet isA = input && typeof input == 'object';\n\tlet isB = expect && typeof expect == 'object';\n\n\tif (isA && isB) input = sort(input, expect);\n\tif (isB) expect = stringify(expect);\n\tif (isA) input = stringify(input);\n\n\tif (expect && typeof expect == 'object') {\n\t\tinput = stringify(sort(input, expect));\n\t\texpect = stringify(expect);\n\t}\n\n\tisA = typeof input == 'string';\n\tisB = typeof expect == 'string';\n\n\tif (isA && /\\r?\\n/.test(input)) return lines(input, ''+expect);\n\tif (isB && /\\r?\\n/.test(expect)) return lines(''+input, expect);\n\tif (isA && isB) return chars(input, expect);\n\n\treturn direct(input, expect);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,SAAS;IACd,MAAM,kIAAA,CAAA,UAAK,CAAC,GAAG;IACf,MAAM,kIAAA,CAAA,UAAK,CAAC,IAAI;IAChB,MAAM,kIAAA,CAAA,UAAK,CAAC,KAAK;AAClB;AAEA,MAAM,QAAQ,kIAAA,CAAA,UAAK,CAAC,GAAG,GAAG,MAAM;AAChC,MAAM,MAAI,kIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,MAAM,QAAM,kIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,MAAM,KAAG,kIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;AAC7D,MAAM,MAAM,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,OAAO,QAAQ;AAC3D,MAAM,OAAO,CAAC,KAAK,IAAM,kIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,MAAM,OAAO,KAAK,QAAQ,CAAC,GAAG,OAAO;AACxE,MAAM,SAAS,CAAA,MAAO,IAAI,OAAO,CAAC,QAAQ,OAAO,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,YAAY;AAEzF,SAAS,KAAK,GAAG,EAAE,IAAI,EAAE,GAAG;IAC3B,IAAI,OAAO,IAAI,OAAO,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO;IACnD,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;IAChD,IAAI,IAAE,GAAG,KAAK,MAAI;IAElB,IAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,gBAAgB;SAChE,IAAI,IAAI,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,cAAc;IAE1E,MAAO,IAAI,IAAI,MAAM,EAAE,IAAK;QAC3B,MAAM,GAAG,CAAC,EAAE;QACZ,IAAI,OAAO,MAAM;YAChB,IAAI,MAAM,OAAO,KAAK,OAAO,GAAG;YAChC,OAAO,IAAI,MAAM,OAAO;QACzB;IACD;IAEA,OAAO;AACR;AAIO,SAAS,OAAO,KAAK,EAAE,MAAM;IACnC,IAAI,MAAM,CAAA,GAAA,wIAAA,CAAA,aAAe,AAAD,EAAE,OAAO;IACjC,IAAI,IAAE,GAAG,IAAE,GAAG,IAAE,GAAG,KAAK,KAAK,MAAM,OAAO;IAC1C,IAAI,MAAM,IAAI,MAAM;IAEpB,MAAO,IAAI,IAAI,MAAM,EAAE,IAAK;QAC3B,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,OAAO,GAAG,OAAO,IAAI,KAAK,GAAG,OAAO;QAE1D,IAAI,IAAI,KAAK,EAAE;YACd,OAAO,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,gBAAgB;QACvD,OAAO,IAAI,IAAI,OAAO,EAAE;YACvB,OAAO,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,cAAc;QACrD;QAEA,IAAK,IAAE,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,IAAK;YACpC,QAAS,IAAI,KAAK,CAAC,EAAE,IAAI,OAAO,IAAI,KAAK,CAAC,EAAE,KAAK;YACjD,MAAM,UAAU,IAAI,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC;YACpC,IAAK,IAAE,GAAG,IAAI,IAAI,MAAM,EAAG;gBAC1B,MAAM,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG;gBACzC,IAAI,SAAS,MAAM,IAAI,MAAM,IAAI,AAAC,IAAI,IAAK,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO;gBACpE,OAAO,IAAI,MAAM;YAClB;QACD;IACD;IAEA,OAAO,MAAM,IAAI,MAAM;AACxB;AAEO,SAAS,MAAM,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC;IAC/C,IAAI,IAAE,GAAG,KAAK,SAAO;IACrB,IAAI,MAAM,CAAA,GAAA,wIAAA,CAAA,YAAc,AAAD,EAAE,OAAO;IAChC,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,UAAU,MAAM,GAAG,SAAS,MAAM;IAEhE,MAAO,IAAI,IAAI,MAAM,EAAE,IAAK;QAC3B,UAAU,KAAK,MAAM,GAAG,CAAC,EAAE,EAAE,SAAS;QACtC,IAAI,WAAW,CAAC,IAAI,OAAO,EAAE,WAAW,IAAI,KAAK;IAClD;IAEA,OAAO;AACR;AAEO,SAAS,MAAM,KAAK,EAAE,MAAM;IAClC,IAAI,MAAM,CAAA,GAAA,wIAAA,CAAA,YAAc,AAAD,EAAE,OAAO;IAChC,IAAI,IAAE,GAAG,SAAO,IAAI;IAEpB,IAAI,KAAK,MAAM,MAAM;IACrB,IAAI,KAAK,OAAO,MAAM;IAEtB,IAAI,KAAK,OAAO;IAChB,IAAI,KAAK,OAAO;IAEhB,MAAM,GAAG,CAAC,EAAE;IAEZ,IAAI,OAAO,IAAI;IACd,oBAAoB;IACrB,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE;QACrC,IAAI,MAAM,IAAI,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK;QACtC,IAAI,OAAO,GAAG;QACb,QAAQ;QACT,OAAO,IAAI,MAAM,GAAG;YACnB,SAAS,IAAI,MAAM,CAAC,OAAO;YAC3B,KAAK,IAAI,MAAM,CAAC,OAAO;YACvB,MAAM;QACP,OAAO,IAAI,MAAM,GAAG;YACnB,QAAQ,IAAI,MAAM,CAAC,CAAC,OAAO;YAC3B,KAAK,IAAI,MAAM,CAAC,CAAC,OAAO;YACxB,MAAM,CAAC;QACR;IACD;IAEA,UAAU,OAAO,IAAI,IAAI,IAAI;IAE7B,IAAI,OAAO,IAAI;QACd,IAAK,MAAI,MAAM,IAAI,IAAI,IAAK;YAC3B,OAAO,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,GAAG,MAAM;QACvC;IACD,OAAO;QACN,IAAK,MAAI,MAAM,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,OAAO,CAAC,AAAC,GAAG,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,CAAC,OAAO,GAAI,MAAM,GAAG,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE;YACpF,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,CAAC,AAAC,GAAG,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,CAAC,IAAE,EAAE,CAAC,OAAO,IAAM,GAAG,CAAC,EAAE,CAAC,OAAO,IAAI,GAAG,CAAC,IAAE,EAAE,CAAC,KAAK,AAAC,GAAG;gBACrG,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK;YACjC;QACD;IACD;IAEA,OAAO,SAAS,kIAAA,CAAA,UAAK,CAAC,GAAG,CAAC;AAC3B;AAEO,SAAS,OAAO,KAAK,EAAE,MAAM,EAAE,OAAO,OAAO,OAAO,MAAM,EAAE,OAAO,OAAO,QAAQ,MAAM;IAC9F,IAAI,SAAS;IACb,IAAI,OAAO,KAAK,GAAG,CAAC,MAAM;IAC1B,IAAI,QAAM,OAAO,OAAO,QAAM,OAAO;IAErC,IAAI,UAAU,OAAO;QACpB,SAAS;QAET,IAAI,OAAO,SAAS,OAAO;QAC3B,IAAI,OAAO,SAAS,OAAO;QAE3B,SAAS,IAAI,MAAM,CAAC,QAAQ,kIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAClD,UAAU,IAAI,MAAM,CAAC,QAAQ,kIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAEnD,QAAQ,OAAO,MAAM,MAAM,GAAG;QAC9B,QAAQ,OAAO,MAAM,MAAM,GAAG;QAC9B,OAAO,KAAK,GAAG,CAAC,MAAM;IACvB;IAEA,IAAI,SAAS,MAAM,CAAC,KAAK,CAAC,OAAO,SAAS,IAAI,MAAM,CAAC,SAAS,OAAO,QAAQ,MAAM,iBAAiB;IACpG,OAAO,SAAS,MAAM,CAAC,KAAK,CAAC,OAAO,QAAQ,IAAI,MAAM,CAAC,SAAS,OAAO,QAAQ,MAAM,eAAe;AACrG;AAEO,SAAS,KAAK,KAAK,EAAE,MAAM;IACjC,IAAI,GAAG,IAAE,GAAG,KAAK,QAAQ,MAAM,OAAO,CAAC;IACvC,IAAI,OAAK,EAAE,EAAE,MAAI,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC;IAEhD,IAAI,OAAO;QACV,IAAK,IAAE,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YAC9B,MAAM,KAAK,CAAC,EAAE;YACd,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU,GAAG,CAAC,EAAE,GAAG;iBACzC,GAAG,CAAC,EAAE,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,GAAG,qBAAqB;QAC1D;IACD,OAAO;QACN,IAAK,KAAK,OACT,KAAK,IAAI,CAAC;QAEX,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;YAC5B,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG;gBAC7D,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,KAAK,OAAO,QAAQ,UAAU,GAAG,CAAC,EAAE,GAAG;qBACtD,GAAG,CAAC,EAAE,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE;YAClC;QACD;QAEA,IAAK,KAAK,MAAO;YAChB,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI;gBAC3B,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE,oBAAoB;YACxC;QACD;IACD;IAEA,OAAO;AACR;AAEO,SAAS;IACf,IAAI,QAAQ,IAAI;IAChB,OAAO,SAAS,MAAM,GAAG,EAAE,GAAG;QAC7B,IAAI,QAAQ,KAAK,GAAG,OAAO;QAC3B,IAAI,OAAO,QAAQ,YAAY,QAAQ,KAAK,OAAO;QACnD,IAAI,OAAO,QAAQ,UAAU,OAAO,IAAI,QAAQ;QAChD,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU,OAAO;QAC5C,IAAI,MAAM,GAAG,CAAC,MAAM,OAAO;QAC3B,MAAM,GAAG,CAAC;QAAM,OAAO;IACxB;AACD;AAEO,SAAS,UAAU,KAAK;IAC9B,OAAO,KAAK,SAAS,CAAC,OAAO,YAAY,GAAG,OAAO,CAAC,kBAAkB,OAAO,OAAO,CAAC,mBAAmB;AACzG;AAEO,SAAS,QAAQ,KAAK,EAAE,MAAM;IACpC,IAAI,MAAM,OAAO,CAAC,WAAW,MAAM,OAAO,CAAC,QAAQ,OAAO,OAAO,OAAO;IACxE,IAAI,kBAAkB,QAAQ,OAAO,MAAM,KAAG,OAAO,KAAG;IAExD,IAAI,MAAM,SAAS,OAAO,SAAS;IACnC,IAAI,MAAM,UAAU,OAAO,UAAU;IAErC,IAAI,OAAO,KAAK,QAAQ,KAAK,OAAO;IACpC,IAAI,KAAK,SAAS,UAAU;IAC5B,IAAI,KAAK,QAAQ,UAAU;IAE3B,IAAI,UAAU,OAAO,UAAU,UAAU;QACxC,QAAQ,UAAU,KAAK,OAAO;QAC9B,SAAS,UAAU;IACpB;IAEA,MAAM,OAAO,SAAS;IACtB,MAAM,OAAO,UAAU;IAEvB,IAAI,OAAO,QAAQ,IAAI,CAAC,QAAQ,OAAO,MAAM,OAAO,KAAG;IACvD,IAAI,OAAO,QAAQ,IAAI,CAAC,SAAS,OAAO,MAAM,KAAG,OAAO;IACxD,IAAI,OAAO,KAAK,OAAO,MAAM,OAAO;IAEpC,OAAO,OAAO,OAAO;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/uvu/assert/index.mjs"], "sourcesContent": ["import { dequal } from 'dequal';\nimport { compare, lines } from 'uvu/diff';\n\nfunction dedent(str) {\n\tstr = str.replace(/\\r?\\n/g, '\\n');\n  let arr = str.match(/^[ \\t]*(?=\\S)/gm);\n  let i = 0, min = 1/0, len = (arr||[]).length;\n  for (; i < len; i++) min = Math.min(min, arr[i].length);\n  return len && min ? str.replace(new RegExp(`^[ \\\\t]{${min}}`, 'gm'), '') : str;\n}\n\nexport class Assertion extends Error {\n\tconstructor(opts={}) {\n\t\tsuper(opts.message);\n\t\tthis.name = 'Assertion';\n\t\tthis.code = 'ERR_ASSERTION';\n\t\tif (Error.captureStackTrace) {\n\t\t\tError.captureStackTrace(this, this.constructor);\n\t\t}\n\t\tthis.details = opts.details || false;\n\t\tthis.generated = !!opts.generated;\n\t\tthis.operator = opts.operator;\n\t\tthis.expects = opts.expects;\n\t\tthis.actual = opts.actual;\n\t}\n}\n\nfunction assert(bool, actual, expects, operator, detailer, backup, msg) {\n\tif (bool) return;\n\tlet message = msg || backup;\n\tif (msg instanceof Error) throw msg;\n\tlet details = detailer && detailer(actual, expects);\n\tthrow new Assertion({ actual, expects, operator, message, details, generated: !msg });\n}\n\nexport function ok(val, msg) {\n\tassert(!!val, false, true, 'ok', false, 'Expected value to be truthy', msg);\n}\n\nexport function is(val, exp, msg) {\n\tassert(val === exp, val, exp, 'is', compare, 'Expected values to be strictly equal:', msg);\n}\n\nexport function equal(val, exp, msg) {\n\tassert(dequal(val, exp), val, exp, 'equal', compare, 'Expected values to be deeply equal:', msg);\n}\n\nexport function unreachable(msg) {\n\tassert(false, true, false, 'unreachable', false, 'Expected not to be reached!', msg);\n}\n\nexport function type(val, exp, msg) {\n\tlet tmp = typeof val;\n\tassert(tmp === exp, tmp, exp, 'type', false, `Expected \"${tmp}\" to be \"${exp}\"`, msg);\n}\n\nexport function instance(val, exp, msg) {\n\tlet name = '`' + (exp.name || exp.constructor.name) + '`';\n\tassert(val instanceof exp, val, exp, 'instance', false, `Expected value to be an instance of ${name}`, msg);\n}\n\nexport function match(val, exp, msg) {\n\tif (typeof exp === 'string') {\n\t\tassert(val.includes(exp), val, exp, 'match', false, `Expected value to include \"${exp}\" substring`, msg);\n\t} else {\n\t\tassert(exp.test(val), val, exp, 'match', false, `Expected value to match \\`${String(exp)}\\` pattern`, msg);\n\t}\n}\n\nexport function snapshot(val, exp, msg) {\n\tval=dedent(val); exp=dedent(exp);\n\tassert(val === exp, val, exp, 'snapshot', lines, 'Expected value to match snapshot:', msg);\n}\n\nconst lineNums = (x, y) => lines(x, y, 1);\nexport function fixture(val, exp, msg) {\n\tval=dedent(val); exp=dedent(exp);\n\tassert(val === exp, val, exp, 'fixture', lineNums, 'Expected value to match fixture:', msg);\n}\n\nexport function throws(blk, exp, msg) {\n\tif (!msg && typeof exp === 'string') {\n\t\tmsg = exp; exp = null;\n\t}\n\n\ttry {\n\t\tblk();\n\t\tassert(false, false, true, 'throws', false, 'Expected function to throw', msg);\n\t} catch (err) {\n\t\tif (err instanceof Assertion) throw err;\n\n\t\tif (typeof exp === 'function') {\n\t\t\tassert(exp(err), false, true, 'throws', false, 'Expected function to throw matching exception', msg);\n\t\t} else if (exp instanceof RegExp) {\n\t\t\tassert(exp.test(err.message), false, true, 'throws', false, `Expected function to throw exception matching \\`${String(exp)}\\` pattern`, msg);\n\t\t}\n\t}\n}\n\n// ---\n\nexport function not(val, msg) {\n\tassert(!val, true, false, 'not', false, 'Expected value to be falsey', msg);\n}\n\nnot.ok = not;\n\nis.not = function (val, exp, msg) {\n\tassert(val !== exp, val, exp, 'is.not', false, 'Expected values not to be strictly equal', msg);\n}\n\nnot.equal = function (val, exp, msg) {\n\tassert(!dequal(val, exp), val, exp, 'not.equal', false, 'Expected values not to be deeply equal', msg);\n}\n\nnot.type = function (val, exp, msg) {\n\tlet tmp = typeof val;\n\tassert(tmp !== exp, tmp, exp, 'not.type', false, `Expected \"${tmp}\" not to be \"${exp}\"`, msg);\n}\n\nnot.instance = function (val, exp, msg) {\n\tlet name = '`' + (exp.name || exp.constructor.name) + '`';\n\tassert(!(val instanceof exp), val, exp, 'not.instance', false, `Expected value not to be an instance of ${name}`, msg);\n}\n\nnot.snapshot = function (val, exp, msg) {\n\tval=dedent(val); exp=dedent(exp);\n\tassert(val !== exp, val, exp, 'not.snapshot', false, 'Expected value not to match snapshot', msg);\n}\n\nnot.fixture = function (val, exp, msg) {\n\tval=dedent(val); exp=dedent(exp);\n\tassert(val !== exp, val, exp, 'not.fixture', false, 'Expected value not to match fixture', msg);\n}\n\nnot.match = function (val, exp, msg) {\n\tif (typeof exp === 'string') {\n\t\tassert(!val.includes(exp), val, exp, 'not.match', false, `Expected value not to include \"${exp}\" substring`, msg);\n\t} else {\n\t\tassert(!exp.test(val), val, exp, 'not.match', false, `Expected value not to match \\`${String(exp)}\\` pattern`, msg);\n\t}\n}\n\nnot.throws = function (blk, exp, msg) {\n\tif (!msg && typeof exp === 'string') {\n\t\tmsg = exp; exp = null;\n\t}\n\n\ttry {\n\t\tblk();\n\t} catch (err) {\n\t\tif (typeof exp === 'function') {\n\t\t\tassert(!exp(err), true, false, 'not.throws', false, 'Expected function not to throw matching exception', msg);\n\t\t} else if (exp instanceof RegExp) {\n\t\t\tassert(!exp.test(err.message), true, false, 'not.throws', false, `Expected function not to throw exception matching \\`${String(exp)}\\` pattern`, msg);\n\t\t} else if (!exp) {\n\t\t\tassert(false, true, false, 'not.throws', false, 'Expected function not to throw', msg);\n\t\t}\n\t}\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEA,SAAS,OAAO,GAAG;IAClB,MAAM,IAAI,OAAO,CAAC,UAAU;IAC3B,IAAI,MAAM,IAAI,KAAK,CAAC;IACpB,IAAI,IAAI,GAAG,MAAM,IAAE,GAAG,MAAM,CAAC,OAAK,EAAE,EAAE,MAAM;IAC5C,MAAO,IAAI,KAAK,IAAK,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM;IACtD,OAAO,OAAO,MAAM,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,MAAM;AAC7E;AAEO,MAAM,kBAAkB;IAC9B,YAAY,OAAK,CAAC,CAAC,CAAE;QACpB,KAAK,CAAC,KAAK,OAAO;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,MAAM,iBAAiB,EAAE;YAC5B,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAC/C;QACA,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI;QAC/B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,SAAS;QACjC,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;QAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC1B;AACD;AAEA,SAAS,OAAO,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACrE,IAAI,MAAM;IACV,IAAI,UAAU,OAAO;IACrB,IAAI,eAAe,OAAO,MAAM;IAChC,IAAI,UAAU,YAAY,SAAS,QAAQ;IAC3C,MAAM,IAAI,UAAU;QAAE;QAAQ;QAAS;QAAU;QAAS;QAAS,WAAW,CAAC;IAAI;AACpF;AAEO,SAAS,GAAG,GAAG,EAAE,GAAG;IAC1B,OAAO,CAAC,CAAC,KAAK,OAAO,MAAM,MAAM,OAAO,+BAA+B;AACxE;AAEO,SAAS,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,OAAO,QAAQ,KAAK,KAAK,KAAK,MAAM,wIAAA,CAAA,UAAO,EAAE,yCAAyC;AACvF;AAEO,SAAS,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG;IAClC,OAAO,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,KAAK,KAAK,SAAS,wIAAA,CAAA,UAAO,EAAE,uCAAuC;AAC7F;AAEO,SAAS,YAAY,GAAG;IAC9B,OAAO,OAAO,MAAM,OAAO,eAAe,OAAO,+BAA+B;AACjF;AAEO,SAAS,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG;IACjC,IAAI,MAAM,OAAO;IACjB,OAAO,QAAQ,KAAK,KAAK,KAAK,QAAQ,OAAO,CAAC,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,CAAC,CAAC,EAAE;AAClF;AAEO,SAAS,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG;IACrC,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,WAAW,CAAC,IAAI,IAAI;IACtD,OAAO,eAAe,KAAK,KAAK,KAAK,YAAY,OAAO,CAAC,oCAAoC,EAAE,MAAM,EAAE;AACxG;AAEO,SAAS,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG;IAClC,IAAI,OAAO,QAAQ,UAAU;QAC5B,OAAO,IAAI,QAAQ,CAAC,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,2BAA2B,EAAE,IAAI,WAAW,CAAC,EAAE;IACrG,OAAO;QACN,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,0BAA0B,EAAE,OAAO,KAAK,UAAU,CAAC,EAAE;IACvG;AACD;AAEO,SAAS,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG;IACrC,MAAI,OAAO;IAAM,MAAI,OAAO;IAC5B,OAAO,QAAQ,KAAK,KAAK,KAAK,YAAY,wIAAA,CAAA,QAAK,EAAE,qCAAqC;AACvF;AAEA,MAAM,WAAW,CAAC,GAAG,IAAM,CAAA,GAAA,wIAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG;AAChC,SAAS,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG;IACpC,MAAI,OAAO;IAAM,MAAI,OAAO;IAC5B,OAAO,QAAQ,KAAK,KAAK,KAAK,WAAW,UAAU,oCAAoC;AACxF;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACpC,MAAM;QAAK,MAAM;IAClB;IAEA,IAAI;QACH;QACA,OAAO,OAAO,OAAO,MAAM,UAAU,OAAO,8BAA8B;IAC3E,EAAE,OAAO,KAAK;QACb,IAAI,eAAe,WAAW,MAAM;QAEpC,IAAI,OAAO,QAAQ,YAAY;YAC9B,OAAO,IAAI,MAAM,OAAO,MAAM,UAAU,OAAO,iDAAiD;QACjG,OAAO,IAAI,eAAe,QAAQ;YACjC,OAAO,IAAI,IAAI,CAAC,IAAI,OAAO,GAAG,OAAO,MAAM,UAAU,OAAO,CAAC,gDAAgD,EAAE,OAAO,KAAK,UAAU,CAAC,EAAE;QACzI;IACD;AACD;AAIO,SAAS,IAAI,GAAG,EAAE,GAAG;IAC3B,OAAO,CAAC,KAAK,MAAM,OAAO,OAAO,OAAO,+BAA+B;AACxE;AAEA,IAAI,EAAE,GAAG;AAET,GAAG,GAAG,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,OAAO,QAAQ,KAAK,KAAK,KAAK,UAAU,OAAO,4CAA4C;AAC5F;AAEA,IAAI,KAAK,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IAClC,OAAO,CAAC,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,KAAK,KAAK,aAAa,OAAO,0CAA0C;AACnG;AAEA,IAAI,IAAI,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IACjC,IAAI,MAAM,OAAO;IACjB,OAAO,QAAQ,KAAK,KAAK,KAAK,YAAY,OAAO,CAAC,UAAU,EAAE,IAAI,aAAa,EAAE,IAAI,CAAC,CAAC,EAAE;AAC1F;AAEA,IAAI,QAAQ,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IACrC,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,WAAW,CAAC,IAAI,IAAI;IACtD,OAAO,CAAC,CAAC,eAAe,GAAG,GAAG,KAAK,KAAK,gBAAgB,OAAO,CAAC,wCAAwC,EAAE,MAAM,EAAE;AACnH;AAEA,IAAI,QAAQ,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IACrC,MAAI,OAAO;IAAM,MAAI,OAAO;IAC5B,OAAO,QAAQ,KAAK,KAAK,KAAK,gBAAgB,OAAO,wCAAwC;AAC9F;AAEA,IAAI,OAAO,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IACpC,MAAI,OAAO;IAAM,MAAI,OAAO;IAC5B,OAAO,QAAQ,KAAK,KAAK,KAAK,eAAe,OAAO,uCAAuC;AAC5F;AAEA,IAAI,KAAK,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IAClC,IAAI,OAAO,QAAQ,UAAU;QAC5B,OAAO,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,KAAK,aAAa,OAAO,CAAC,+BAA+B,EAAE,IAAI,WAAW,CAAC,EAAE;IAC9G,OAAO;QACN,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,aAAa,OAAO,CAAC,8BAA8B,EAAE,OAAO,KAAK,UAAU,CAAC,EAAE;IAChH;AACD;AAEA,IAAI,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACpC,MAAM;QAAK,MAAM;IAClB;IAEA,IAAI;QACH;IACD,EAAE,OAAO,KAAK;QACb,IAAI,OAAO,QAAQ,YAAY;YAC9B,OAAO,CAAC,IAAI,MAAM,MAAM,OAAO,cAAc,OAAO,qDAAqD;QAC1G,OAAO,IAAI,eAAe,QAAQ;YACjC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,GAAG,MAAM,OAAO,cAAc,OAAO,CAAC,oDAAoD,EAAE,OAAO,KAAK,UAAU,CAAC,EAAE;QAClJ,OAAO,IAAI,CAAC,KAAK;YAChB,OAAO,OAAO,MAAM,OAAO,cAAc,OAAO,kCAAkC;QACnF;IACD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/mdast-util-to-string/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Root|import('mdast').Content} Node\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s.\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML.\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} value\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Node}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC,GAED,oBAAoB;;;AACpB,MAAM,eAAe,CAAC;AAef,SAAS,SAAS,KAAK,EAAE,OAAO;IACrC,MAAM,WAAW,WAAW;IAC5B,MAAM,kBACJ,OAAO,SAAS,eAAe,KAAK,YAChC,SAAS,eAAe,GACxB;IACN,MAAM,cACJ,OAAO,SAAS,WAAW,KAAK,YAAY,SAAS,WAAW,GAAG;IAErE,OAAO,IAAI,OAAO,iBAAiB;AACrC;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,KAAK,EAAE,eAAe,EAAE,WAAW;IAC9C,IAAI,KAAK,QAAQ;QACf,IAAI,WAAW,OAAO;YACpB,OAAO,MAAM,IAAI,KAAK,UAAU,CAAC,cAAc,KAAK,MAAM,KAAK;QACjE;QAEA,IAAI,mBAAmB,SAAS,SAAS,MAAM,GAAG,EAAE;YAClD,OAAO,MAAM,GAAG;QAClB;QAEA,IAAI,cAAc,OAAO;YACvB,OAAO,IAAI,MAAM,QAAQ,EAAE,iBAAiB;QAC9C;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO,IAAI,OAAO,iBAAiB;IACrC;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,IAAI,MAAM,EAAE,eAAe,EAAE,WAAW;IAC/C,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,iBAAiB;IACtD;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,KAAK;IACjB,OAAO,QAAQ,SAAS,OAAO,UAAU;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-symbol/constants.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */ ({\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeContent: 'content',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlRaw: 1, // Symbol for `<script>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlBasic: 6, // Symbol for `<div`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,MAAM,YAAkC;IAC7C,qBAAqB;IACrB,oBAAoB;IACpB,+BAA+B;IAC/B,uBAAuB;IACvB,uBAAuB;IACvB,oBAAoB;IACpB,0BAA0B;IAC1B,2BAA2B;IAC3B,kCAAkC;IAClC,sCAAsC;IACtC,gCAAgC;IAChC,2BAA2B;IAC3B,qBAAqB;IACrB,iBAAiB;IACjB,oBAAoB;IACpB,mBAAmB;IACnB,iBAAiB;IACjB,wBAAwB;IACxB,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,iBAAiB;IACjB,WAAW;IACX,WAAW;IACX,cAAc;IACd,gBAAgB;IAChB,mCAAmC;IACnC,sBAAsB;IACtB,sBAAsB;IACtB,oBAAoB;IACpB,wBAAwB;IACxB,SAAS;IACT,6BAA6B;IAC7B,oBAAoB,MAAM,kHAAkH;AAC9I", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-symbol/codes.js"], "sourcesContent": ["/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65279,\n  // Unicode Specials block.\n  replacementCharacter: 65533 // `�`\n})\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC;;;AACM,MAAM,QAA8B;IACzC,gBAAgB,CAAC;IACjB,UAAU,CAAC;IACX,wBAAwB,CAAC;IACzB,eAAe,CAAC;IAChB,cAAc,CAAC;IACf,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,WAAW;IACX,oBAAoB;IACpB,OAAO;IACP,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,OAAO;IACP,KAAK;IACL,0BAA0B;IAC1B,iBAAiB;IACjB,0BAA0B;IAC1B,sBAAsB,MAAM,MAAM;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-symbol/types.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nexport const types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC,GAED,2EAA2E;AAC3E,wCAAwC;;;;AACjC,MAAM,QAA8B;IACzC,iEAAiE;IACjE,MAAM;IAEN,wEAAwE;IACxE,2DAA2D;IAC3D,YAAY;IAEZ,+EAA+E;IAC/E,cAAc;IACd,YAAY;IAEZ,0CAA0C;IAC1C,iBAAiB;IAEjB,+EAA+E;IAC/E,QAAQ;IACR,YAAY;IAEZ,6EAA6E;IAC7E,QAAQ;IACR,YAAY;IAEZ,qBAAqB;IACrB,EAAE;IACF,cAAc;IACd,IAAI;IACJ,WAAW;IACX,gBAAgB;IAChB,MAAM;IACN,EAAE;IACF,iEAAiE;IACjE,YAAY;IAEZ,sDAAsD;IACtD,oBAAoB;IAEpB,uCAAuC;IACvC,iBAAiB;IACjB,gBAAgB;IAEhB,oEAAoE;IACpE,uEAAuE;IACvE,UAAU;IAEV,mDAAmD;IACnD,eAAe;IAEf,uEAAuE;IACvE,gBAAgB;IAEhB,wDAAwD;IACxD,kBAAkB;IAElB,mCAAmC;IACnC,sDAAsD;IACtD,iBAAiB;IAEjB,+BAA+B;IAC/B,sBAAsB;IAEtB,oEAAoE;IACpE,mDAAmD;IACnD,+DAA+D;IAC/D,0EAA0E;IAC1E,oBAAoB;IAEpB,wCAAwC;IACxC,0BAA0B;IAE1B,mCAAmC;IACnC,iCAAiC;IAEjC,0CAA0C;IAC1C,qCAAqC;IAErC,wEAAwE;IACxE,yBAAyB;IAEzB,qBAAqB;IACrB,EAAE;IACF,eAAe;IACf,QAAQ;IACR,WAAW;IACX,MAAM;IACN,OAAO;IACP,YAAY;IAEZ,sEAAsE;IACtE,eAAe;IACf,iBAAiB;IAEjB,qEAAqE;IACrE,yBAAyB;IAEzB,+BAA+B;IAC/B,mBAAmB;IACnB,qBAAqB;IAErB,2CAA2C;IAC3C,mBAAmB;IACnB,qBAAqB;IAErB,kBAAkB;IAClB,eAAe;IAEf,uBAAuB;IACvB,EAAE;IACF,cAAc;IACd,eAAe;IACf,MAAM;IACN,EAAE;IACF,4DAA4D;IAC5D,cAAc;IAEd,iCAAiC;IACjC,6EAA6E;IAC7E,qBAAqB;IACrB,UAAU;IAEV,cAAc;IAEd,uDAAuD;IACvD,iBAAiB;IAEjB,8BAA8B;IAC9B,kBAAkB;IAElB,iBAAiB;IACjB,EAAE;IACF,cAAc;IACd,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,EAAE;IACF,yCAAyC;IACzC,SAAS;IACT,oBAAoB;IACpB,EAAE;IACF,cAAc;IACd,sDAAsD;IACtD,MAAM;IACN,EAAE;IACF,gEAAgE;IAChE,8EAA8E;IAC9E,YAAY;IAEZ,2EAA2E;IAC3E,+CAA+C;IAC/C,yEAAyE;IACzE,uBAAuB;IAEvB,uCAAuC;IACvC,gDAAgD;IAChD,+DAA+D;IAC/D,iCAAiC;IACjC,8BAA8B;IAE9B,8DAA8D;IAC9D,oCAAoC;IAEpC,yCAAyC;IACzC,8CAA8C;IAC9C,0CAA0C;IAC1C,0BAA0B;IAE1B,qEAAqE;IACrE,mBAAmB;IACnB,6BAA6B;IAE7B,yCAAyC;IACzC,gEAAgE;IAChE,iBAAiB;IAEjB,8CAA8C;IAC9C,uBAAuB;IAEvB,6CAA6C;IAC7C,mBAAmB;IACnB,uBAAuB;IAEvB,kDAAkD;IAClD,kBAAkB;IAElB,kDAAkD;IAClD,2EAA2E;IAC3E,iBAAiB;IAEjB,iEAAiE;IACjE,uBAAuB;IAEvB,yCAAyC;IACzC,mBAAmB;IACnB,uBAAuB;IAEvB,wBAAwB;IACxB,kDAAkD;IAClD,UAAU;IAEV,6CAA6C;IAC7C,kBAAkB;IAElB,2BAA2B;IAC3B,iBAAiB;IACjB,cAAc;IAEd,qCAAqC;IACrC,cAAc;IAEd,iDAAiD;IACjD,0CAA0C;IAC1C,iBAAiB;IAEjB,sDAAsD;IACtD,oCAAoC;IACpC,mBAAmB;IAEnB,aAAa;IACb,EAAE;IACF,cAAc;IACd,OAAO;IACP,MAAM;IACN,EAAE;IACF,yCAAyC;IACzC,UAAU;IAEV,cAAc;IAEd,uCAAuC;IACvC,yCAAyC;IACzC,UAAU;IAEV,cAAc;IAEd,sEAAsE;IACtE,eAAe;IACf,8DAA8D;IAC9D,OAAO;IAEP,kCAAkC;IAClC,qEAAqE;IACrE,OAAO;IAEP,gCAAgC;IAChC,iBAAiB;IACjB,WAAW;IAEX,4BAA4B;IAC5B,4BAA4B;IAC5B,WAAW;IAEX,+BAA+B;IAC/B,iDAAiD;IACjD,YAAY;IAEZ,kCAAkC;IAClC,aAAa;IAEb,kCAAkC;IAClC,kBAAkB;IAElB,qBAAqB;IACrB,0BAA0B;IAC1B,UAAU;IAEV,8EAA8E;IAC9E,8DAA8D;IAC9D,MAAM;IAEN,mBAAmB;IACnB,EAAE;IACF,cAAc;IACd,QAAQ;IACR,SAAS;IACT,MAAM;IACN,EAAE;IACF,iBAAiB;IACjB,WAAW;IAEX,mCAAmC;IACnC,gEAAgE;IAChE,WAAW;IAEX,mCAAmC;IACnC,iBAAiB;IAEjB,4BAA4B;IAC5B,mBAAmB;IACnB,iBAAiB;IAEjB,gDAAgD;IAChD,gFAAgF;IAChF,oCAAoC;IACpC,UAAU;IAEV,kDAAkD;IAClD,qEAAqE;IACrE,qBAAqB;IAErB,4DAA4D;IAC5D,6DAA6D;IAC7D,+BAA+B;IAC/B,4BAA4B;IAE5B,8CAA8C;IAC9C,kCAAkC;IAElC,sDAAsD;IACtD,wCAAwC;IACxC,wBAAwB;IAExB,qDAAqD;IACrD,mBAAmB;IACnB,2BAA2B;IAE3B,kCAAkC;IAClC,gBAAgB;IAEhB,yDAAyD;IACzD,uEAAuE;IACvE,eAAe;IAEf,mDAAmD;IACnD,qBAAqB;IAErB,wCAAwC;IACxC,mBAAmB;IACnB,qBAAqB;IAErB,wBAAwB;IACxB,EAAE;IACF,cAAc;IACd,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,EAAE;IACF,gEAAgE;IAChE,uBAAuB;IACvB,eAAe;IAEf,gDAAgD;IAChD,iBAAiB;IACjB,mBAAmB;IAEnB,qEAAqE;IACrE,wCAAwC;IACxC,mBAAmB;IAEnB,gFAAgF;IAChF,2BAA2B;IAE3B,wBAAwB;IACxB,8CAA8C;IAC9C,QAAQ;IAER,6CAA6C;IAC7C,gBAAgB;IAEhB,yBAAyB;IACzB,iBAAiB;IACjB,YAAY;IAEZ,wBAAwB;IACxB,EAAE;IACF,cAAc;IACd,QAAQ;IACR,MAAM;IACN,EAAE;IACF,qDAAqD;IACrD,eAAe;IAEf,4DAA4D;IAC5D,uBAAuB;IAEvB,qBAAqB;IACrB,EAAE;IACF,cAAc;IACd,MAAM;IACN,IAAI;IACJ,MAAM;IACN,MAAM;IACN,EAAE;IACF,wCAAwC;IACxC,YAAY;IACZ,oCAAoC;IACpC,kBAAkB;IAClB,mCAAmC;IACnC,kBAAkB;IAClB,4CAA4C;IAC5C,4BAA4B;IAE5B,wBAAwB;IACxB,EAAE;IACF,cAAc;IACd,MAAM;IACN,MAAM;IACN,MAAM;IACN,EAAE;IACF,+EAA+E;IAC/E,SAAS;IACT,aAAa;IAEb,sBAAsB;IACtB,EAAE;IACF,cAAc;IACd,OAAO;IACP,OAAO;IACP,MAAM;IACN,EAAE;IACF,+EAA+E;IAC/E,SAAS;IACT,eAAe;IAEf,yCAAyC;IACzC,gBAAgB;IAEhB,+CAA+C;IAC/C,gBAAgB;IAEhB,oDAAoD;IACpD,6DAA6D;IAC7D,iEAAiE;IACjE,gBAAgB;IAEhB,iCAAiC;IACjC,0BAA0B;IAE1B,0CAA0C;IAC1C,eAAe;IAEf,uDAAuD;IACvD,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,aAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-symbol/values.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,MAAM,SAA+B;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,WAAW;IACX,oBAAoB;IACpB,OAAO;IACP,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,OAAO;IACP,sBAAsB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-chunked/dev/index.js"], "sourcesContent": ["import {constants} from 'micromark-util-symbol/constants.js'\n\n/**\n * Like `Array#splice`, but smarter for giant arrays.\n *\n * `Array#splice` takes all items to be inserted as individual argument which\n * causes a stack overflow in V8 when trying to insert 100k items for instance.\n *\n * Otherwise, this does not return the removed items, and takes `items` as an\n * array instead of rest parameters.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {number} start\n *   Index to remove/insert at (can be negative).\n * @param {number} remove\n *   Number of items to remove.\n * @param {Array<T>} items\n *   Items to inject into `list`.\n * @returns {void}\n *   Nothing.\n */\nexport function splice(list, start, remove, items) {\n  const end = list.length\n  let chunkStart = 0\n  /** @type {Array<unknown>} */\n  let parameters\n\n  // Make start between zero and `end` (included).\n  if (start < 0) {\n    start = -start > end ? 0 : end + start\n  } else {\n    start = start > end ? end : start\n  }\n\n  remove = remove > 0 ? remove : 0\n\n  // No need to chunk the items if there’s only a couple (10k) items.\n  if (items.length < constants.v8MaxSafeChunkSize) {\n    parameters = Array.from(items)\n    parameters.unshift(start, remove)\n    // @ts-expect-error Hush, it’s fine.\n    list.splice(...parameters)\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) list.splice(start, remove)\n\n    // Insert the items in chunks to not cause stack overflows.\n    while (chunkStart < items.length) {\n      parameters = items.slice(\n        chunkStart,\n        chunkStart + constants.v8MaxSafeChunkSize\n      )\n      parameters.unshift(start, 0)\n      // @ts-expect-error Hush, it’s fine.\n      list.splice(...parameters)\n\n      chunkStart += constants.v8MaxSafeChunkSize\n      start += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n\n/**\n * Append `items` (an array) at the end of `list` (another array).\n * When `list` was empty, returns `items` instead.\n *\n * This prevents a potentially expensive operation when `list` is empty,\n * and adds items in batches to prevent V8 from hanging.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {Array<T>} items\n *   Items to add to `list`.\n * @returns {Array<T>}\n *   Either `list` or `items`.\n */\nexport function push(list, items) {\n  if (list.length > 0) {\n    splice(list, list.length, 0, items)\n    return list\n  }\n\n  return items\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAwBO,SAAS,OAAO,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;IAC/C,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,aAAa;IACjB,2BAA2B,GAC3B,IAAI;IAEJ,gDAAgD;IAChD,IAAI,QAAQ,GAAG;QACb,QAAQ,CAAC,QAAQ,MAAM,IAAI,MAAM;IACnC,OAAO;QACL,QAAQ,QAAQ,MAAM,MAAM;IAC9B;IAEA,SAAS,SAAS,IAAI,SAAS;IAE/B,mEAAmE;IACnE,IAAI,MAAM,MAAM,GAAG,2JAAA,CAAA,YAAS,CAAC,kBAAkB,EAAE;QAC/C,aAAa,MAAM,IAAI,CAAC;QACxB,WAAW,OAAO,CAAC,OAAO;QAC1B,oCAAoC;QACpC,KAAK,MAAM,IAAI;IACjB,OAAO;QACL,8CAA8C;QAC9C,IAAI,QAAQ,KAAK,MAAM,CAAC,OAAO;QAE/B,2DAA2D;QAC3D,MAAO,aAAa,MAAM,MAAM,CAAE;YAChC,aAAa,MAAM,KAAK,CACtB,YACA,aAAa,2JAAA,CAAA,YAAS,CAAC,kBAAkB;YAE3C,WAAW,OAAO,CAAC,OAAO;YAC1B,oCAAoC;YACpC,KAAK,MAAM,IAAI;YAEf,cAAc,2JAAA,CAAA,YAAS,CAAC,kBAAkB;YAC1C,SAAS,2JAAA,CAAA,YAAS,CAAC,kBAAkB;QACvC;IACF;AACF;AAkBO,SAAS,KAAK,IAAI,EAAE,KAAK;IAC9B,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,OAAO,MAAM,KAAK,MAAM,EAAE,GAAG;QAC7B,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-combine-extensions/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').Handles} Handles\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n * @typedef {import('micromark-util-types').NormalizedExtension} NormalizedExtension\n */\n\nimport {splice} from 'micromark-util-chunked'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * Combine multiple syntax extensions into one.\n *\n * @param {Array<Extension>} extensions\n *   List of syntax extensions.\n * @returns {NormalizedExtension}\n *   A single combined extension.\n */\nexport function combineExtensions(extensions) {\n  /** @type {NormalizedExtension} */\n  const all = {}\n  let index = -1\n\n  while (++index < extensions.length) {\n    syntaxExtension(all, extensions[index])\n  }\n\n  return all\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {NormalizedExtension} all\n *   Extension to merge into.\n * @param {Extension} extension\n *   Extension to merge.\n * @returns {void}\n */\nfunction syntaxExtension(all, extension) {\n  /** @type {keyof Extension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    /** @type {Record<string, unknown>} */\n    const left = maybe || (all[hook] = {})\n    /** @type {Record<string, unknown> | undefined} */\n    const right = extension[hook]\n    /** @type {string} */\n    let code\n\n    if (right) {\n      for (code in right) {\n        if (!hasOwnProperty.call(left, code)) left[code] = []\n        const value = right[code]\n        constructs(\n          // @ts-expect-error Looks like a list.\n          left[code],\n          Array.isArray(value) ? value : value ? [value] : []\n        )\n      }\n    }\n  }\n}\n\n/**\n * Merge `list` into `existing` (both lists of constructs).\n * Mutates `existing`.\n *\n * @param {Array<unknown>} existing\n * @param {Array<unknown>} list\n * @returns {void}\n */\nfunction constructs(existing, list) {\n  let index = -1\n  /** @type {Array<unknown>} */\n  const before = []\n\n  while (++index < list.length) {\n    // @ts-expect-error Looks like an object.\n    ;(list[index].add === 'after' ? existing : before).push(list[index])\n  }\n\n  splice(existing, 0, 0, before)\n}\n\n/**\n * Combine multiple HTML extensions into one.\n *\n * @param {Array<HtmlExtension>} htmlExtensions\n *   List of HTML extensions.\n * @returns {HtmlExtension}\n *   A single combined HTML extension.\n */\nexport function combineHtmlExtensions(htmlExtensions) {\n  /** @type {HtmlExtension} */\n  const handlers = {}\n  let index = -1\n\n  while (++index < htmlExtensions.length) {\n    htmlExtension(handlers, htmlExtensions[index])\n  }\n\n  return handlers\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {HtmlExtension} all\n *   Extension to merge into.\n * @param {HtmlExtension} extension\n *   Extension to merge.\n * @returns {void}\n */\nfunction htmlExtension(all, extension) {\n  /** @type {keyof HtmlExtension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    const left = maybe || (all[hook] = {})\n    const right = extension[hook]\n    /** @type {keyof Handles} */\n    let type\n\n    if (right) {\n      for (type in right) {\n        // @ts-expect-error assume document vs regular handler are managed correctly.\n        left[type] = right[type]\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;;AAEA,MAAM,iBAAiB,CAAC,EAAE,cAAc;AAUjC,SAAS,kBAAkB,UAAU;IAC1C,gCAAgC,GAChC,MAAM,MAAM,CAAC;IACb,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,WAAW,MAAM,CAAE;QAClC,gBAAgB,KAAK,UAAU,CAAC,MAAM;IACxC;IAEA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,gBAAgB,GAAG,EAAE,SAAS;IACrC,4BAA4B,GAC5B,IAAI;IAEJ,IAAK,QAAQ,UAAW;QACtB,MAAM,QAAQ,eAAe,IAAI,CAAC,KAAK,QAAQ,GAAG,CAAC,KAAK,GAAG;QAC3D,oCAAoC,GACpC,MAAM,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACrC,gDAAgD,GAChD,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,mBAAmB,GACnB,IAAI;QAEJ,IAAI,OAAO;YACT,IAAK,QAAQ,MAAO;gBAClB,IAAI,CAAC,eAAe,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE;gBACrD,MAAM,QAAQ,KAAK,CAAC,KAAK;gBACzB,WACE,sCAAsC;gBACtC,IAAI,CAAC,KAAK,EACV,MAAM,OAAO,CAAC,SAAS,QAAQ,QAAQ;oBAAC;iBAAM,GAAG,EAAE;YAEvD;QACF;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,QAAQ,EAAE,IAAI;IAChC,IAAI,QAAQ,CAAC;IACb,2BAA2B,GAC3B,MAAM,SAAS,EAAE;IAEjB,MAAO,EAAE,QAAQ,KAAK,MAAM,CAAE;QAC5B,yCAAyC;;QACxC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,UAAU,WAAW,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;IACrE;IAEA,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,UAAU,GAAG,GAAG;AACzB;AAUO,SAAS,sBAAsB,cAAc;IAClD,0BAA0B,GAC1B,MAAM,WAAW,CAAC;IAClB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,eAAe,MAAM,CAAE;QACtC,cAAc,UAAU,cAAc,CAAC,MAAM;IAC/C;IAEA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,cAAc,GAAG,EAAE,SAAS;IACnC,gCAAgC,GAChC,IAAI;IAEJ,IAAK,QAAQ,UAAW;QACtB,MAAM,QAAQ,eAAe,IAAI,CAAC,KAAK,QAAQ,GAAG,CAAC,KAAK,GAAG;QAC3D,MAAM,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACrC,MAAM,QAAQ,SAAS,CAAC,KAAK;QAC7B,0BAA0B,GAC1B,IAAI;QAEJ,IAAI,OAAO;YACT,IAAK,QAAQ,MAAO;gBAClB,6EAA6E;gBAC7E,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;YAC1B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js"], "sourcesContent": ["// This module is generated by `script/`.\n//\n// CommonMark handles attention (emphasis, strong) markers based on what comes\n// before or after them.\n// One such difference is if those characters are Unicode punctuation.\n// This script is generated from the Unicode data.\n\n/**\n * Regular expression that matches a unicode punctuation character.\n */\nexport const unicodePunctuationRegex =\n  /[!-/:-@[-`{-~\\u00A1\\u00A7\\u00AB\\u00B6\\u00B7\\u00BB\\u00BF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061D-\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C77\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1B7D\\u1B7E\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4F\\u2E52-\\u2E5D\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]/\n"], "names": [], "mappings": "AAAA,yCAAyC;AACzC,EAAE;AACF,8EAA8E;AAC9E,wBAAwB;AACxB,sEAAsE;AACtE,kDAAkD;AAElD;;CAEC;;;AACM,MAAM,0BACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-character/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {unicodePunctuationRegex} from './lib/unicode-punctuation-regex.js'\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < codes.space || code === codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEnding(code) {\n  return code !== null && code < codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEndingOrSpace(code) {\n  return code !== null && (code < codes.nul || code === codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownSpace(code) {\n  return (\n    code === codes.horizontalTab ||\n    code === codes.virtualSpace ||\n    code === codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodePunctuation = regexCheck(unicodePunctuationRegex)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n * @returns {(code: Code) => boolean}\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && regex.test(String.fromCharCode(code))\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;AAED;AACA;;;AAmBO,MAAM,aAAa,WAAW;AAc9B,MAAM,oBAAoB,WAAW;AAuBrC,MAAM,aAAa,WAAW;AAa9B,SAAS,aAAa,IAAI;IAC/B,OACE,wEAAwE;IACxE,gBAAgB;IAChB,SAAS,QAAQ,CAAC,OAAO,uJAAA,CAAA,QAAK,CAAC,KAAK,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG;AAE9D;AAaO,MAAM,aAAa,WAAW;AAoB9B,MAAM,gBAAgB,WAAW;AAejC,MAAM,mBAAmB,WAAW;AAiBpC,SAAS,mBAAmB,IAAI;IACrC,OAAO,SAAS,QAAQ,OAAO,uJAAA,CAAA,QAAK,CAAC,aAAa;AACpD;AAWO,SAAS,0BAA0B,IAAI;IAC5C,OAAO,SAAS,QAAQ,CAAC,OAAO,uJAAA,CAAA,QAAK,CAAC,GAAG,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,KAAK;AACnE;AAiBO,SAAS,cAAc,IAAI;IAChC,OACE,SAAS,uJAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,uJAAA,CAAA,QAAK,CAAC,YAAY,IAC3B,SAAS,uJAAA,CAAA,QAAK,CAAC,KAAK;AAExB;AAuBO,MAAM,qBAAqB,WAAW,kMAAA,CAAA,0BAAuB;AAsB7D,MAAM,oBAAoB,WAAW;AAE5C;;;;;CAKC,GACD,SAAS,WAAW,KAAK;IACvB,OAAO;;IAEP;;;;;;;GAOC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,SAAS,QAAQ,MAAM,IAAI,CAAC,OAAO,YAAY,CAAC;IACzD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-factory-space/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport {markdownSpace} from 'micromark-util-character'\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns\n *   Start state.\n */\nexport function factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownSpace(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if (markdownSpace(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAoCO,SAAS,aAAa,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG;IACjD,MAAM,QAAQ,MAAM,MAAM,IAAI,OAAO,iBAAiB;IACtD,IAAI,OAAO;IAEX,OAAO;;IAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,KAAK,CAAC;YACd,OAAO,OAAO;QAChB;QAEA,OAAO,GAAG;IACZ;IAEA,kBAAkB,GAClB,SAAS,OAAO,IAAI;QAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,SAAS,OAAO;YACzC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC;QACb,OAAO,GAAG;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/initialize/content.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {InitialConstruct} */\nexport const content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n * @type {Initializer}\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, contentStart, types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText)\n      effects.exit(types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      effects.exit(types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,UAAU;IAAC,UAAU;AAAiB;AAEnD;;;CAGC,GACD,SAAS,kBAAkB,OAAO;IAChC,MAAM,eAAe,QAAQ,OAAO,CAClC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EACrC,4BACA;IAEF,kBAAkB,GAClB,IAAI;IAEJ,OAAO;;IAEP,kBAAkB,GAClB,SAAS,2BAA2B,IAAI;QACtC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OACzC;QAGF,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,SAAS,cAAc,uJAAA,CAAA,QAAK,CAAC,UAAU;IAC7D;IAEA,kBAAkB,GAClB,SAAS,iBAAiB,IAAI;QAC5B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAC,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAC1C;QAEF,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS;QAC7B,OAAO,UAAU;IACnB;IAEA,kBAAkB,GAClB,SAAS,UAAU,IAAI;QACrB,MAAM,QAAQ,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS,EAAE;YAC3C,aAAa,2JAAA,CAAA,YAAS,CAAC,eAAe;YACtC;QACF;QAEA,IAAI,UAAU;YACZ,SAAS,IAAI,GAAG;QAClB;QAEA,WAAW;QAEX,OAAO,KAAK;IACd;IAEA,kBAAkB,GAClB,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS;YAC5B,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS;YAC5B,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS;YAC5B,OAAO;QACT;QAEA,QAAQ;QACR,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/initialize/document.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ContainerState} ContainerState\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {InitialConstruct} */\nexport const document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n * @type {Initializer}\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      assert(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = Object.assign({}, point)\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    assert(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(types.chunkFlow, {\n      contentType: constants.contentTypeFlow,\n      previous: childToken,\n      _tokenizer: childFlow\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === codes.eof) {\n      writeToChild(effects.exit(types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   * @param {boolean | undefined} [eof]\n   * @returns {void}\n   */\n  function writeToChild(token, eof) {\n    assert(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (eof) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = Object.assign({}, point)\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   * @returns {void}\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      assert(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    assert(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  assert(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return factorySpace(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;CAEC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAGO,MAAM,WAAW;IAAC,UAAU;AAAkB;AAErD,sBAAsB,GACtB,MAAM,qBAAqB;IAAC,UAAU;AAAiB;AAEvD;;;CAGC,GACD,SAAS,mBAAmB,OAAO;IACjC,MAAM,OAAO,IAAI;IACjB,6BAA6B,GAC7B,MAAM,QAAQ,EAAE;IAChB,IAAI,YAAY;IAChB,wCAAwC,GACxC,IAAI;IACJ,8BAA8B,GAC9B,IAAI;IACJ,mBAAmB,GACnB,IAAI;IAEJ,OAAO;;IAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,mEAAmE;QACnE,uEAAuE;QACvE,SAAS;QACT,4EAA4E;QAC5E,kBAAkB;QAClB,uDAAuD;QACvD,yCAAyC;QACzC,kEAAkE;QAClE,uEAAuE;QACvE,qBAAqB;QACrB,IAAI,YAAY,MAAM,MAAM,EAAE;YAC5B,MAAM,OAAO,KAAK,CAAC,UAAU;YAC7B,KAAK,cAAc,GAAG,IAAI,CAAC,EAAE;YAC7B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,IAAI,CAAC,EAAE,CAAC,YAAY,EACpB;YAEF,OAAO,QAAQ,OAAO,CACpB,IAAI,CAAC,EAAE,CAAC,YAAY,EACpB,kBACA,oBACA;QACJ;QAEA,QAAQ;QACR,OAAO,mBAAmB;IAC5B;IAEA,kBAAkB,GAClB,SAAS,iBAAiB,IAAI;QAC5B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,cAAc,EACnB;QAGF;QAEA,yEAAyE;QACzE,wEAAwE;QACxE,cAAc;QACd,IAAI,KAAK,cAAc,CAAC,UAAU,EAAE;YAClC,KAAK,cAAc,CAAC,UAAU,GAAG;YAEjC,IAAI,WAAW;gBACb;YACF;YAEA,kEAAkE;YAClE,4DAA4D;YAC5D,MAAM,mBAAmB,KAAK,MAAM,CAAC,MAAM;YAC3C,IAAI,kBAAkB;YACtB,8BAA8B,GAC9B,IAAI;YAEJ,uBAAuB;YACvB,MAAO,kBAAmB;gBACxB,IACE,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,KAAK,UACpC,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,SAAS,EACxD;oBACA,QAAQ,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG;oBAC3C;gBACF;YACF;YAEA,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO;YAEd,eAAe;YAEf,iBAAiB;YACjB,IAAI,QAAQ;YAEZ,MAAO,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAE;gBACjC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG;gBAC9C;YACF;YAEA,4DAA4D;YAC5D,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EACH,KAAK,MAAM,EACX,kBAAkB,GAClB,GACA,KAAK,MAAM,CAAC,KAAK,CAAC;YAGpB,+BAA+B;YAC/B,KAAK,MAAM,CAAC,MAAM,GAAG;YAErB,OAAO,mBAAmB;QAC5B;QAEA,OAAO,MAAM;IACf;IAEA,kBAAkB,GAClB,SAAS,mBAAmB,IAAI;QAC9B,yEAAyE;QACzE,0DAA0D;QAC1D,sEAAsE;QACtE,sEAAsE;QACtE,SAAS;QACT,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,sEAAsE;YACtE,iBAAiB;YACjB,qDAAqD;YACrD,IAAI,CAAC,WAAW;gBACd,OAAO,kBAAkB;YAC3B;YAEA,kEAAkE;YAClE,qEAAqE;YACrE,SAAS;YACT,IAAI,UAAU,gBAAgB,IAAI,UAAU,gBAAgB,CAAC,QAAQ,EAAE;gBACrE,OAAO,UAAU;YACnB;YAEA,sDAAsD;YACtD,sEAAsE;YACtE,aAAa;YACb,uEAAuE;YACvE,kDAAkD;YAClD,KAAK,SAAS,GAAG,QACf,UAAU,gBAAgB,IAAI,CAAC,UAAU,6BAA6B;QAE1E;QAEA,qCAAqC;QACrC,KAAK,cAAc,GAAG,CAAC;QACvB,OAAO,QAAQ,KAAK,CAClB,oBACA,sBACA,uBACA;IACJ;IAEA,kBAAkB,GAClB,SAAS,qBAAqB,IAAI;QAChC,IAAI,WAAW;QACf,eAAe;QACf,OAAO,kBAAkB;IAC3B;IAEA,kBAAkB,GAClB,SAAS,sBAAsB,IAAI;QACjC,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,cAAc,MAAM,MAAM;QAC9D,kBAAkB,KAAK,GAAG,GAAG,MAAM;QACnC,OAAO,UAAU;IACnB;IAEA,kBAAkB,GAClB,SAAS,kBAAkB,IAAI;QAC7B,sBAAsB;QACtB,KAAK,cAAc,GAAG,CAAC;QACvB,OAAO,QAAQ,OAAO,CACpB,oBACA,mBACA,WACA;IACJ;IAEA,kBAAkB,GAClB,SAAS,kBAAkB,IAAI;QAC7B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,gBAAgB,EACrB;QAEF,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,cAAc,EACnB;QAEF;QACA,MAAM,IAAI,CAAC;YAAC,KAAK,gBAAgB;YAAE,KAAK,cAAc;SAAC;QACvD,eAAe;QACf,OAAO,kBAAkB;IAC3B;IAEA,kBAAkB,GAClB,SAAS,UAAU,IAAI;QACrB,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,IAAI,WAAW;YACf,eAAe;YACf,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,YAAY,aAAa,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG;QAClD,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS,EAAE;YAC7B,aAAa,2JAAA,CAAA,YAAS,CAAC,eAAe;YACtC,UAAU;YACV,YAAY;QACd;QAEA,OAAO,aAAa;IACtB;IAEA,kBAAkB,GAClB,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,aAAa,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS,GAAG;YAC5C,eAAe;YACf,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,OAAO,CAAC;YAChB,aAAa,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS;YACzC,+BAA+B;YAC/B,YAAY;YACZ,KAAK,SAAS,GAAG;YACjB,OAAO;QACT;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;GAIC,GACD,SAAS,aAAa,KAAK,EAAE,GAAG;QAC9B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,WAAW;QAClB,MAAM,SAAS,KAAK,WAAW,CAAC;QAChC,IAAI,KAAK,OAAO,IAAI,CAAC;QACrB,MAAM,QAAQ,GAAG;QACjB,IAAI,YAAY,WAAW,IAAI,GAAG;QAClC,aAAa;QACb,UAAU,UAAU,CAAC,MAAM,KAAK;QAChC,UAAU,KAAK,CAAC;QAEhB,yCAAyC;QACzC,EAAE;QACF,cAAc;QACd,MAAM;QACN,KAAK;QACL,EAAE;QACF,MAAM;QACN,EAAE;QACF,SAAS;QACT,IAAI;QACJ,EAAE;QACF,MAAM;QACN,EAAE;QACF,UAAU;QACV,IAAI;QACJ,MAAM;QACN,EAAE;QACF,yEAAyE;QACzE,uEAAuE;QACvE,yCAAyC;QACzC,yEAAyE;QACzE,wDAAwD;QACxD,EAAE;QACF,qEAAqE;QACrE,qBAAqB;QACrB,oEAAoE;QACpE,uBAAuB;QACvB,yEAAyE;QACzE,8CAA8C;QAC9C,EAAE;QACF,sEAAsE;QACtE,kDAAkD;QAClD,yEAAyE;QACzE,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,QAAQ,UAAU,MAAM,CAAC,MAAM;YAEnC,MAAO,QAAS;gBACd,IACE,2CAA2C;gBAC3C,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,mBAC1C,gCAAgC;gBAChC,CAAC,CAAC,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAC9B,qBAAqB;gBACrB,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,eAAe,GACzD;oBACA,mEAAmE;oBACnE,qBAAqB;oBACrB;gBACF;YACF;YAEA,kEAAkE;YAClE,qDAAqD;YACrD,MAAM,mBAAmB,KAAK,MAAM,CAAC,MAAM;YAC3C,IAAI,kBAAkB;YACtB,gCAAgC,GAChC,IAAI;YACJ,8BAA8B,GAC9B,IAAI;YAEJ,0DAA0D;YAC1D,MAAO,kBAAmB;gBACxB,IACE,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,KAAK,UACpC,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,SAAS,EACxD;oBACA,IAAI,MAAM;wBACR,QAAQ,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG;wBAC3C;oBACF;oBAEA,OAAO;gBACT;YACF;YAEA,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO;YAEd,eAAe;YAEf,iBAAiB;YACjB,QAAQ;YAER,MAAO,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAE;gBACjC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG;gBAC9C;YACF;YAEA,4DAA4D;YAC5D,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EACH,KAAK,MAAM,EACX,kBAAkB,GAClB,GACA,KAAK,MAAM,CAAC,KAAK,CAAC;YAGpB,+BAA+B;YAC/B,KAAK,MAAM,CAAC,MAAM,GAAG;QACvB;IACF;IAEA;;;GAGC,GACD,SAAS,eAAe,IAAI;QAC1B,IAAI,QAAQ,MAAM,MAAM;QAExB,wBAAwB;QACxB,MAAO,UAAU,KAAM;YACrB,MAAM,QAAQ,KAAK,CAAC,MAAM;YAC1B,KAAK,cAAc,GAAG,KAAK,CAAC,EAAE;YAC9B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,CAAC,EAAE,CAAC,IAAI,EACb;YAEF,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;QAC3B;QAEA,MAAM,MAAM,GAAG;IACjB;IAEA,SAAS;QACP,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,cAAc,EACnB;QAEF,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,WAAW;QAClB,UAAU,KAAK,CAAC;YAAC,uJAAA,CAAA,QAAK,CAAC,GAAG;SAAC;QAC3B,aAAa;QACb,YAAY;QACZ,KAAK,cAAc,CAAC,UAAU,GAAG;IACnC;AACF;AAEA;;;CAGC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,GAAG;IACzC,gCAAgC;IAChC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;IAEF,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAChB,SACA,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,MACrD,uJAAA,CAAA,QAAK,CAAC,UAAU,EAChB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,2JAAA,CAAA,YAAS,CAAC,OAAO;AAEzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/initialize/flow.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\nimport {blankLine, content} from 'micromark-core-commonmark'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {InitialConstruct} */\nexport const flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n * @type {Initializer}\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      factorySpace(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(content, afterConstruct)\n        ),\n        types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,OAAO;IAAC,UAAU;AAAc;AAE7C;;;CAGC,GACD,SAAS,eAAe,OAAO;IAC7B,MAAM,OAAO,IAAI;IACjB,MAAM,UAAU,QAAQ,OAAO,CAC7B,6BAA6B;IAC7B,iLAAA,CAAA,YAAS,EACT,eACA,sDAAsD;IACtD,QAAQ,OAAO,CACb,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAClC,gBACA,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EACT,SACA,QAAQ,OAAO,CACb,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAC3B,gBACA,QAAQ,OAAO,CAAC,2KAAA,CAAA,UAAO,EAAE,kBAE3B,uJAAA,CAAA,QAAK,CAAC,UAAU;IAKtB,OAAO;;IAEP,kBAAkB,GAClB,SAAS,cAAc,IAAI;QACzB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OACzC;QAGF,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,eAAe;QACnC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,eAAe;QAClC,KAAK,gBAAgB,GAAG;QACxB,OAAO;IACT;IAEA,kBAAkB,GAClB,SAAS,eAAe,IAAI;QAC1B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OACzC;QAGF,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,OAAO,CAAC;YAChB;QACF;QAEA,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,KAAK,gBAAgB,GAAG;QACxB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/initialize/text.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\nexport const resolver = {resolveAll: createResolver()}\nexport const string = initializeFactory('string')\nexport const text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n * @returns {InitialConstruct}\n */\nfunction initializeFactory(field) {\n  return {\n    tokenize: initializeText,\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    )\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     * @returns {boolean}\n     */\n    function atBreak(code) {\n      if (code === codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        assert(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n * @returns {Resolver}\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === types.lineEnding) &&\n      events[eventIndex - 1][1].type === types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < constants.hardBreakPrefixSizeMin\n              ? types.lineSuffix\n              : types.hardBreakTrailing,\n          start: {\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size,\n            _index: data.start._index + index,\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex\n          },\n          end: Object.assign({}, data.end)\n        }\n\n        data.end = Object.assign({}, token.start)\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AAED;AACA;AACA;AACA;;;;;AAEO,MAAM,WAAW;IAAC,YAAY;AAAgB;AAC9C,MAAM,SAAS,kBAAkB;AACjC,MAAM,OAAO,kBAAkB;AAEtC;;;CAGC,GACD,SAAS,kBAAkB,KAAK;IAC9B,OAAO;QACL,UAAU;QACV,YAAY,eACV,UAAU,SAAS,yBAAyB;IAEhD;;IAEA;;;GAGC,GACD,SAAS,eAAe,OAAO;QAC7B,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM;QAChD,MAAM,OAAO,QAAQ,OAAO,CAAC,YAAY,OAAO;QAEhD,OAAO;;QAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;YACjB,OAAO,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;QAC9C;QAEA,kBAAkB,GAClB,SAAS,QAAQ,IAAI;YACnB,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;gBACtB,QAAQ,OAAO,CAAC;gBAChB;YACF;YAEA,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,IAAI;YACxB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,kBAAkB,GAClB,SAAS,KAAK,IAAI;YAChB,IAAI,QAAQ,OAAO;gBACjB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,IAAI;gBACvB,OAAO,KAAK;YACd;YAEA,QAAQ;YACR,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA;;;KAGC,GACD,SAAS,QAAQ,IAAI;YACnB,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;gBACtB,OAAO;YACT;YAEA,MAAM,OAAO,UAAU,CAAC,KAAK;YAC7B,IAAI,QAAQ,CAAC;YAEb,IAAI,MAAM;gBACR,gCAAgC;gBAChC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO;gBAE5B,MAAO,EAAE,QAAQ,KAAK,MAAM,CAAE;oBAC5B,MAAM,OAAO,IAAI,CAAC,MAAM;oBACxB,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG;wBAC7D,OAAO;oBACT;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEA;;;CAGC,GACD,SAAS,eAAe,aAAa;IACnC,OAAO;;IAEP,qBAAqB,GACrB,SAAS,eAAe,MAAM,EAAE,OAAO;QACrC,IAAI,QAAQ,CAAC;QACb,+BAA+B,GAC/B,IAAI;QAEJ,sEAAsE;QACtE,kCAAkC;QAClC,MAAO,EAAE,SAAS,OAAO,MAAM,CAAE;YAC/B,IAAI,UAAU,WAAW;gBACvB,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,IAAI,EAAE;oBACzD,QAAQ;oBACR;gBACF;YACF,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,IAAI,EAAE;gBACjE,gDAAgD;gBAChD,IAAI,UAAU,QAAQ,GAAG;oBACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,GAAG;oBAC/C,OAAO,MAAM,CAAC,QAAQ,GAAG,QAAQ,QAAQ;oBACzC,QAAQ,QAAQ;gBAClB;gBAEA,QAAQ;YACV;QACF;QAEA,OAAO,gBAAgB,cAAc,QAAQ,WAAW;IAC1D;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,uBAAuB,MAAM,EAAE,OAAO;IAC7C,IAAI,aAAa,EAAE,cAAc;;IAEjC,MAAO,EAAE,cAAc,OAAO,MAAM,CAAE;QACpC,IACE,CAAC,eAAe,OAAO,MAAM,IAC3B,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,UAAU,KACjD,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,IAAI,EAC7C;YACA,MAAM,OAAO,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE;YACtC,MAAM,SAAS,QAAQ,WAAW,CAAC;YACnC,IAAI,QAAQ,OAAO,MAAM;YACzB,IAAI,cAAc,CAAC;YACnB,IAAI,OAAO;YACX,gCAAgC,GAChC,IAAI;YAEJ,MAAO,QAAS;gBACd,MAAM,QAAQ,MAAM,CAAC,MAAM;gBAE3B,IAAI,OAAO,UAAU,UAAU;oBAC7B,cAAc,MAAM,MAAM;oBAE1B,MAAO,MAAM,UAAU,CAAC,cAAc,OAAO,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAE;wBACxD;wBACA;oBACF;oBAEA,IAAI,aAAa;oBACjB,cAAc,CAAC;gBACjB,OAEK,IAAI,UAAU,uJAAA,CAAA,QAAK,CAAC,aAAa,EAAE;oBACtC,OAAO;oBACP;gBACF,OAAO,IAAI,UAAU,uJAAA,CAAA,QAAK,CAAC,YAAY,EAAE;gBACvC,QAAQ;gBACV,OAAO;oBACL,+BAA+B;oBAC/B;oBACA;gBACF;YACF;YAEA,IAAI,MAAM;gBACR,MAAM,QAAQ;oBACZ,MACE,eAAe,OAAO,MAAM,IAC5B,QACA,OAAO,2JAAA,CAAA,YAAS,CAAC,sBAAsB,GACnC,uJAAA,CAAA,QAAK,CAAC,UAAU,GAChB,uJAAA,CAAA,QAAK,CAAC,iBAAiB;oBAC7B,OAAO;wBACL,MAAM,KAAK,GAAG,CAAC,IAAI;wBACnB,QAAQ,KAAK,GAAG,CAAC,MAAM,GAAG;wBAC1B,QAAQ,KAAK,GAAG,CAAC,MAAM,GAAG;wBAC1B,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG;wBAC5B,cAAc,QACV,cACA,KAAK,KAAK,CAAC,YAAY,GAAG;oBAChC;oBACA,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG;gBACjC;gBAEA,KAAK,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK;gBAExC,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,EAAE;oBACzC,OAAO,MAAM,CAAC,MAAM;gBACtB,OAAO;oBACL,OAAO,MAAM,CACX,YACA,GACA;wBAAC;wBAAS;wBAAO;qBAAQ,EACzB;wBAAC;wBAAQ;wBAAO;qBAAQ;oBAE1B,cAAc;gBAChB;YACF;YAEA;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/create-tokenizer.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ConstructRecord} ConstructRecord\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').ParseContext} ParseContext\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenType} TokenType\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\n/**\n * @callback Restore\n * @returns {void}\n *\n * @typedef Info\n * @property {Restore} restore\n * @property {number} from\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n * @param {Info} info\n * @returns {void}\n */\n\nimport createDebug from 'debug'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {values} from 'micromark-util-symbol/values.js'\nimport {ok as assert} from 'uvu/assert'\n\nconst debug = createDebug('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n * @param {InitialConstruct} initialize\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n * @returns {TokenizeContext}\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = Object.assign(\n    from ? Object.assign({}, from) : {line: 1, column: 1, offset: 0},\n    {_index: 0, _bufferIndex: -1}\n  )\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    consume,\n    enter,\n    exit,\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    previous: codes.eof,\n    code: codes.eof,\n    containerState: {},\n    events: [],\n    parser,\n    sliceStream,\n    sliceSerialize,\n    now,\n    defineSkip,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | void}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {line, column, offset, _index, _bufferIndex} = point\n    return {line, column, offset, _index, _bufferIndex}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {void}\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   * @returns {void}\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    assert(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    assert(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    assert(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if (markdownLineEnding(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      // @ts-expect-error Points w/ non-negative `_bufferIndex` reference\n      // strings.\n      if (point._bufferIndex === chunks[point._index].length) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    assert(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    assert(type === token.type, 'expected exit token to match current token')\n\n    assert(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | Construct | ConstructRecord} constructs\n     * @param {State} returnState\n     * @param {State | undefined} [bogusState]\n     * @returns {State}\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {Array<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n        ? // @ts-expect-error Looks like a construct.\n          handleListOfConstructs([constructs])\n        : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       * @returns {State}\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const def = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(def) ? def : def ? [def] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {Array<Construct>} list\n       * @returns {State}\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       * @returns {State}\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   * @param {number} from\n   * @returns {void}\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      splice(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    assert(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {restore, from: startEventsIndex}\n\n    /**\n     * Restore state.\n     *\n     * @returns {void}\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {void}\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {Array<Chunk>} chunks\n * @param {Pick<Token, 'end' | 'start'>} token\n * @returns {Array<Chunk>}\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index')\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {Array<Chunk>} chunks\n * @param {boolean | undefined} [expandTabs=false]\n * @returns {string}\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case codes.carriageReturn: {\n          value = values.cr\n\n          break\n        }\n\n        case codes.lineFeed: {\n          value = values.lf\n\n          break\n        }\n\n        case codes.carriageReturnLineFeed: {\n          value = values.cr + values.lf\n\n          break\n        }\n\n        case codes.horizontalTab: {\n          value = expandTabs ? values.space : values.ht\n\n          break\n        }\n\n        case codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = values.space\n\n          break\n        }\n\n        default: {\n          assert(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC,GAED;;;;;;;;;;;;;CAaC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,QAAQ,CAAA,GAAA,0IAAA,CAAA,UAAW,AAAD,EAAE;AAgBnB,SAAS,gBAAgB,MAAM,EAAE,UAAU,EAAE,IAAI;IACtD,kBAAkB,GAClB,IAAI,QAAQ,OAAO,MAAM,CACvB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ;QAAC,MAAM;QAAG,QAAQ;QAAG,QAAQ;IAAC,GAC/D;QAAC,QAAQ;QAAG,cAAc,CAAC;IAAC;IAE9B,mCAAmC,GACnC,MAAM,cAAc,CAAC;IACrB,6BAA6B,GAC7B,MAAM,uBAAuB,EAAE;IAC/B,yBAAyB,GACzB,IAAI,SAAS,EAAE;IACf,yBAAyB,GACzB,IAAI,QAAQ,EAAE;IACd,gCAAgC,GAChC,IAAI,WAAW;IAEf;;;;GAIC,GACD,MAAM,UAAU;QACd;QACA;QACA;QACA,SAAS,iBAAiB;QAC1B,OAAO,iBAAiB;QACxB,WAAW,iBAAiB,mBAAmB;YAAC,WAAW;QAAI;IACjE;IAEA;;;;GAIC,GACD,MAAM,UAAU;QACd,UAAU,uJAAA,CAAA,QAAK,CAAC,GAAG;QACnB,MAAM,uJAAA,CAAA,QAAK,CAAC,GAAG;QACf,gBAAgB,CAAC;QACjB,QAAQ,EAAE;QACV;QACA;QACA;QACA;QACA;QACA;IACF;IAEA;;;;GAIC,GACD,IAAI,QAAQ,WAAW,QAAQ,CAAC,IAAI,CAAC,SAAS;IAE9C;;;;GAIC,GACD,IAAI;IAEJ,IAAI,WAAW,UAAU,EAAE;QACzB,qBAAqB,IAAI,CAAC;IAC5B;IAEA,OAAO;;IAEP,qCAAqC,GACrC,SAAS,MAAM,KAAK;QAClB,SAAS,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;QAEtB;QAEA,sDAAsD;QACtD,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YAC3C,OAAO,EAAE;QACX;QAEA,UAAU,YAAY;QAEtB,gCAAgC;QAChC,QAAQ,MAAM,GAAG,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE,sBAAsB,QAAQ,MAAM,EAAE;QAElE,OAAO,QAAQ,MAAM;IACvB;IAEA,EAAE;IACF,SAAS;IACT,EAAE;IAEF,8CAA8C,GAC9C,SAAS,eAAe,KAAK,EAAE,UAAU;QACvC,OAAO,gBAAgB,YAAY,QAAQ;IAC7C;IAEA,2CAA2C,GAC3C,SAAS,YAAY,KAAK;QACxB,OAAO,YAAY,QAAQ;IAC7B;IAEA,mCAAmC,GACnC,SAAS;QACP,iFAAiF;QACjF,MAAM,EAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG;QACrD,OAAO;YAAC;YAAM;YAAQ;YAAQ;YAAQ;QAAY;IACpD;IAEA,0CAA0C,GAC1C,SAAS,WAAW,KAAK;QACvB,WAAW,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,MAAM;QACtC;QACA,MAAM,+BAA+B;IACvC;IAEA,EAAE;IACF,oBAAoB;IACpB,EAAE;IAEF;;;;;;;;;GASC,GACD,SAAS;QACP,mBAAmB,GACnB,IAAI;QAEJ,MAAO,MAAM,MAAM,GAAG,OAAO,MAAM,CAAE;YACnC,MAAM,QAAQ,MAAM,CAAC,MAAM,MAAM,CAAC;YAElC,+CAA+C;YAC/C,IAAI,OAAO,UAAU,UAAU;gBAC7B,aAAa,MAAM,MAAM;gBAEzB,IAAI,MAAM,YAAY,GAAG,GAAG;oBAC1B,MAAM,YAAY,GAAG;gBACvB;gBAEA,MACE,MAAM,MAAM,KAAK,cACjB,MAAM,YAAY,GAAG,MAAM,MAAM,CACjC;oBACA,GAAG,MAAM,UAAU,CAAC,MAAM,YAAY;gBACxC;YACF,OAAO;gBACL,GAAG;YACL;QACF;IACF;IAEA;;;;;GAKC,GACD,SAAS,GAAG,IAAI;QACd,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,aAAa,MAAM;QAC1B,WAAW;QACX,MAAM,4BAA4B,MAAM,SAAS,MAAM,IAAI;QAC3D,eAAe;QACf,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,UAAU,YAAY;QACpC,QAAQ,MAAM;IAChB;IAEA,+BAA+B,GAC/B,SAAS,QAAQ,IAAI;QACnB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,cAAc;QAE9B,MAAM,iBAAiB;QAEvB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,aAAa,WACb;QAEF,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,SAAS,OACL,QAAQ,MAAM,CAAC,MAAM,KAAK,KACxB,QAAQ,MAAM,CAAC,QAAQ,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,SACnD,QAAQ,MAAM,CAAC,QAAQ,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,SACrD;QAGF,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,MAAM,IAAI;YACV,MAAM,MAAM,GAAG;YACf,MAAM,MAAM,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,sBAAsB,GAAG,IAAI;YAC5D;YACA,MAAM,6BAA6B;QACrC,OAAO,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,YAAY,EAAE;YACtC,MAAM,MAAM;YACZ,MAAM,MAAM;QACd;QAEA,yBAAyB;QACzB,IAAI,MAAM,YAAY,GAAG,GAAG;YAC1B,MAAM,MAAM;QACd,OAAO;YACL,MAAM,YAAY;YAElB,0BAA0B;YAC1B,mEAAmE;YACnE,WAAW;YACX,IAAI,MAAM,YAAY,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC,MAAM,EAAE;gBACtD,MAAM,YAAY,GAAG,CAAC;gBACtB,MAAM,MAAM;YACd;QACF;QAEA,iCAAiC;QACjC,QAAQ,QAAQ,GAAG;QAEnB,oBAAoB;QACpB,WAAW;IACb;IAEA,6BAA6B,GAC7B,SAAS,MAAM,IAAI,EAAE,MAAM;QACzB,kBAAkB,GAClB,uEAAuE;QACvE,MAAM,QAAQ,UAAU,CAAC;QACzB,MAAM,IAAI,GAAG;QACb,MAAM,KAAK,GAAG;QAEd,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,SAAS,UAAU;QACjC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,MAAM,GAAG,GAAG;QACxB,MAAM,eAAe;QAErB,QAAQ,MAAM,CAAC,IAAI,CAAC;YAAC;YAAS;YAAO;SAAQ;QAE7C,MAAM,IAAI,CAAC;QAEX,OAAO;IACT;IAEA,4BAA4B,GAC5B,SAAS,KAAK,IAAI;QAChB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,SAAS,UAAU;QACjC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,MAAM,GAAG,GAAG;QAExB,MAAM,QAAQ,MAAM,GAAG;QACvB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO;QACd,MAAM,GAAG,GAAG;QAEZ,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,MAAM,IAAI,EAAE;QAE5B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,CAAC,CACC,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,GAAG,CAAC,MAAM,IACvC,MAAM,KAAK,CAAC,YAAY,KAAK,MAAM,GAAG,CAAC,YAAY,AACrD,GACA,gCAAgC,OAAO;QAGzC,MAAM,cAAc,MAAM,IAAI;QAC9B,QAAQ,MAAM,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAO;SAAQ;QAE5C,OAAO;IACT;IAEA;;;;GAIC,GACD,SAAS,sBAAsB,SAAS,EAAE,IAAI;QAC5C,UAAU,WAAW,KAAK,IAAI;IAChC;IAEA;;;;GAIC,GACD,SAAS,kBAAkB,CAAC,EAAE,IAAI;QAChC,KAAK,OAAO;IACd;IAEA;;;;;GAKC,GACD,SAAS,iBAAiB,QAAQ,EAAE,MAAM;QACxC,OAAO;;QAEP;;;;;;;;KAQC,GACD,SAAS,KAAK,UAAU,EAAE,WAAW,EAAE,UAAU;YAC/C,6BAA6B,GAC7B,IAAI;YACJ,mBAAmB,GACnB,IAAI;YACJ,sBAAsB,GACtB,IAAI;YACJ,iBAAiB,GACjB,IAAI;YAEJ,OAAO,MAAM,OAAO,CAAC,cACjB,oBAAoB,GACpB,uBAAuB,cACvB,cAAc,aAEd,uBAAuB;gBAAC;aAAW,IACnC,sBAAsB;;YAE1B;;;;;OAKC,GACD,SAAS,sBAAsB,GAAG;gBAChC,OAAO;;gBAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;oBACjB,MAAM,MAAM,SAAS,QAAQ,GAAG,CAAC,KAAK;oBACtC,MAAM,MAAM,SAAS,QAAQ,IAAI,IAAI;oBACrC,MAAM,OAAO;wBACX,mCAAmC;wBACnC,oBAAoB,MAChB,MAAM,OAAO,CAAC,OAAO,MAAM,MAAM;4BAAC;yBAAI,GAAG,EAAE;2BAC3C,MAAM,OAAO,CAAC,OAAO,MAAM,MAAM;4BAAC;yBAAI,GAAG,EAAE;qBAChD;oBAED,OAAO,uBAAuB,MAAM;gBACtC;YACF;YAEA;;;;;OAKC,GACD,SAAS,uBAAuB,IAAI;gBAClC,mBAAmB;gBACnB,iBAAiB;gBAEjB,IAAI,KAAK,MAAM,KAAK,GAAG;oBACrB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,YAAY;oBACnB,OAAO;gBACT;gBAEA,OAAO,gBAAgB,IAAI,CAAC,eAAe;YAC7C;YAEA;;;;;OAKC,GACD,SAAS,gBAAgB,SAAS;gBAChC,OAAO;;gBAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;oBACjB,mEAAmE;oBACnE,oEAAoE;oBACpE,uEAAuE;oBACvE,kBAAkB;oBAClB,OAAO;oBACP,mBAAmB;oBAEnB,IAAI,CAAC,UAAU,OAAO,EAAE;wBACtB,QAAQ,gBAAgB,GAAG;oBAC7B;oBAEA,gCAAgC;oBAChC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,QAAQ,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACtC;oBAGF,IACE,UAAU,IAAI,IACd,QAAQ,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,GAC9D;wBACA,OAAO,IAAI;oBACb;oBAEA,OAAO,UAAU,QAAQ,CAAC,IAAI,CAC5B,6DAA6D;oBAC7D,aAAa;oBACb,iEAAiE;oBACjE,SAAS,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,UAAU,UAAU,SACzD,SACA,IACA,KACA;gBACJ;YACF;YAEA,kBAAkB,GAClB,SAAS,GAAG,IAAI;gBACd,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,cAAc;gBAC9B,WAAW;gBACX,SAAS,kBAAkB;gBAC3B,OAAO;YACT;YAEA,kBAAkB,GAClB,SAAS,IAAI,IAAI;gBACf,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,cAAc;gBAC9B,WAAW;gBACX,KAAK,OAAO;gBAEZ,IAAI,EAAE,iBAAiB,iBAAiB,MAAM,EAAE;oBAC9C,OAAO,gBAAgB,gBAAgB,CAAC,eAAe;gBACzD;gBAEA,OAAO;YACT;QACF;IACF;IAEA;;;;GAIC,GACD,SAAS,UAAU,SAAS,EAAE,IAAI;QAChC,IAAI,UAAU,UAAU,IAAI,CAAC,qBAAqB,QAAQ,CAAC,YAAY;YACrE,qBAAqB,IAAI,CAAC;QAC5B;QAEA,IAAI,UAAU,OAAO,EAAE;YACrB,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EACH,QAAQ,MAAM,EACd,MACA,QAAQ,MAAM,CAAC,MAAM,GAAG,MACxB,UAAU,OAAO,CAAC,QAAQ,MAAM,CAAC,KAAK,CAAC,OAAO;QAElD;QAEA,IAAI,UAAU,SAAS,EAAE;YACvB,QAAQ,MAAM,GAAG,UAAU,SAAS,CAAC,QAAQ,MAAM,EAAE;QACvD;QAEA,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,UAAU,OAAO,IACf,QAAQ,MAAM,CAAC,MAAM,KAAK,KAC1B,QAAQ,MAAM,CAAC,QAAQ,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,QACnD;IAEJ;IAEA;;;;GAIC,GACD,SAAS;QACP,MAAM,aAAa;QACnB,MAAM,gBAAgB,QAAQ,QAAQ;QACtC,MAAM,wBAAwB,QAAQ,gBAAgB;QACtD,MAAM,mBAAmB,QAAQ,MAAM,CAAC,MAAM;QAC9C,MAAM,aAAa,MAAM,IAAI,CAAC;QAE9B,OAAO;YAAC;YAAS,MAAM;QAAgB;;QAEvC;;;;KAIC,GACD,SAAS;YACP,QAAQ;YACR,QAAQ,QAAQ,GAAG;YACnB,QAAQ,gBAAgB,GAAG;YAC3B,QAAQ,MAAM,CAAC,MAAM,GAAG;YACxB,QAAQ;YACR;YACA,MAAM,2BAA2B;QACnC;IACF;IAEA;;;;;GAKC,GACD,SAAS;QACP,IAAI,MAAM,IAAI,IAAI,eAAe,MAAM,MAAM,GAAG,GAAG;YACjD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC;YACtC,MAAM,MAAM,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,GAAG;QAC5C;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM;IACrC,MAAM,mBAAmB,MAAM,KAAK,CAAC,YAAY;IACjD,MAAM,WAAW,MAAM,GAAG,CAAC,MAAM;IACjC,MAAM,iBAAiB,MAAM,GAAG,CAAC,YAAY;IAC7C,yBAAyB,GACzB,IAAI;IAEJ,IAAI,eAAe,UAAU;QAC3B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,iBAAiB,CAAC,GAAG;QAC5B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,mBAAmB,CAAC,GAAG;QAC9B,4DAA4D;QAC5D,OAAO;YAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAkB;SAAgB;IACrE,OAAO;QACL,OAAO,OAAO,KAAK,CAAC,YAAY;QAEhC,IAAI,mBAAmB,CAAC,GAAG;YACzB,MAAM,OAAO,IAAI,CAAC,EAAE;YACpB,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC;YACvB,OAAO;gBACL,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,qBAAqB,GAAG;gBAC/B,KAAK,KAAK;YACZ;QACF;QAEA,IAAI,iBAAiB,GAAG;YACtB,4DAA4D;YAC5D,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;QACtC;IACF;IAEA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,gBAAgB,MAAM,EAAE,UAAU;IACzC,IAAI,QAAQ,CAAC;IACb,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,gCAAgC,GAChC,IAAI;IAEJ,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,QAAQ,MAAM,CAAC,MAAM;QAC3B,mBAAmB,GACnB,IAAI;QAEJ,IAAI,OAAO,UAAU,UAAU;YAC7B,QAAQ;QACV,OACE,OAAQ;YACN,KAAK,uJAAA,CAAA,QAAK,CAAC,cAAc;gBAAE;oBACzB,QAAQ,wJAAA,CAAA,SAAM,CAAC,EAAE;oBAEjB;gBACF;YAEA,KAAK,uJAAA,CAAA,QAAK,CAAC,QAAQ;gBAAE;oBACnB,QAAQ,wJAAA,CAAA,SAAM,CAAC,EAAE;oBAEjB;gBACF;YAEA,KAAK,uJAAA,CAAA,QAAK,CAAC,sBAAsB;gBAAE;oBACjC,QAAQ,wJAAA,CAAA,SAAM,CAAC,EAAE,GAAG,wJAAA,CAAA,SAAM,CAAC,EAAE;oBAE7B;gBACF;YAEA,KAAK,uJAAA,CAAA,QAAK,CAAC,aAAa;gBAAE;oBACxB,QAAQ,aAAa,wJAAA,CAAA,SAAM,CAAC,KAAK,GAAG,wJAAA,CAAA,SAAM,CAAC,EAAE;oBAE7C;gBACF;YAEA,KAAK,uJAAA,CAAA,QAAK,CAAC,YAAY;gBAAE;oBACvB,IAAI,CAAC,cAAc,OAAO;oBAC1B,QAAQ,wJAAA,CAAA,SAAM,CAAC,KAAK;oBAEpB;gBACF;YAEA;gBAAS;oBACP,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,UAAU,UAAU;oBAClC,wCAAwC;oBACxC,QAAQ,OAAO,YAAY,CAAC;gBAC9B;QACF;QAEF,QAAQ,UAAU,uJAAA,CAAA,QAAK,CAAC,aAAa;QACrC,OAAO,IAAI,CAAC;IACd;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/constructs.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Extension} Extension\n */\n\nimport {\n  attention,\n  autolink,\n  blockQuote,\n  characterEscape,\n  characterReference,\n  codeFenced,\n  codeIndented,\n  codeText,\n  definition,\n  hardBreakEscape,\n  headingAtx,\n  htmlFlow,\n  htmlText,\n  labelEnd,\n  labelStartImage,\n  labelStartLink,\n  lineEnding,\n  list,\n  setextUnderline,\n  thematicBreak\n} from 'micromark-core-commonmark'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {resolver as resolveText} from './initialize/text.js'\n\n/** @satisfies {Extension['document']} */\nexport const document = {\n  [codes.asterisk]: list,\n  [codes.plusSign]: list,\n  [codes.dash]: list,\n  [codes.digit0]: list,\n  [codes.digit1]: list,\n  [codes.digit2]: list,\n  [codes.digit3]: list,\n  [codes.digit4]: list,\n  [codes.digit5]: list,\n  [codes.digit6]: list,\n  [codes.digit7]: list,\n  [codes.digit8]: list,\n  [codes.digit9]: list,\n  [codes.greaterThan]: blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nexport const contentInitial = {\n  [codes.leftSquareBracket]: definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nexport const flowInitial = {\n  [codes.horizontalTab]: codeIndented,\n  [codes.virtualSpace]: codeIndented,\n  [codes.space]: codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nexport const flow = {\n  [codes.numberSign]: headingAtx,\n  [codes.asterisk]: thematicBreak,\n  [codes.dash]: [setextUnderline, thematicBreak],\n  [codes.lessThan]: htmlFlow,\n  [codes.equalsTo]: setextUnderline,\n  [codes.underscore]: thematicBreak,\n  [codes.graveAccent]: codeFenced,\n  [codes.tilde]: codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nexport const string = {\n  [codes.ampersand]: characterReference,\n  [codes.backslash]: characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nexport const text = {\n  [codes.carriageReturn]: lineEnding,\n  [codes.lineFeed]: lineEnding,\n  [codes.carriageReturnLineFeed]: lineEnding,\n  [codes.exclamationMark]: labelStartImage,\n  [codes.ampersand]: characterReference,\n  [codes.asterisk]: attention,\n  [codes.lessThan]: [autolink, htmlText],\n  [codes.leftSquareBracket]: labelStartLink,\n  [codes.backslash]: [hardBreakEscape, characterEscape],\n  [codes.rightSquareBracket]: labelEnd,\n  [codes.underscore]: attention,\n  [codes.graveAccent]: codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nexport const insideSpan = {null: [attention, resolveText]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nexport const attentionMarkers = {null: [codes.asterisk, codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nexport const disable = {null: []}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;AAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;;;;AAGO,MAAM,WAAW;IACtB,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,wKAAA,CAAA,OAAI;IACtB,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,wKAAA,CAAA,OAAI;IACtB,CAAC,uJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,EAAE,wKAAA,CAAA,OAAI;IAClB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,EAAE,wKAAA,CAAA,OAAI;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW,CAAC,EAAE,kLAAA,CAAA,aAAU;AACjC;AAGO,MAAM,iBAAiB;IAC5B,CAAC,uJAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,EAAE,8KAAA,CAAA,aAAU;AACvC;AAGO,MAAM,cAAc;IACzB,CAAC,uJAAA,CAAA,QAAK,CAAC,aAAa,CAAC,EAAE,oLAAA,CAAA,eAAY;IACnC,CAAC,uJAAA,CAAA,QAAK,CAAC,YAAY,CAAC,EAAE,oLAAA,CAAA,eAAY;IAClC,CAAC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE,oLAAA,CAAA,eAAY;AAC7B;AAGO,MAAM,OAAO;IAClB,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU,CAAC,EAAE,kLAAA,CAAA,aAAU;IAC9B,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,qLAAA,CAAA,gBAAa;IAC/B,CAAC,uJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,EAAE;QAAC,uLAAA,CAAA,kBAAe;QAAE,qLAAA,CAAA,gBAAa;KAAC;IAC9C,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,gLAAA,CAAA,WAAQ;IAC1B,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,uLAAA,CAAA,kBAAe;IACjC,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU,CAAC,EAAE,qLAAA,CAAA,gBAAa;IACjC,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW,CAAC,EAAE,kLAAA,CAAA,aAAU;IAC/B,CAAC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE,kLAAA,CAAA,aAAU;AAC3B;AAGO,MAAM,SAAS;IACpB,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE,0LAAA,CAAA,qBAAkB;IACrC,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE,uLAAA,CAAA,kBAAe;AACpC;AAGO,MAAM,OAAO;IAClB,CAAC,uJAAA,CAAA,QAAK,CAAC,cAAc,CAAC,EAAE,kLAAA,CAAA,aAAU;IAClC,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,kLAAA,CAAA,aAAU;IAC5B,CAAC,uJAAA,CAAA,QAAK,CAAC,sBAAsB,CAAC,EAAE,kLAAA,CAAA,aAAU;IAC1C,CAAC,uJAAA,CAAA,QAAK,CAAC,eAAe,CAAC,EAAE,2LAAA,CAAA,kBAAe;IACxC,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE,0LAAA,CAAA,qBAAkB;IACrC,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE,6KAAA,CAAA,YAAS;IAC3B,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ,CAAC,EAAE;QAAC,4KAAA,CAAA,WAAQ;QAAE,gLAAA,CAAA,WAAQ;KAAC;IACtC,CAAC,uJAAA,CAAA,QAAK,CAAC,iBAAiB,CAAC,EAAE,0LAAA,CAAA,iBAAc;IACzC,CAAC,uJAAA,CAAA,QAAK,CAAC,SAAS,CAAC,EAAE;QAAC,2LAAA,CAAA,kBAAe;QAAE,uLAAA,CAAA,kBAAe;KAAC;IACrD,CAAC,uJAAA,CAAA,QAAK,CAAC,kBAAkB,CAAC,EAAE,gLAAA,CAAA,WAAQ;IACpC,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU,CAAC,EAAE,6KAAA,CAAA,YAAS;IAC7B,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW,CAAC,EAAE,gLAAA,CAAA,WAAQ;AAC/B;AAGO,MAAM,aAAa;IAAC,MAAM;QAAC,6KAAA,CAAA,YAAS;QAAE,gKAAA,CAAA,WAAW;KAAC;AAAA;AAGlD,MAAM,mBAAmB;IAAC,MAAM;QAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ;QAAE,uJAAA,CAAA,QAAK,CAAC,UAAU;KAAC;AAAA;AAGlE,MAAM,UAAU;IAAC,MAAM,EAAE;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/parse.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Create} Create\n * @typedef {import('micromark-util-types').FullNormalizedExtension} FullNormalizedExtension\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').ParseContext} ParseContext\n * @typedef {import('micromark-util-types').ParseOptions} ParseOptions\n */\n\nimport {combineExtensions} from 'micromark-util-combine-extensions'\nimport {content} from './initialize/content.js'\nimport {document} from './initialize/document.js'\nimport {flow} from './initialize/flow.js'\nimport {text, string} from './initialize/text.js'\nimport {createTokenizer} from './create-tokenizer.js'\nimport * as defaultConstructs from './constructs.js'\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n * @returns {ParseContext}\n */\nexport function parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    combineExtensions([defaultConstructs, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    defined: [],\n    lazy: {},\n    constructs,\n    content: create(content),\n    document: create(document),\n    flow: create(flow),\n    string: create(string),\n    text: create(text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from)\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMO,SAAS,MAAM,OAAO;IAC3B,MAAM,WAAW,WAAW,CAAC;IAC7B,MAAM,aACJ,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE;QAAC;WAAuB,SAAS,UAAU,IAAI,EAAE;KAAE;IAGvE,yBAAyB,GACzB,MAAM,SAAS;QACb,SAAS,EAAE;QACX,MAAM,CAAC;QACP;QACA,SAAS,OAAO,mKAAA,CAAA,UAAO;QACvB,UAAU,OAAO,oKAAA,CAAA,WAAQ;QACzB,MAAM,OAAO,gKAAA,CAAA,OAAI;QACjB,QAAQ,OAAO,gKAAA,CAAA,SAAM;QACrB,MAAM,OAAO,gKAAA,CAAA,OAAI;IACnB;IAEA,OAAO;;IAEP;;GAEC,GACD,SAAS,OAAO,OAAO;QACrB,OAAO;;QACP,mBAAmB,GACnB,SAAS,QAAQ,IAAI;YACnB,OAAO,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,SAAS;QAC1C;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/preprocess.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Encoding} Encoding\n * @typedef {import('micromark-util-types').Value} Value\n */\n\n/**\n * @callback Preprocessor\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {boolean | null | undefined} [end=false]\n * @returns {Array<Chunk>}\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n */\nexport function preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    // @ts-expect-error `Buffer` does allow an encoding.\n    value = buffer + value.toString(encoding)\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case codes.nul: {\n            chunks.push(codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case codes.ht: {\n            next = Math.ceil(column / constants.tabSize) * constants.tabSize\n            chunks.push(codes.horizontalTab)\n            while (column++ < next) chunks.push(codes.virtualSpace)\n\n            break\n          }\n\n          case codes.lf: {\n            chunks.push(codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(codes.eof)\n    }\n\n    return chunks\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;;;;CAMC;;;AAED;AACA;;;AAEA,MAAM,SAAS;AAKR,SAAS;IACd,IAAI,SAAS;IACb,IAAI,SAAS;IACb,gCAAgC,GAChC,IAAI,QAAQ;IACZ,gCAAgC,GAChC,IAAI;IAEJ,OAAO;;IAEP,yBAAyB,GACzB,SAAS,aAAa,KAAK,EAAE,QAAQ,EAAE,GAAG;QACxC,yBAAyB,GACzB,MAAM,SAAS,EAAE;QACjB,oCAAoC,GACpC,IAAI;QACJ,mBAAmB,GACnB,IAAI;QACJ,mBAAmB,GACnB,IAAI;QACJ,mBAAmB,GACnB,IAAI;QACJ,iBAAiB,GACjB,IAAI;QAEJ,oDAAoD;QACpD,QAAQ,SAAS,MAAM,QAAQ,CAAC;QAChC,gBAAgB;QAChB,SAAS;QAET,IAAI,OAAO;YACT,+DAA+D;YAC/D,IAAI,MAAM,UAAU,CAAC,OAAO,uJAAA,CAAA,QAAK,CAAC,eAAe,EAAE;gBACjD;YACF;YAEA,QAAQ;QACV;QAEA,MAAO,gBAAgB,MAAM,MAAM,CAAE;YACnC,OAAO,SAAS,GAAG;YACnB,QAAQ,OAAO,IAAI,CAAC;YACpB,cACE,SAAS,MAAM,KAAK,KAAK,YAAY,MAAM,KAAK,GAAG,MAAM,MAAM;YACjE,OAAO,MAAM,UAAU,CAAC;YAExB,IAAI,CAAC,OAAO;gBACV,SAAS,MAAM,KAAK,CAAC;gBACrB;YACF;YAEA,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,EAAE,IACjB,kBAAkB,eAClB,kBACA;gBACA,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,sBAAsB;gBACxC,mBAAmB;YACrB,OAAO;gBACL,IAAI,kBAAkB;oBACpB,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,cAAc;oBAChC,mBAAmB;gBACrB;gBAEA,IAAI,gBAAgB,aAAa;oBAC/B,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,eAAe;oBACvC,UAAU,cAAc;gBAC1B;gBAEA,OAAQ;oBACN,KAAK,uJAAA,CAAA,QAAK,CAAC,GAAG;wBAAE;4BACd,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,oBAAoB;4BACtC;4BAEA;wBACF;oBAEA,KAAK,uJAAA,CAAA,QAAK,CAAC,EAAE;wBAAE;4BACb,OAAO,KAAK,IAAI,CAAC,SAAS,2JAAA,CAAA,YAAS,CAAC,OAAO,IAAI,2JAAA,CAAA,YAAS,CAAC,OAAO;4BAChE,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,aAAa;4BAC/B,MAAO,WAAW,KAAM,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,YAAY;4BAEtD;wBACF;oBAEA,KAAK,uJAAA,CAAA,QAAK,CAAC,EAAE;wBAAE;4BACb,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,QAAQ;4BAC1B,SAAS;4BAET;wBACF;oBAEA;wBAAS;4BACP,mBAAmB;4BACnB,SAAS;wBACX;gBACF;YACF;YAEA,gBAAgB,cAAc;QAChC;QAEA,IAAI,KAAK;YACP,IAAI,kBAAkB,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,cAAc;YACtD,IAAI,QAAQ,OAAO,IAAI,CAAC;YACxB,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,GAAG;QACvB;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark/dev/lib/postprocess.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Event} Event\n */\n\nimport {subtokenize} from 'micromark-util-subtokenize'\n\n/**\n * @param {Array<Event>} events\n * @returns {Array<Event>}\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n\n  return events\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AAMO,SAAS,YAAY,MAAM;IAChC,MAAO,CAAC,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE,QAAS;IAC3B,QAAQ;IACV;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-subtokenize/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Token} Token\n */\n\nimport {splice} from 'micromark-util-chunked'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} events\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\nexport function subtokenize(events) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events[index]\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === types.chunkFlow &&\n      events[index - 1][1].type === types.listItemPrefix\n    ) {\n      assert(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events[otherIndex]\n\n        if (\n          otherEvent[1].type === types.lineEnding ||\n          otherEvent[1].type === types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events[lineIndex][1].type = types.lineEndingBlank\n            }\n\n            otherEvent[1].type = types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = Object.assign({}, events[lineIndex][1].start)\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        splice(events, lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {Array<Event>} events\n * @param {number} eventIndex\n * @returns {Record<string, number>}\n */\nfunction subcontent(events, eventIndex) {\n  const token = events[eventIndex][1]\n  const context = events[eventIndex][2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  assert(token.contentType, 'expected `contentType` on subtokens')\n  const tokenizer =\n    token._tokenizer || context.parser[token.contentType](token.start)\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events[++startPosition][1] !== current) {\n      // Empty.\n    }\n\n    assert(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    assert(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      assert(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    assert(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    assert(start !== undefined, 'expected a start position when splicing')\n    jumps.unshift([start, start + slice.length - 1])\n    splice(events, start, 2, slice)\n  }\n\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AACA;AACA;AACA;;;;;AAUO,SAAS,YAAY,MAAM;IAChC,mCAAmC,GACnC,MAAM,QAAQ,CAAC;IACf,IAAI,QAAQ,CAAC;IACb,kBAAkB,GAClB,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IACJ,mBAAmB,GACnB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IACJ,yBAAyB,GACzB,IAAI;IACJ,yBAAyB,GACzB,IAAI;IACJ,gCAAgC,GAChC,IAAI;IAEJ,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAO,SAAS,MAAO;YACrB,QAAQ,KAAK,CAAC,MAAM;QACtB;QAEA,QAAQ,MAAM,CAAC,MAAM;QAErB,yEAAyE;QACzE,0CAA0C;QAC1C,IACE,SACA,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,SAAS,IACjC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,cAAc,EAClD;YACA,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE;YAC5B,YAAY,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM;YACtC,aAAa;YAEb,IACE,aAAa,UAAU,MAAM,IAC7B,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,eAAe,EACvD;gBACA,cAAc;YAChB;YAEA,IACE,aAAa,UAAU,MAAM,IAC7B,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,OAAO,EAC/C;gBACA,MAAO,EAAE,aAAa,UAAU,MAAM,CAAE;oBACtC,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,OAAO,EAAE;wBACnD;oBACF;oBAEA,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,SAAS,EAAE;wBACrD,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,2BAA2B,GAAG;wBACvD;oBACF;gBACF;YACF;QACF;QAEA,SAAS;QACT,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS;YACxB,IAAI,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE;gBACxB,OAAO,MAAM,CAAC,OAAO,WAAW,QAAQ;gBACxC,QAAQ,KAAK,CAAC,MAAM;gBACpB,OAAO;YACT;QACF,OAEK,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE;YAC5B,aAAa;YACb,YAAY;YAEZ,MAAO,aAAc;gBACnB,aAAa,MAAM,CAAC,WAAW;gBAE/B,IACE,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,UAAU,IACvC,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,eAAe,EAC5C;oBACA,IAAI,UAAU,CAAC,EAAE,KAAK,SAAS;wBAC7B,IAAI,WAAW;4BACb,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG,uJAAA,CAAA,QAAK,CAAC,eAAe;wBACnD;wBAEA,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG,uJAAA,CAAA,QAAK,CAAC,UAAU;wBACrC,YAAY;oBACd;gBACF,OAAO;oBACL;gBACF;YACF;YAEA,IAAI,WAAW;gBACb,gBAAgB;gBAChB,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK;gBAE3D,yCAAyC;gBACzC,aAAa,OAAO,KAAK,CAAC,WAAW;gBACrC,WAAW,OAAO,CAAC;gBACnB,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,WAAW,QAAQ,YAAY,GAAG;YACnD;QACF;IACF;IAEA,OAAO,CAAC;AACV;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,MAAM,EAAE,UAAU;IACpC,MAAM,QAAQ,MAAM,CAAC,WAAW,CAAC,EAAE;IACnC,MAAM,UAAU,MAAM,CAAC,WAAW,CAAC,EAAE;IACrC,IAAI,gBAAgB,aAAa;IACjC,0BAA0B,GAC1B,MAAM,iBAAiB,EAAE;IACzB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM,WAAW,EAAE;IAC1B,MAAM,YACJ,MAAM,UAAU,IAAI,QAAQ,MAAM,CAAC,MAAM,WAAW,CAAC,CAAC,MAAM,KAAK;IACnE,MAAM,cAAc,UAAU,MAAM;IACpC,oCAAoC,GACpC,MAAM,QAAQ,EAAE;IAChB,mCAAmC,GACnC,MAAM,OAAO,CAAC;IACd,yBAAyB,GACzB,IAAI;IACJ,8BAA8B,GAC9B,IAAI;IACJ,IAAI,QAAQ,CAAC;IACb,8BAA8B,GAC9B,IAAI,UAAU;IACd,IAAI,SAAS;IACb,IAAI,QAAQ;IACZ,MAAM,SAAS;QAAC;KAAM;IAEtB,sEAAsE;IACtE,gBAAgB;IAChB,MAAO,QAAS;QACd,iDAAiD;QACjD,MAAO,MAAM,CAAC,EAAE,cAAc,CAAC,EAAE,KAAK,QAAS;QAC7C,SAAS;QACX;QAEA,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,CAAC,YAAY,QAAQ,QAAQ,KAAK,UAClC;QAEF,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,CAAC,YAAY,SAAS,IAAI,KAAK,SAAS;QAE/C,eAAe,IAAI,CAAC;QAEpB,IAAI,CAAC,QAAQ,UAAU,EAAE;YACvB,SAAS,QAAQ,WAAW,CAAC;YAE7B,IAAI,CAAC,QAAQ,IAAI,EAAE;gBACjB,OAAO,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,GAAG;YACvB;YAEA,IAAI,UAAU;gBACZ,UAAU,UAAU,CAAC,QAAQ,KAAK;YACpC;YAEA,IAAI,QAAQ,2BAA2B,EAAE;gBACvC,UAAU,kCAAkC,GAAG;YACjD;YAEA,UAAU,KAAK,CAAC;YAEhB,IAAI,QAAQ,2BAA2B,EAAE;gBACvC,UAAU,kCAAkC,GAAG;YACjD;QACF;QAEA,0BAA0B;QAC1B,WAAW;QACX,UAAU,QAAQ,IAAI;IACxB;IAEA,6EAA6E;IAC7E,sBAAsB;IACtB,UAAU;IAEV,MAAO,EAAE,QAAQ,YAAY,MAAM,CAAE;QACnC,IACE,2CAA2C;QAC3C,WAAW,CAAC,MAAM,CAAC,EAAE,KAAK,UAC1B,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,WAC9B,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,IAC7D,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EACnE;YACA,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS;YAChB,QAAQ,QAAQ;YAChB,OAAO,IAAI,CAAC;YACZ,WAAW;YACX,QAAQ,UAAU,GAAG;YACrB,QAAQ,QAAQ,GAAG;YACnB,UAAU,QAAQ,IAAI;QACxB;IACF;IAEA,WAAW;IACX,UAAU,MAAM,GAAG,EAAE;IAErB,yEAAyE;IACzE,2DAA2D;IAC3D,sEAAsE;IACtE,IAAI,SAAS;QACX,WAAW;QACX,QAAQ,UAAU,GAAG;QACrB,QAAQ,QAAQ,GAAG;QACnB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,CAAC,QAAQ,IAAI,EAAE;IACxB,OAAO;QACL,OAAO,GAAG;IACZ;IAEA,uEAAuE;IACvE,+DAA+D;IAC/D,QAAQ,OAAO,MAAM;IAErB,MAAO,QAAS;QACd,MAAM,QAAQ,YAAY,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE;QAChE,MAAM,QAAQ,eAAe,GAAG;QAChC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,UAAU,WAAW;QAC5B,MAAM,OAAO,CAAC;YAAC;YAAO,QAAQ,MAAM,MAAM,GAAG;SAAE;QAC/C,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,OAAO,GAAG;IAC3B;IAEA,QAAQ,CAAC;IAET,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,IAAI,CAAC,SAAS,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,EAAE;QACzD,UAAU,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG;IAChD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/ms/index.js"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,IAAI,IAAI;AACR,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AACZ,IAAI,IAAI,IAAI;AAEZ;;;;;;;;;;;;CAYC,GAED,OAAO,OAAO,GAAG,SAAU,GAAG,EAAE,OAAO;IACrC,UAAU,WAAW,CAAC;IACtB,IAAI,OAAO,OAAO;IAClB,IAAI,SAAS,YAAY,IAAI,MAAM,GAAG,GAAG;QACvC,OAAO,MAAM;IACf,OAAO,IAAI,SAAS,YAAY,SAAS,MAAM;QAC7C,OAAO,QAAQ,IAAI,GAAG,QAAQ,OAAO,SAAS;IAChD;IACA,MAAM,IAAI,MACR,0DACE,KAAK,SAAS,CAAC;AAErB;AAEA;;;;;;CAMC,GAED,SAAS,MAAM,GAAG;IAChB,MAAM,OAAO;IACb,IAAI,IAAI,MAAM,GAAG,KAAK;QACpB;IACF;IACA,IAAI,QAAQ,mIAAmI,IAAI,CACjJ;IAEF,IAAI,CAAC,OAAO;QACV;IACF;IACA,IAAI,IAAI,WAAW,KAAK,CAAC,EAAE;IAC3B,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE,WAAW;IACzC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,IAAI;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA;;;;;;CAMC,GAED,SAAS,SAAS,EAAE;IAClB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;;;;;CAMC,GAED,SAAS,QAAQ,EAAE;IACjB,IAAI,QAAQ,KAAK,GAAG,CAAC;IACrB,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,IAAI,SAAS,GAAG;QACd,OAAO,OAAO,IAAI,OAAO,GAAG;IAC9B;IACA,OAAO,KAAK;AACd;AAEA;;CAEC,GAED,SAAS,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI;IAChC,IAAI,WAAW,SAAS,IAAI;IAC5B,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,MAAM,OAAO,CAAC,WAAW,MAAM,EAAE;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/debug/src/common.js"], "sourcesContent": ["\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(/\\s+/g, ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n"], "names": [], "mappings": "AACA;;;CAGC,GAED,SAAS,MAAM,GAAG;IACjB,YAAY,KAAK,GAAG;IACpB,YAAY,OAAO,GAAG;IACtB,YAAY,MAAM,GAAG;IACrB,YAAY,OAAO,GAAG;IACtB,YAAY,MAAM,GAAG;IACrB,YAAY,OAAO,GAAG;IACtB,YAAY,QAAQ;IACpB,YAAY,OAAO,GAAG;IAEtB,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,CAAA;QACxB,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;IAC5B;IAEA;;CAEA,GAEA,YAAY,KAAK,GAAG,EAAE;IACtB,YAAY,KAAK,GAAG,EAAE;IAEtB;;;;CAIA,GACA,YAAY,UAAU,GAAG,CAAC;IAE1B;;;;;CAKA,GACA,SAAS,YAAY,SAAS;QAC7B,IAAI,OAAO;QAEX,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YAC1C,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ,UAAU,UAAU,CAAC;YACnD,QAAQ,GAAG,2BAA2B;QACvC;QAEA,OAAO,YAAY,MAAM,CAAC,KAAK,GAAG,CAAC,QAAQ,YAAY,MAAM,CAAC,MAAM,CAAC;IACtE;IACA,YAAY,WAAW,GAAG;IAE1B;;;;;;CAMA,GACA,SAAS,YAAY,SAAS;QAC7B,IAAI;QACJ,IAAI,iBAAiB;QACrB,IAAI;QACJ,IAAI;QAEJ,SAAS,MAAM,GAAG,IAAI;YACrB,YAAY;YACZ,IAAI,CAAC,MAAM,OAAO,EAAE;gBACnB;YACD;YAEA,MAAM,OAAO;YAEb,uBAAuB;YACvB,MAAM,OAAO,OAAO,IAAI;YACxB,MAAM,KAAK,OAAO,CAAC,YAAY,IAAI;YACnC,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,WAAW;YAEX,IAAI,CAAC,EAAE,GAAG,YAAY,MAAM,CAAC,IAAI,CAAC,EAAE;YAEpC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;gBAChC,sCAAsC;gBACtC,KAAK,OAAO,CAAC;YACd;YAEA,yCAAyC;YACzC,IAAI,QAAQ;YACZ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO;gBAClD,mEAAmE;gBACnE,IAAI,UAAU,MAAM;oBACnB,OAAO;gBACR;gBACA;gBACA,MAAM,YAAY,YAAY,UAAU,CAAC,OAAO;gBAChD,IAAI,OAAO,cAAc,YAAY;oBACpC,MAAM,MAAM,IAAI,CAAC,MAAM;oBACvB,QAAQ,UAAU,IAAI,CAAC,MAAM;oBAE7B,yEAAyE;oBACzE,KAAK,MAAM,CAAC,OAAO;oBACnB;gBACD;gBACA,OAAO;YACR;YAEA,+CAA+C;YAC/C,YAAY,UAAU,CAAC,IAAI,CAAC,MAAM;YAElC,MAAM,QAAQ,KAAK,GAAG,IAAI,YAAY,GAAG;YACzC,MAAM,KAAK,CAAC,MAAM;QACnB;QAEA,MAAM,SAAS,GAAG;QAClB,MAAM,SAAS,GAAG,YAAY,SAAS;QACvC,MAAM,KAAK,GAAG,YAAY,WAAW,CAAC;QACtC,MAAM,MAAM,GAAG;QACf,MAAM,OAAO,GAAG,YAAY,OAAO,EAAE,4DAA4D;QAEjG,OAAO,cAAc,CAAC,OAAO,WAAW;YACvC,YAAY;YACZ,cAAc;YACd,KAAK;gBACJ,IAAI,mBAAmB,MAAM;oBAC5B,OAAO;gBACR;gBACA,IAAI,oBAAoB,YAAY,UAAU,EAAE;oBAC/C,kBAAkB,YAAY,UAAU;oBACxC,eAAe,YAAY,OAAO,CAAC;gBACpC;gBAEA,OAAO;YACR;YACA,KAAK,CAAA;gBACJ,iBAAiB;YAClB;QACD;QAEA,wDAAwD;QACxD,IAAI,OAAO,YAAY,IAAI,KAAK,YAAY;YAC3C,YAAY,IAAI,CAAC;QAClB;QAEA,OAAO;IACR;IAEA,SAAS,OAAO,SAAS,EAAE,SAAS;QACnC,MAAM,WAAW,YAAY,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,cAAc,cAAc,MAAM,SAAS,IAAI;QACrG,SAAS,GAAG,GAAG,IAAI,CAAC,GAAG;QACvB,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,OAAO,UAAU;QACzB,YAAY,IAAI,CAAC;QACjB,YAAY,UAAU,GAAG;QAEzB,YAAY,KAAK,GAAG,EAAE;QACtB,YAAY,KAAK,GAAG,EAAE;QAEtB,MAAM,QAAQ,CAAC,OAAO,eAAe,WAAW,aAAa,EAAE,EAC7D,IAAI,GACJ,OAAO,CAAC,QAAQ,KAChB,KAAK,CAAC,KACN,MAAM,CAAC;QAET,KAAK,MAAM,MAAM,MAAO;YACvB,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK;gBAClB,YAAY,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACjC,OAAO;gBACN,YAAY,KAAK,CAAC,IAAI,CAAC;YACxB;QACD;IACD;IAEA;;;;;;;EAOC,GACD,SAAS,gBAAgB,MAAM,EAAE,QAAQ;QACxC,IAAI,cAAc;QAClB,IAAI,gBAAgB;QACpB,IAAI,YAAY,CAAC;QACjB,IAAI,aAAa;QAEjB,MAAO,cAAc,OAAO,MAAM,CAAE;YACnC,IAAI,gBAAgB,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,KAAK,MAAM,CAAC,YAAY,IAAI,QAAQ,CAAC,cAAc,KAAK,GAAG,GAAG;gBAC5H,2CAA2C;gBAC3C,IAAI,QAAQ,CAAC,cAAc,KAAK,KAAK;oBACpC,YAAY;oBACZ,aAAa;oBACb,iBAAiB,eAAe;gBACjC,OAAO;oBACN;oBACA;gBACD;YACD,OAAO,IAAI,cAAc,CAAC,GAAG;gBAC5B,6DAA6D;gBAC7D,gBAAgB,YAAY;gBAC5B;gBACA,cAAc;YACf,OAAO;gBACN,OAAO,OAAO,WAAW;YAC1B;QACD;QAEA,kCAAkC;QAClC,MAAO,gBAAgB,SAAS,MAAM,IAAI,QAAQ,CAAC,cAAc,KAAK,IAAK;YAC1E;QACD;QAEA,OAAO,kBAAkB,SAAS,MAAM;IACzC;IAEA;;;;;CAKA,GACA,SAAS;QACR,MAAM,aAAa;eACf,YAAY,KAAK;eACjB,YAAY,KAAK,CAAC,GAAG,CAAC,CAAA,YAAa,MAAM;SAC5C,CAAC,IAAI,CAAC;QACP,YAAY,MAAM,CAAC;QACnB,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,QAAQ,IAAI;QACpB,KAAK,MAAM,QAAQ,YAAY,KAAK,CAAE;YACrC,IAAI,gBAAgB,MAAM,OAAO;gBAChC,OAAO;YACR;QACD;QAEA,KAAK,MAAM,MAAM,YAAY,KAAK,CAAE;YACnC,IAAI,gBAAgB,MAAM,KAAK;gBAC9B,OAAO;YACR;QACD;QAEA,OAAO;IACR;IAEA;;;;;;CAMA,GACA,SAAS,OAAO,GAAG;QAClB,IAAI,eAAe,OAAO;YACzB,OAAO,IAAI,KAAK,IAAI,IAAI,OAAO;QAChC;QACA,OAAO;IACR;IAEA;;;CAGA,GACA,SAAS;QACR,QAAQ,IAAI,CAAC;IACd;IAEA,YAAY,MAAM,CAAC,YAAY,IAAI;IAEnC,OAAO;AACR;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/debug/src/browser.js"], "sourcesContent": ["/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug') || exports.storage.getItem('DEBUG') ;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n"], "names": [], "mappings": "AAAA,sBAAsB,GAEtB;;CAEC,GAgOiB;AA9NlB,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG;AACf,QAAQ,IAAI,GAAG;AACf,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG,CAAC;IAClB,IAAI,SAAS;IAEb,OAAO;QACN,IAAI,CAAC,QAAQ;YACZ,SAAS;YACT,QAAQ,IAAI,CAAC;QACd;IACD;AACD,CAAC;AAED;;CAEC,GAED,QAAQ,MAAM,GAAG;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACA;AAED;;;;;;CAMC,GAED,sCAAsC;AACtC,SAAS;IACR,4EAA4E;IAC5E,0EAA0E;IAC1E,aAAa;IACb,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,IAAI,KAAK,cAAc,OAAO,OAAO,CAAC,MAAM,GAAG;QACrH,OAAO;IACR;IAEA,oDAAoD;IACpD,IAAI,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,0BAA0B;QAChI,OAAO;IACR;IAEA,IAAI;IAEJ,wDAAwD;IACxD,4FAA4F;IAC5F,4CAA4C;IAC5C,OAAO,AAAC,OAAO,aAAa,eAAe,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,KAAK,IAAI,SAAS,eAAe,CAAC,KAAK,CAAC,gBAAgB,IAEtJ,OAAO,WAAW,eAAe,OAAO,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO,IAAK,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,KAAK,AAAC,KAGhI,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,CAAC,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,EAAE,EAAE,OAAO,MAEpJ,OAAO,cAAc,eAAe,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;AACtG;AAEA;;;;CAIC,GAED,SAAS,WAAW,IAAI;IACvB,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,IACpC,IAAI,CAAC,SAAS,GACd,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAC7B,IAAI,CAAC,EAAE,GACP,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAC7B,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;IAExC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACpB;IACD;IAEA,MAAM,IAAI,YAAY,IAAI,CAAC,KAAK;IAChC,KAAK,MAAM,CAAC,GAAG,GAAG,GAAG;IAErB,kEAAkE;IAClE,gEAAgE;IAChE,sDAAsD;IACtD,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,eAAe,CAAA;QAC9B,IAAI,UAAU,MAAM;YACnB;QACD;QACA;QACA,IAAI,UAAU,MAAM;YACnB,0CAA0C;YAC1C,yCAAyC;YACzC,QAAQ;QACT;IACD;IAEA,KAAK,MAAM,CAAC,OAAO,GAAG;AACvB;AAEA;;;;;;;CAOC,GACD,QAAQ,GAAG,GAAG,QAAQ,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAO,CAAC;AAEvD;;;;;CAKC,GACD,SAAS,KAAK,UAAU;IACvB,IAAI;QACH,IAAI,YAAY;YACf,QAAQ,OAAO,CAAC,OAAO,CAAC,SAAS;QAClC,OAAO;YACN,QAAQ,OAAO,CAAC,UAAU,CAAC;QAC5B;IACD,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;AACD;AAEA;;;;;CAKC,GACD,SAAS;IACR,IAAI;IACJ,IAAI;QACH,IAAI,QAAQ,OAAO,CAAC,OAAO,CAAC,YAAY,QAAQ,OAAO,CAAC,OAAO,CAAC;IACjE,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;IAEA,sEAAsE;IACtE,IAAI,CAAC,KAAK,OAAO,gKAAA,CAAA,UAAO,KAAK,eAAe,SAAS,gKAAA,CAAA,UAAO,EAAE;QAC7D,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,KAAK;IACtB;IAEA,OAAO;AACR;AAEA;;;;;;;;;CASC,GAED,SAAS;IACR,IAAI;QACH,uGAAuG;QACvG,2DAA2D;QAC3D,OAAO;IACR,EAAE,OAAO,OAAO;IACf,UAAU;IACV,0CAA0C;IAC3C;AACD;AAEA,OAAO,OAAO,GAAG,gGAAoB;AAErC,MAAM,EAAC,UAAU,EAAC,GAAG,OAAO,OAAO;AAEnC;;CAEC,GAED,WAAW,CAAC,GAAG,SAAU,CAAC;IACzB,IAAI;QACH,OAAO,KAAK,SAAS,CAAC;IACvB,EAAE,OAAO,OAAO;QACf,OAAO,iCAAiC,MAAM,OAAO;IACtD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-resolve-all/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {Array<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nexport function resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = []\n  let index = -1\n\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll\n\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context)\n      called.push(resolve)\n    }\n  }\n\n  return events\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;CAWC;;;AACM,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,OAAO;IACpD,4BAA4B,GAC5B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,WAAW,MAAM,CAAE;QAClC,MAAM,UAAU,UAAU,CAAC,MAAM,CAAC,UAAU;QAE5C,IAAI,WAAW,CAAC,OAAO,QAAQ,CAAC,UAAU;YACxC,SAAS,QAAQ,QAAQ;YACzB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5619, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-classify-character/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\nimport {\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (\n    code === codes.eof ||\n    markdownLineEndingOrSpace(code) ||\n    unicodeWhitespace(code)\n  ) {\n    return constants.characterGroupWhitespace\n  }\n\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AAKA;AACA;;;;AAgBO,SAAS,kBAAkB,IAAI;IACpC,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAClB,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,SAC1B,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;QACA,OAAO,2JAAA,CAAA,YAAS,CAAC,wBAAwB;IAC3C;IAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QAC5B,OAAO,2JAAA,CAAA,YAAS,CAAC,yBAAyB;IAC5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/decode-named-character-reference/index.dom.js"], "sourcesContent": ["/// <reference lib=\"dom\" />\n\n/* global document */\n\nconst element = document.createElement('i')\n\n/**\n * @param {string} value\n * @returns {string | false}\n */\nexport function decodeNamedCharacterReference(value) {\n  const characterReference = '&' + value + ';'\n  element.innerHTML = characterReference\n  const character = element.textContent\n\n  // Some named character references do not require the closing semicolon\n  // (`&not`, for instance), which leads to situations where parsing the assumed\n  // named reference of `&notit;` will result in the string `¬it;`.\n  // When we encounter a trailing semicolon after parsing, and the character\n  // reference to decode was not a semicolon (`&semi;`), we can assume that the\n  // matching was not complete.\n  if (\n    // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n    // yield `null`.\n    character.charCodeAt(character.length - 1) === 59 /* `;` */ &&\n    value !== 'semi'\n  ) {\n    return false\n  }\n\n  // If the decoded string is equal to the input, the character reference was\n  // not valid.\n  // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n  // yield `null`.\n  return character === characterReference ? false : character\n}\n"], "names": [], "mappings": "AAAA,2BAA2B;AAE3B,mBAAmB;;;AAEnB,MAAM,UAAU,SAAS,aAAa,CAAC;AAMhC,SAAS,8BAA8B,KAAK;IACjD,MAAM,qBAAqB,MAAM,QAAQ;IACzC,QAAQ,SAAS,GAAG;IACpB,MAAM,YAAY,QAAQ,WAAW;IAErC,uEAAuE;IACvE,8EAA8E;IAC9E,iEAAiE;IACjE,0EAA0E;IAC1E,6EAA6E;IAC7E,6BAA6B;IAC7B,IACE,2EAA2E;IAC3E,gBAAgB;IAChB,UAAU,UAAU,CAAC,UAAU,MAAM,GAAG,OAAO,GAAG,OAAO,OACzD,UAAU,QACV;QACA,OAAO;IACT;IAEA,2EAA2E;IAC3E,aAAa;IACb,2EAA2E;IAC3E,gBAAgB;IAChB,OAAO,cAAc,qBAAqB,QAAQ;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-factory-destination/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport {\n  asciiControl,\n  markdownLineEndingOrSpace,\n  markdownLineEnding\n} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\n\n/**\n * Parse destinations.\n *\n * ###### Examples\n *\n * ```markdown\n * <a>\n * <a\\>b>\n * <a b>\n * <a)>\n * a\n * a\\)b\n * a(b)c\n * a(b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type for whole (`<a>` or `b`).\n * @param {TokenType} literalType\n *   Type when enclosed (`<a>`).\n * @param {TokenType} literalMarkerType\n *   Type for enclosing (`<` and `>`).\n * @param {TokenType} rawType\n *   Type when not enclosed (`b`).\n * @param {TokenType} stringType\n *   Type for the value (`a` or `b`).\n * @param {number | undefined} [max=Infinity]\n *   Depth of nested parens (inclusive).\n * @returns {State}\n *   Start state.\n */\n// eslint-disable-next-line max-params\nexport function factoryDestination(\n  effects,\n  ok,\n  nok,\n  type,\n  literalType,\n  literalMarkerType,\n  rawType,\n  stringType,\n  max\n) {\n  const limit = max || Number.POSITIVE_INFINITY\n  let balance = 0\n\n  return start\n\n  /**\n   * Start of destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *     ^\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.lessThan) {\n      effects.enter(type)\n      effects.enter(literalType)\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      return enclosedBefore\n    }\n\n    // ASCII control, space, closing paren.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.rightParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter(type)\n    effects.enter(rawType)\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return raw(code)\n  }\n\n  /**\n   * After `<`, at an enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedBefore(code) {\n    if (code === codes.greaterThan) {\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      effects.exit(literalType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return enclosed(code)\n  }\n\n  /**\n   * In enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosed(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      return enclosedBefore(code)\n    }\n\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      markdownLineEnding(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? enclosedEscape : enclosed\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | <a\\*a>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedEscape(code) {\n    if (\n      code === codes.lessThan ||\n      code === codes.greaterThan ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return enclosed\n    }\n\n    return enclosed(code)\n  }\n\n  /**\n   * In raw destination.\n   *\n   * ```markdown\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function raw(code) {\n    if (\n      !balance &&\n      (code === codes.eof ||\n        code === codes.rightParenthesis ||\n        markdownLineEndingOrSpace(code))\n    ) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      effects.exit(rawType)\n      effects.exit(type)\n      return ok(code)\n    }\n\n    if (balance < limit && code === codes.leftParenthesis) {\n      effects.consume(code)\n      balance++\n      return raw\n    }\n\n    if (code === codes.rightParenthesis) {\n      effects.consume(code)\n      balance--\n      return raw\n    }\n\n    // ASCII control (but *not* `\\0`) and space and `(`.\n    // Note: in `markdown-rs`, `\\0` exists in codes, in `micromark-js` it\n    // doesn’t.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.leftParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? rawEscape : raw\n  }\n\n  /**\n   * After `\\`, at special character.\n   *\n   * ```markdown\n   * > | a\\*a\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function rawEscape(code) {\n    if (\n      code === codes.leftParenthesis ||\n      code === codes.rightParenthesis ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return raw\n    }\n\n    return raw(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;AAKA;AACA;AACA;;;;;AAwCO,SAAS,mBACd,OAAO,EACP,EAAE,EACF,GAAG,EACH,IAAI,EACJ,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,UAAU,EACV,GAAG;IAEH,MAAM,QAAQ,OAAO,OAAO,iBAAiB;IAC7C,IAAI,UAAU;IAEd,OAAO;;IAEP;;;;;;;;;;;GAWC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;YAC3B,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,uCAAuC;QACvC,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,uJAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,uJAAA,CAAA,QAAK,CAAC,gBAAgB,IAC/B,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,OACb;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAAC,aAAa,2JAAA,CAAA,YAAS,CAAC,iBAAiB;QAAA;QAC1E,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,eAAe,IAAI;QAC1B,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAAC,aAAa,2JAAA,CAAA,YAAS,CAAC,iBAAiB;QAAA;QAC1E,OAAO,SAAS;IAClB;IAEA;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI;QACpB,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,QAAQ,IAAI,CAAC;YACb,OAAO,eAAe;QACxB;QAEA,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,uJAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OACnB;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,GAAG,iBAAiB;IACrD;IAEA;;;;;;;;;GASC,GACD,SAAS,eAAe,IAAI;QAC1B,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,uJAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,EACxB;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,SAAS;IAClB;IAEA;;;;;;;;;GASC,GACD,SAAS,IAAI,IAAI;QACf,IACE,CAAC,WACD,CAAC,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IACjB,SAAS,uJAAA,CAAA,QAAK,CAAC,gBAAgB,IAC/B,CAAA,GAAA,iKAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,GACjC;YACA,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO,GAAG;QACZ;QAEA,IAAI,UAAU,SAAS,SAAS,uJAAA,CAAA,QAAK,CAAC,eAAe,EAAE;YACrD,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,gBAAgB,EAAE;YACnC,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,oDAAoD;QACpD,qEAAqE;QACrE,WAAW;QACX,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,uJAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,uJAAA,CAAA,QAAK,CAAC,eAAe,IAC9B,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,OACb;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,GAAG,YAAY;IAChD;IAEA;;;;;;;;;GASC,GACD,SAAS,UAAU,IAAI;QACrB,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,eAAe,IAC9B,SAAS,uJAAA,CAAA,QAAK,CAAC,gBAAgB,IAC/B,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,EACxB;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-factory-label/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/**\n * Parse labels.\n *\n * > 👉 **Note**: labels in markdown are capped at 999 characters in the string.\n *\n * ###### Examples\n *\n * ```markdown\n * [a]\n * [a\n * b]\n * [a\\]b]\n * ```\n *\n * @this {TokenizeContext}\n *   Tokenize context.\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole label (`[a]`).\n * @param {TokenType} markerType\n *   Type for the markers (`[` and `]`).\n * @param {TokenType} stringType\n *   Type for the identifier (`a`).\n * @returns {State}\n *   Start state.\n */\n// eslint-disable-next-line max-params\nexport function factoryLabel(effects, ok, nok, type, markerType, stringType) {\n  const self = this\n  let size = 0\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /**\n   * Start of label.\n   *\n   * ```markdown\n   * > | [a]\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(type)\n    effects.enter(markerType)\n    effects.consume(code)\n    effects.exit(markerType)\n    effects.enter(stringType)\n    return atBreak\n  }\n\n  /**\n   * In label, at something, before something else.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (\n      size > constants.linkReferenceSizeMax ||\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      (code === codes.rightSquareBracket && !seen) ||\n      // To do: remove in the future once we’ve switched from\n      // `micromark-extension-footnote` to `micromark-extension-gfm-footnote`,\n      // which doesn’t need this.\n      // Hidden footnotes hook.\n      /* c8 ignore next 3 */\n      (code === codes.caret &&\n        !size &&\n        '_hiddenFootnoteSupport' in self.parser.constructs)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit(stringType)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    // To do: indent? Link chunks and EOLs together?\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return atBreak\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return labelInside(code)\n  }\n\n  /**\n   * In label, in text.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      code === codes.rightSquareBracket ||\n      markdownLineEnding(code) ||\n      size++ > constants.linkReferenceSizeMax\n    ) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    if (!seen) seen = !markdownSpace(code)\n    return code === codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | [a\\*a]\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAkCO,SAAS,aAAa,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU;IACzE,MAAM,OAAO,IAAI;IACjB,IAAI,OAAO;IACX,oBAAoB,GACpB,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,uJAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QACzC,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC;QACb,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,IACE,OAAO,2JAAA,CAAA,YAAS,CAAC,oBAAoB,IACrC,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,uJAAA,CAAA,QAAK,CAAC,iBAAiB,IAC/B,SAAS,uJAAA,CAAA,QAAK,CAAC,kBAAkB,IAAI,CAAC,QAMtC,SAAS,uJAAA,CAAA,QAAK,CAAC,KAAK,IACnB,CAAC,QACD,4BAA4B,KAAK,MAAM,CAAC,UAAU,EACpD;YACA,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,IAAI,CAAC;YACb,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,gDAAgD;QAChD,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO;QACT;QAEA,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAAC,aAAa,2JAAA,CAAA,YAAS,CAAC,iBAAiB;QAAA;QAC1E,OAAO,YAAY;IACrB;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,uJAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,SAAS,uJAAA,CAAA,QAAK,CAAC,kBAAkB,IACjC,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,SACnB,SAAS,2JAAA,CAAA,YAAS,CAAC,oBAAoB,EACvC;YACA,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,OAAO,QAAQ;QACjB;QAEA,QAAQ,OAAO,CAAC;QAChB,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE;QACjC,OAAO,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,GAAG,cAAc;IAClD;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,iBAAiB,IAChC,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,IACxB,SAAS,uJAAA,CAAA,QAAK,CAAC,kBAAkB,EACjC;YACA,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,OAAO,YAAY;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-factory-title/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\n\n/**\n * Parse titles.\n *\n * ###### Examples\n *\n * ```markdown\n * \"a\"\n * 'b'\n * (c)\n * \"a\n * b\"\n * 'a\n *     b'\n * (a\\)b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole title (`\"a\"`, `'b'`, `(c)`).\n * @param {TokenType} markerType\n *   Type for the markers (`\"`, `'`, `(`, and `)`).\n * @param {TokenType} stringType\n *   Type for the value (`a`).\n * @returns {State}\n *   Start state.\n */\n// eslint-disable-next-line max-params\nexport function factoryTitle(effects, ok, nok, type, markerType, stringType) {\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of title.\n   *\n   * ```markdown\n   * > | \"a\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      effects.enter(type)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      marker = code === codes.leftParenthesis ? codes.rightParenthesis : code\n      return begin\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After opening marker.\n   *\n   * This is also used at the closing marker.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function begin(code) {\n    if (code === marker) {\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    return atBreak(code)\n  }\n\n  /**\n   * At something, before something else.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.exit(stringType)\n      return begin(marker)\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // Note: blank lines can’t exist in content.\n    if (markdownLineEnding(code)) {\n      // To do: use `space_or_tab_eol_with_options`, connect.\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return factorySpace(effects, atBreak, types.linePrefix)\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return inside(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker || code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? escape : inside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | \"a\\*b\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function escape(code) {\n    if (code === marker || code === codes.backslash) {\n      effects.consume(code)\n      return inside\n    }\n\n    return inside(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAkCO,SAAS,aAAa,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU;IACzE,8BAA8B,GAC9B,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,IACE,SAAS,uJAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,uJAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,uJAAA,CAAA,QAAK,CAAC,eAAe,EAC9B;YACA,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,SAAS,SAAS,uJAAA,CAAA,QAAK,CAAC,eAAe,GAAG,uJAAA,CAAA,QAAK,CAAC,gBAAgB,GAAG;YACnE,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,QAAQ;YACnB,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,QAAQ,KAAK,CAAC;QACd,OAAO,QAAQ;IACjB;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,IAAI,SAAS,QAAQ;YACnB,QAAQ,IAAI,CAAC;YACb,OAAO,MAAM;QACf;QAEA,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,4CAA4C;QAC5C,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,uDAAuD;YACvD,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,SAAS,SAAS,uJAAA,CAAA,QAAK,CAAC,UAAU;QACxD;QAEA,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAAC,aAAa,2JAAA,CAAA,YAAS,CAAC,iBAAiB;QAAA;QAC1E,OAAO,OAAO;IAChB;IAEA;;;;GAIC,GACD,SAAS,OAAO,IAAI;QAClB,IAAI,SAAS,UAAU,SAAS,uJAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YACrE,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,OAAO,QAAQ;QACjB;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,GAAG,SAAS;IAC7C;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,IAAI,SAAS,UAAU,SAAS,uJAAA,CAAA,QAAK,CAAC,SAAS,EAAE;YAC/C,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,OAAO;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-factory-whitespace/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol/types.js'\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   line endings or spaces in markdown are often optional, in which case this\n *     factory can be used and `ok` will be switched to whether spaces were found\n *     or not\n * *   one line ending or space can be detected with\n *     `markdownLineEndingOrSpace(code)` right before using `factoryWhitespace`\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @returns\n *   Start state.\n */\nexport function factoryWhitespace(effects, ok) {\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      seen = true\n      return start\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        start,\n        seen ? types.linePrefix : types.lineSuffix\n      )(code)\n    }\n\n    return ok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;;;;AAoBO,SAAS,kBAAkB,OAAO,EAAE,EAAE;IAC3C,oBAAoB,GACpB,IAAI;IAEJ,OAAO;;IAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,IAAI,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,KAAK,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,uJAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO;YACP,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAChB,SACA,OACA,OAAO,uJAAA,CAAA,QAAK,CAAC,UAAU,GAAG,uJAAA,CAAA,QAAK,CAAC,UAAU,EAC1C;QACJ;QAEA,OAAO,GAAG;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-normalize-identifier/dev/index.js"], "sourcesContent": ["import {values} from 'micromark-util-symbol/values.js'\n\n/**\n * Normalize an identifier (as found in references, definitions).\n *\n * Collapses markdown whitespace, trim, and then lower- and uppercase.\n *\n * Some characters are considered “uppercase”, such as U+03F4 (`ϴ`), but if their\n * lowercase counterpart (U+03B8 (`θ`)) is uppercased will result in a different\n * uppercase character (U+0398 (`Θ`)).\n * So, to get a canonical form, we perform both lower- and uppercase.\n *\n * Using uppercase last makes sure keys will never interact with default\n * prototypal values (such as `constructor`): nothing in the prototype of\n * `Object` is uppercase.\n *\n * @param {string} value\n *   Identifier to normalize.\n * @returns {string}\n *   Normalized identifier.\n */\nexport function normalizeIdentifier(value) {\n  return (\n    value\n      // Collapse markdown whitespace.\n      .replace(/[\\t\\n\\r ]+/g, values.space)\n      // Trim.\n      .replace(/^ | $/g, '')\n      // Some characters are considered “uppercase”, but if their lowercase\n      // counterpart is uppercased will result in a different uppercase\n      // character.\n      // Hence, to get that form, we perform both lower- and uppercase.\n      // Upper case makes sure keys will not interact with default prototypal\n      // methods: no method is uppercase.\n      .toLowerCase()\n      .toUpperCase()\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;;AAqBO,SAAS,oBAAoB,KAAK;IACvC,OACE,KACE,gCAAgC;KAC/B,OAAO,CAAC,eAAe,wJAAA,CAAA,SAAM,CAAC,KAAK,CACpC,QAAQ;KACP,OAAO,CAAC,UAAU,GACnB,qEAAqE;IACrE,iEAAiE;IACjE,aAAa;IACb,iEAAiE;IACjE,uEAAuE;IACvE,mCAAmC;KAClC,WAAW,GACX,WAAW;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-html-tag-name/index.js"], "sourcesContent": ["/**\n * List of lowercase HTML “block” tag names.\n *\n * The list, when parsing HTML (flow), results in more relaxed rules (condition\n * 6).\n * Because they are known blocks, the HTML-like syntax doesn’t have to be\n * strictly parsed.\n * For tag names not in this list, a more strict algorithm (condition 7) is used\n * to detect whether the HTML-like syntax is seen as HTML (flow) or not.\n *\n * This is copied from:\n * <https://spec.commonmark.org/0.30/#html-blocks>.\n *\n * > 👉 **Note**: `search` was added in `CommonMark@0.31`.\n */\nexport const htmlBlockNames = [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'search',\n  'section',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n]\n\n/**\n * List of lowercase HTML “raw” tag names.\n *\n * The list, when parsing HTML (flow), results in HTML that can include lines\n * without exiting, until a closing tag also in this list is found (condition\n * 1).\n *\n * This module is copied from:\n * <https://spec.commonmark.org/0.30/#html-blocks>.\n *\n * > 👉 **Note**: `textarea` was added in `CommonMark@0.30`.\n */\nexport const htmlRawNames = ['pre', 'script', 'style', 'textarea']\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AACM,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAcM,MAAM,eAAe;IAAC;IAAO;IAAU;IAAS;CAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-decode-numeric-character-reference/dev/index.js"], "sourcesContent": ["import {codes} from 'micromark-util-symbol/codes.js'\nimport {values} from 'micromark-util-symbol/values.js'\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCharCode(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < codes.ht ||\n    code === codes.vt ||\n    (code > codes.cr && code < codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55295 && code < 57344) ||\n    // Noncharacters.\n    (code > 64975 && code < 65008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65535) === 65535 ||\n    (code & 65535) === 65534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1114111\n  ) {\n    return values.replacementCharacter\n  }\n\n  return String.fromCharCode(code)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAgBO,SAAS,gCAAgC,KAAK,EAAE,IAAI;IACzD,MAAM,OAAO,OAAO,QAAQ,CAAC,OAAO;IAEpC,IACE,uCAAuC;IACvC,OAAO,uJAAA,CAAA,QAAK,CAAC,EAAE,IACf,SAAS,uJAAA,CAAA,QAAK,CAAC,EAAE,IAChB,OAAO,uJAAA,CAAA,QAAK,CAAC,EAAE,IAAI,OAAO,uJAAA,CAAA,QAAK,CAAC,KAAK,IAErC,OAAO,uJAAA,CAAA,QAAK,CAAC,KAAK,IAAI,OAAO,OAE7B,OAAO,SAAS,OAAO,SAEvB,OAAO,SAAS,OAAO,SACxB,6BAA6B,GAC7B,CAAC,OAAO,KAAK,MAAM,SACnB,CAAC,OAAO,KAAK,MAAM,SACnB,4BAA4B,GAC5B,eAAe;IACf,OAAO,SACP;QACA,OAAO,wJAAA,CAAA,SAAM,CAAC,oBAAoB;IACpC;IAEA,OAAO,OAAO,YAAY,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/micromark-util-decode-string/dev/index.js"], "sourcesContent": ["import {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\n\nconst characterEscapeOrReference =\n  /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nexport function decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode)\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @param {string} $2\n * @returns {string}\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0)\n\n  if (head === codes.numberSign) {\n    const head = $2.charCodeAt(1)\n    const hex = head === codes.lowercaseX || head === codes.uppercaseX\n    return decodeNumericCharacterReference(\n      $2.slice(hex ? 2 : 1),\n      hex ? constants.numericBaseHexadecimal : constants.numericBaseDecimal\n    )\n  }\n\n  return decodeNamedCharacterReference($2) || $0\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,6BACJ;AAcK,SAAS,aAAa,KAAK;IAChC,OAAO,MAAM,OAAO,CAAC,4BAA4B;AACnD;AAEA;;;;;CAKC,GACD,SAAS,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;IACxB,IAAI,IAAI;QACN,UAAU;QACV,OAAO;IACT;IAEA,aAAa;IACb,MAAM,OAAO,GAAG,UAAU,CAAC;IAE3B,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QAC7B,MAAM,OAAO,GAAG,UAAU,CAAC;QAC3B,MAAM,MAAM,SAAS,uJAAA,CAAA,QAAK,CAAC,UAAU,IAAI,SAAS,uJAAA,CAAA,QAAK,CAAC,UAAU;QAClE,OAAO,CAAA,GAAA,mMAAA,CAAA,kCAA+B,AAAD,EACnC,GAAG,KAAK,CAAC,MAAM,IAAI,IACnB,MAAM,2JAAA,CAAA,YAAS,CAAC,sBAAsB,GAAG,2JAAA,CAAA,YAAS,CAAC,kBAAkB;IAEzE;IAEA,OAAO,CAAA,GAAA,4KAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/unist-util-stringify-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Position | PositionLike | Point | PointLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;;;;;;;;CAcC;;;AACM,SAAS,kBAAkB,KAAK;IACrC,WAAW;IACX,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACvC,OAAO;IACT;IAEA,QAAQ;IACR,IAAI,cAAc,SAAS,UAAU,OAAO;QAC1C,OAAO,SAAS,MAAM,QAAQ;IAChC;IAEA,YAAY;IACZ,IAAI,WAAW,SAAS,SAAS,OAAO;QACtC,OAAO,SAAS;IAClB;IAEA,SAAS;IACT,IAAI,UAAU,SAAS,YAAY,OAAO;QACxC,OAAO,MAAM;IACf;IAEA,IAAI;IACJ,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,SAAS,MAAM,IAAI,IAAI,MAAM,MAAM,SAAS,MAAM,MAAM;AACvE;AAEA;;;CAGC,GACD,SAAS,SAAS,GAAG;IACnB,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI,GAAG;AAC7D;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,SAAS,OAAO,UAAU,WAAW,QAAQ;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/mdast-util-from-markdown/dev/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Encoding} Encoding\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').ParseOptions} ParseOptions\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Value} Value\n *\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist').Point} Point\n *\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n * @typedef {import('mdast').StaticPhrasingContent} StaticPhrasingContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Break} Break\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('mdast').Code} Code\n * @typedef {import('mdast').Definition} Definition\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('mdast').HTML} HTML\n * @typedef {import('mdast').Image} Image\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('mdast').Link} Link\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('mdast').List} List\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('mdast').Text} Text\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('mdast').ReferenceType} ReferenceType\n * @typedef {import('../index.js').CompileData} CompileData\n */\n\n/**\n * @typedef {Root | Content} Node\n * @typedef {Extract<Node, UnistParent>} Parent\n *\n * @typedef {Omit<UnistParent, 'type' | 'children'> & {type: 'fragment', children: Array<PhrasingContent>}} Fragment\n */\n\n/**\n * @callback Transform\n *   Extra transform, to change the AST afterwards.\n * @param {Root} tree\n *   Tree to transform.\n * @returns {Root | undefined | null | void}\n *   New tree or nothing (in which case the current tree is used).\n *\n * @callback Handle\n *   Handle a token.\n * @param {CompileContext} this\n *   Context.\n * @param {Token} token\n *   Current token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {Record<string, Handle>} Handles\n *   Token types mapping to handles\n *\n * @callback OnEnterError\n *   Handle the case where the `right` token is open, but it is closed (by the\n *   `left` token) or because we reached the end of the document.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token | undefined} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @callback OnExitError\n *   Handle the case where the `right` token is open but it is closed by\n *   exiting the `left` token.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {[Token, OnEnterError | undefined]} TokenTuple\n *   Open token on the stack, with an optional error handler for when\n *   that token isn’t closed properly.\n */\n\n/**\n * @typedef Config\n *   Configuration.\n *\n *   We have our defaults, but extensions will add more.\n * @property {Array<string>} canContainEols\n *   Token types where line endings are used.\n * @property {Handles} enter\n *   Opening handles.\n * @property {Handles} exit\n *   Closing handles.\n * @property {Array<Transform>} transforms\n *   Tree transforms.\n *\n * @typedef {Partial<Config>} Extension\n *   Change how markdown tokens from micromark are turned into mdast.\n *\n * @typedef CompileContext\n *   mdast compiler context.\n * @property {Array<Node | Fragment>} stack\n *   Stack of nodes.\n * @property {Array<TokenTuple>} tokenStack\n *   Stack of tokens.\n * @property {<Key extends keyof CompileData>(key: Key) => CompileData[Key]} getData\n *   Get data from the key/value store.\n * @property {<Key extends keyof CompileData>(key: Key, value?: CompileData[Key]) => void} setData\n *   Set data into the key/value store.\n * @property {(this: CompileContext) => void} buffer\n *   Capture some of the output data.\n * @property {(this: CompileContext) => string} resume\n *   Stop capturing and access the output data.\n * @property {<Kind extends Node>(this: CompileContext, node: Kind, token: Token, onError?: OnEnterError) => Kind} enter\n *   Enter a token.\n * @property {(this: CompileContext, token: Token, onError?: OnExitError) => Node} exit\n *   Exit a token.\n * @property {TokenizeContext['sliceSerialize']} sliceSerialize\n *   Get the string value of a token.\n * @property {Config} config\n *   Configuration.\n *\n * @typedef FromMarkdownOptions\n *   Configuration for how to build mdast.\n * @property {Array<Extension | Array<Extension>> | null | undefined} [mdastExtensions]\n *   Extensions for this utility to change how tokens are turned into a tree.\n *\n * @typedef {ParseOptions & FromMarkdownOptions} Options\n *   Configuration.\n */\n\n// To do: micromark: create a registry of tokens?\n// To do: next major: don’t return given `Node` from `enter`.\n// To do: next major: remove setter/getter.\n\nimport {ok as assert} from 'uvu/assert'\nimport {toString} from 'mdast-util-to-string'\nimport {parse} from 'micromark/lib/parse.js'\nimport {preprocess} from 'micromark/lib/preprocess.js'\nimport {postprocess} from 'micromark/lib/postprocess.js'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {decodeString} from 'micromark-util-decode-string'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param value\n *   Markdown to parse.\n * @param encoding\n *   Character encoding for when `value` is `Buffer`.\n * @param options\n *   Configuration.\n * @returns\n *   mdast tree.\n */\nexport const fromMarkdown =\n  /**\n   * @type {(\n   *   ((value: Value, encoding: Encoding, options?: Options | null | undefined) => Root) &\n   *   ((value: Value, options?: Options | null | undefined) => Root)\n   * )}\n   */\n  (\n    /**\n     * @param {Value} value\n     * @param {Encoding | Options | null | undefined} [encoding]\n     * @param {Options | null | undefined} [options]\n     * @returns {Root}\n     */\n    function (value, encoding, options) {\n      if (typeof encoding !== 'string') {\n        options = encoding\n        encoding = undefined\n      }\n\n      return compiler(options)(\n        postprocess(\n          parse(options).document().write(preprocess()(value, encoding, true))\n        )\n      )\n    }\n  )\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      setData,\n      getData\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          assert(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      if (\n        event[1].type === types.listUnordered ||\n        event[1].type === types.listOrdered ||\n        event[1].type === types.blockQuote\n      ) {\n        if (event[0] === 'enter') {\n          containerBalance++\n        } else {\n          containerBalance--\n        }\n\n        atMarker = undefined\n      } else if (event[1].type === types.lineEndingBlank) {\n        if (event[0] === 'enter') {\n          if (\n            listItem &&\n            !atMarker &&\n            !containerBalance &&\n            !firstBlankLineIndex\n          ) {\n            firstBlankLineIndex = index\n          }\n\n          atMarker = undefined\n        }\n      } else if (\n        event[1].type === types.linePrefix ||\n        event[1].type === types.listItemValue ||\n        event[1].type === types.listItemMarker ||\n        event[1].type === types.listItemPrefix ||\n        event[1].type === types.listItemPrefixWhitespace\n      ) {\n        // Empty.\n      } else {\n        atMarker = undefined\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === types.listUnordered ||\n            event[1].type === types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === types.lineEnding ||\n              tailEvent[1].type === types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === types.linePrefix ||\n              tailEvent[1].type === types.blockQuotePrefix ||\n              tailEvent[1].type === types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === types.blockQuoteMarker ||\n              tailEvent[1].type === types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === types.listItemPrefix) {\n          listItem = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          // @ts-expect-error: `listItem` is most definitely defined, TS...\n          events.splice(index, 0, ['enter', listItem, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Set data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @param {CompileData[Key]} [value]\n   *   New value.\n   * @returns {void}\n   *   Nothing.\n   */\n  function setData(key, value) {\n    data[key] = value\n  }\n\n  /**\n   * Get data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @returns {CompileData[Key]}\n   *   Value.\n   */\n  function getData(key) {\n    return data[key]\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Node} create\n   *   Create a node.\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {void}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @template {Node} Kind\n   *   Node type.\n   * @this {CompileContext}\n   *   Context.\n   * @param {Kind} node\n   *   Node to enter.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnEnterError | undefined} [errorHandler]\n   *   Handle the case where this token is open, but it is closed by something else.\n   * @returns {Kind}\n   *   The given node.\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    assert(parent, 'expected `parent`')\n    assert('children' in parent, 'expected `parent`')\n    // @ts-expect-error: Assume `Node` can exist as a child of `parent`.\n    parent.children.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler])\n    // @ts-expect-error: `end` will be patched later.\n    node.position = {start: point(token.start)}\n    return node\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   *   Context.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnExitError | undefined} [onExitError]\n   *   Handle the case where another token is open.\n   * @returns {Node}\n   *   The closed node.\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    assert(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          stringifyPosition({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    assert(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    assert(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n    return node\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {string}\n   */\n  function resume() {\n    return toString(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    setData('expectingFirstListItemValue', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectingFirstListItemValue')) {\n      const ancestor = this.stack[this.stack.length - 2]\n      assert(ancestor, 'expected nodes on stack')\n      assert(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n      setData('expectingFirstListItemValue')\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (getData('flowCodeInside')) return\n    this.buffer()\n    setData('flowCodeInside', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    setData('flowCodeInside')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      assert(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('setextHeadingSlurpLineEnding', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    setData('setextHeadingSlurpLineEnding')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert('children' in node, 'expected parent on stack')\n\n    let tail = node.children[node.children.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      // @ts-expect-error: we’ll add `end` later.\n      tail.position = {start: point(token.start)}\n      // @ts-expect-error: Assume `parent` accepts `text`.\n      node.children.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected a `node` to be on the stack')\n    assert('value' in tail, 'expected a `literal` to be on the stack')\n    assert(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    assert(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (getData('atHardBreak')) {\n      assert('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      assert(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      setData('atHardBreak')\n      return\n    }\n\n    if (\n      !getData('setextHeadingSlurpLineEnding') &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    setData('atHardBreak', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    setData('referenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    setData('referenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    assert(ancestor, 'expected ancestor on stack')\n    assert(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = decodeString(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = normalizeIdentifier(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    assert(fragment, 'expected node on stack')\n    assert(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    setData('inReference', true)\n\n    if (node.type === 'link') {\n      /** @type {Array<StaticPhrasingContent>} */\n      // @ts-expect-error: Assume static phrasing content.\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    setData('inReference')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    setData('referenceType', 'collapsed')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    setData('referenceType', 'full')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    assert(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    setData('characterReferenceType', token.type)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = getData('characterReferenceType')\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = decodeNumericCharacterReference(\n        data,\n        type === types.characterReferenceMarkerNumeric\n          ? constants.numericBaseDecimal\n          : constants.numericBaseHexadecimal\n      )\n      setData('characterReferenceType')\n    } else {\n      const result = decodeNamedCharacterReference(data)\n      assert(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack.pop()\n    assert(tail, 'expected `node`')\n    assert(tail.position, 'expected `node.position`')\n    assert('value' in tail, 'expected `node.value`')\n    tail.value += value\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    // @ts-expect-error `depth` will be set later.\n    return {type: 'heading', depth: undefined, children: []}\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {HTML} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Extension | Array<Extension>>} extensions\n * @returns {void}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {void}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      if (key === 'canContainEols') {\n        const right = extension[key]\n        if (right) {\n          combined[key].push(...right)\n        }\n      } else if (key === 'transforms') {\n        const right = extension[key]\n        if (right) {\n          combined[key].push(...right)\n        }\n      } else if (key === 'enter' || key === 'exit') {\n        const right = extension[key]\n        if (right) {\n          Object.assign(combined[key], right)\n        }\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        stringifyPosition({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GAED,iDAAiD;AACjD,6DAA6D;AAC7D,2CAA2C;;;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAYtB,MAAM,eAQT;;;;;KAKC,GACD,SAAU,KAAK,EAAE,QAAQ,EAAE,OAAO;IAChC,IAAI,OAAO,aAAa,UAAU;QAChC,UAAU;QACV,WAAW;IACb;IAEA,OAAO,SAAS,SACd,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACR,CAAA,GAAA,mJAAA,CAAA,QAAK,AAAD,EAAE,SAAS,QAAQ,GAAG,KAAK,CAAC,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,IAAI,OAAO,UAAU;AAGpE;AAGJ;;;;CAIC,GACD,SAAS,SAAS,OAAO;IACvB,mBAAmB,GACnB,MAAM,SAAS;QACb,YAAY,EAAE;QACd,gBAAgB;YAAC;YAAY;YAAY;YAAW;YAAa;SAAS;QAC1E,OAAO;YACL,UAAU,OAAO;YACjB,kBAAkB;YAClB,eAAe;YACf,YAAY,OAAO;YACnB,YAAY,OAAO;YACnB,iBAAiB;YACjB,oBAAoB;YACpB,YAAY,OAAO;YACnB,qBAAqB;YACrB,qBAAqB;YACrB,cAAc,OAAO,UAAU;YAC/B,UAAU,OAAO,UAAU;YAC3B,cAAc;YACd,MAAM;YACN,eAAe;YACf,YAAY,OAAO;YACnB,6BAA6B;YAC7B,uBAAuB;YACvB,uBAAuB;YACvB,UAAU,OAAO;YACjB,iBAAiB,OAAO;YACxB,mBAAmB,OAAO;YAC1B,UAAU,OAAO,MAAM;YACvB,cAAc;YACd,UAAU,OAAO,MAAM;YACvB,cAAc;YACd,OAAO,OAAO;YACd,OAAO;YACP,MAAM,OAAO;YACb,UAAU,OAAO;YACjB,eAAe;YACf,aAAa,OAAO,MAAM;YAC1B,eAAe,OAAO;YACtB,WAAW,OAAO;YAClB,WAAW;YACX,iBAAiB;YACjB,2BAA2B;YAC3B,qBAAqB;YACrB,eAAe,OAAO;YACtB,QAAQ,OAAO;YACf,eAAe,OAAO;QACxB;QACA,MAAM;YACJ,YAAY;YACZ,oBAAoB;YACpB,UAAU;YACV,eAAe;YACf,kBAAkB;YAClB,YAAY;YACZ,sBAAsB;YACtB,qCAAqC;YACrC,iCAAiC;YACjC,yBAAyB;YACzB,YAAY,OAAO;YACnB,iBAAiB;YACjB,qBAAqB;YACrB,qBAAqB;YACrB,eAAe;YACf,cAAc,OAAO;YACrB,UAAU,OAAO;YACjB,cAAc;YACd,MAAM;YACN,YAAY;YACZ,6BAA6B;YAC7B,uBAAuB;YACvB,uBAAuB;YACvB,UAAU;YACV,iBAAiB,OAAO;YACxB,mBAAmB,OAAO;YAC1B,UAAU,OAAO;YACjB,cAAc;YACd,UAAU,OAAO;YACjB,cAAc;YACd,OAAO,OAAO;YACd,OAAO;YACP,WAAW;YACX,YAAY;YACZ,MAAM,OAAO;YACb,UAAU;YACV,aAAa;YACb,eAAe;YACf,WAAW;YACX,iBAAiB;YACjB,2BAA2B;YAC3B,qBAAqB;YACrB,UAAU;YACV,eAAe,OAAO;YACtB,2BAA2B;YAC3B,mBAAmB;YACnB,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,UAAU,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,eAAe,IAAI,EAAE;IAEvD,wBAAwB,GACxB,MAAM,OAAO,CAAC;IAEd,OAAO;;IAEP;;;;;;;GAOC,GACD,SAAS,QAAQ,MAAM;QACrB,iBAAiB,GACjB,IAAI,OAAO;YAAC,MAAM;YAAQ,UAAU,EAAE;QAAA;QACtC,mDAAmD,GACnD,MAAM,UAAU;YACd,OAAO;gBAAC;aAAK;YACb,YAAY,EAAE;YACd;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QACA,0BAA0B,GAC1B,MAAM,YAAY,EAAE;QACpB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,qEAAqE;YACrE,wCAAwC;YACxC,IACE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,WAAW,IAC3C,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,aAAa,EAC7C;gBACA,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,SAAS;oBAChC,UAAU,IAAI,CAAC;gBACjB,OAAO;oBACL,MAAM,OAAO,UAAU,GAAG;oBAC1B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,SAAS,UAAU;oBACjC,QAAQ,YAAY,QAAQ,MAAM;gBACpC;YACF;QACF;QAEA,QAAQ,CAAC;QAET,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,MAAM,UAAU,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YAExC,IAAI,IAAI,IAAI,CAAC,SAAS,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;gBAC5C,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CACjC,OAAO,MAAM,CACX;oBAAC,gBAAgB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;gBAAA,GAChD,UAEF,MAAM,CAAC,MAAM,CAAC,EAAE;YAEpB;QACF;QAEA,kCAAkC;QAClC,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;YACjC,MAAM,OAAO,QAAQ,UAAU,CAAC,QAAQ,UAAU,CAAC,MAAM,GAAG,EAAE;YAC9D,MAAM,UAAU,IAAI,CAAC,EAAE,IAAI;YAC3B,QAAQ,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,EAAE;QAC1C;QAEA,8BAA8B;QAC9B,KAAK,QAAQ,GAAG;YACd,OAAO,MACL,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,GAAG;gBAAC,MAAM;gBAAG,QAAQ;gBAAG,QAAQ;YAAC;YAEzE,KAAK,MACH,OAAO,MAAM,GAAG,IACZ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAChC;gBAAC,MAAM;gBAAG,QAAQ;gBAAG,QAAQ;YAAC;QAEtC;QAEA,mBAAmB;QACnB,QAAQ,CAAC;QACT,MAAO,EAAE,QAAQ,OAAO,UAAU,CAAC,MAAM,CAAE;YACzC,OAAO,OAAO,UAAU,CAAC,MAAM,CAAC,SAAS;QAC3C;QAEA,OAAO;IACT;IAEA;;;;;GAKC,GACD,SAAS,YAAY,MAAM,EAAE,KAAK,EAAE,MAAM;QACxC,IAAI,QAAQ,QAAQ;QACpB,IAAI,mBAAmB,CAAC;QACxB,IAAI,aAAa;QACjB,8BAA8B,GAC9B,IAAI;QACJ,+BAA+B,GAC/B,IAAI;QACJ,+BAA+B,GAC/B,IAAI;QACJ,gCAAgC,GAChC,IAAI;QAEJ,MAAO,EAAE,SAAS,OAAQ;YACxB,MAAM,QAAQ,MAAM,CAAC,MAAM;YAE3B,IACE,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,aAAa,IACrC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,WAAW,IACnC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,UAAU,EAClC;gBACA,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS;oBACxB;gBACF,OAAO;oBACL;gBACF;gBAEA,WAAW;YACb,OAAO,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,eAAe,EAAE;gBAClD,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS;oBACxB,IACE,YACA,CAAC,YACD,CAAC,oBACD,CAAC,qBACD;wBACA,sBAAsB;oBACxB;oBAEA,WAAW;gBACb;YACF,OAAO,IACL,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,UAAU,IAClC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,aAAa,IACrC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,cAAc,IACtC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,cAAc,IACtC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,wBAAwB,EAChD;YACA,SAAS;YACX,OAAO;gBACL,WAAW;YACb;YAEA,IACE,AAAC,CAAC,oBACA,KAAK,CAAC,EAAE,KAAK,WACb,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,cAAc,IACvC,qBAAqB,CAAC,KACrB,KAAK,CAAC,EAAE,KAAK,UACb,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,aAAa,IACpC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,WAAW,GACvC;gBACA,IAAI,UAAU;oBACZ,IAAI,YAAY;oBAChB,YAAY;oBAEZ,MAAO,YAAa;wBAClB,MAAM,YAAY,MAAM,CAAC,UAAU;wBAEnC,IACE,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,UAAU,IACtC,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,eAAe,EAC3C;4BACA,IAAI,SAAS,CAAC,EAAE,KAAK,QAAQ;4BAE7B,IAAI,WAAW;gCACb,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG,uJAAA,CAAA,QAAK,CAAC,eAAe;gCACjD,aAAa;4BACf;4BAEA,SAAS,CAAC,EAAE,CAAC,IAAI,GAAG,uJAAA,CAAA,QAAK,CAAC,UAAU;4BACpC,YAAY;wBACd,OAAO,IACL,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,UAAU,IACtC,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,gBAAgB,IAC5C,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,0BAA0B,IACtD,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,gBAAgB,IAC5C,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,cAAc,EAC1C;wBACA,QAAQ;wBACV,OAAO;4BACL;wBACF;oBACF;oBAEA,IACE,uBACA,CAAC,CAAC,aAAa,sBAAsB,SAAS,GAC9C;wBACA,SAAS,OAAO,GAAG;oBACrB;oBAEA,gBAAgB;oBAChB,SAAS,GAAG,GAAG,OAAO,MAAM,CAC1B,CAAC,GACD,YAAY,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG;oBAGvD,OAAO,MAAM,CAAC,aAAa,OAAO,GAAG;wBAAC;wBAAQ;wBAAU,KAAK,CAAC,EAAE;qBAAC;oBACjE;oBACA;gBACF;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uJAAA,CAAA,QAAK,CAAC,cAAc,EAAE;oBAC1C,WAAW;wBACT,MAAM;wBACN,SAAS;wBACT,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK;wBACvC,iDAAiD;wBACjD,KAAK;oBACP;oBACA,iEAAiE;oBACjE,OAAO,MAAM,CAAC,OAAO,GAAG;wBAAC;wBAAS;wBAAU,KAAK,CAAC,EAAE;qBAAC;oBACrD;oBACA;oBACA,sBAAsB;oBACtB,WAAW;gBACb;YACF;QACF;QAEA,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,GAAG;QAC3B,OAAO;IACT;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,QAAQ,GAAG,EAAE,KAAK;QACzB,IAAI,CAAC,IAAI,GAAG;IACd;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,GAAG;QAClB,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,MAAM,EAAE,GAAG;QACzB,OAAO;;QAEP;;;;KAIC,GACD,SAAS,KAAK,KAAK;YACjB,MAAM,IAAI,CAAC,IAAI,EAAE,OAAO,QAAQ;YAChC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE;QAC1B;IACF;IAEA;;;GAGC,GACD,SAAS;QACP,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAAC,MAAM;YAAY,UAAU,EAAE;QAAA;IACjD;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,MAAM,IAAI,EAAE,KAAK,EAAE,YAAY;QACtC,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAChD,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,QAAQ;QACf,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,cAAc,QAAQ;QAC7B,oEAAoE;QACpE,OAAO,QAAQ,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAAC;YAAO;SAAa;QAC1C,iDAAiD;QACjD,KAAK,QAAQ,GAAG;YAAC,OAAO,MAAM,MAAM,KAAK;QAAC;QAC1C,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,SAAS,OAAO,GAAG;QACjB,OAAO;;QAEP;;;;KAIC,GACD,SAAS,MAAM,KAAK;YAClB,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE;YACxB,KAAK,IAAI,CAAC,IAAI,EAAE;QAClB;IACF;IAEA;;;;;;;;;GASC,GACD,SAAS,KAAK,KAAK,EAAE,WAAW;QAC9B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;QAC3B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG;QAEhC,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MACR,mBACE,MAAM,IAAI,GACV,QACA,CAAA,GAAA,yKAAA,CAAA,oBAAiB,AAAD,EAAE;gBAAC,OAAO,MAAM,KAAK;gBAAE,KAAK,MAAM,GAAG;YAAA,KACrD;QAEN,OAAO,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM,IAAI,EAAE;YACtC,IAAI,aAAa;gBACf,YAAY,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,EAAE;YACvC,OAAO;gBACL,MAAM,UAAU,IAAI,CAAC,EAAE,IAAI;gBAC3B,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,EAAE;YACnC;QACF;QAEA,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,YAAY;QACjC,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,QAAQ,EAAE;QACtB,KAAK,QAAQ,CAAC,GAAG,GAAG,MAAM,MAAM,GAAG;QACnC,OAAO;IACT;IAEA;;;GAGC,GACD,SAAS;QACP,OAAO,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAChC;IAEA,EAAE;IACF,YAAY;IACZ,EAAE;IAEF;;;GAGC,GACD,SAAS;QACP,QAAQ,+BAA+B;IACzC;IAEA;;;GAGC,GACD,SAAS,qBAAqB,KAAK;QACjC,IAAI,QAAQ,gCAAgC;YAC1C,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;YAClD,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,UAAU;YACjB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,IAAI,KAAK,QAAQ;YACjC,SAAS,KAAK,GAAG,OAAO,QAAQ,CAC9B,IAAI,CAAC,cAAc,CAAC,QACpB,2JAAA,CAAA,YAAS,CAAC,kBAAkB;YAE9B,QAAQ;QACV;IACF;IAEA;;;GAGC,GACD,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAC7B,KAAK,IAAI,GAAG;IACd;IAEA;;;GAGC,GACD,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAC7B,KAAK,IAAI,GAAG;IACd;IAEA;;;GAGC,GACD,SAAS;QACP,qCAAqC;QACrC,IAAI,QAAQ,mBAAmB;QAC/B,IAAI,CAAC,MAAM;QACX,QAAQ,kBAAkB;IAC5B;IAEA;;;GAGC,GACD,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAE7B,KAAK,KAAK,GAAG,KAAK,OAAO,CAAC,4BAA4B;QACtD,QAAQ;IACV;IAEA;;;GAGC,GACD,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAE7B,KAAK,KAAK,GAAG,KAAK,OAAO,CAAC,gBAAgB;IAC5C;IAEA;;;GAGC,GACD,SAAS,4BAA4B,KAAK;QACxC,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,cAAc;QAEnC,KAAK,KAAK,GAAG;QACb,KAAK,UAAU,GAAG,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAClC,IAAI,CAAC,cAAc,CAAC,QACpB,WAAW;IACf;IAEA;;;GAGC,GACD,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,cAAc;QAEnC,KAAK,KAAK,GAAG;IACf;IAEA;;;GAGC,GACD,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,cAAc;QAEnC,KAAK,GAAG,GAAG;IACb;IAEA;;;GAGC,GACD,SAAS,yBAAyB,KAAK;QACrC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,WAAW;QAEhC,IAAI,CAAC,KAAK,KAAK,EAAE;YACf,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC,OAAO,MAAM;YAE/C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,UAAU,KACR,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,GACZ;YAGF,KAAK,KAAK,GAAG;QACf;IACF;IAEA;;;GAGC,GACD,SAAS;QACP,QAAQ,gCAAgC;IAC1C;IAEA;;;GAGC,GACD,SAAS,gCAAgC,KAAK;QAC5C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,WAAW;QAEhC,KAAK,KAAK,GACR,IAAI,CAAC,cAAc,CAAC,OAAO,UAAU,CAAC,OAAO,uJAAA,CAAA,QAAK,CAAC,QAAQ,GAAG,IAAI;IACtE;IAEA;;;GAGC,GACD,SAAS;QACP,QAAQ;IACV;IAEA;;;GAGC,GAED,SAAS,YAAY,KAAK;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,cAAc,MAAM;QAE3B,IAAI,OAAO,KAAK,QAAQ,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,EAAE;QAElD,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,QAAQ;YACjC,uBAAuB;YACvB,OAAO;YACP,2CAA2C;YAC3C,KAAK,QAAQ,GAAG;gBAAC,OAAO,MAAM,MAAM,KAAK;YAAC;YAC1C,oDAAoD;YACpD,KAAK,QAAQ,CAAC,IAAI,CAAC;QACrB;QAEA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAClB;IAEA;;;GAGC,GAED,SAAS,WAAW,KAAK;QACvB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;QAC3B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,WAAW,MAAM;QACxB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,QAAQ,EAAE;QACtB,KAAK,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC;QAClC,KAAK,QAAQ,CAAC,GAAG,GAAG,MAAM,MAAM,GAAG;IACrC;IAEA;;;GAGC,GAED,SAAS,iBAAiB,KAAK;QAC7B,MAAM,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QACjD,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS;QAEhB,8DAA8D;QAC9D,IAAI,QAAQ,gBAAgB;YAC1B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,cAAc,SAAS;YAC9B,MAAM,OAAO,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,CAAC,MAAM,GAAG,EAAE;YAC1D,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,QAAQ,EAAE;YACtB,KAAK,QAAQ,CAAC,GAAG,GAAG,MAAM,MAAM,GAAG;YACnC,QAAQ;YACR;QACF;QAEA,IACE,CAAC,QAAQ,mCACT,OAAO,cAAc,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAC3C;YACA,YAAY,IAAI,CAAC,IAAI,EAAE;YACvB,WAAW,IAAI,CAAC,IAAI,EAAE;QACxB;IACF;IAEA;;;GAGC,GAED,SAAS;QACP,QAAQ,eAAe;IACzB;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAE7B,KAAK,KAAK,GAAG;IACf;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAE7B,KAAK,KAAK,GAAG;IACf;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,cAAc;QAEnC,KAAK,KAAK,GAAG;IACf;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAE7B,0EAA0E;QAC1E,iCAAiC;QAEjC,gBAAgB;QAChB,IAAI,QAAQ,gBAAgB;YAC1B,0BAA0B,GAC1B,MAAM,gBAAgB,QAAQ,oBAAoB;YAElD,KAAK,IAAI,IAAI;YACb,4BAA4B;YAC5B,KAAK,aAAa,GAAG;YACrB,4BAA4B;YAC5B,OAAO,KAAK,GAAG;YACf,OAAO,KAAK,KAAK;QACnB,OAAO;YACL,4BAA4B;YAC5B,OAAO,KAAK,UAAU;YACtB,4BAA4B;YAC5B,OAAO,KAAK,KAAK;QACnB;QAEA,QAAQ;IACV;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,SAAS;QAE9B,0EAA0E;QAC1E,iCAAiC;QAEjC,gBAAgB;QAChB,IAAI,QAAQ,gBAAgB;YAC1B,0BAA0B,GAC1B,MAAM,gBAAgB,QAAQ,oBAAoB;YAElD,KAAK,IAAI,IAAI;YACb,4BAA4B;YAC5B,KAAK,aAAa,GAAG;YACrB,4BAA4B;YAC5B,OAAO,KAAK,GAAG;YACf,OAAO,KAAK,KAAK;QACnB,OAAO;YACL,4BAA4B;YAC5B,OAAO,KAAK,UAAU;YACtB,4BAA4B;YAC5B,OAAO,KAAK,KAAK;QACnB;QAEA,QAAQ;IACV;IAEA;;;GAGC,GAED,SAAS,gBAAgB,KAAK;QAC5B,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC;QACnC,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAClD,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,UAAU;QACjB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,QAC/C;QAGF,2EAA2E;QAC3E,SAAS;QACT,SAAS,KAAK,GAAG,CAAA,GAAA,wKAAA,CAAA,eAAY,AAAD,EAAE;QAC9B,mCAAmC;QACnC,SAAS,UAAU,GAAG,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,WAAW;IAC/D;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAClD,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,UAAU;QACjB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,IAAI,KAAK,YAAY;QACrC,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,QACvC;QAGF,sBAAsB;QACtB,QAAQ,eAAe;QAEvB,IAAI,KAAK,IAAI,KAAK,QAAQ;YACxB,yCAAyC,GACzC,oDAAoD;YACpD,MAAM,WAAW,SAAS,QAAQ;YAElC,KAAK,QAAQ,GAAG;QAClB,OAAO;YACL,KAAK,GAAG,GAAG;QACb;IACF;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,QACvC;QAEF,KAAK,GAAG,GAAG;IACb;IAEA;;;GAGC,GAED,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,QACvC;QAEF,KAAK,KAAK,GAAG;IACf;IAEA;;;GAGC,GAED,SAAS;QACP,QAAQ;IACV;IAEA;;;GAGC,GAED,SAAS;QACP,QAAQ,iBAAiB;IAC3B;IAEA;;;GAGC,GAED,SAAS,sBAAsB,KAAK;QAClC,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,QACvC;QAGF,2EAA2E;QAC3E,SAAS;QACT,KAAK,KAAK,GAAG;QACb,mCAAmC;QACnC,KAAK,UAAU,GAAG,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAClC,IAAI,CAAC,cAAc,CAAC,QACpB,WAAW;QACb,QAAQ,iBAAiB;IAC3B;IAEA;;;GAGC,GAED,SAAS,+BAA+B,KAAK;QAC3C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EACH,MAAM,IAAI,KAAK,qCACb,MAAM,IAAI,KAAK;QAEnB,QAAQ,0BAA0B,MAAM,IAAI;IAC9C;IAEA;;;GAGC,GACD,SAAS,8BAA8B,KAAK;QAC1C,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC;QACjC,MAAM,OAAO,QAAQ;QACrB,mBAAmB,GACnB,IAAI;QAEJ,IAAI,MAAM;YACR,QAAQ,CAAA,GAAA,mMAAA,CAAA,kCAA+B,AAAD,EACpC,MACA,SAAS,uJAAA,CAAA,QAAK,CAAC,+BAA+B,GAC1C,2JAAA,CAAA,YAAS,CAAC,kBAAkB,GAC5B,2JAAA,CAAA,YAAS,CAAC,sBAAsB;YAEtC,QAAQ;QACV,OAAO;YACL,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,gCAA6B,AAAD,EAAE;YAC7C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,WAAW,OAAO;YACzB,QAAQ;QACV;QAEA,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;QAC3B,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,QAAQ,EAAE;QACtB,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,WAAW,MAAM;QACxB,KAAK,KAAK,IAAI;QACd,KAAK,QAAQ,CAAC,GAAG,GAAG,MAAM,MAAM,GAAG;IACrC;IAEA;;;GAGC,GACD,SAAS,uBAAuB,KAAK;QACnC,WAAW,IAAI,CAAC,IAAI,EAAE;QACtB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAE7B,KAAK,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;IACjC;IAEA;;;GAGC,GACD,SAAS,oBAAoB,KAAK;QAChC,WAAW,IAAI,CAAC,IAAI,EAAE;QACtB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;QAC9C,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;QACb,CAAA,GAAA,0IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,IAAI,KAAK,QAAQ;QAE7B,KAAK,GAAG,GAAG,YAAY,IAAI,CAAC,cAAc,CAAC;IAC7C;IAEA,EAAE;IACF,YAAY;IACZ,EAAE;IAEF,0BAA0B,GAC1B,SAAS;QACP,OAAO;YAAC,MAAM;YAAc,UAAU,EAAE;QAAA;IAC1C;IAEA,oBAAoB,GACpB,SAAS;QACP,OAAO;YAAC,MAAM;YAAQ,MAAM;YAAM,MAAM;YAAM,OAAO;QAAE;IACzD;IAEA,0BAA0B,GAC1B,SAAS;QACP,OAAO;YAAC,MAAM;YAAc,OAAO;QAAE;IACvC;IAEA,0BAA0B,GAC1B,SAAS;QACP,OAAO;YACL,MAAM;YACN,YAAY;YACZ,OAAO;YACP,OAAO;YACP,KAAK;QACP;IACF;IAEA,wBAAwB,GACxB,SAAS;QACP,OAAO;YAAC,MAAM;YAAY,UAAU,EAAE;QAAA;IACxC;IAEA,uBAAuB,GACvB,SAAS;QACP,8CAA8C;QAC9C,OAAO;YAAC,MAAM;YAAW,OAAO;YAAW,UAAU,EAAE;QAAA;IACzD;IAEA,qBAAqB,GACrB,SAAS;QACP,OAAO;YAAC,MAAM;QAAO;IACvB;IAEA,oBAAoB,GACpB,SAAS;QACP,OAAO;YAAC,MAAM;YAAQ,OAAO;QAAE;IACjC;IAEA,qBAAqB,GACrB,SAAS;QACP,OAAO;YAAC,MAAM;YAAS,OAAO;YAAM,KAAK;YAAI,KAAK;QAAI;IACxD;IAEA,oBAAoB,GACpB,SAAS;QACP,OAAO;YAAC,MAAM;YAAQ,OAAO;YAAM,KAAK;YAAI,UAAU,EAAE;QAAA;IAC1D;IAEA;;;GAGC,GACD,SAAS,KAAK,KAAK;QACjB,OAAO;YACL,MAAM;YACN,SAAS,MAAM,IAAI,KAAK;YACxB,OAAO;YACP,QAAQ,MAAM,OAAO;YACrB,UAAU,EAAE;QACd;IACF;IAEA;;;GAGC,GACD,SAAS,SAAS,KAAK;QACrB,OAAO;YACL,MAAM;YACN,QAAQ,MAAM,OAAO;YACrB,SAAS;YACT,UAAU,EAAE;QACd;IACF;IAEA,yBAAyB,GACzB,SAAS;QACP,OAAO;YAAC,MAAM;YAAa,UAAU,EAAE;QAAA;IACzC;IAEA,sBAAsB,GACtB,SAAS;QACP,OAAO;YAAC,MAAM;YAAU,UAAU,EAAE;QAAA;IACtC;IAEA,oBAAoB,GACpB,SAAS;QACP,OAAO;YAAC,MAAM;YAAQ,OAAO;QAAE;IACjC;IAEA,6BAA6B,GAC7B,SAAS;QACP,OAAO;YAAC,MAAM;QAAe;IAC/B;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,MAAM,CAAC;IACd,OAAO;QAAC,MAAM,EAAE,IAAI;QAAE,QAAQ,EAAE,MAAM;QAAE,QAAQ,EAAE,MAAM;IAAA;AAC1D;AAEA;;;;CAIC,GACD,SAAS,UAAU,QAAQ,EAAE,UAAU;IACrC,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,WAAW,MAAM,CAAE;QAClC,MAAM,QAAQ,UAAU,CAAC,MAAM;QAE/B,IAAI,MAAM,OAAO,CAAC,QAAQ;YACxB,UAAU,UAAU;QACtB,OAAO;YACL,UAAU,UAAU;QACtB;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,UAAU,QAAQ,EAAE,SAAS;IACpC,4BAA4B,GAC5B,IAAI;IAEJ,IAAK,OAAO,UAAW;QACrB,IAAI,IAAI,IAAI,CAAC,WAAW,MAAM;YAC5B,IAAI,QAAQ,kBAAkB;gBAC5B,MAAM,QAAQ,SAAS,CAAC,IAAI;gBAC5B,IAAI,OAAO;oBACT,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI;gBACxB;YACF,OAAO,IAAI,QAAQ,cAAc;gBAC/B,MAAM,QAAQ,SAAS,CAAC,IAAI;gBAC5B,IAAI,OAAO;oBACT,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI;gBACxB;YACF,OAAO,IAAI,QAAQ,WAAW,QAAQ,QAAQ;gBAC5C,MAAM,QAAQ,SAAS,CAAC,IAAI;gBAC5B,IAAI,OAAO;oBACT,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAC/B;YACF;QACF;IACF;AACF;AAEA,yBAAyB,GACzB,SAAS,eAAe,IAAI,EAAE,KAAK;IACjC,IAAI,MAAM;QACR,MAAM,IAAI,MACR,mBACE,KAAK,IAAI,GACT,QACA,CAAA,GAAA,yKAAA,CAAA,oBAAiB,AAAD,EAAE;YAAC,OAAO,KAAK,KAAK;YAAE,KAAK,KAAK,GAAG;QAAA,KACnD,4BACA,MAAM,IAAI,GACV,QACA,CAAA,GAAA,yKAAA,CAAA,oBAAiB,AAAD,EAAE;YAAC,OAAO,MAAM,KAAK;YAAE,KAAK,MAAM,GAAG;QAAA,KACrD;IAEN,OAAO;QACL,MAAM,IAAI,MACR,sCACE,MAAM,IAAI,GACV,QACA,CAAA,GAAA,yKAAA,CAAA,oBAAiB,AAAD,EAAE;YAAC,OAAO,MAAM,KAAK;YAAE,KAAK,MAAM,GAAG;QAAA,KACrD;IAEN;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7590, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/khroma/dist/methods/channel.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport Color from '../color/index.js';\n/* MAIN */\nconst channel = (color, channel) => {\n    return _.lang.round(Color.parse(color)[channel]);\n};\n/* EXPORT */\nexport default channel;\n"], "names": [], "mappings": "AAAA,UAAU;;;AACV;AACA;;;AACA,QAAQ,GACR,MAAM,UAAU,CAAC,OAAO;IACpB,OAAO,mJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ;AACnD;uCAEe", "ignoreList": [0], "debugId": null}}]}