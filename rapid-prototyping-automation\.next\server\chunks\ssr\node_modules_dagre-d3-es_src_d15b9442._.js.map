{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/graph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nvar DEFAULT_EDGE_NAME = '\\x00';\nvar GRAPH_NODE = '\\x00';\nvar EDGE_KEY_DELIM = '\\x01';\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\nexport class Graph {\n  constructor(opts = {}) {\n    this._isDirected = _.has(opts, 'directed') ? opts.directed : true;\n    this._isMultigraph = _.has(opts, 'multigraph') ? opts.multigraph : false;\n    this._isCompound = _.has(opts, 'compound') ? opts.compound : false;\n\n    // Label for the graph itself\n    this._label = undefined;\n\n    // Defaults to be set when creating a new node\n    this._defaultNodeLabelFn = _.constant(undefined);\n\n    // Defaults to be set when creating a new edge\n    this._defaultEdgeLabelFn = _.constant(undefined);\n\n    // v -> label\n    this._nodes = {};\n\n    if (this._isCompound) {\n      // v -> parent\n      this._parent = {};\n\n      // v -> children\n      this._children = {};\n      this._children[GRAPH_NODE] = {};\n    }\n\n    // v -> edgeObj\n    this._in = {};\n\n    // u -> v -> Number\n    this._preds = {};\n\n    // v -> edgeObj\n    this._out = {};\n\n    // v -> w -> Number\n    this._sucs = {};\n\n    // e -> edgeObj\n    this._edgeObjs = {};\n\n    // e -> label\n    this._edgeLabels = {};\n  }\n  /* === Graph functions ========= */\n  isDirected() {\n    return this._isDirected;\n  }\n  isMultigraph() {\n    return this._isMultigraph;\n  }\n  isCompound() {\n    return this._isCompound;\n  }\n  setGraph(label) {\n    this._label = label;\n    return this;\n  }\n  graph() {\n    return this._label;\n  }\n  /* === Node functions ========== */\n  setDefaultNodeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultNodeLabelFn = newDefault;\n    return this;\n  }\n  nodeCount() {\n    return this._nodeCount;\n  }\n  nodes() {\n    return _.keys(this._nodes);\n  }\n  sources() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._in[v]);\n    });\n  }\n  sinks() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._out[v]);\n    });\n  }\n  setNodes(vs, value) {\n    var args = arguments;\n    var self = this;\n    _.each(vs, function (v) {\n      if (args.length > 1) {\n        self.setNode(v, value);\n      } else {\n        self.setNode(v);\n      }\n    });\n    return this;\n  }\n  setNode(v, value) {\n    if (_.has(this._nodes, v)) {\n      if (arguments.length > 1) {\n        this._nodes[v] = value;\n      }\n      return this;\n    }\n\n    // @ts-expect-error\n    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n    if (this._isCompound) {\n      this._parent[v] = GRAPH_NODE;\n      this._children[v] = {};\n      this._children[GRAPH_NODE][v] = true;\n    }\n    this._in[v] = {};\n    this._preds[v] = {};\n    this._out[v] = {};\n    this._sucs[v] = {};\n    ++this._nodeCount;\n    return this;\n  }\n  node(v) {\n    return this._nodes[v];\n  }\n  hasNode(v) {\n    return _.has(this._nodes, v);\n  }\n  removeNode(v) {\n    var self = this;\n    if (_.has(this._nodes, v)) {\n      var removeEdge = function (e) {\n        self.removeEdge(self._edgeObjs[e]);\n      };\n      delete this._nodes[v];\n      if (this._isCompound) {\n        this._removeFromParentsChildList(v);\n        delete this._parent[v];\n        _.each(this.children(v), function (child) {\n          self.setParent(child);\n        });\n        delete this._children[v];\n      }\n      _.each(_.keys(this._in[v]), removeEdge);\n      delete this._in[v];\n      delete this._preds[v];\n      _.each(_.keys(this._out[v]), removeEdge);\n      delete this._out[v];\n      delete this._sucs[v];\n      --this._nodeCount;\n    }\n    return this;\n  }\n  setParent(v, parent) {\n    if (!this._isCompound) {\n      throw new Error('Cannot set parent in a non-compound graph');\n    }\n\n    if (_.isUndefined(parent)) {\n      parent = GRAPH_NODE;\n    } else {\n      // Coerce parent to string\n      parent += '';\n      for (var ancestor = parent; !_.isUndefined(ancestor); ancestor = this.parent(ancestor)) {\n        if (ancestor === v) {\n          throw new Error('Setting ' + parent + ' as parent of ' + v + ' would create a cycle');\n        }\n      }\n\n      this.setNode(parent);\n    }\n\n    this.setNode(v);\n    this._removeFromParentsChildList(v);\n    this._parent[v] = parent;\n    this._children[parent][v] = true;\n    return this;\n  }\n  _removeFromParentsChildList(v) {\n    delete this._children[this._parent[v]][v];\n  }\n  parent(v) {\n    if (this._isCompound) {\n      var parent = this._parent[v];\n      if (parent !== GRAPH_NODE) {\n        return parent;\n      }\n    }\n  }\n  children(v) {\n    if (_.isUndefined(v)) {\n      v = GRAPH_NODE;\n    }\n\n    if (this._isCompound) {\n      var children = this._children[v];\n      if (children) {\n        return _.keys(children);\n      }\n    } else if (v === GRAPH_NODE) {\n      return this.nodes();\n    } else if (this.hasNode(v)) {\n      return [];\n    }\n  }\n  predecessors(v) {\n    var predsV = this._preds[v];\n    if (predsV) {\n      return _.keys(predsV);\n    }\n  }\n  successors(v) {\n    var sucsV = this._sucs[v];\n    if (sucsV) {\n      return _.keys(sucsV);\n    }\n  }\n  neighbors(v) {\n    var preds = this.predecessors(v);\n    if (preds) {\n      return _.union(preds, this.successors(v));\n    }\n  }\n  isLeaf(v) {\n    var neighbors;\n    if (this.isDirected()) {\n      neighbors = this.successors(v);\n    } else {\n      neighbors = this.neighbors(v);\n    }\n    return neighbors.length === 0;\n  }\n  filterNodes(filter) {\n    // @ts-expect-error\n    var copy = new this.constructor({\n      directed: this._isDirected,\n      multigraph: this._isMultigraph,\n      compound: this._isCompound,\n    });\n\n    copy.setGraph(this.graph());\n\n    var self = this;\n    _.each(this._nodes, function (value, v) {\n      if (filter(v)) {\n        copy.setNode(v, value);\n      }\n    });\n\n    _.each(this._edgeObjs, function (e) {\n      // @ts-expect-error\n      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n        copy.setEdge(e, self.edge(e));\n      }\n    });\n\n    var parents = {};\n    function findParent(v) {\n      var parent = self.parent(v);\n      if (parent === undefined || copy.hasNode(parent)) {\n        parents[v] = parent;\n        return parent;\n      } else if (parent in parents) {\n        return parents[parent];\n      } else {\n        return findParent(parent);\n      }\n    }\n\n    if (this._isCompound) {\n      _.each(copy.nodes(), function (v) {\n        copy.setParent(v, findParent(v));\n      });\n    }\n\n    return copy;\n  }\n  /* === Edge functions ========== */\n  setDefaultEdgeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultEdgeLabelFn = newDefault;\n    return this;\n  }\n  edgeCount() {\n    return this._edgeCount;\n  }\n  edges() {\n    return _.values(this._edgeObjs);\n  }\n  setPath(vs, value) {\n    var self = this;\n    var args = arguments;\n    _.reduce(vs, function (v, w) {\n      if (args.length > 1) {\n        self.setEdge(v, w, value);\n      } else {\n        self.setEdge(v, w);\n      }\n      return w;\n    });\n    return this;\n  }\n  /*\n   * setEdge(v, w, [value, [name]])\n   * setEdge({ v, w, [name] }, [value])\n   */\n  setEdge() {\n    var v, w, name, value;\n    var valueSpecified = false;\n    var arg0 = arguments[0];\n\n    if (typeof arg0 === 'object' && arg0 !== null && 'v' in arg0) {\n      v = arg0.v;\n      w = arg0.w;\n      name = arg0.name;\n      if (arguments.length === 2) {\n        value = arguments[1];\n        valueSpecified = true;\n      }\n    } else {\n      v = arg0;\n      w = arguments[1];\n      name = arguments[3];\n      if (arguments.length > 2) {\n        value = arguments[2];\n        valueSpecified = true;\n      }\n    }\n\n    v = '' + v;\n    w = '' + w;\n    if (!_.isUndefined(name)) {\n      name = '' + name;\n    }\n\n    var e = edgeArgsToId(this._isDirected, v, w, name);\n    if (_.has(this._edgeLabels, e)) {\n      if (valueSpecified) {\n        this._edgeLabels[e] = value;\n      }\n      return this;\n    }\n\n    if (!_.isUndefined(name) && !this._isMultigraph) {\n      throw new Error('Cannot set a named edge when isMultigraph = false');\n    }\n\n    // It didn't exist, so we need to create it.\n    // First ensure the nodes exist.\n    this.setNode(v);\n    this.setNode(w);\n\n    // @ts-expect-error\n    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n    // Ensure we add undirected edges in a consistent way.\n    v = edgeObj.v;\n    w = edgeObj.w;\n\n    Object.freeze(edgeObj);\n    this._edgeObjs[e] = edgeObj;\n    incrementOrInitEntry(this._preds[w], v);\n    incrementOrInitEntry(this._sucs[v], w);\n    this._in[w][e] = edgeObj;\n    this._out[v][e] = edgeObj;\n    this._edgeCount++;\n    return this;\n  }\n  edge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return this._edgeLabels[e];\n  }\n  hasEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return _.has(this._edgeLabels, e);\n  }\n  removeEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    var edge = this._edgeObjs[e];\n    if (edge) {\n      v = edge.v;\n      w = edge.w;\n      delete this._edgeLabels[e];\n      delete this._edgeObjs[e];\n      decrementOrRemoveEntry(this._preds[w], v);\n      decrementOrRemoveEntry(this._sucs[v], w);\n      delete this._in[w][e];\n      delete this._out[v][e];\n      this._edgeCount--;\n    }\n    return this;\n  }\n  inEdges(v, u) {\n    var inV = this._in[v];\n    if (inV) {\n      var edges = _.values(inV);\n      if (!u) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.v === u;\n      });\n    }\n  }\n  outEdges(v, w) {\n    var outV = this._out[v];\n    if (outV) {\n      var edges = _.values(outV);\n      if (!w) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.w === w;\n      });\n    }\n  }\n  nodeEdges(v, w) {\n    var inEdges = this.inEdges(v, w);\n    if (inEdges) {\n      return inEdges.concat(this.outEdges(v, w));\n    }\n  }\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) {\n    delete map[k];\n  }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj = { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEA,IAAI,oBAAoB;AACxB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AAqBd,MAAM;IACX,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,cAAc,KAAK,QAAQ,GAAG;QAC7D,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,gBAAgB,KAAK,UAAU,GAAG;QACnE,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,cAAc,KAAK,QAAQ,GAAG;QAE7D,6BAA6B;QAC7B,IAAI,CAAC,MAAM,GAAG;QAEd,8CAA8C;QAC9C,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;QAEtC,8CAA8C;QAC9C,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;QAEtC,aAAa;QACb,IAAI,CAAC,MAAM,GAAG,CAAC;QAEf,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,cAAc;YACd,IAAI,CAAC,OAAO,GAAG,CAAC;YAEhB,gBAAgB;YAChB,IAAI,CAAC,SAAS,GAAG,CAAC;YAClB,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC;QAChC;QAEA,eAAe;QACf,IAAI,CAAC,GAAG,GAAG,CAAC;QAEZ,mBAAmB;QACnB,IAAI,CAAC,MAAM,GAAG,CAAC;QAEf,eAAe;QACf,IAAI,CAAC,IAAI,GAAG,CAAC;QAEb,mBAAmB;QACnB,IAAI,CAAC,KAAK,GAAG,CAAC;QAEd,eAAe;QACf,IAAI,CAAC,SAAS,GAAG,CAAC;QAElB,aAAa;QACb,IAAI,CAAC,WAAW,GAAG,CAAC;IACtB;IACA,iCAAiC,GACjC,aAAa;QACX,OAAO,IAAI,CAAC,WAAW;IACzB;IACA,eAAe;QACb,OAAO,IAAI,CAAC,aAAa;IAC3B;IACA,aAAa;QACX,OAAO,IAAI,CAAC,WAAW;IACzB;IACA,SAAS,KAAK,EAAE;QACd,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,IAAI;IACb;IACA,QAAQ;QACN,OAAO,IAAI,CAAC,MAAM;IACpB;IACA,iCAAiC,GACjC,oBAAoB,UAAU,EAAE;QAC9B,IAAI,CAAC,CAAA,GAAA,mLAAA,CAAA,aAAY,AAAD,EAAE,aAAa;YAC7B,aAAa,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;QAC1B;QACA,IAAI,CAAC,mBAAmB,GAAG;QAC3B,OAAO,IAAI;IACb;IACA,YAAY;QACV,OAAO,IAAI,CAAC,UAAU;IACxB;IACA,QAAQ;QACN,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,IAAI,CAAC,MAAM;IAC3B;IACA,UAAU;QACR,IAAI,OAAO,IAAI;QACf,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,IAAI,CAAC,KAAK,IAAI,SAAU,CAAC;YACvC,OAAO,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,GAAG,CAAC,EAAE;QAC9B;IACF;IACA,QAAQ;QACN,IAAI,OAAO,IAAI;QACf,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,IAAI,CAAC,KAAK,IAAI,SAAU,CAAC;YACvC,OAAO,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,IAAI,CAAC,EAAE;QAC/B;IACF;IACA,SAAS,EAAE,EAAE,KAAK,EAAE;QAClB,IAAI,OAAO;QACX,IAAI,OAAO,IAAI;QACf,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,IAAI,SAAU,CAAC;YACpB,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,KAAK,OAAO,CAAC,GAAG;YAClB,OAAO;gBACL,KAAK,OAAO,CAAC;YACf;QACF;QACA,OAAO,IAAI;IACb;IACA,QAAQ,CAAC,EAAE,KAAK,EAAE;QAChB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI;YACzB,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;YACnB;YACA,OAAO,IAAI;QACb;QAEA,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,GAAG,IAAI,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACzE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,GAAG;QAClC;QACA,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;QACf,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;QACjB,EAAE,IAAI,CAAC,UAAU;QACjB,OAAO,IAAI;IACb;IACA,KAAK,CAAC,EAAE;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;IACvB;IACA,QAAQ,CAAC,EAAE;QACT,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE;IAC5B;IACA,WAAW,CAAC,EAAE;QACZ,IAAI,OAAO,IAAI;QACf,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI;YACzB,IAAI,aAAa,SAAU,CAAC;gBAC1B,KAAK,UAAU,CAAC,KAAK,SAAS,CAAC,EAAE;YACnC;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;YACrB,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,2BAA2B,CAAC;gBACjC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;gBACtB,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,SAAU,KAAK;oBACtC,KAAK,SAAS,CAAC;gBACjB;gBACA,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;YAC1B;YACA,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG;YAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;YACrB,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;YAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;YACnB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;YACpB,EAAE,IAAI,CAAC,UAAU;QACnB;QACA,OAAO,IAAI;IACb;IACA,UAAU,CAAC,EAAE,MAAM,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,SAAS;YACzB,SAAS;QACX,OAAO;YACL,0BAA0B;YAC1B,UAAU;YACV,IAAK,IAAI,WAAW,QAAQ,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,WAAW,WAAW,IAAI,CAAC,MAAM,CAAC,UAAW;gBACtF,IAAI,aAAa,GAAG;oBAClB,MAAM,IAAI,MAAM,aAAa,SAAS,mBAAmB,IAAI;gBAC/D;YACF;YAEA,IAAI,CAAC,OAAO,CAAC;QACf;QAEA,IAAI,CAAC,OAAO,CAAC;QACb,IAAI,CAAC,2BAA2B,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG;QAC5B,OAAO,IAAI;IACb;IACA,4BAA4B,CAAC,EAAE;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3C;IACA,OAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE;YAC5B,IAAI,WAAW,YAAY;gBACzB,OAAO;YACT;QACF;IACF;IACA,SAAS,CAAC,EAAE;QACV,IAAI,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,IAAI;YACpB,IAAI;QACN;QAEA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,EAAE;YAChC,IAAI,UAAU;gBACZ,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE;YAChB;QACF,OAAO,IAAI,MAAM,YAAY;YAC3B,OAAO,IAAI,CAAC,KAAK;QACnB,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI;YAC1B,OAAO,EAAE;QACX;IACF;IACA,aAAa,CAAC,EAAE;QACd,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;QAC3B,IAAI,QAAQ;YACV,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE;QAChB;IACF;IACA,WAAW,CAAC,EAAE;QACZ,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;QACzB,IAAI,OAAO;YACT,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE;QAChB;IACF;IACA,UAAU,CAAC,EAAE;QACX,IAAI,QAAQ,IAAI,CAAC,YAAY,CAAC;QAC9B,IAAI,OAAO;YACT,OAAO,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC;QACxC;IACF;IACA,OAAO,CAAC,EAAE;QACR,IAAI;QACJ,IAAI,IAAI,CAAC,UAAU,IAAI;YACrB,YAAY,IAAI,CAAC,UAAU,CAAC;QAC9B,OAAO;YACL,YAAY,IAAI,CAAC,SAAS,CAAC;QAC7B;QACA,OAAO,UAAU,MAAM,KAAK;IAC9B;IACA,YAAY,MAAM,EAAE;QAClB,mBAAmB;QACnB,IAAI,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC;YAC9B,UAAU,IAAI,CAAC,WAAW;YAC1B,YAAY,IAAI,CAAC,aAAa;YAC9B,UAAU,IAAI,CAAC,WAAW;QAC5B;QAEA,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK;QAExB,IAAI,OAAO,IAAI;QACf,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,SAAU,KAAK,EAAE,CAAC;YACpC,IAAI,OAAO,IAAI;gBACb,KAAK,OAAO,CAAC,GAAG;YAClB;QACF;QAEA,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,SAAU,CAAC;YAChC,mBAAmB;YACnB,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG;gBAC1C,KAAK,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC;YAC5B;QACF;QAEA,IAAI,UAAU,CAAC;QACf,SAAS,WAAW,CAAC;YACnB,IAAI,SAAS,KAAK,MAAM,CAAC;YACzB,IAAI,WAAW,aAAa,KAAK,OAAO,CAAC,SAAS;gBAChD,OAAO,CAAC,EAAE,GAAG;gBACb,OAAO;YACT,OAAO,IAAI,UAAU,SAAS;gBAC5B,OAAO,OAAO,CAAC,OAAO;YACxB,OAAO;gBACL,OAAO,WAAW;YACpB;QACF;QAEA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,KAAK,KAAK,IAAI,SAAU,CAAC;gBAC9B,KAAK,SAAS,CAAC,GAAG,WAAW;YAC/B;QACF;QAEA,OAAO;IACT;IACA,iCAAiC,GACjC,oBAAoB,UAAU,EAAE;QAC9B,IAAI,CAAC,CAAA,GAAA,mLAAA,CAAA,aAAY,AAAD,EAAE,aAAa;YAC7B,aAAa,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;QAC1B;QACA,IAAI,CAAC,mBAAmB,GAAG;QAC3B,OAAO,IAAI;IACb;IACA,YAAY;QACV,OAAO,IAAI,CAAC,UAAU;IACxB;IACA,QAAQ;QACN,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,IAAI,CAAC,SAAS;IAChC;IACA,QAAQ,EAAE,EAAE,KAAK,EAAE;QACjB,IAAI,OAAO,IAAI;QACf,IAAI,OAAO;QACX,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,IAAI,SAAU,CAAC,EAAE,CAAC;YACzB,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,KAAK,OAAO,CAAC,GAAG,GAAG;YACrB,OAAO;gBACL,KAAK,OAAO,CAAC,GAAG;YAClB;YACA,OAAO;QACT;QACA,OAAO,IAAI;IACb;IACA;;;GAGC,GACD,UAAU;QACR,IAAI,GAAG,GAAG,MAAM;QAChB,IAAI,iBAAiB;QACrB,IAAI,OAAO,SAAS,CAAC,EAAE;QAEvB,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,MAAM;YAC5D,IAAI,KAAK,CAAC;YACV,IAAI,KAAK,CAAC;YACV,OAAO,KAAK,IAAI;YAChB,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,QAAQ,SAAS,CAAC,EAAE;gBACpB,iBAAiB;YACnB;QACF,OAAO;YACL,IAAI;YACJ,IAAI,SAAS,CAAC,EAAE;YAChB,OAAO,SAAS,CAAC,EAAE;YACnB,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,QAAQ,SAAS,CAAC,EAAE;gBACpB,iBAAiB;YACnB;QACF;QAEA,IAAI,KAAK;QACT,IAAI,KAAK;QACT,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,OAAO;YACxB,OAAO,KAAK;QACd;QAEA,IAAI,IAAI,aAAa,IAAI,CAAC,WAAW,EAAE,GAAG,GAAG;QAC7C,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI;YAC9B,IAAI,gBAAgB;gBAClB,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG;YACxB;YACA,OAAO,IAAI;QACb;QAEA,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,4CAA4C;QAC5C,gCAAgC;QAChC,IAAI,CAAC,OAAO,CAAC;QACb,IAAI,CAAC,OAAO,CAAC;QAEb,mBAAmB;QACnB,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,iBAAiB,QAAQ,IAAI,CAAC,mBAAmB,CAAC,GAAG,GAAG;QAE9E,IAAI,UAAU,cAAc,IAAI,CAAC,WAAW,EAAE,GAAG,GAAG;QACpD,sDAAsD;QACtD,IAAI,QAAQ,CAAC;QACb,IAAI,QAAQ,CAAC;QAEb,OAAO,MAAM,CAAC;QACd,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG;QACpB,qBAAqB,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACrC,qBAAqB,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG;QACjB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,UAAU;QACf,OAAO,IAAI;IACb;IACA,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;QACf,IAAI,IACF,UAAU,MAAM,KAAK,IACjB,YAAY,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,IAC1C,aAAa,IAAI,CAAC,WAAW,EAAE,GAAG,GAAG;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE;IAC5B;IACA,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;QAClB,IAAI,IACF,UAAU,MAAM,KAAK,IACjB,YAAY,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,IAC1C,aAAa,IAAI,CAAC,WAAW,EAAE,GAAG,GAAG;QAC3C,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE;IACjC;IACA,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;QACrB,IAAI,IACF,UAAU,MAAM,KAAK,IACjB,YAAY,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,IAC1C,aAAa,IAAI,CAAC,WAAW,EAAE,GAAG,GAAG;QAC3C,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;QAC5B,IAAI,MAAM;YACR,IAAI,KAAK,CAAC;YACV,IAAI,KAAK,CAAC;YACV,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;YACxB,uBAAuB,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACvC,uBAAuB,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;YACtC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACtB,IAAI,CAAC,UAAU;QACjB;QACA,OAAO,IAAI;IACb;IACA,QAAQ,CAAC,EAAE,CAAC,EAAE;QACZ,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;QACrB,IAAI,KAAK;YACP,IAAI,QAAQ,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE;YACrB,IAAI,CAAC,GAAG;gBACN,OAAO;YACT;YACA,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,OAAO,SAAU,IAAI;gBACnC,OAAO,KAAK,CAAC,KAAK;YACpB;QACF;IACF;IACA,SAAS,CAAC,EAAE,CAAC,EAAE;QACb,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;QACvB,IAAI,MAAM;YACR,IAAI,QAAQ,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE;YACrB,IAAI,CAAC,GAAG;gBACN,OAAO;YACT;YACA,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,OAAO,SAAU,IAAI;gBACnC,OAAO,KAAK,CAAC,KAAK;YACpB;QACF;IACF;IACA,UAAU,CAAC,EAAE,CAAC,EAAE;QACd,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG;QAC9B,IAAI,SAAS;YACX,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;QACzC;IACF;AACF;AAEA,+EAA+E,GAC/E,MAAM,SAAS,CAAC,UAAU,GAAG;AAE7B,+EAA+E,GAC/E,MAAM,SAAS,CAAC,UAAU,GAAG;AAE7B,SAAS,qBAAqB,GAAG,EAAE,CAAC;IAClC,IAAI,GAAG,CAAC,EAAE,EAAE;QACV,GAAG,CAAC,EAAE;IACR,OAAO;QACL,GAAG,CAAC,EAAE,GAAG;IACX;AACF;AAEA,SAAS,uBAAuB,GAAG,EAAE,CAAC;IACpC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE;QACb,OAAO,GAAG,CAAC,EAAE;IACf;AACF;AAEA,SAAS,aAAa,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI;IAC5C,IAAI,IAAI,KAAK;IACb,IAAI,IAAI,KAAK;IACb,IAAI,CAAC,cAAc,IAAI,GAAG;QACxB,IAAI,MAAM;QACV,IAAI;QACJ,IAAI;IACN;IACA,OAAO,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,QAAQ,oBAAoB,IAAI;AAClG;AAEA,SAAS,cAAc,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI;IAC7C,IAAI,IAAI,KAAK;IACb,IAAI,IAAI,KAAK;IACb,IAAI,CAAC,cAAc,IAAI,GAAG;QACxB,IAAI,MAAM;QACV,IAAI;QACJ,IAAI;IACN;IACA,IAAI,UAAU;QAAE,GAAG;QAAG,GAAG;IAAE;IAC3B,IAAI,MAAM;QACR,QAAQ,IAAI,GAAG;IACjB;IACA,OAAO;AACT;AAEA,SAAS,YAAY,UAAU,EAAE,OAAO;IACtC,OAAO,aAAa,YAAY,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,IAAI;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/index.js"], "sourcesContent": ["// Includes only the \"core\" of graphlib\n\nimport { Graph } from './graph.js';\n\nconst version = '2.1.9-pre';\n\nexport { Graph, version };\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AAEvC;;AAEA,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/data/list.js"], "sourcesContent": ["/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nexport { List };\n\nclass List {\n  constructor() {\n    var sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n  dequeue() {\n    var sentinel = this._sentinel;\n    var entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    var sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n  toString() {\n    var strs = [];\n    var sentinel = this._sentinel;\n    var curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return '[' + strs.join(', ') + ']';\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== '_next' && k !== '_prev') {\n    return v;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID,MAAM;IACJ,aAAc;QACZ,IAAI,WAAW,CAAC;QAChB,SAAS,KAAK,GAAG,SAAS,KAAK,GAAG;QAClC,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,UAAU;QACR,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,QAAQ,SAAS,KAAK;QAC1B,IAAI,UAAU,UAAU;YACtB,OAAO;YACP,OAAO;QACT;IACF;IACA,QAAQ,KAAK,EAAE;QACb,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,EAAE;YAC9B,OAAO;QACT;QACA,MAAM,KAAK,GAAG,SAAS,KAAK;QAC5B,SAAS,KAAK,CAAC,KAAK,GAAG;QACvB,SAAS,KAAK,GAAG;QACjB,MAAM,KAAK,GAAG;IAChB;IACA,WAAW;QACT,IAAI,OAAO,EAAE;QACb,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,OAAO,SAAS,KAAK;QACzB,MAAO,SAAS,SAAU;YACxB,KAAK,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM;YAC/B,OAAO,KAAK,KAAK;QACnB;QACA,OAAO,MAAM,KAAK,IAAI,CAAC,QAAQ;IACjC;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,MAAM,KAAK,CAAC,KAAK,GAAG,MAAM,KAAK;IAC/B,MAAM,KAAK,CAAC,KAAK,GAAG,MAAM,KAAK;IAC/B,OAAO,MAAM,KAAK;IAClB,OAAO,MAAM,KAAK;AACpB;AAEA,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,IAAI,MAAM,WAAW,MAAM,SAAS;QAClC,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/greedy-fas.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { List } from './data/list.js';\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nexport { greedyFAS };\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(\n    _.map(results, function (e) {\n      return g.outEdges(e.v, e.w);\n    })\n  );\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    while ((entry = sources.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry['in'] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function (v) {\n    fasGraph.setNode(v, { v: v, in: 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function (e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, (fasGraph.node(e.v).out += weight));\n    maxIn = Math.max(maxIn, (fasGraph.node(e.w)['in'] += weight));\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function () {\n    return new List();\n  });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function (v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry['in']) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry['in'] + zeroIdx].enqueue(entry);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;AAWA,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;AAEnC,SAAS,UAAU,CAAC,EAAE,QAAQ;IAC5B,IAAI,EAAE,SAAS,MAAM,GAAG;QACtB,OAAO,EAAE;IACX;IACA,IAAI,QAAQ,WAAW,GAAG,YAAY;IACtC,IAAI,UAAU,YAAY,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE,MAAM,OAAO;IAEnE,qBAAqB;IACrB,OAAO,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EACb,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,SAAU,CAAC;QACxB,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5B;AAEJ;AAEA,SAAS,YAAY,CAAC,EAAE,OAAO,EAAE,OAAO;IACtC,IAAI,UAAU,EAAE;IAChB,IAAI,UAAU,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;IACzC,IAAI,QAAQ,OAAO,CAAC,EAAE;IAEtB,IAAI;IACJ,MAAO,EAAE,SAAS,GAAI;QACpB,MAAQ,QAAQ,MAAM,OAAO,GAAK;YAChC,WAAW,GAAG,SAAS,SAAS;QAClC;QACA,MAAQ,QAAQ,QAAQ,OAAO,GAAK;YAClC,WAAW,GAAG,SAAS,SAAS;QAClC;QACA,IAAI,EAAE,SAAS,IAAI;YACjB,IAAK,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG;gBAC3C,QAAQ,OAAO,CAAC,EAAE,CAAC,OAAO;gBAC1B,IAAI,OAAO;oBACT,UAAU,QAAQ,MAAM,CAAC,WAAW,GAAG,SAAS,SAAS,OAAO;oBAChE;gBACF;YACF;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,WAAW,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB;IACjE,IAAI,UAAU,sBAAsB,EAAE,GAAG;IAEzC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,SAAU,IAAI;QAC1C,IAAI,SAAS,EAAE,IAAI,CAAC;QACpB,IAAI,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC;QAE1B,IAAI,qBAAqB;YACvB,QAAQ,IAAI,CAAC;gBAAE,GAAG,KAAK,CAAC;gBAAE,GAAG,KAAK,CAAC;YAAC;QACtC;QAEA,OAAO,GAAG,IAAI;QACd,aAAa,SAAS,SAAS;IACjC;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,SAAU,IAAI;QAC3C,IAAI,SAAS,EAAE,IAAI,CAAC;QACpB,IAAI,IAAI,KAAK,CAAC;QACd,IAAI,SAAS,EAAE,IAAI,CAAC;QACpB,MAAM,CAAC,KAAK,IAAI;QAChB,aAAa,SAAS,SAAS;IACjC;IAEA,EAAE,UAAU,CAAC,MAAM,CAAC;IAEpB,OAAO;AACT;AAEA,SAAS,WAAW,CAAC,EAAE,QAAQ;IAC7B,IAAI,WAAW,IAAI,6JAAA,CAAA,QAAK;IACxB,IAAI,QAAQ;IACZ,IAAI,SAAS;IAEb,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,SAAS,OAAO,CAAC,GAAG;YAAE,GAAG;YAAG,IAAI;YAAG,KAAK;QAAE;IAC5C;IAEA,0EAA0E;IAC1E,uCAAuC;IACvC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,aAAa,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK;QAC5C,IAAI,SAAS,SAAS;QACtB,IAAI,aAAa,aAAa;QAC9B,SAAS,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;QAC3B,SAAS,KAAK,GAAG,CAAC,QAAS,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI;QACrD,QAAQ,KAAK,GAAG,CAAC,OAAQ,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI;IACvD;IAEA,IAAI,UAAU,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,SAAS,QAAQ,GAAG,GAAG,CAAC;QAC5C,OAAO,IAAI,iKAAA,CAAA,OAAI;IACjB;IACA,IAAI,UAAU,QAAQ;IAEtB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,SAAS,KAAK,IAAI,SAAU,CAAC;QACrC,aAAa,SAAS,SAAS,SAAS,IAAI,CAAC;IAC/C;IAEA,OAAO;QAAE,OAAO;QAAU,SAAS;QAAS,SAAS;IAAQ;AAC/D;AAEA,SAAS,aAAa,OAAO,EAAE,OAAO,EAAE,KAAK;IAC3C,IAAI,CAAC,MAAM,GAAG,EAAE;QACd,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;IACrB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QACvB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC;IACtC,OAAO;QACL,OAAO,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC;IACrD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/acyclic.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { greedyFAS } from './greedy-fas.js';\n\nexport { run, undo };\n\nfunction run(g) {\n  var fas = g.graph().acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId('rev'));\n  });\n\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (_.has(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (_.has(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;;;;AAIA,SAAS,IAAI,CAAC;IACZ,IAAI,MAAM,EAAE,KAAK,GAAG,SAAS,KAAK,WAAW,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE,GAAG,SAAS,MAAM,OAAO;IAChF,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,SAAU,CAAC;QACxB,IAAI,QAAQ,EAAE,IAAI,CAAC;QACnB,EAAE,UAAU,CAAC;QACb,MAAM,WAAW,GAAG,EAAE,IAAI;QAC1B,MAAM,QAAQ,GAAG;QACjB,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;IACxC;IAEA,SAAS,SAAS,CAAC;QACjB,OAAO,SAAU,CAAC;YAChB,OAAO,EAAE,IAAI,CAAC,GAAG,MAAM;QACzB;IACF;AACF;AAEA,SAAS,OAAO,CAAC;IACf,IAAI,MAAM,EAAE;IACZ,IAAI,QAAQ,CAAC;IACb,IAAI,UAAU,CAAC;IAEf,SAAS,IAAI,CAAC;QACZ,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;YACrB;QACF;QACA,OAAO,CAAC,EAAE,GAAG;QACb,KAAK,CAAC,EAAE,GAAG;QACX,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,CAAC,IAAI,SAAU,CAAC;YAClC,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,OAAO,EAAE,CAAC,GAAG;gBACrB,IAAI,IAAI,CAAC;YACX,OAAO;gBACL,IAAI,EAAE,CAAC;YACT;QACF;QACA,OAAO,KAAK,CAAC,EAAE;IACjB;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI;IACrB,OAAO;AACT;AAEA,SAAS,KAAK,CAAC;IACb,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,QAAQ,EAAE,IAAI,CAAC;QACnB,IAAI,MAAM,QAAQ,EAAE;YAClB,EAAE,UAAU,CAAC;YAEb,IAAI,cAAc,MAAM,WAAW;YACnC,OAAO,MAAM,QAAQ;YACrB,OAAO,MAAM,WAAW;YACxB,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO;QAC7B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/util.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\n\nexport {\n  addDummyNode,\n  simplify,\n  asNonCompoundGraph,\n  successorWeights,\n  predecessorWeights,\n  intersectRect,\n  buildLayerMatrix,\n  normalizeRanks,\n  removeEmptyRanks,\n  addBorderNode,\n  maxRank,\n  partition,\n  time,\n  notime,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen),\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error('Not possible to find intersection inside of the rectangle');\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    })\n  );\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, 'rank')) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    })\n  );\n\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0,\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(\n    _.map(g.nodes(), function (v) {\n      var rank = g.node(v).rank;\n      if (!_.isUndefined(rank)) {\n        return rank;\n      }\n    })\n  );\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + ' time: ' + (_.now() - start) + 'ms');\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;;;;AAmBA;;CAEC,GACD,SAAS,aAAa,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IACxC,IAAI;IACJ,GAAG;QACD,IAAI,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;IACjB,QAAS,EAAE,OAAO,CAAC,GAAI;IAEvB,MAAM,KAAK,GAAG;IACd,EAAE,OAAO,CAAC,GAAG;IACb,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,SAAS,CAAC;IACjB,IAAI,aAAa,IAAI,6JAAA,CAAA,QAAK,GAAG,QAAQ,CAAC,EAAE,KAAK;IAC7C,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,WAAW,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC;IAC/B;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,cAAc,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK;YAAE,QAAQ;YAAG,QAAQ;QAAE;QACtE,IAAI,QAAQ,EAAE,IAAI,CAAC;QACnB,WAAW,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;YAC3B,QAAQ,YAAY,MAAM,GAAG,MAAM,MAAM;YACzC,QAAQ,KAAK,GAAG,CAAC,YAAY,MAAM,EAAE,MAAM,MAAM;QACnD;IACF;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAC;IAC3B,IAAI,aAAa,IAAI,6JAAA,CAAA,QAAK,CAAC;QAAE,YAAY,EAAE,YAAY;IAAG,GAAG,QAAQ,CAAC,EAAE,KAAK;IAC7E,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM,EAAE;YACzB,WAAW,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC;QAC/B;IACF;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,WAAW,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC;IAC/B;IACA,OAAO;AACT;AAEA,SAAS,iBAAiB,CAAC;IACzB,IAAI,YAAY,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC1C,IAAI,OAAO,CAAC;QACZ,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,CAAC,IAAI,SAAU,CAAC;YAClC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM;QACjD;QACA,OAAO;IACT;IACA,OAAO,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EAAE,EAAE,KAAK,IAAI;AAChC;AAEA,SAAS,mBAAmB,CAAC;IAC3B,IAAI,YAAY,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC1C,IAAI,QAAQ,CAAC;QACb,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,OAAO,CAAC,IAAI,SAAU,CAAC;YACjC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM;QACnD;QACA,OAAO;IACT;IACA,OAAO,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EAAE,EAAE,KAAK,IAAI;AAChC;AAEA;;;CAGC,GACD,SAAS,cAAc,IAAI,EAAE,KAAK;IAChC,IAAI,IAAI,KAAK,CAAC;IACd,IAAI,IAAI,KAAK,CAAC;IAEd,yCAAyC;IACzC,6EAA6E;IAC7E,IAAI,KAAK,MAAM,CAAC,GAAG;IACnB,IAAI,KAAK,MAAM,CAAC,GAAG;IACnB,IAAI,IAAI,KAAK,KAAK,GAAG;IACrB,IAAI,IAAI,KAAK,MAAM,GAAG;IAEtB,IAAI,CAAC,MAAM,CAAC,IAAI;QACd,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,IAAI;IACR,IAAI,KAAK,GAAG,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,MAAM,GAAG;QACvC,yCAAyC;QACzC,IAAI,KAAK,GAAG;YACV,IAAI,CAAC;QACP;QACA,KAAK,AAAC,IAAI,KAAM;QAChB,KAAK;IACP,OAAO;QACL,yCAAyC;QACzC,IAAI,KAAK,GAAG;YACV,IAAI,CAAC;QACP;QACA,KAAK;QACL,KAAK,AAAC,IAAI,KAAM;IAClB;IAEA,OAAO;QAAE,GAAG,IAAI;QAAI,GAAG,IAAI;IAAG;AAChC;AAEA;;;CAGC,GACD,SAAS,iBAAiB,CAAC;IACzB,IAAI,WAAW,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,QAAQ,KAAK,IAAI;QAC5C,OAAO,EAAE;IACX;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,OAAO;YACxB,QAAQ,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;QAC/B;IACF;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,eAAe,CAAC;IACvB,IAAI,MAAM,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EACZ,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC1B,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI;IACvB;IAEF,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,SAAS;YACvB,KAAK,IAAI,IAAI;QACf;IACF;AACF;AAEA,SAAS,iBAAiB,CAAC;IACzB,sDAAsD;IACtD,IAAI,SAAS,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EACf,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC1B,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI;IACvB;IAGF,IAAI,SAAS,EAAE;IACf,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG;QAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACjB,MAAM,CAAC,KAAK,GAAG,EAAE;QACnB;QACA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;IACpB;IAEA,IAAI,QAAQ;IACZ,IAAI,iBAAiB,EAAE,KAAK,GAAG,cAAc;IAC7C,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,SAAU,EAAE,EAAE,CAAC;QAC/B,IAAI,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,OAAO,IAAI,mBAAmB,GAAG;YACjD,EAAE;QACJ,OAAO,IAAI,OAAO;YAChB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,IAAI,SAAU,CAAC;gBACvB,EAAE,IAAI,CAAC,GAAG,IAAI,IAAI;YACpB;QACF;IACF;AACF;AAEA,SAAS,cAAc,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK;IAC3C,IAAI,OAAO;QACT,OAAO;QACP,QAAQ;IACV;IACA,IAAI,UAAU,MAAM,IAAI,GAAG;QACzB,KAAK,IAAI,GAAG;QACZ,KAAK,KAAK,GAAG;IACf;IACA,OAAO,aAAa,GAAG,UAAU,MAAM;AACzC;AAEA,SAAS,QAAQ,CAAC;IAChB,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EACT,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC1B,IAAI,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI;QACzB,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,OAAO;YACxB,OAAO;QACT;IACF;AAEJ;AAEA;;;;CAIC,GACD,SAAS,UAAU,UAAU,EAAE,EAAE;IAC/B,IAAI,SAAS;QAAE,KAAK,EAAE;QAAE,KAAK,EAAE;IAAC;IAChC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,YAAY,SAAU,KAAK;QACnC,IAAI,GAAG,QAAQ;YACb,OAAO,GAAG,CAAC,IAAI,CAAC;QAClB,OAAO;YACL,OAAO,GAAG,CAAC,IAAI,CAAC;QAClB;IACF;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,KAAK,IAAI,EAAE,EAAE;IACpB,IAAI,QAAQ,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD;IAChB,IAAI;QACF,OAAO;IACT,SAAU;QACR,QAAQ,GAAG,CAAC,OAAO,YAAY,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,MAAM,KAAK,IAAI;IACrD;AACF;AAEA,SAAS,OAAO,IAAI,EAAE,EAAE;IACtB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/add-border-segments.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { addBorderSegments };\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (_.has(node, 'minRank')) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, 'borderLeft', '_bl', v, node, rank);\n        addBorderNode(g, 'borderRight', '_br', v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, 'border', label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;;AAIA,SAAS,kBAAkB,CAAC;IAC1B,SAAS,IAAI,CAAC;QACZ,IAAI,WAAW,EAAE,QAAQ,CAAC;QAC1B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,SAAS,MAAM,EAAE;YACnB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU;QACtB;QAEA,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,YAAY;YAC1B,KAAK,UAAU,GAAG,EAAE;YACpB,KAAK,WAAW,GAAG,EAAE;YACrB,IAAK,IAAI,OAAO,KAAK,OAAO,EAAE,UAAU,KAAK,OAAO,GAAG,GAAG,OAAO,SAAS,EAAE,KAAM;gBAChF,cAAc,GAAG,cAAc,OAAO,GAAG,MAAM;gBAC/C,cAAc,GAAG,eAAe,OAAO,GAAG,MAAM;YAClD;QACF;IACF;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,IAAI;AAC1B;AAEA,SAAS,cAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI;IACtD,IAAI,QAAQ;QAAE,OAAO;QAAG,QAAQ;QAAG,MAAM;QAAM,YAAY;IAAK;IAChE,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;IACjC,IAAI,OAAO,CAAA,GAAA,yJAAA,CAAA,eAAiB,AAAD,EAAE,GAAG,UAAU,OAAO;IACjD,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG;IACrB,EAAE,SAAS,CAAC,MAAM;IAClB,IAAI,MAAM;QACR,EAAE,OAAO,CAAC,MAAM,MAAM;YAAE,QAAQ;QAAE;IACpC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/coordinate-system.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { adjust, undo };\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'bt' || rankDir === 'rl') {\n    reverseY(g);\n  }\n\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (_.has(edge, 'y')) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (_.has(edge, 'x')) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAIA,SAAS,OAAO,CAAC;IACf,IAAI,UAAU,EAAE,KAAK,GAAG,OAAO,CAAC,WAAW;IAC3C,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,gBAAgB;IAClB;AACF;AAEA,SAAS,KAAK,CAAC;IACb,IAAI,UAAU,EAAE,KAAK,GAAG,OAAO,CAAC,WAAW;IAC3C,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,SAAS;IACX;IAEA,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,OAAO;QACP,gBAAgB;IAClB;AACF;AAEA,SAAS,gBAAgB,CAAC;IACxB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,mBAAmB,EAAE,IAAI,CAAC;IAC5B;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,mBAAmB,EAAE,IAAI,CAAC;IAC5B;AACF;AAEA,SAAS,mBAAmB,KAAK;IAC/B,IAAI,IAAI,MAAM,KAAK;IACnB,MAAM,KAAK,GAAG,MAAM,MAAM;IAC1B,MAAM,MAAM,GAAG;AACjB;AAEA,SAAS,SAAS,CAAC;IACjB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,YAAY,EAAE,IAAI,CAAC;IACrB;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,MAAM,EAAE;QACvB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,MAAM;YACpB,YAAY;QACd;IACF;AACF;AAEA,SAAS,YAAY,KAAK;IACxB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;AACpB;AAEA,SAAS,OAAO,CAAC;IACf,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,UAAU,EAAE,IAAI,CAAC;IACnB;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,MAAM,EAAE;QACvB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,MAAM;YACpB,UAAU;QACZ;IACF;AACF;AAEA,SAAS,UAAU,KAAK;IACtB,IAAI,IAAI,MAAM,CAAC;IACf,MAAM,CAAC,GAAG,MAAM,CAAC;IACjB,MAAM,CAAC,GAAG;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/normalize.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, undo };\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\n\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  var dummy, attrs, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank,\n    };\n    dummy = util.addDummyNode(g, 'edge', attrs, '_d');\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      // @ts-expect-error\n      attrs.dummy = 'edge-label';\n      // @ts-expect-error\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === 'edge-label') {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAIA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,IAAI,CAAC;IACZ,EAAE,KAAK,GAAG,WAAW,GAAG,EAAE;IAC1B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,IAAI;QACjC,cAAc,GAAG;IACnB;AACF;AAEA,SAAS,cAAc,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,EAAE,CAAC;IACX,IAAI,QAAQ,EAAE,IAAI,CAAC,GAAG,IAAI;IAC1B,IAAI,IAAI,EAAE,CAAC;IACX,IAAI,QAAQ,EAAE,IAAI,CAAC,GAAG,IAAI;IAC1B,IAAI,OAAO,EAAE,IAAI;IACjB,IAAI,YAAY,EAAE,IAAI,CAAC;IACvB,IAAI,YAAY,UAAU,SAAS;IAEnC,IAAI,UAAU,QAAQ,GAAG;IAEzB,EAAE,UAAU,CAAC;IAEb,IAAI,OAAO,OAAO;IAClB,IAAK,IAAI,GAAG,EAAE,OAAO,QAAQ,OAAO,EAAE,GAAG,EAAE,MAAO;QAChD,UAAU,MAAM,GAAG,EAAE;QACrB,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,WAAW;YACX,SAAS;YACT,MAAM;QACR;QACA,QAAQ,CAAA,GAAA,yJAAA,CAAA,eAAiB,AAAD,EAAE,GAAG,QAAQ,OAAO;QAC5C,IAAI,UAAU,WAAW;YACvB,MAAM,KAAK,GAAG,UAAU,KAAK;YAC7B,MAAM,MAAM,GAAG,UAAU,MAAM;YAC/B,mBAAmB;YACnB,MAAM,KAAK,GAAG;YACd,mBAAmB;YACnB,MAAM,QAAQ,GAAG,UAAU,QAAQ;QACrC;QACA,EAAE,OAAO,CAAC,GAAG,OAAO;YAAE,QAAQ,UAAU,MAAM;QAAC,GAAG;QAClD,IAAI,MAAM,GAAG;YACX,EAAE,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC;QAC7B;QACA,IAAI;IACN;IAEA,EAAE,OAAO,CAAC,GAAG,GAAG;QAAE,QAAQ,UAAU,MAAM;IAAC,GAAG;AAChD;AAEA,SAAS,KAAK,CAAC;IACb,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,GAAG,WAAW,EAAE,SAAU,CAAC;QAC1C,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,YAAY,KAAK,SAAS;QAC9B,IAAI;QACJ,EAAE,OAAO,CAAC,KAAK,OAAO,EAAE;QACxB,MAAO,KAAK,KAAK,CAAE;YACjB,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE;YACtB,EAAE,UAAU,CAAC;YACb,UAAU,MAAM,CAAC,IAAI,CAAC;gBAAE,GAAG,KAAK,CAAC;gBAAE,GAAG,KAAK,CAAC;YAAC;YAC7C,IAAI,KAAK,KAAK,KAAK,cAAc;gBAC/B,UAAU,CAAC,GAAG,KAAK,CAAC;gBACpB,UAAU,CAAC,GAAG,KAAK,CAAC;gBACpB,UAAU,KAAK,GAAG,KAAK,KAAK;gBAC5B,UAAU,MAAM,GAAG,KAAK,MAAM;YAChC;YACA,IAAI;YACJ,OAAO,EAAE,IAAI,CAAC;QAChB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/rank/util.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { longestPath, slack };\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (_.has(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(\n      _.map(g.outEdges(v), function (e) {\n        return dfs(e.w) - g.edge(e).minlen;\n      })\n    );\n\n    if (\n      rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n      rank === undefined || // return value of _.map([]) for Lodash 4\n      rank === null\n    ) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAIA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,YAAY,CAAC;IACpB,IAAI,UAAU,CAAC;IAEf,SAAS,IAAI,CAAC;QACZ,IAAI,QAAQ,EAAE,IAAI,CAAC;QACnB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;YACrB,OAAO,MAAM,IAAI;QACnB;QACA,OAAO,CAAC,EAAE,GAAG;QAEb,IAAI,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EACb,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,QAAQ,CAAC,IAAI,SAAU,CAAC;YAC9B,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM;QACpC;QAGF,IACE,SAAS,OAAO,iBAAiB,IAAI,yCAAyC;QAC9E,SAAS,aAAa,yCAAyC;QAC/D,SAAS,MACT;YACA,gCAAgC;YAChC,OAAO;QACT;QAEA,OAAQ,MAAM,IAAI,GAAG;IACvB;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,OAAO,IAAI;AACzB;AAEA;;;CAGC,GACD,SAAS,MAAM,CAAC,EAAE,CAAC;IACjB,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,MAAM;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/rank/feasible-tree.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport { slack } from './util.js';\n\nexport { feasibleTree };\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function (e) {\n      var edgeV = e.v,\n        w = v === edgeV ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function (e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function (v) {\n    g.node(v).rank += delta;\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,aAAa,CAAC;IACrB,IAAI,IAAI,IAAI,6JAAA,CAAA,QAAK,CAAC;QAAE,UAAU;IAAM;IAEpC,qDAAqD;IACrD,IAAI,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE;IACxB,IAAI,OAAO,EAAE,SAAS;IACtB,EAAE,OAAO,CAAC,OAAO,CAAC;IAElB,IAAI,MAAM;IACV,MAAO,UAAU,GAAG,KAAK,KAAM;QAC7B,OAAO,iBAAiB,GAAG;QAC3B,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,QAAQ,CAAC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG;QACvD,WAAW,GAAG,GAAG;IACnB;IAEA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,SAAS,IAAI,CAAC;QACZ,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,SAAS,CAAC,IAAI,SAAU,CAAC;YACnC,IAAI,QAAQ,EAAE,CAAC,EACb,IAAI,MAAM,QAAQ,EAAE,CAAC,GAAG;YAC1B,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,IAAI;gBACjC,EAAE,OAAO,CAAC,GAAG,CAAC;gBACd,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;gBACjB,IAAI;YACN;QACF;IACF;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI;IACrB,OAAO,EAAE,SAAS;AACpB;AAEA;;;CAGC,GACD,SAAS,iBAAiB,CAAC,EAAE,CAAC;IAC5B,OAAO,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QACnC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG;YACrC,OAAO,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG;QAClB;IACF;AACF;AAEA,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK;IAC7B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,EAAE,IAAI,CAAC,GAAG,IAAI,IAAI;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/components.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { components };\n\nfunction components(g) {\n  var visited = {};\n  var cmpts = [];\n  var cmpt;\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    cmpt.push(v);\n    _.each(g.successors(v), dfs);\n    _.each(g.predecessors(v), dfs);\n  }\n\n  _.each(g.nodes(), function (v) {\n    cmpt = [];\n    dfs(v);\n    if (cmpt.length) {\n      cmpts.push(cmpt);\n    }\n  });\n\n  return cmpts;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;;AAIA,SAAS,WAAW,CAAC;IACnB,IAAI,UAAU,CAAC;IACf,IAAI,QAAQ,EAAE;IACd,IAAI;IAEJ,SAAS,IAAI,CAAC;QACZ,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;QACvB,OAAO,CAAC,EAAE,GAAG;QACb,KAAK,IAAI,CAAC;QACV,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,EAAE,UAAU,CAAC,IAAI;QACxB,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,EAAE,YAAY,CAAC,IAAI;IAC5B;IAEA,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC3B,OAAO,EAAE;QACT,IAAI;QACJ,IAAI,KAAK,MAAM,EAAE;YACf,MAAM,IAAI,CAAC;QACb;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/data/priority-queue.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { PriorityQueue };\n\n/**\n * A min-priority queue data structure. This algorithm is derived from <PERSON><PERSON><PERSON>,\n * et al., \"Introduction to Algorithms\". The basic idea of a min-priority\n * queue is that you can efficiently (in O(1) time) get the smallest key in\n * the queue. Adding and removing elements takes O(log n) time. A key can\n * have its priority decreased in O(log n) time.\n */\nclass PriorityQueue {\n  constructor() {\n    this._arr = [];\n    this._keyIndices = {};\n  }\n  /**\n   * Returns the number of elements in the queue. Takes `O(1)` time.\n   */\n  size() {\n    return this._arr.length;\n  }\n  /**\n   * Returns the keys that are in the queue. Takes `O(n)` time.\n   */\n  keys() {\n    return this._arr.map(function (x) {\n      return x.key;\n    });\n  }\n  /**\n   * Returns `true` if **key** is in the queue and `false` if not.\n   */\n  has(key) {\n    return _.has(this._keyIndices, key);\n  }\n  /**\n   * Returns the priority for **key**. If **key** is not present in the queue\n   * then this function returns `undefined`. Takes `O(1)` time.\n   *\n   * @param {Object} key\n   */\n  priority(key) {\n    var index = this._keyIndices[key];\n    if (index !== undefined) {\n      return this._arr[index].priority;\n    }\n  }\n  /**\n   * Returns the key for the minimum element in this queue. If the queue is\n   * empty this function throws an Error. Takes `O(1)` time.\n   */\n  min() {\n    if (this.size() === 0) {\n      throw new Error('Queue underflow');\n    }\n    return this._arr[0].key;\n  }\n  /**\n   * Inserts a new key into the priority queue. If the key already exists in\n   * the queue this function returns `false`; otherwise it will return `true`.\n   * Takes `O(n)` time.\n   *\n   * @param {Object} key the key to add\n   * @param {Number} priority the initial priority for the key\n   */\n  add(key, priority) {\n    var keyIndices = this._keyIndices;\n    key = String(key);\n    if (!_.has(keyIndices, key)) {\n      var arr = this._arr;\n      var index = arr.length;\n      keyIndices[key] = index;\n      arr.push({ key: key, priority: priority });\n      this._decrease(index);\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Removes and returns the smallest key in the queue. Takes `O(log n)` time.\n   */\n  removeMin() {\n    this._swap(0, this._arr.length - 1);\n    var min = this._arr.pop();\n    delete this._keyIndices[min.key];\n    this._heapify(0);\n    return min.key;\n  }\n  /**\n   * Decreases the priority for **key** to **priority**. If the new priority is\n   * greater than the previous priority, this function will throw an Error.\n   *\n   * @param {Object} key the key for which to raise priority\n   * @param {Number} priority the new priority for the key\n   */\n  decrease(key, priority) {\n    var index = this._keyIndices[key];\n    if (priority > this._arr[index].priority) {\n      throw new Error(\n        'New priority is greater than current priority. ' +\n          'Key: ' +\n          key +\n          ' Old: ' +\n          this._arr[index].priority +\n          ' New: ' +\n          priority\n      );\n    }\n    this._arr[index].priority = priority;\n    this._decrease(index);\n  }\n  _heapify(i) {\n    var arr = this._arr;\n    var l = 2 * i;\n    var r = l + 1;\n    var largest = i;\n    if (l < arr.length) {\n      largest = arr[l].priority < arr[largest].priority ? l : largest;\n      if (r < arr.length) {\n        largest = arr[r].priority < arr[largest].priority ? r : largest;\n      }\n      if (largest !== i) {\n        this._swap(i, largest);\n        this._heapify(largest);\n      }\n    }\n  }\n  _decrease(index) {\n    var arr = this._arr;\n    var priority = arr[index].priority;\n    var parent;\n    while (index !== 0) {\n      parent = index >> 1;\n      if (arr[parent].priority < priority) {\n        break;\n      }\n      this._swap(index, parent);\n      index = parent;\n    }\n  }\n  _swap(i, j) {\n    var arr = this._arr;\n    var keyIndices = this._keyIndices;\n    var origArrI = arr[i];\n    var origArrJ = arr[j];\n    arr[i] = origArrJ;\n    arr[j] = origArrI;\n    keyIndices[origArrJ.key] = i;\n    keyIndices[origArrI.key] = j;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA;;;;;;CAMC,GACD,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,WAAW,GAAG,CAAC;IACtB;IACA;;GAEC,GACD,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;IACzB;IACA;;GAEC,GACD,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAU,CAAC;YAC9B,OAAO,EAAE,GAAG;QACd;IACF;IACA;;GAEC,GACD,IAAI,GAAG,EAAE;QACP,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE;IACjC;IACA;;;;;GAKC,GACD,SAAS,GAAG,EAAE;QACZ,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;QAClC;IACF;IACA;;;GAGC,GACD,MAAM;QACJ,IAAI,IAAI,CAAC,IAAI,OAAO,GAAG;YACrB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;IACzB;IACA;;;;;;;GAOC,GACD,IAAI,GAAG,EAAE,QAAQ,EAAE;QACjB,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,MAAM,OAAO;QACb,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,YAAY,MAAM;YAC3B,IAAI,MAAM,IAAI,CAAC,IAAI;YACnB,IAAI,QAAQ,IAAI,MAAM;YACtB,UAAU,CAAC,IAAI,GAAG;YAClB,IAAI,IAAI,CAAC;gBAAE,KAAK;gBAAK,UAAU;YAAS;YACxC,IAAI,CAAC,SAAS,CAAC;YACf,OAAO;QACT;QACA,OAAO;IACT;IACA;;GAEC,GACD,YAAY;QACV,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QACjC,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC;QACd,OAAO,IAAI,GAAG;IAChB;IACA;;;;;;GAMC,GACD,SAAS,GAAG,EAAE,QAAQ,EAAE;QACtB,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACxC,MAAM,IAAI,MACR,oDACE,UACA,MACA,WACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GACzB,WACA;QAEN;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG;QAC5B,IAAI,CAAC,SAAS,CAAC;IACjB;IACA,SAAS,CAAC,EAAE;QACV,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,IAAI,IAAI;QACZ,IAAI,IAAI,IAAI;QACZ,IAAI,UAAU;QACd,IAAI,IAAI,IAAI,MAAM,EAAE;YAClB,UAAU,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI;YACxD,IAAI,IAAI,IAAI,MAAM,EAAE;gBAClB,UAAU,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI;YAC1D;YACA,IAAI,YAAY,GAAG;gBACjB,IAAI,CAAC,KAAK,CAAC,GAAG;gBACd,IAAI,CAAC,QAAQ,CAAC;YAChB;QACF;IACF;IACA,UAAU,KAAK,EAAE;QACf,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,WAAW,GAAG,CAAC,MAAM,CAAC,QAAQ;QAClC,IAAI;QACJ,MAAO,UAAU,EAAG;YAClB,SAAS,SAAS;YAClB,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,GAAG,UAAU;gBACnC;YACF;YACA,IAAI,CAAC,KAAK,CAAC,OAAO;YAClB,QAAQ;QACV;IACF;IACA,MAAM,CAAC,EAAE,CAAC,EAAE;QACV,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,WAAW,GAAG,CAAC,EAAE;QACrB,IAAI,WAAW,GAAG,CAAC,EAAE;QACrB,GAAG,CAAC,EAAE,GAAG;QACT,GAAG,CAAC,EAAE,GAAG;QACT,UAAU,CAAC,SAAS,GAAG,CAAC,GAAG;QAC3B,UAAU,CAAC,SAAS,GAAG,CAAC,GAAG;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/dijkstra.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\n\nexport { dijkstra };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(\n    g,\n    String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      }\n  );\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\n        'dijkstra does not allow negative edge weights. ' +\n          'Bad edge: ' +\n          edge +\n          ' Weight: ' +\n          weight\n      );\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAIA,IAAI,sBAAsB,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;AAErC,SAAS,SAAS,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;IAC3C,OAAO,YACL,GACA,OAAO,SACP,YAAY,qBACZ,UACE,SAAU,CAAC;QACT,OAAO,EAAE,QAAQ,CAAC;IACpB;AAEN;AAEA,SAAS,YAAY,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;IAC9C,IAAI,UAAU,CAAC;IACf,IAAI,KAAK,IAAI,iLAAA,CAAA,gBAAa;IAC1B,IAAI,GAAG;IAEP,IAAI,kBAAkB,SAAU,IAAI;QAClC,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC;QACtC,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,SAAS,SAAS;QACtB,IAAI,WAAW,OAAO,QAAQ,GAAG;QAEjC,IAAI,SAAS,GAAG;YACd,MAAM,IAAI,MACR,oDACE,eACA,OACA,cACA;QAEN;QAEA,IAAI,WAAW,OAAO,QAAQ,EAAE;YAC9B,OAAO,QAAQ,GAAG;YAClB,OAAO,WAAW,GAAG;YACrB,GAAG,QAAQ,CAAC,GAAG;QACjB;IACF;IAEA,EAAE,KAAK,GAAG,OAAO,CAAC,SAAU,CAAC;QAC3B,IAAI,WAAW,MAAM,SAAS,IAAI,OAAO,iBAAiB;QAC1D,OAAO,CAAC,EAAE,GAAG;YAAE,UAAU;QAAS;QAClC,GAAG,GAAG,CAAC,GAAG;IACZ;IAEA,MAAO,GAAG,IAAI,KAAK,EAAG;QACpB,IAAI,GAAG,SAAS;QAChB,SAAS,OAAO,CAAC,EAAE;QACnB,IAAI,OAAO,QAAQ,KAAK,OAAO,iBAAiB,EAAE;YAChD;QACF;QAEA,OAAO,GAAG,OAAO,CAAC;IACpB;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/dijkstra-all.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { dijkstra } from './dijkstra.js';\n\nexport { dijkstraAll };\n\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return _.transform(\n    g.nodes(),\n    function (acc, v) {\n      acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n    },\n    {}\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAIA,SAAS,YAAY,CAAC,EAAE,UAAU,EAAE,QAAQ;IAC1C,OAAO,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EACf,EAAE,KAAK,IACP,SAAU,GAAG,EAAE,CAAC;QACd,GAAG,CAAC,EAAE,GAAG,CAAA,GAAA,uKAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,GAAG,YAAY;IACtC,GACA,CAAC;AAEL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/tarjan.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { tarjan };\n\nfunction tarjan(g) {\n  var index = 0;\n  var stack = [];\n  var visited = {}; // node id -> { onStack, lowlink, index }\n  var results = [];\n\n  function dfs(v) {\n    var entry = (visited[v] = {\n      onStack: true,\n      lowlink: index,\n      index: index++,\n    });\n    stack.push(v);\n\n    g.successors(v).forEach(function (w) {\n      if (!_.has(visited, w)) {\n        dfs(w);\n        entry.lowlink = Math.min(entry.lowlink, visited[w].lowlink);\n      } else if (visited[w].onStack) {\n        entry.lowlink = Math.min(entry.lowlink, visited[w].index);\n      }\n    });\n\n    if (entry.lowlink === entry.index) {\n      var cmpt = [];\n      var w;\n      do {\n        w = stack.pop();\n        visited[w].onStack = false;\n        cmpt.push(w);\n      } while (v !== w);\n      results.push(cmpt);\n    }\n  }\n\n  g.nodes().forEach(function (v) {\n    if (!_.has(visited, v)) {\n      dfs(v);\n    }\n  });\n\n  return results;\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,SAAS,OAAO,CAAC;IACf,IAAI,QAAQ;IACZ,IAAI,QAAQ,EAAE;IACd,IAAI,UAAU,CAAC,GAAG,yCAAyC;IAC3D,IAAI,UAAU,EAAE;IAEhB,SAAS,IAAI,CAAC;QACZ,IAAI,QAAS,OAAO,CAAC,EAAE,GAAG;YACxB,SAAS;YACT,SAAS;YACT,OAAO;QACT;QACA,MAAM,IAAI,CAAC;QAEX,EAAE,UAAU,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YACjC,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;gBACtB,IAAI;gBACJ,MAAM,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;YAC5D,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;gBAC7B,MAAM,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK;YAC1D;QACF;QAEA,IAAI,MAAM,OAAO,KAAK,MAAM,KAAK,EAAE;YACjC,IAAI,OAAO,EAAE;YACb,IAAI;YACJ,GAAG;gBACD,IAAI,MAAM,GAAG;gBACb,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG;gBACrB,KAAK,IAAI,CAAC;YACZ,QAAS,MAAM,EAAG;YAClB,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,EAAE,KAAK,GAAG,OAAO,CAAC,SAAU,CAAC;QAC3B,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;YACtB,IAAI;QACN;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/find-cycles.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { tarjan } from './tarjan.js';\n\nexport { findCycles };\n\nfunction findCycles(g) {\n  return _.filter(tarjan(g), function (cmpt) {\n    return cmpt.length > 1 || (cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]));\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAIA,SAAS,WAAW,CAAC;IACnB,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,qKAAA,CAAA,SAAM,AAAD,EAAE,IAAI,SAAU,IAAI;QACvC,OAAO,KAAK,MAAM,GAAG,KAAM,KAAK,MAAM,KAAK,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IAC5E;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/floyd-warshall.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { floyd<PERSON>ars<PERSON> };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(\n    g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      }\n  );\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function (v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function (w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function (edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function (k) {\n    var rowK = results[k];\n    nodes.forEach(function (i) {\n      var rowI = results[i];\n      nodes.forEach(function (j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,IAAI,sBAAsB,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;AAErC,SAAS,cAAc,CAAC,EAAE,QAAQ,EAAE,MAAM;IACxC,OAAO,iBACL,GACA,YAAY,qBACZ,UACE,SAAU,CAAC;QACT,OAAO,EAAE,QAAQ,CAAC;IACpB;AAEN;AAEA,SAAS,iBAAiB,CAAC,EAAE,QAAQ,EAAE,MAAM;IAC3C,IAAI,UAAU,CAAC;IACf,IAAI,QAAQ,EAAE,KAAK;IAEnB,MAAM,OAAO,CAAC,SAAU,CAAC;QACvB,OAAO,CAAC,EAAE,GAAG,CAAC;QACd,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG;YAAE,UAAU;QAAE;QAC9B,MAAM,OAAO,CAAC,SAAU,CAAC;YACvB,IAAI,MAAM,GAAG;gBACX,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG;oBAAE,UAAU,OAAO,iBAAiB;gBAAC;YACvD;QACF;QACA,OAAO,GAAG,OAAO,CAAC,SAAU,IAAI;YAC9B,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC;YACtC,IAAI,IAAI,SAAS;YACjB,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG;gBAAE,UAAU;gBAAG,aAAa;YAAE;QAChD;IACF;IAEA,MAAM,OAAO,CAAC,SAAU,CAAC;QACvB,IAAI,OAAO,OAAO,CAAC,EAAE;QACrB,MAAM,OAAO,CAAC,SAAU,CAAC;YACvB,IAAI,OAAO,OAAO,CAAC,EAAE;YACrB,MAAM,OAAO,CAAC,SAAU,CAAC;gBACvB,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChB,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChB,IAAI,cAAc,GAAG,QAAQ,GAAG,GAAG,QAAQ;gBAC3C,IAAI,cAAc,GAAG,QAAQ,EAAE;oBAC7B,GAAG,QAAQ,GAAG;oBACd,GAAG,WAAW,GAAG,GAAG,WAAW;gBACjC;YACF;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/topsort.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { topsort, CycleException };\n\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (_.has(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!_.has(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;;AAIA,QAAQ,cAAc,GAAG;AAEzB,SAAS,QAAQ,CAAC;IAChB,IAAI,UAAU,CAAC;IACf,IAAI,QAAQ,CAAC;IACb,IAAI,UAAU,EAAE;IAEhB,SAAS,MAAM,IAAI;QACjB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,OAAO,OAAO;YACtB,MAAM,IAAI;QACZ;QAEA,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,OAAO;YACzB,KAAK,CAAC,KAAK,GAAG;YACd,OAAO,CAAC,KAAK,GAAG;YAChB,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,EAAE,YAAY,CAAC,OAAO;YAC7B,OAAO,KAAK,CAAC,KAAK;YAClB,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,EAAE,KAAK,IAAI;IAElB,IAAI,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,aAAa,EAAE,SAAS,IAAI;QACrC,MAAM,IAAI;IACZ;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB;AAC3B,eAAe,SAAS,GAAG,IAAI,SAAS,+CAA+C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/is-acyclic.js"], "sourcesContent": ["import { topsort, CycleException } from './topsort.js';\n\nexport { isAcyclic };\n\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,SAAS,UAAU,CAAC;IAClB,IAAI;QACF,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;IACV,EAAE,OAAO,GAAG;QACV,IAAI,aAAa,sKAAA,CAAA,iBAAc,EAAE;YAC/B,OAAO;QACT;QACA,MAAM;IACR;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/dfs.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { dfs };\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error('Graph does not have node: ' + v);\n    }\n\n    doDfs(g, v, order === 'post', visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!_.has(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;;;AAIA;;;;;;;CAOC,GACD,SAAS,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK;IACvB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK;QAClB,KAAK;YAAC;SAAG;IACX;IAEA,IAAI,aAAa,CAAC,EAAE,UAAU,KAAK,EAAE,UAAU,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC;IAEpE,IAAI,MAAM,EAAE;IACZ,IAAI,UAAU,CAAC;IACf,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,IAAI,SAAU,CAAC;QACpB,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI;YACjB,MAAM,IAAI,MAAM,+BAA+B;QACjD;QAEA,MAAM,GAAG,GAAG,UAAU,QAAQ,SAAS,YAAY;IACrD;IACA,OAAO;AACT;AAEA,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG;IACtD,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;QACtB,OAAO,CAAC,EAAE,GAAG;QAEb,IAAI,CAAC,WAAW;YACd,IAAI,IAAI,CAAC;QACX;QACA,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,WAAW,IAAI,SAAU,CAAC;YAC/B,MAAM,GAAG,GAAG,WAAW,SAAS,YAAY;QAC9C;QACA,IAAI,WAAW;YACb,IAAI,IAAI,CAAC;QACX;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/postorder.js"], "sourcesContent": ["import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,SAAS,UAAU,CAAC,EAAE,EAAE;IACtB,OAAO,CAAA,GAAA,kKAAA,CAAA,MAAG,AAAD,EAAE,GAAG,IAAI;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/preorder.js"], "sourcesContent": ["import { dfs } from './dfs.js';\n\nexport { preorder };\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, 'pre');\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,SAAS,SAAS,CAAC,EAAE,EAAE;IACrB,OAAO,CAAA,GAAA,kKAAA,CAAA,MAAG,AAAD,EAAE,GAAG,IAAI;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/prim.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\nimport { Graph } from '../graph.js';\n\nexport { prim };\n\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n\n  _.each(g.nodes(), function (v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (_.has(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error('Input graph is not connected: ' + g);\n    } else {\n      init = true;\n    }\n\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;;AAIA,SAAS,KAAK,CAAC,EAAE,UAAU;IACzB,IAAI,SAAS,IAAI,6JAAA,CAAA,QAAK;IACtB,IAAI,UAAU,CAAC;IACf,IAAI,KAAK,IAAI,iLAAA,CAAA,gBAAa;IAC1B,IAAI;IAEJ,SAAS,gBAAgB,IAAI;QAC3B,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC;QACtC,IAAI,MAAM,GAAG,QAAQ,CAAC;QACtB,IAAI,QAAQ,WAAW;YACrB,IAAI,aAAa,WAAW;YAC5B,IAAI,aAAa,KAAK;gBACpB,OAAO,CAAC,EAAE,GAAG;gBACb,GAAG,QAAQ,CAAC,GAAG;YACjB;QACF;IACF;IAEA,IAAI,EAAE,SAAS,OAAO,GAAG;QACvB,OAAO;IACT;IAEA,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC3B,GAAG,GAAG,CAAC,GAAG,OAAO,iBAAiB;QAClC,OAAO,OAAO,CAAC;IACjB;IAEA,+BAA+B;IAC/B,GAAG,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;IAE1B,IAAI,OAAO;IACX,MAAO,GAAG,IAAI,KAAK,EAAG;QACpB,IAAI,GAAG,SAAS;QAChB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;YACrB,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE;QAC9B,OAAO,IAAI,MAAM;YACf,MAAM,IAAI,MAAM,mCAAmC;QACrD,OAAO;YACL,OAAO;QACT;QAEA,EAAE,SAAS,CAAC,GAAG,OAAO,CAAC;IACzB;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/alg/index.js"], "sourcesContent": ["import { components } from './components.js';\nimport { dijkstra } from './dijkstra.js';\nimport { dijkstraAll } from './dijkstra-all.js';\nimport { findCycles } from './find-cycles.js';\nimport { floydWarshall } from './floyd-warshall.js';\nimport { isAcyclic } from './is-acyclic.js';\nimport { postorder } from './postorder.js';\nimport { preorder } from './preorder.js';\nimport { prim } from './prim.js';\nimport { tarjan } from './tarjan.js';\nimport { topsort } from './topsort.js';\n\nexport {\n  components,\n  dijkstra,\n  dijkstraAll,\n  findCycles,\n  floydWarshall,\n  isAcyclic,\n  postorder,\n  preorder,\n  prim,\n  tarjan,\n  topsort,\n};\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/rank/network-simplex.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport * as alg from '../../graphlib/alg/index.js';\nimport { simplify } from '../util.js';\nimport { feasibleTree } from './feasible-tree.js';\nimport { longestPath, slack } from './util.js';\n\nexport { networkSimplex };\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  longestPath(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = alg.postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function (v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function (e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function (w) {\n    if (!_.has(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function (e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function (edge) {\n    return (\n      flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n      flip !== isDescendant(t, t.node(edge.w), tailLabel)\n    );\n  });\n\n  return _.minBy(candidates, function (edge) {\n    return slack(g, edge);\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function (v) {\n    return !g.node(v).parent;\n  });\n  var vs = alg.preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function (v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAIA,6CAA6C;AAC7C,eAAe,gBAAgB,GAAG;AAClC,eAAe,aAAa,GAAG;AAC/B,eAAe,YAAY,GAAG;AAC9B,eAAe,SAAS,GAAG;AAC3B,eAAe,SAAS,GAAG;AAC3B,eAAe,aAAa,GAAG;AAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCC,GACD,SAAS,eAAe,CAAC;IACvB,IAAI,CAAA,GAAA,yJAAA,CAAA,WAAQ,AAAD,EAAE;IACb,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACZ,IAAI,IAAI,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACrB,iBAAiB;IACjB,cAAc,GAAG;IAEjB,IAAI,GAAG;IACP,MAAQ,IAAI,UAAU,GAAK;QACzB,IAAI,UAAU,GAAG,GAAG;QACpB,cAAc,GAAG,GAAG,GAAG;IACzB;AACF;AAEA;;CAEC,GACD,SAAS,cAAc,CAAC,EAAE,CAAC;IACzB,IAAI,KAAK,CAAA,GAAA,wKAAA,CAAA,YAAa,AAAD,EAAE,GAAG,EAAE,KAAK;IACjC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,MAAM,GAAG;IAC7B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,IAAI,SAAU,CAAC;QACvB,eAAe,GAAG,GAAG;IACvB;AACF;AAEA,SAAS,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK;IACjC,IAAI,WAAW,EAAE,IAAI,CAAC;IACtB,IAAI,SAAS,SAAS,MAAM;IAC5B,EAAE,IAAI,CAAC,OAAO,QAAQ,QAAQ,GAAG,aAAa,GAAG,GAAG;AACtD;AAEA;;;CAGC,GACD,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,KAAK;IAC/B,IAAI,WAAW,EAAE,IAAI,CAAC;IACtB,IAAI,SAAS,SAAS,MAAM;IAC5B,yEAAyE;IACzE,IAAI,cAAc;IAClB,qDAAqD;IACrD,IAAI,YAAY,EAAE,IAAI,CAAC,OAAO;IAC9B,0EAA0E;IAC1E,IAAI,WAAW;IAEf,IAAI,CAAC,WAAW;QACd,cAAc;QACd,YAAY,EAAE,IAAI,CAAC,QAAQ;IAC7B;IAEA,WAAW,UAAU,MAAM;IAE3B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,SAAS,CAAC,QAAQ,SAAU,CAAC;QACvC,IAAI,YAAY,EAAE,CAAC,KAAK,OACtB,QAAQ,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC;QAE/B,IAAI,UAAU,QAAQ;YACpB,IAAI,eAAe,cAAc,aAC/B,cAAc,EAAE,IAAI,CAAC,GAAG,MAAM;YAEhC,YAAY,eAAe,cAAc,CAAC;YAC1C,IAAI,WAAW,GAAG,OAAO,QAAQ;gBAC/B,IAAI,gBAAgB,EAAE,IAAI,CAAC,OAAO,OAAO,QAAQ;gBACjD,YAAY,eAAe,CAAC,gBAAgB;YAC9C;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,iBAAiB,IAAI,EAAE,IAAI;IAClC,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO,KAAK,KAAK,EAAE,CAAC,EAAE;IACxB;IACA,gBAAgB,MAAM,CAAC,GAAG,GAAG;AAC/B;AAEA,SAAS,gBAAgB,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM;IACxD,IAAI,MAAM;IACV,IAAI,QAAQ,KAAK,IAAI,CAAC;IAEtB,OAAO,CAAC,EAAE,GAAG;IACb,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,SAAS,CAAC,IAAI,SAAU,CAAC;QACtC,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;YACtB,UAAU,gBAAgB,MAAM,SAAS,SAAS,GAAG;QACvD;IACF;IAEA,MAAM,GAAG,GAAG;IACZ,MAAM,GAAG,GAAG;IACZ,IAAI,QAAQ;QACV,MAAM,MAAM,GAAG;IACjB,OAAO;QACL,0EAA0E;QAC1E,OAAO,MAAM,MAAM;IACrB;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,IAAI;IACrB,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,KAAK,KAAK,IAAI,SAAU,CAAC;QACrC,OAAO,KAAK,IAAI,CAAC,GAAG,QAAQ,GAAG;IACjC;AACF;AAEA,SAAS,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI;IAC3B,IAAI,IAAI,KAAK,CAAC;IACd,IAAI,IAAI,KAAK,CAAC;IAEd,0EAA0E;IAC1E,wEAAwE;IACxE,iCAAiC;IACjC,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG,IAAI;QACpB,IAAI,KAAK,CAAC;QACV,IAAI,KAAK,CAAC;IACZ;IAEA,IAAI,SAAS,EAAE,IAAI,CAAC;IACpB,IAAI,SAAS,EAAE,IAAI,CAAC;IACpB,IAAI,YAAY;IAChB,IAAI,OAAO;IAEX,6EAA6E;IAC7E,uEAAuE;IACvE,IAAI,OAAO,GAAG,GAAG,OAAO,GAAG,EAAE;QAC3B,YAAY;QACZ,OAAO;IACT;IAEA,IAAI,aAAa,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,IAAI;QACjD,OACE,SAAS,aAAa,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,cACzC,SAAS,aAAa,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAE7C;IAEA,OAAO,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,YAAY,SAAU,IAAI;QACvC,OAAO,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG;IAClB;AACF;AAEA,SAAS,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC/B,IAAI,IAAI,EAAE,CAAC;IACX,IAAI,IAAI,EAAE,CAAC;IACX,EAAE,UAAU,CAAC,GAAG;IAChB,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IACrB,iBAAiB;IACjB,cAAc,GAAG;IACjB,YAAY,GAAG;AACjB;AAEA,SAAS,YAAY,CAAC,EAAE,CAAC;IACvB,IAAI,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QACtC,OAAO,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM;IAC1B;IACA,IAAI,KAAK,CAAA,GAAA,uKAAA,CAAA,WAAY,AAAD,EAAE,GAAG;IACzB,KAAK,GAAG,KAAK,CAAC;IACd,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,IAAI,SAAU,CAAC;QACvB,IAAI,SAAS,EAAE,IAAI,CAAC,GAAG,MAAM,EAC3B,OAAO,EAAE,IAAI,CAAC,GAAG,SACjB,UAAU;QAEZ,IAAI,CAAC,MAAM;YACT,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,UAAU;QACZ;QAEA,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAC,UAAU,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM;IAC9E;AACF;AAEA;;CAEC,GACD,SAAS,WAAW,IAAI,EAAE,CAAC,EAAE,CAAC;IAC5B,OAAO,KAAK,OAAO,CAAC,GAAG;AACzB;AAEA;;;CAGC,GACD,SAAS,aAAa,IAAI,EAAE,MAAM,EAAE,SAAS;IAC3C,OAAO,UAAU,GAAG,IAAI,OAAO,GAAG,IAAI,OAAO,GAAG,IAAI,UAAU,GAAG;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/rank/index.js"], "sourcesContent": ["import { feasibleTree } from './feasible-tree.js';\nimport { networkSimplex } from './network-simplex.js';\nimport { longestPath } from './util.js';\n\nexport { rank };\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch (g.graph().ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    default:\n      networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;AAIA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,KAAK,CAAC;IACb,OAAQ,EAAE,KAAK,GAAG,MAAM;QACtB,KAAK;YACH,qBAAqB;YACrB;QACF,KAAK;YACH,gBAAgB;YAChB;QACF,KAAK;YACH,kBAAkB;YAClB;QACF;YACE,qBAAqB;IACzB;AACF;AAEA,8DAA8D;AAC9D,IAAI,oBAAoB,iKAAA,CAAA,cAAW;AAEnC,SAAS,gBAAgB,CAAC;IACxB,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACZ,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;AACf;AAEA,SAAS,qBAAqB,CAAC;IAC7B,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/nesting-graph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, cleanup };\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, 'root', {}, '_root');\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, '_bt');\n  var bottom = util.addBorderNode(g, '_bb');\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(\n    g.edges(),\n    function (acc, e) {\n      return acc + g.edge(e).weight;\n    },\n    0\n  );\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,IAAI,CAAC;IACZ,IAAI,OAAO,CAAA,GAAA,yJAAA,CAAA,eAAiB,AAAD,EAAE,GAAG,QAAQ,CAAC,GAAG;IAC5C,IAAI,SAAS,WAAW;IACxB,IAAI,SAAS,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,WAAW,GAAG,yCAAyC;IACnF,IAAI,UAAU,IAAI,SAAS;IAE3B,EAAE,KAAK,GAAG,WAAW,GAAG;IAExB,iEAAiE;IACjE,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,EAAE,IAAI,CAAC,GAAG,MAAM,IAAI;IACtB;IAEA,6EAA6E;IAC7E,IAAI,SAAS,WAAW,KAAK;IAE7B,uCAAuC;IACvC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,IAAI,SAAU,KAAK;QACrC,IAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ;IAChD;IAEA,wEAAwE;IACxE,UAAU;IACV,EAAE,KAAK,GAAG,cAAc,GAAG;AAC7B;AAEA,SAAS,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IACtD,IAAI,WAAW,EAAE,QAAQ,CAAC;IAC1B,IAAI,CAAC,SAAS,MAAM,EAAE;QACpB,IAAI,MAAM,MAAM;YACd,EAAE,OAAO,CAAC,MAAM,GAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAQ;QAClD;QACA;IACF;IAEA,IAAI,MAAM,CAAA,GAAA,yJAAA,CAAA,gBAAkB,AAAD,EAAE,GAAG;IAChC,IAAI,SAAS,CAAA,GAAA,yJAAA,CAAA,gBAAkB,AAAD,EAAE,GAAG;IACnC,IAAI,QAAQ,EAAE,IAAI,CAAC;IAEnB,EAAE,SAAS,CAAC,KAAK;IACjB,MAAM,SAAS,GAAG;IAClB,EAAE,SAAS,CAAC,QAAQ;IACpB,MAAM,YAAY,GAAG;IAErB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;QACjC,IAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ;QAE9C,IAAI,YAAY,EAAE,IAAI,CAAC;QACvB,IAAI,WAAW,UAAU,SAAS,GAAG,UAAU,SAAS,GAAG;QAC3D,IAAI,cAAc,UAAU,YAAY,GAAG,UAAU,YAAY,GAAG;QACpE,IAAI,aAAa,UAAU,SAAS,GAAG,SAAS,IAAI;QACpD,IAAI,SAAS,aAAa,cAAc,IAAI,SAAS,MAAM,CAAC,EAAE,GAAG;QAEjE,EAAE,OAAO,CAAC,KAAK,UAAU;YACvB,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;QAEA,EAAE,OAAO,CAAC,aAAa,QAAQ;YAC7B,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI;QAChB,EAAE,OAAO,CAAC,MAAM,KAAK;YAAE,QAAQ;YAAG,QAAQ,SAAS,MAAM,CAAC,EAAE;QAAC;IAC/D;AACF;AAEA,SAAS,WAAW,CAAC;IACnB,IAAI,SAAS,CAAC;IACd,SAAS,IAAI,CAAC,EAAE,KAAK;QACnB,IAAI,WAAW,EAAE,QAAQ,CAAC;QAC1B,IAAI,YAAY,SAAS,MAAM,EAAE;YAC/B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;gBACjC,IAAI,OAAO,QAAQ;YACrB;QACF;QACA,MAAM,CAAC,EAAE,GAAG;IACd;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,IAAI,SAAU,CAAC;QACjC,IAAI,GAAG;IACT;IACA,OAAO;AACT;AAEA,SAAS,WAAW,CAAC;IACnB,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EACZ,EAAE,KAAK,IACP,SAAU,GAAG,EAAE,CAAC;QACd,OAAO,MAAM,EAAE,IAAI,CAAC,GAAG,MAAM;IAC/B,GACA;AAEJ;AAEA,SAAS,QAAQ,CAAC;IAChB,IAAI,aAAa,EAAE,KAAK;IACxB,EAAE,UAAU,CAAC,WAAW,WAAW;IACnC,OAAO,WAAW,WAAW;IAC7B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,KAAK,WAAW,EAAE;YACpB,EAAE,UAAU,CAAC;QACf;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/add-subgraph-constraints.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { addSubgraphConstraints };\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,SAAS,uBAAuB,CAAC,EAAE,EAAE,EAAE,EAAE;IACvC,IAAI,OAAO,CAAC,GACV;IAEF,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,IAAI,SAAU,CAAC;QACvB,IAAI,QAAQ,EAAE,MAAM,CAAC,IACnB,QACA;QACF,MAAO,MAAO;YACZ,SAAS,EAAE,MAAM,CAAC;YAClB,IAAI,QAAQ;gBACV,YAAY,IAAI,CAAC,OAAO;gBACxB,IAAI,CAAC,OAAO,GAAG;YACjB,OAAO;gBACL,YAAY;gBACZ,WAAW;YACb;YACA,IAAI,aAAa,cAAc,OAAO;gBACpC,GAAG,OAAO,CAAC,WAAW;gBACtB;YACF;YACA,QAAQ;QACV;IACF;AAEA;;;;;;;;;;;;;;;;;;;;;;EAsBA,GACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (_.has(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6BC,GACD,SAAS,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY;IAC5C,IAAI,OAAO,eAAe,IACxB,SAAS,IAAI,6JAAA,CAAA,QAAK,CAAC;QAAE,UAAU;IAAK,GACjC,QAAQ,CAAC;QAAE,MAAM;IAAK,GACtB,mBAAmB,CAAC,SAAU,CAAC;QAC9B,OAAO,EAAE,IAAI,CAAC;IAChB;IAEJ,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC,IAChB,SAAS,EAAE,MAAM,CAAC;QAEpB,IAAI,KAAK,IAAI,KAAK,QAAS,KAAK,OAAO,IAAI,QAAQ,QAAQ,KAAK,OAAO,EAAG;YACxE,OAAO,OAAO,CAAC;YACf,OAAO,SAAS,CAAC,GAAG,UAAU;YAE9B,yCAAyC;YACzC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI,SAAU,CAAC;gBACvC,IAAI,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAC3B,OAAO,OAAO,IAAI,CAAC,GAAG,IACtB,SAAS,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,QAAQ,KAAK,MAAM,GAAG;gBAChD,OAAO,OAAO,CAAC,GAAG,GAAG;oBAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG;gBAAO;YAC3D;YAEA,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,YAAY;gBAC1B,OAAO,OAAO,CAAC,GAAG;oBAChB,YAAY,KAAK,UAAU,CAAC,KAAK;oBACjC,aAAa,KAAK,WAAW,CAAC,KAAK;gBACrC;YACF;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,CAAC;IACvB,IAAI;IACJ,MAAO,EAAE,OAAO,CAAE,IAAI,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE;IACjC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/cross-count.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    })\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos'\n      );\n    })\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    })\n  );\n\n  return cc;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAIA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,WAAW,CAAC,EAAE,QAAQ;IAC7B,IAAI,KAAK;IACT,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,EAAE,EAAG;QACxC,MAAM,mBAAmB,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,EAAE;IAC1D;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAC,EAAE,UAAU,EAAE,UAAU;IACnD,6EAA6E;IAC7E,4EAA4E;IAC5E,iCAAiC;IACjC,IAAI,WAAW,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EACvB,YACA,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,YAAY,SAAU,CAAC,EAAE,CAAC;QAC9B,OAAO;IACT;IAEF,IAAI,eAAe,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EACzB,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,YAAY,SAAU,CAAC;QAC3B,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EACZ,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,QAAQ,CAAC,IAAI,SAAU,CAAC;YAC9B,OAAO;gBAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,MAAM;YAAC;QACxD,IACA;IAEJ;IAGF,6BAA6B;IAC7B,IAAI,aAAa;IACjB,MAAO,aAAa,WAAW,MAAM,CAAE,eAAe;IACtD,IAAI,WAAW,IAAI,aAAa;IAChC,cAAc;IACd,IAAI,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,IAAI,MAAM,WAAW;QACpC,OAAO;IACT;IAEA,mCAAmC;IACnC,IAAI,KAAK;IACT,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EACN,mBAAmB;IACnB,aAAa,OAAO,CAAC,SAAU,KAAK;QAClC,IAAI,QAAQ,MAAM,GAAG,GAAG;QACxB,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM;QAC3B,IAAI,YAAY;QAChB,mBAAmB;QACnB,MAAO,QAAQ,EAAG;YAChB,mBAAmB;YACnB,IAAI,QAAQ,GAAG;gBACb,aAAa,IAAI,CAAC,QAAQ,EAAE;YAC9B;YACA,mBAAmB;YACnB,QAAQ,AAAC,QAAQ,KAAM;YACvB,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM;QAC7B;QACA,MAAM,MAAM,MAAM,GAAG;IACvB;IAGF,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2619, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/init-order.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { initOrder };\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nfunction initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(\n    _.map(simpleNodes, function (v) {\n      return g.node(v).rank;\n    })\n  );\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAIA;;;;;;;;;;CAUC,GACD,SAAS,UAAU,CAAC;IAClB,IAAI,UAAU,CAAC;IACf,IAAI,cAAc,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC/C,OAAO,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM;IAC9B;IACA,IAAI,UAAU,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAChB,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,aAAa,SAAU,CAAC;QAC5B,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI;IACvB;IAEF,IAAI,SAAS,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,UAAU,IAAI;QACvC,OAAO,EAAE;IACX;IAEA,SAAS,IAAI,CAAC;QACZ,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,IAAI;QACvB,OAAO,CAAC,EAAE,GAAG;QACb,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC;QACvB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,UAAU,CAAC,IAAI;IAC7B;IAEA,IAAI,YAAY,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,aAAa,SAAU,CAAC;QAC/C,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI;IACvB;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,WAAW;IAErB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/barycenter.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { barycenter };\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(\n        inV,\n        function (acc, e) {\n          var edge = g.edge(e),\n            nodeU = g.node(e.v);\n          return {\n            sum: acc.sum + edge.weight * nodeU.order,\n            weight: acc.weight + edge.weight,\n          };\n        },\n        { sum: 0, weight: 0 }\n      );\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight,\n      };\n    }\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;;AAIA,SAAS,WAAW,CAAC,EAAE,OAAO;IAC5B,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,SAAU,CAAC;QAC/B,IAAI,MAAM,EAAE,OAAO,CAAC;QACpB,IAAI,CAAC,IAAI,MAAM,EAAE;YACf,OAAO;gBAAE,GAAG;YAAE;QAChB,OAAO;YACL,IAAI,SAAS,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAClB,KACA,SAAU,GAAG,EAAE,CAAC;gBACd,IAAI,OAAO,EAAE,IAAI,CAAC,IAChB,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;gBACpB,OAAO;oBACL,KAAK,IAAI,GAAG,GAAG,KAAK,MAAM,GAAG,MAAM,KAAK;oBACxC,QAAQ,IAAI,MAAM,GAAG,KAAK,MAAM;gBAClC;YACF,GACA;gBAAE,KAAK;gBAAG,QAAQ;YAAE;YAGtB,OAAO;gBACL,GAAG;gBACH,YAAY,OAAO,GAAG,GAAG,OAAO,MAAM;gBACtC,QAAQ,OAAO,MAAM;YACvB;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    }\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,iBAAiB,OAAO,EAAE,EAAE;IACnC,IAAI,gBAAgB,CAAC;IACrB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,SAAS,SAAU,KAAK,EAAE,CAAC;QACnC,IAAI,MAAO,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG;YAClC,UAAU;YACV,IAAI,EAAE;YACN,KAAK,EAAE;YACP,IAAI;gBAAC,MAAM,CAAC;aAAC;YACb,GAAG;QACL;QACA,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,MAAM,UAAU,GAAG;YACpC,mBAAmB;YACnB,IAAI,UAAU,GAAG,MAAM,UAAU;YACjC,mBAAmB;YACnB,IAAI,MAAM,GAAG,MAAM,MAAM;QAC3B;IACF;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,GAAG,KAAK,IAAI,SAAU,CAAC;QAC/B,IAAI,SAAS,aAAa,CAAC,EAAE,CAAC,CAAC;QAC/B,IAAI,SAAS,aAAa,CAAC,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,WAAW,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,SAAS;YACpD,OAAO,QAAQ;YACf,OAAO,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QACpC;IACF;IAEA,IAAI,YAAY,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,eAAe,SAAU,KAAK;QACrD,mBAAmB;QACnB,OAAO,CAAC,MAAM,QAAQ;IACxB;IAEA,OAAO,mBAAmB;AAC5B;AAEA,SAAS,mBAAmB,SAAS;IACnC,IAAI,UAAU,EAAE;IAEhB,SAAS,SAAS,MAAM;QACtB,OAAO,SAAU,MAAM;YACrB,IAAI,OAAO,MAAM,EAAE;gBACjB;YACF;YACA,IACE,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,OAAO,UAAU,KAC/B,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,OAAO,UAAU,KAC/B,OAAO,UAAU,IAAI,OAAO,UAAU,EACtC;gBACA,aAAa,QAAQ;YACvB;QACF;IACF;IAEA,SAAS,UAAU,MAAM;QACvB,OAAO,SAAU,MAAM;YACrB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;YAClB,IAAI,EAAE,OAAO,QAAQ,KAAK,GAAG;gBAC3B,UAAU,IAAI,CAAC;YACjB;QACF;IACF;IAEA,MAAO,UAAU,MAAM,CAAE;QACvB,IAAI,QAAQ,UAAU,GAAG;QACzB,QAAQ,IAAI,CAAC;QACb,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,SAAS;QAC1C,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,MAAM,GAAG,EAAE,UAAU;IACjC;IAEA,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EACT,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,SAAS,SAAU,KAAK;QAC/B,OAAO,CAAC,MAAM,MAAM;IACtB,IACA,SAAU,KAAK;QACb,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,OAAO;YAAC;YAAM;YAAK;YAAc;SAAS;IAC1D;AAEJ;AAEA,SAAS,aAAa,MAAM,EAAE,MAAM;IAClC,IAAI,MAAM;IACV,IAAI,SAAS;IAEb,IAAI,OAAO,MAAM,EAAE;QACjB,OAAO,OAAO,UAAU,GAAG,OAAO,MAAM;QACxC,UAAU,OAAO,MAAM;IACzB;IAEA,IAAI,OAAO,MAAM,EAAE;QACjB,OAAO,OAAO,UAAU,GAAG,OAAO,MAAM;QACxC,UAAU,OAAO,MAAM;IACzB;IAEA,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE;IACtC,OAAO,UAAU,GAAG,MAAM;IAC1B,OAAO,MAAM,GAAG;IAChB,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;IACtC,OAAO,MAAM,GAAG;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/sort.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport * as util from '../util.js';\n\nexport { sort };\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return _.has(entry, 'barycenter');\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;AAIA,SAAS,KAAK,OAAO,EAAE,SAAS;IAC9B,IAAI,QAAQ,CAAA,GAAA,yJAAA,CAAA,YAAc,AAAD,EAAE,SAAS,SAAU,KAAK;QACjD,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,OAAO;IACtB;IACA,IAAI,WAAW,MAAM,GAAG,EACtB,aAAa,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,MAAM,GAAG,EAAE,SAAU,KAAK;QAC9C,OAAO,CAAC,MAAM,CAAC;IACjB,IACA,KAAK,EAAE,EACP,MAAM,GACN,SAAS,GACT,UAAU;IAEZ,SAAS,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAEhC,UAAU,kBAAkB,IAAI,YAAY;IAE5C,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;QACjC,WAAW,MAAM,EAAE,CAAC,MAAM;QAC1B,GAAG,IAAI,CAAC,MAAM,EAAE;QAChB,OAAO,MAAM,UAAU,GAAG,MAAM,MAAM;QACtC,UAAU,MAAM,MAAM;QACtB,UAAU,kBAAkB,IAAI,YAAY;IAC9C;IAEA,IAAI,SAAS;QAAE,IAAI,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE;IAAI;IACjC,IAAI,QAAQ;QACV,OAAO,UAAU,GAAG,MAAM;QAC1B,OAAO,MAAM,GAAG;IAClB;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,EAAE,EAAE,UAAU,EAAE,KAAK;IAC9C,IAAI;IACJ,MAAO,WAAW,MAAM,IAAI,CAAC,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,WAAW,EAAE,CAAC,IAAI,MAAO;QAClE,WAAW,GAAG;QACd,GAAG,IAAI,CAAC,KAAK,EAAE;QACf;IACF;IACA,OAAO;AACT;AAEA,SAAS,gBAAgB,IAAI;IAC3B,OAAO,SAAU,MAAM,EAAE,MAAM;QAC7B,IAAI,OAAO,UAAU,GAAG,OAAO,UAAU,EAAE;YACzC,OAAO,CAAC;QACV,OAAO,IAAI,OAAO,UAAU,GAAG,OAAO,UAAU,EAAE;YAChD,OAAO;QACT;QAEA,OAAO,CAAC,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;IAC1D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (_.has(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!_.has(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      })\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAIA,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,SAAS;IACvC,IAAI,UAAU,EAAE,QAAQ,CAAC;IACzB,IAAI,OAAO,EAAE,IAAI,CAAC;IAClB,IAAI,KAAK,OAAO,KAAK,UAAU,GAAG;IAClC,IAAI,KAAK,OAAO,KAAK,WAAW,GAAG;IACnC,IAAI,YAAY,CAAC;IAEjB,IAAI,IAAI;QACN,UAAU,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,SAAS,SAAU,CAAC;YACrC,OAAO,MAAM,MAAM,MAAM;QAC3B;IACF;IAEA,IAAI,cAAc,CAAA,GAAA,wKAAA,CAAA,aAAU,AAAD,EAAE,GAAG;IAChC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,aAAa,SAAU,KAAK;QACpC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE;YAC9B,IAAI,iBAAiB,aAAa,GAAG,MAAM,CAAC,EAAE,IAAI;YAClD,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG;YACrB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,gBAAgB,eAAe;gBACvC,iBAAiB,OAAO;YAC1B;QACF;IACF;IAEA,IAAI,UAAU,CAAA,GAAA,kLAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa;IAC5C,gBAAgB,SAAS;IAEzB,IAAI,SAAS,CAAA,GAAA,kKAAA,CAAA,OAAI,AAAD,EAAE,SAAS;IAE3B,IAAI,IAAI;QACN,OAAO,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE;YAAC;YAAI,OAAO,EAAE;YAAE;SAAG;QACzC,IAAI,EAAE,YAAY,CAAC,IAAI,MAAM,EAAE;YAC7B,IAAI,SAAS,EAAE,IAAI,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,GACvC,SAAS,EAAE,IAAI,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,QAAQ,eAAe;gBAChC,OAAO,UAAU,GAAG;gBACpB,OAAO,MAAM,GAAG;YAClB;YACA,OAAO,UAAU,GACf,CAAC,OAAO,UAAU,GAAG,OAAO,MAAM,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK,IAAI,CAAC,OAAO,MAAM,GAAG,CAAC;YACxF,OAAO,MAAM,IAAI;QACnB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,OAAO,EAAE,SAAS;IACzC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,SAAS,SAAU,KAAK;QAChC,MAAM,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EACjB,MAAM,EAAE,CAAC,GAAG,CAAC,SAAU,CAAC;YACtB,IAAI,SAAS,CAAC,EAAE,EAAE;gBAChB,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE;YACxB;YACA,OAAO;QACT;IAEJ;AACF;AAEA,SAAS,iBAAiB,MAAM,EAAE,KAAK;IACrC,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,OAAO,UAAU,GAAG;QACrC,OAAO,UAAU,GACf,CAAC,OAAO,UAAU,GAAG,OAAO,MAAM,GAAG,MAAM,UAAU,GAAG,MAAM,MAAM,IACpE,CAAC,OAAO,MAAM,GAAG,MAAM,MAAM;QAC/B,OAAO,MAAM,IAAI,MAAM,MAAM;IAC/B,OAAO;QACL,OAAO,UAAU,GAAG,MAAM,UAAU;QACpC,OAAO,MAAM,GAAG,MAAM,MAAM;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/order/index.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\nimport { addSubgraphConstraints } from './add-subgraph-constraints.js';\nimport { buildLayerGraph } from './build-layer-graph.js';\nimport { crossCount } from './cross-count.js';\nimport { initOrder } from './init-order.js';\nimport { sortSubgraph } from './sort-subgraph.js';\n\nexport { order };\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), 'inEdges'),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), 'outEdges');\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function (rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function (lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function (v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAIA;;;;;;;;;;;;;;CAcC,GACD,SAAS,MAAM,CAAC;IACd,IAAI,UAAU,CAAA,GAAA,yJAAA,CAAA,UAAY,AAAD,EAAE,IACzB,kBAAkB,iBAAiB,GAAG,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,GAAG,UAAU,IAAI,YAC/D,gBAAgB,iBAAiB,GAAG,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,IAAI;IAEpE,IAAI,WAAW,CAAA,GAAA,2KAAA,CAAA,YAAS,AAAD,EAAE;IACzB,YAAY,GAAG;IAEf,IAAI,SAAS,OAAO,iBAAiB,EACnC;IAEF,IAAK,IAAI,IAAI,GAAG,WAAW,GAAG,WAAW,GAAG,EAAE,GAAG,EAAE,SAAU;QAC3D,iBAAiB,IAAI,IAAI,kBAAkB,eAAe,IAAI,KAAK;QAEnE,WAAW,CAAA,GAAA,yJAAA,CAAA,mBAAqB,AAAD,EAAE;QACjC,IAAI,KAAK,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE,GAAG;QACvB,IAAI,KAAK,QAAQ;YACf,WAAW;YACX,OAAO,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EAAE;YACnB,SAAS;QACX;IACF;IAEA,YAAY,GAAG;AACjB;AAEA,SAAS,iBAAiB,CAAC,EAAE,KAAK,EAAE,YAAY;IAC9C,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,OAAO,SAAU,IAAI;QAChC,OAAO,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,MAAM;IAClC;AACF;AAEA,SAAS,iBAAiB,WAAW,EAAE,SAAS;IAC9C,IAAI,KAAK,IAAI,6JAAA,CAAA,QAAK;IAClB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,aAAa,SAAU,EAAE;QACjC,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI;QAC1B,IAAI,SAAS,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,IAAI,MAAM,IAAI;QACxC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,EAAE,EAAE,SAAU,CAAC,EAAE,CAAC;YACjC,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG;QACrB;QACA,CAAA,GAAA,4LAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,IAAI,OAAO,EAAE;IAC1C;AACF;AAEA,SAAS,YAAY,CAAC,EAAE,QAAQ;IAC9B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;QACjC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC,EAAE,CAAC;YAC7B,EAAE,IAAI,CAAC,GAAG,KAAK,GAAG;QACpB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/parent-dummy-chains.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nexport { parentDummyChains };\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (\n          pathIdx < path.length - 1 &&\n          g.node((pathV = path[pathIdx + 1])).minRank <= node.rank\n        ) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,SAAS,kBAAkB,CAAC;IAC1B,IAAI,gBAAgB,UAAU;IAE9B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,GAAG,WAAW,EAAE,SAAU,CAAC;QAC1C,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,UAAU,KAAK,OAAO;QAC1B,IAAI,WAAW,SAAS,GAAG,eAAe,QAAQ,CAAC,EAAE,QAAQ,CAAC;QAC9D,IAAI,OAAO,SAAS,IAAI;QACxB,IAAI,MAAM,SAAS,GAAG;QACtB,IAAI,UAAU;QACd,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAI,YAAY;QAEhB,MAAO,MAAM,QAAQ,CAAC,CAAE;YACtB,OAAO,EAAE,IAAI,CAAC;YAEd,IAAI,WAAW;gBACb,MAAO,CAAC,QAAQ,IAAI,CAAC,QAAQ,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,OAAO,GAAG,KAAK,IAAI,CAAE;oBAC3E;gBACF;gBAEA,IAAI,UAAU,KAAK;oBACjB,YAAY;gBACd;YACF;YAEA,IAAI,CAAC,WAAW;gBACd,MACE,UAAU,KAAK,MAAM,GAAG,KACxB,EAAE,IAAI,CAAE,QAAQ,IAAI,CAAC,UAAU,EAAE,EAAG,OAAO,IAAI,KAAK,IAAI,CACxD;oBACA;gBACF;gBACA,QAAQ,IAAI,CAAC,QAAQ;YACvB;YAEA,EAAE,SAAS,CAAC,GAAG;YACf,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE;QACxB;IACF;AACF;AAEA,+EAA+E;AAC/E,yBAAyB;AACzB,SAAS,SAAS,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;IACtC,IAAI,QAAQ,EAAE;IACd,IAAI,QAAQ,EAAE;IACd,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG;IAC7D,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG;IAC7D,IAAI;IACJ,IAAI;IAEJ,qCAAqC;IACrC,SAAS;IACT,GAAG;QACD,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,IAAI,CAAC;IACb,QAAS,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,GAAG,OAAO,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,EAAG;IACzF,MAAM;IAEN,yBAAyB;IACzB,SAAS;IACT,MAAO,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,MAAM,IAAK;QAC1C,MAAM,IAAI,CAAC;IACb;IAEA,OAAO;QAAE,MAAM,MAAM,MAAM,CAAC,MAAM,OAAO;QAAK,KAAK;IAAI;AACzD;AAEA,SAAS,UAAU,CAAC;IAClB,IAAI,SAAS,CAAC;IACd,IAAI,MAAM;IAEV,SAAS,IAAI,CAAC;QACZ,IAAI,MAAM;QACV,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,CAAC,IAAI;QACzB,MAAM,CAAC,EAAE,GAAG;YAAE,KAAK;YAAK,KAAK;QAAM;IACrC;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,QAAQ,IAAI;IAExB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/position/bk.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nexport {\n  positionX,\n  findType1Conflicts,\n  findType2Conflicts,\n  addConflict,\n  hasConflict,\n  verticalAlignment,\n  horizontalCompaction,\n  alignCoordinates,\n  findSmallestWidthAlignment,\n  balance,\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        // @ts-expect-error\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === 'border') {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          // @ts-expect-error\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return _.has(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? 'borderLeft' : 'borderRight';\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach(['u', 'd'], function (vert) {\n    _.forEach(['l', 'r'], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === 'l' ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach(['u', 'd'], function (vert) {\n    adjustedLayering = vert === 'u' ? layering : _.values(layering).reverse();\n    _.forEach(['l', 'r'], function (horiz) {\n      if (horiz === 'r') {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === 'u' ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === 'r');\n      if (horiz === 'r') {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (_.has(vLabel, 'labelpos')) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = -vLabel.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (_.has(wLabel, 'labelpos')) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = wLabel.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;AAoBA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,mBAAmB,CAAC,EAAE,QAAQ;IACrC,IAAI,YAAY,CAAC;IAEjB,SAAS,WAAW,SAAS,EAAE,KAAK;QAClC,IACE,WAAW;QACX,KAAK,GACL,yEAAyE;QACzE,WAAW;QACX,UAAU,GACV,kBAAkB,UAAU,MAAM,EAClC,WAAW,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE;QAEpB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC,EAAE,CAAC;YAC7B,IAAI,IAAI,0BAA0B,GAAG,IACnC,KAAK,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK,GAAG;YAE7B,IAAI,KAAK,MAAM,UAAU;gBACvB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,MAAM,KAAK,CAAC,SAAS,IAAI,IAAI,SAAU,QAAQ;oBACvD,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,YAAY,CAAC,WAAW,SAAU,CAAC;wBAC7C,IAAI,SAAS,EAAE,IAAI,CAAC,IAClB,OAAO,OAAO,KAAK;wBACrB,IAAI,CAAC,OAAO,MAAM,KAAK,IAAI,KAAK,CAAC,CAAC,OAAO,KAAK,IAAI,EAAE,IAAI,CAAC,UAAU,KAAK,GAAG;4BACzE,YAAY,WAAW,GAAG;wBAC5B;oBACF;gBACF;gBACA,mBAAmB;gBACnB,UAAU,IAAI;gBACd,KAAK;YACP;QACF;QAEA,OAAO;IACT;IAEA,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,UAAU;IACnB,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAC,EAAE,QAAQ;IACrC,IAAI,YAAY,CAAC;IAEjB,SAAS,KAAK,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe;QACvE,IAAI;QACJ,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,UAAU,WAAW,SAAU,CAAC;YAChD,IAAI,KAAK,CAAC,EAAE;YACZ,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK,EAAE;gBACnB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,YAAY,CAAC,IAAI,SAAU,CAAC;oBACtC,IAAI,QAAQ,EAAE,IAAI,CAAC;oBACnB,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,GAAG,mBAAmB,MAAM,KAAK,GAAG,eAAe,GAAG;wBACnF,YAAY,WAAW,GAAG;oBAC5B;gBACF;YACF;QACF;IACF;IAEA,SAAS,WAAW,KAAK,EAAE,KAAK;QAC9B,IAAI,eAAe,CAAC,GAClB,cACA,WAAW;QAEb,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC,EAAE,cAAc;YAC1C,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK,KAAK,UAAU;gBAChC,IAAI,eAAe,EAAE,YAAY,CAAC;gBAClC,IAAI,aAAa,MAAM,EAAE;oBACvB,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK;oBAC5C,KAAK,OAAO,UAAU,gBAAgB,cAAc;oBACpD,mBAAmB;oBACnB,WAAW;oBACX,eAAe;gBACjB;YACF;YACA,KAAK,OAAO,UAAU,MAAM,MAAM,EAAE,cAAc,MAAM,MAAM;QAChE;QAEA,OAAO;IACT;IAEA,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,UAAU;IACnB,OAAO;AACT;AAEA,SAAS,0BAA0B,CAAC,EAAE,CAAC;IACrC,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK,EAAE;QACnB,OAAO,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,EAAE,YAAY,CAAC,IAAI,SAAU,CAAC;YAC1C,OAAO,EAAE,IAAI,CAAC,GAAG,KAAK;QACxB;IACF;AACF;AAEA,SAAS,YAAY,SAAS,EAAE,CAAC,EAAE,CAAC;IAClC,IAAI,IAAI,GAAG;QACT,IAAI,MAAM;QACV,IAAI;QACJ,IAAI;IACN;IAEA,IAAI,aAAa,SAAS,CAAC,EAAE;IAC7B,IAAI,CAAC,YAAY;QACf,SAAS,CAAC,EAAE,GAAG,aAAa,CAAC;IAC/B;IACA,UAAU,CAAC,EAAE,GAAG;AAClB;AAEA,SAAS,YAAY,SAAS,EAAE,CAAC,EAAE,CAAC;IAClC,IAAI,IAAI,GAAG;QACT,IAAI,MAAM;QACV,IAAI;QACJ,IAAI;IACN;IACA,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,CAAC,EAAE,EAAE;AAC7B;AAEA;;;;;;;CAOC,GACD,SAAS,kBAAkB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU;IAC3D,IAAI,OAAO,CAAC,GACV,QAAQ,CAAC,GACT,MAAM,CAAC;IAET,yEAAyE;IACzE,qEAAqE;IACrE,yCAAyC;IACzC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;QACjC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC,EAAE,KAAK;YACjC,IAAI,CAAC,EAAE,GAAG;YACV,KAAK,CAAC,EAAE,GAAG;YACX,GAAG,CAAC,EAAE,GAAG;QACX;IACF;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;QACjC,IAAI,UAAU,CAAC;QACf,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC;YAC1B,IAAI,KAAK,WAAW;YACpB,IAAI,GAAG,MAAM,EAAE;gBACb,KAAK,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,IAAI,SAAU,CAAC;oBAC3B,OAAO,GAAG,CAAC,EAAE;gBACf;gBACA,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI;gBAC3B,IAAK,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,EAAG;oBAC7D,IAAI,IAAI,EAAE,CAAC,EAAE;oBACb,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,YAAY,WAAW,GAAG,IAAI;wBACvE,KAAK,CAAC,EAAE,GAAG;wBACX,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;wBAC5B,UAAU,GAAG,CAAC,EAAE;oBAClB;gBACF;YACF;QACF;IACF;IAEA,OAAO;QAAE,MAAM;QAAM,OAAO;IAAM;AACpC;AAEA,SAAS,qBAAqB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU;IAChE,6EAA6E;IAC7E,uEAAuE;IACvE,mEAAmE;IACnE,6EAA6E;IAC7E,qDAAqD;IACrD,IAAI,KAAK,CAAC,GACR,SAAS,gBAAgB,GAAG,UAAU,MAAM,aAC5C,aAAa,aAAa,eAAe;IAE3C,SAAS,QAAQ,SAAS,EAAE,aAAa;QACvC,IAAI,QAAQ,OAAO,KAAK;QACxB,IAAI,OAAO,MAAM,GAAG;QACpB,IAAI,UAAU,CAAC;QACf,MAAO,KAAM;YACX,IAAI,OAAO,CAAC,KAAK,EAAE;gBACjB,UAAU;YACZ,OAAO;gBACL,OAAO,CAAC,KAAK,GAAG;gBAChB,MAAM,IAAI,CAAC;gBACX,QAAQ,MAAM,MAAM,CAAC,cAAc;YACrC;YAEA,OAAO,MAAM,GAAG;QAClB;IACF;IAEA,0CAA0C;IAC1C,SAAS,MAAM,IAAI;QACjB,EAAE,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;YACrD,OAAO,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC;QAC7C,GAAG;IACL;IAEA,2CAA2C;IAC3C,SAAS,MAAM,IAAI;QACjB,IAAI,MAAM,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;YACrD,OAAO,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC;QAC7C,GAAG,OAAO,iBAAiB;QAE3B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,QAAQ,OAAO,iBAAiB,IAAI,KAAK,UAAU,KAAK,YAAY;YACtE,EAAE,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE;QAChC;IACF;IAEA,QAAQ,OAAO,OAAO,YAAY,CAAC,IAAI,CAAC;IACxC,QAAQ,OAAO,OAAO,UAAU,CAAC,IAAI,CAAC;IAEtC,oCAAoC;IACpC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC;QAC1B,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;IACrB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU;IACpD,IAAI,aAAa,IAAI,6JAAA,CAAA,QAAK,IACxB,aAAa,EAAE,KAAK,IACpB,QAAQ,IAAI,WAAW,OAAO,EAAE,WAAW,OAAO,EAAE;IAEtD,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;QACjC,IAAI;QACJ,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC;YAC1B,IAAI,QAAQ,IAAI,CAAC,EAAE;YACnB,WAAW,OAAO,CAAC;YACnB,IAAI,GAAG;gBACL,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,UAAU,WAAW,IAAI,CAAC,OAAO;gBACnC,WAAW,OAAO,CAAC,OAAO,OAAO,KAAK,GAAG,CAAC,MAAM,GAAG,GAAG,IAAI,WAAW;YACvE;YACA,IAAI;QACN;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,2BAA2B,CAAC,EAAE,GAAG;IACxC,OAAO,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,MAAM,SAAU,EAAE;QACxC,IAAI,MAAM,OAAO,iBAAiB;QAClC,IAAI,MAAM,OAAO,iBAAiB;QAElC,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,IAAI,SAAU,CAAC,EAAE,CAAC;YACxB,IAAI,YAAY,MAAM,GAAG,KAAK;YAE9B,MAAM,KAAK,GAAG,CAAC,IAAI,WAAW;YAC9B,MAAM,KAAK,GAAG,CAAC,IAAI,WAAW;QAChC;QAEA,OAAO,MAAM;IACf;AACF;AAEA;;;;;;CAMC,GACD,SAAS,iBAAiB,GAAG,EAAE,OAAO;IACpC,IAAI,cAAc,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,UACzB,aAAa,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,cACnB,aAAa,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE;IAErB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE;QAAC;QAAK;KAAI,EAAE,SAAU,IAAI;QAClC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE;YAAC;YAAK;SAAI,EAAE,SAAU,KAAK;YACnC,IAAI,YAAY,OAAO,OACrB,KAAK,GAAG,CAAC,UAAU,EACnB;YACF,IAAI,OAAO,SAAS;YAEpB,IAAI,SAAS,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE;YACtB,QAAQ,UAAU,MAAM,aAAa,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,UAAU,aAAa,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE;YAExE,IAAI,OAAO;gBACT,GAAG,CAAC,UAAU,GAAG,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EAAE,IAAI,SAAU,CAAC;oBAC1C,OAAO,IAAI;gBACb;YACF;QACF;IACF;AACF;AAEA,SAAS,QAAQ,GAAG,EAAE,KAAK;IACzB,OAAO,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EAAE,IAAI,EAAE,EAAE,SAAU,MAAM,EAAE,CAAC;QAC5C,IAAI,OAAO;YACT,OAAO,GAAG,CAAC,MAAM,WAAW,GAAG,CAAC,EAAE;QACpC,OAAO;YACL,IAAI,KAAK,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,KAAK;YAC7B,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI;QAC3B;IACF;AACF;AAEA,SAAS,UAAU,CAAC;IAClB,IAAI,WAAW,CAAA,GAAA,yJAAA,CAAA,mBAAqB,AAAD,EAAE;IACrC,IAAI,YAAY,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,mBAAmB,GAAG,WAAW,mBAAmB,GAAG;IAE/E,IAAI,MAAM,CAAC;IACX,IAAI;IACJ,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE;QAAC;QAAK;KAAI,EAAE,SAAU,IAAI;QAClC,mBAAmB,SAAS,MAAM,WAAW,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,UAAU,OAAO;QACvE,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE;YAAC;YAAK;SAAI,EAAE,SAAU,KAAK;YACnC,IAAI,UAAU,KAAK;gBACjB,mBAAmB,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,kBAAkB,SAAU,KAAK;oBACxD,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,OAAO,OAAO;gBAChC;YACF;YAEA,IAAI,aAAa,CAAC,SAAS,MAAM,EAAE,YAAY,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC;YACrE,IAAI,QAAQ,kBAAkB,GAAG,kBAAkB,WAAW;YAC9D,IAAI,KAAK,qBAAqB,GAAG,kBAAkB,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,UAAU;YACtF,IAAI,UAAU,KAAK;gBACjB,KAAK,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EAAE,IAAI,SAAU,CAAC;oBAC9B,OAAO,CAAC;gBACV;YACF;YACA,GAAG,CAAC,OAAO,MAAM,GAAG;QACtB;IACF;IAEA,IAAI,gBAAgB,2BAA2B,GAAG;IAClD,iBAAiB,KAAK;IACtB,OAAO,QAAQ,KAAK,EAAE,KAAK,GAAG,KAAK;AACrC;AAEA,SAAS,IAAI,OAAO,EAAE,OAAO,EAAE,UAAU;IACvC,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACtB,IAAI,SAAS,EAAE,IAAI,CAAC;QACpB,IAAI,SAAS,EAAE,IAAI,CAAC;QACpB,IAAI,MAAM;QACV,IAAI;QAEJ,OAAO,OAAO,KAAK,GAAG;QACtB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,QAAQ,aAAa;YAC7B,OAAQ,OAAO,QAAQ,CAAC,WAAW;gBACjC,KAAK;oBACH,QAAQ,CAAC,OAAO,KAAK,GAAG;oBACxB;gBACF,KAAK;oBACH,QAAQ,OAAO,KAAK,GAAG;oBACvB;YACJ;QACF;QACA,IAAI,OAAO;YACT,OAAO,aAAa,QAAQ,CAAC;QAC/B;QACA,QAAQ;QAER,OAAO,CAAC,OAAO,KAAK,GAAG,UAAU,OAAO,IAAI;QAC5C,OAAO,CAAC,OAAO,KAAK,GAAG,UAAU,OAAO,IAAI;QAE5C,OAAO,OAAO,KAAK,GAAG;QACtB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,QAAQ,aAAa;YAC7B,OAAQ,OAAO,QAAQ,CAAC,WAAW;gBACjC,KAAK;oBACH,QAAQ,OAAO,KAAK,GAAG;oBACvB;gBACF,KAAK;oBACH,QAAQ,CAAC,OAAO,KAAK,GAAG;oBACxB;YACJ;QACF;QACA,IAAI,OAAO;YACT,OAAO,aAAa,QAAQ,CAAC;QAC/B;QACA,QAAQ;QAER,OAAO;IACT;AACF;AAEA,SAAS,MAAM,CAAC,EAAE,CAAC;IACjB,OAAO,EAAE,IAAI,CAAC,GAAG,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/position/index.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport * as util from '../util.js';\nimport { positionX } from './bk.js';\n\nexport { position };\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forOwn(positionX(g), function (x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function (layer) {\n    var maxHeight = _.max(\n      _.map(layer, function (v) {\n        return g.node(v).height;\n      })\n    );\n    _.forEach(layer, function (v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAIA,SAAS,SAAS,CAAC;IACjB,IAAI,CAAA,GAAA,yJAAA,CAAA,qBAAuB,AAAD,EAAE;IAE5B,UAAU;IACV,CAAA,GAAA,2KAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,IAAI,SAAU,CAAC,EAAE,CAAC;QACnC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG;IAChB;AACF;AAEA,SAAS,UAAU,CAAC;IAClB,IAAI,WAAW,CAAA,GAAA,yJAAA,CAAA,mBAAqB,AAAD,EAAE;IACrC,IAAI,UAAU,EAAE,KAAK,GAAG,OAAO;IAC/B,IAAI,QAAQ;IACZ,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,UAAU,SAAU,KAAK;QACjC,IAAI,YAAY,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAClB,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,OAAO,SAAU,CAAC;YACtB,OAAO,EAAE,IAAI,CAAC,GAAG,MAAM;QACzB;QAEF,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC;YAC1B,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,YAAY;QACpC;QACA,SAAS,YAAY;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/layout.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { addBorderSegments } from './add-border-segments.js';\nimport * as coordinateSystem from './coordinate-system.js';\nimport * as acyclic from './acyclic.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\nimport * as nestingGraph from './nesting-graph.js';\nimport { order } from './order/index.js';\nimport { parentDummyChains } from './parent-dummy-chains.js';\nimport { position } from './position/index.js';\nimport * as util from './util.js';\n\nexport { layout };\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time('layout', function () {\n    var layoutGraph = time('  buildLayoutGraph', function () {\n      return buildLayoutGraph(g);\n    });\n    time('  runLayout', function () {\n      runLayout(layoutGraph, time);\n    });\n    time('  updateInputGraph', function () {\n      updateInputGraph(g, layoutGraph);\n    });\n  });\n}\n\nfunction runLayout(g, time) {\n  time('    makeSpaceForEdgeLabels', function () {\n    makeSpaceForEdgeLabels(g);\n  });\n  time('    removeSelfEdges', function () {\n    removeSelfEdges(g);\n  });\n  time('    acyclic', function () {\n    acyclic.run(g);\n  });\n  time('    nestingGraph.run', function () {\n    nestingGraph.run(g);\n  });\n  time('    rank', function () {\n    rank(util.asNonCompoundGraph(g));\n  });\n  time('    injectEdgeLabelProxies', function () {\n    injectEdgeLabelProxies(g);\n  });\n  time('    removeEmptyRanks', function () {\n    util.removeEmptyRanks(g);\n  });\n  time('    nestingGraph.cleanup', function () {\n    nestingGraph.cleanup(g);\n  });\n  time('    normalizeRanks', function () {\n    util.normalizeRanks(g);\n  });\n  time('    assignRankMinMax', function () {\n    assignRankMinMax(g);\n  });\n  time('    removeEdgeLabelProxies', function () {\n    removeEdgeLabelProxies(g);\n  });\n  time('    normalize.run', function () {\n    normalize.run(g);\n  });\n  time('    parentDummyChains', function () {\n    parentDummyChains(g);\n  });\n  time('    addBorderSegments', function () {\n    addBorderSegments(g);\n  });\n  time('    order', function () {\n    order(g);\n  });\n  time('    insertSelfEdges', function () {\n    insertSelfEdges(g);\n  });\n  time('    adjustCoordinateSystem', function () {\n    coordinateSystem.adjust(g);\n  });\n  time('    position', function () {\n    position(g);\n  });\n  time('    positionSelfEdges', function () {\n    positionSelfEdges(g);\n  });\n  time('    removeBorderNodes', function () {\n    removeBorderNodes(g);\n  });\n  time('    normalize.undo', function () {\n    normalize.undo(g);\n  });\n  time('    fixupEdgeLabelCoords', function () {\n    fixupEdgeLabelCoords(g);\n  });\n  time('    undoCoordinateSystem', function () {\n    coordinateSystem.undo(g);\n  });\n  time('    translateGraph', function () {\n    translateGraph(g);\n  });\n  time('    assignNodeIntersects', function () {\n    assignNodeIntersects(g);\n  });\n  time('    reversePoints', function () {\n    reversePointsForReversedEdges(g);\n  });\n  time('    acyclic.undo', function () {\n    acyclic.undo(g);\n  });\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (_.has(layoutLabel, 'x')) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = ['nodesep', 'edgesep', 'ranksep', 'marginx', 'marginy'];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: 'tb' };\nvar graphAttrs = ['acyclicer', 'ranker', 'rankdir', 'align'];\nvar nodeNumAttrs = ['width', 'height'];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = ['minlen', 'weight', 'width', 'height', 'labeloffset'];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: 'r',\n};\nvar edgeAttrs = ['labelpos'];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(\n    _.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs))\n  );\n\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(\n      e,\n      _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs))\n    );\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== 'c') {\n      if (graph.rankdir === 'TB' || graph.rankdir === 'BT') {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, 'edge-proxy', label, '_ep');\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      // @ts-expect-error\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'edge-proxy') {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (_.has(edge, 'x')) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (_.has(edge, 'x')) {\n      edge.x -= minX;\n    }\n    if (_.has(edge, 'y')) {\n      edge.y -= minY;\n    }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (_.has(edge, 'x')) {\n      if (edge.labelpos === 'l' || edge.labelpos === 'r') {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case 'l':\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case 'r':\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === 'border') {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(\n          g,\n          'selfedge',\n          {\n            width: selfEdge.label.width,\n            height: selfEdge.label.height,\n            rank: node.rank,\n            order: i + ++orderShift,\n            e: selfEdge.e,\n            label: selfEdge.label,\n          },\n          '_se'\n        );\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'selfedge') {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + (2 * dx) / 3, y: y - dy },\n        { x: x + (5 * dx) / 6, y: y - dy },\n        { x: x + dx, y: y },\n        { x: x + (5 * dx) / 6, y: y + dy },\n        { x: x + (2 * dx) / 3, y: y + dy },\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAIA,SAAS,OAAO,CAAC,EAAE,IAAI;IACrB,IAAI,OAAO,QAAQ,KAAK,WAAW,GAAG,yJAAA,CAAA,OAAS,GAAG,yJAAA,CAAA,SAAW;IAC7D,KAAK,UAAU;QACb,IAAI,cAAc,KAAK,sBAAsB;YAC3C,OAAO,iBAAiB;QAC1B;QACA,KAAK,eAAe;YAClB,UAAU,aAAa;QACzB;QACA,KAAK,sBAAsB;YACzB,iBAAiB,GAAG;QACtB;IACF;AACF;AAEA,SAAS,UAAU,CAAC,EAAE,IAAI;IACxB,KAAK,8BAA8B;QACjC,uBAAuB;IACzB;IACA,KAAK,uBAAuB;QAC1B,gBAAgB;IAClB;IACA,KAAK,eAAe;QAClB,CAAA,GAAA,4JAAA,CAAA,MAAW,AAAD,EAAE;IACd;IACA,KAAK,wBAAwB;QAC3B,CAAA,GAAA,qKAAA,CAAA,MAAgB,AAAD,EAAE;IACnB;IACA,KAAK,YAAY;QACf,CAAA,GAAA,kKAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,yJAAA,CAAA,qBAAuB,AAAD,EAAE;IAC/B;IACA,KAAK,8BAA8B;QACjC,uBAAuB;IACzB;IACA,KAAK,wBAAwB;QAC3B,CAAA,GAAA,yJAAA,CAAA,mBAAqB,AAAD,EAAE;IACxB;IACA,KAAK,4BAA4B;QAC/B,CAAA,GAAA,qKAAA,CAAA,UAAoB,AAAD,EAAE;IACvB;IACA,KAAK,sBAAsB;QACzB,CAAA,GAAA,yJAAA,CAAA,iBAAmB,AAAD,EAAE;IACtB;IACA,KAAK,wBAAwB;QAC3B,iBAAiB;IACnB;IACA,KAAK,8BAA8B;QACjC,uBAAuB;IACzB;IACA,KAAK,qBAAqB;QACxB,CAAA,GAAA,8JAAA,CAAA,MAAa,AAAD,EAAE;IAChB;IACA,KAAK,yBAAyB;QAC5B,CAAA,GAAA,8KAAA,CAAA,oBAAiB,AAAD,EAAE;IACpB;IACA,KAAK,yBAAyB;QAC5B,CAAA,GAAA,8KAAA,CAAA,oBAAiB,AAAD,EAAE;IACpB;IACA,KAAK,aAAa;QAChB,CAAA,GAAA,mKAAA,CAAA,QAAK,AAAD,EAAE;IACR;IACA,KAAK,uBAAuB;QAC1B,gBAAgB;IAClB;IACA,KAAK,8BAA8B;QACjC,CAAA,GAAA,yKAAA,CAAA,SAAuB,AAAD,EAAE;IAC1B;IACA,KAAK,gBAAgB;QACnB,CAAA,GAAA,sKAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IACA,KAAK,yBAAyB;QAC5B,kBAAkB;IACpB;IACA,KAAK,yBAAyB;QAC5B,kBAAkB;IACpB;IACA,KAAK,sBAAsB;QACzB,CAAA,GAAA,8JAAA,CAAA,OAAc,AAAD,EAAE;IACjB;IACA,KAAK,4BAA4B;QAC/B,qBAAqB;IACvB;IACA,KAAK,4BAA4B;QAC/B,CAAA,GAAA,yKAAA,CAAA,OAAqB,AAAD,EAAE;IACxB;IACA,KAAK,sBAAsB;QACzB,eAAe;IACjB;IACA,KAAK,4BAA4B;QAC/B,qBAAqB;IACvB;IACA,KAAK,qBAAqB;QACxB,8BAA8B;IAChC;IACA,KAAK,oBAAoB;QACvB,CAAA,GAAA,4JAAA,CAAA,OAAY,AAAD,EAAE;IACf;AACF;AAEA;;;;;CAKC,GACD,SAAS,iBAAiB,UAAU,EAAE,WAAW;IAC/C,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,WAAW,KAAK,IAAI,SAAU,CAAC;QACvC,IAAI,aAAa,WAAW,IAAI,CAAC;QACjC,IAAI,cAAc,YAAY,IAAI,CAAC;QAEnC,IAAI,YAAY;YACd,WAAW,CAAC,GAAG,YAAY,CAAC;YAC5B,WAAW,CAAC,GAAG,YAAY,CAAC;YAE5B,IAAI,YAAY,QAAQ,CAAC,GAAG,MAAM,EAAE;gBAClC,WAAW,KAAK,GAAG,YAAY,KAAK;gBACpC,WAAW,MAAM,GAAG,YAAY,MAAM;YACxC;QACF;IACF;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,WAAW,KAAK,IAAI,SAAU,CAAC;QACvC,IAAI,aAAa,WAAW,IAAI,CAAC;QACjC,IAAI,cAAc,YAAY,IAAI,CAAC;QAEnC,WAAW,MAAM,GAAG,YAAY,MAAM;QACtC,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,aAAa,MAAM;YAC3B,WAAW,CAAC,GAAG,YAAY,CAAC;YAC5B,WAAW,CAAC,GAAG,YAAY,CAAC;QAC9B;IACF;IAEA,WAAW,KAAK,GAAG,KAAK,GAAG,YAAY,KAAK,GAAG,KAAK;IACpD,WAAW,KAAK,GAAG,MAAM,GAAG,YAAY,KAAK,GAAG,MAAM;AACxD;AAEA,IAAI,gBAAgB;IAAC;IAAW;IAAW;IAAW;IAAW;CAAU;AAC3E,IAAI,gBAAgB;IAAE,SAAS;IAAI,SAAS;IAAI,SAAS;IAAI,SAAS;AAAK;AAC3E,IAAI,aAAa;IAAC;IAAa;IAAU;IAAW;CAAQ;AAC5D,IAAI,eAAe;IAAC;IAAS;CAAS;AACtC,IAAI,eAAe;IAAE,OAAO;IAAG,QAAQ;AAAE;AACzC,IAAI,eAAe;IAAC;IAAU;IAAU;IAAS;IAAU;CAAc;AACzE,IAAI,eAAe;IACjB,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,aAAa;IACb,UAAU;AACZ;AACA,IAAI,YAAY;IAAC;CAAW;AAE5B;;;;;CAKC,GACD,SAAS,iBAAiB,UAAU;IAClC,IAAI,IAAI,IAAI,6JAAA,CAAA,QAAK,CAAC;QAAE,YAAY;QAAM,UAAU;IAAK;IACrD,IAAI,QAAQ,aAAa,WAAW,KAAK;IAEzC,EAAE,QAAQ,CACR,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,CAAC,GAAG,eAAe,kBAAkB,OAAO,gBAAgB,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,OAAO;IAGpF,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,WAAW,KAAK,IAAI,SAAU,CAAC;QACvC,IAAI,OAAO,aAAa,WAAW,IAAI,CAAC;QACxC,EAAE,OAAO,CAAC,GAAG,CAAA,GAAA,+KAAA,CAAA,WAAU,AAAD,EAAE,kBAAkB,MAAM,eAAe;QAC/D,EAAE,SAAS,CAAC,GAAG,WAAW,MAAM,CAAC;IACnC;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,WAAW,KAAK,IAAI,SAAU,CAAC;QACvC,IAAI,OAAO,aAAa,WAAW,IAAI,CAAC;QACxC,EAAE,OAAO,CACP,GACA,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,CAAC,GAAG,cAAc,kBAAkB,MAAM,eAAe,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,MAAM;IAElF;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,uBAAuB,CAAC;IAC/B,IAAI,QAAQ,EAAE,KAAK;IACnB,MAAM,OAAO,IAAI;IACjB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,KAAK,MAAM,IAAI;QACf,IAAI,KAAK,QAAQ,CAAC,WAAW,OAAO,KAAK;YACvC,IAAI,MAAM,OAAO,KAAK,QAAQ,MAAM,OAAO,KAAK,MAAM;gBACpD,KAAK,KAAK,IAAI,KAAK,WAAW;YAChC,OAAO;gBACL,KAAK,MAAM,IAAI,KAAK,WAAW;YACjC;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,uBAAuB,CAAC;IAC/B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,EAAE;YAC7B,IAAI,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClB,IAAI,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClB,IAAI,QAAQ;gBAAE,MAAM,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI;gBAAE,GAAG;YAAE;YACzD,CAAA,GAAA,yJAAA,CAAA,eAAiB,AAAD,EAAE,GAAG,cAAc,OAAO;QAC5C;IACF;AACF;AAEA,SAAS,iBAAiB,CAAC;IACzB,IAAI,UAAU;IACd,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,KAAK,SAAS,EAAE;YAClB,KAAK,OAAO,GAAG,EAAE,IAAI,CAAC,KAAK,SAAS,EAAE,IAAI;YAC1C,KAAK,OAAO,GAAG,EAAE,IAAI,CAAC,KAAK,YAAY,EAAE,IAAI;YAC7C,mBAAmB;YACnB,UAAU,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,SAAS,KAAK,OAAO;QACvC;IACF;IACA,EAAE,KAAK,GAAG,OAAO,GAAG;AACtB;AAEA,SAAS,uBAAuB,CAAC;IAC/B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,KAAK,KAAK,KAAK,cAAc;YAC/B,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,GAAG,KAAK,IAAI;YACpC,EAAE,UAAU,CAAC;QACf;IACF;AACF;AAEA,SAAS,eAAe,CAAC;IACvB,IAAI,OAAO,OAAO,iBAAiB;IACnC,IAAI,OAAO;IACX,IAAI,OAAO,OAAO,iBAAiB;IACnC,IAAI,OAAO;IACX,IAAI,aAAa,EAAE,KAAK;IACxB,IAAI,UAAU,WAAW,OAAO,IAAI;IACpC,IAAI,UAAU,WAAW,OAAO,IAAI;IAEpC,SAAS,YAAY,KAAK;QACxB,IAAI,IAAI,MAAM,CAAC;QACf,IAAI,IAAI,MAAM,CAAC;QACf,IAAI,IAAI,MAAM,KAAK;QACnB,IAAI,IAAI,MAAM,MAAM;QACpB,OAAO,KAAK,GAAG,CAAC,MAAM,IAAI,IAAI;QAC9B,OAAO,KAAK,GAAG,CAAC,MAAM,IAAI,IAAI;QAC9B,OAAO,KAAK,GAAG,CAAC,MAAM,IAAI,IAAI;QAC9B,OAAO,KAAK,GAAG,CAAC,MAAM,IAAI,IAAI;IAChC;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,YAAY,EAAE,IAAI,CAAC;IACrB;IACA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,MAAM;YACpB,YAAY;QACd;IACF;IAEA,QAAQ;IACR,QAAQ;IAER,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,KAAK,CAAC,IAAI;QACV,KAAK,CAAC,IAAI;IACZ;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,MAAM,EAAE,SAAU,CAAC;YAChC,EAAE,CAAC,IAAI;YACP,EAAE,CAAC,IAAI;QACT;QACA,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,MAAM;YACpB,KAAK,CAAC,IAAI;QACZ;QACA,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,MAAM;YACpB,KAAK,CAAC,IAAI;QACZ;IACF;IAEA,WAAW,KAAK,GAAG,OAAO,OAAO;IACjC,WAAW,MAAM,GAAG,OAAO,OAAO;AACpC;AAEA,SAAS,qBAAqB,CAAC;IAC7B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;QACtB,IAAI,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;QACtB,IAAI,IAAI;QACR,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,KAAK,MAAM,GAAG,EAAE;YAChB,KAAK;YACL,KAAK;QACP,OAAO;YACL,KAAK,KAAK,MAAM,CAAC,EAAE;YACnB,KAAK,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAC1C;QACA,KAAK,MAAM,CAAC,OAAO,CAAC,CAAA,GAAA,yJAAA,CAAA,gBAAkB,AAAD,EAAE,OAAO;QAC9C,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,yJAAA,CAAA,gBAAkB,AAAD,EAAE,OAAO;IAC7C;AACF;AAEA,SAAS,qBAAqB,CAAC;IAC7B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,MAAM,MAAM;YACpB,IAAI,KAAK,QAAQ,KAAK,OAAO,KAAK,QAAQ,KAAK,KAAK;gBAClD,KAAK,KAAK,IAAI,KAAK,WAAW;YAChC;YACA,OAAQ,KAAK,QAAQ;gBACnB,KAAK;oBACH,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,WAAW;oBAC3C;gBACF,KAAK;oBACH,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,WAAW;oBAC3C;YACJ;QACF;IACF;AACF;AAEA,SAAS,8BAA8B,CAAC;IACtC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,KAAK,QAAQ,EAAE;YACjB,KAAK,MAAM,CAAC,OAAO;QACrB;IACF;AACF;AAEA,SAAS,kBAAkB,CAAC;IAC1B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,EAAE,QAAQ,CAAC,GAAG,MAAM,EAAE;YACxB,IAAI,OAAO,EAAE,IAAI,CAAC;YAClB,IAAI,IAAI,EAAE,IAAI,CAAC,KAAK,SAAS;YAC7B,IAAI,IAAI,EAAE,IAAI,CAAC,KAAK,YAAY;YAChC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,KAAK,UAAU;YACrC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,KAAK,WAAW;YAEtC,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YAC/B,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YAChC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,KAAK,GAAG;YAC5B,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,MAAM,GAAG;QAC/B;IACF;IAEA,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK,KAAK,UAAU;YAChC,EAAE,UAAU,CAAC;QACf;IACF;AACF;AAEA,SAAS,gBAAgB,CAAC;IACxB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;YACf,IAAI,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,SAAS,EAAE;gBACnB,KAAK,SAAS,GAAG,EAAE;YACrB;YACA,KAAK,SAAS,CAAC,IAAI,CAAC;gBAAE,GAAG;gBAAG,OAAO,EAAE,IAAI,CAAC;YAAG;YAC7C,EAAE,UAAU,CAAC;QACf;IACF;AACF;AAEA,SAAS,gBAAgB,CAAC;IACxB,IAAI,SAAS,CAAA,GAAA,yJAAA,CAAA,mBAAqB,AAAD,EAAE;IACnC,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,SAAU,KAAK;QAC/B,IAAI,aAAa;QACjB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC,EAAE,CAAC;YAC7B,IAAI,OAAO,EAAE,IAAI,CAAC;YAClB,KAAK,KAAK,GAAG,IAAI;YACjB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,KAAK,SAAS,EAAE,SAAU,QAAQ;gBAC1C,CAAA,GAAA,yJAAA,CAAA,eAAiB,AAAD,EACd,GACA,YACA;oBACE,OAAO,SAAS,KAAK,CAAC,KAAK;oBAC3B,QAAQ,SAAS,KAAK,CAAC,MAAM;oBAC7B,MAAM,KAAK,IAAI;oBACf,OAAO,IAAI,EAAE;oBACb,GAAG,SAAS,CAAC;oBACb,OAAO,SAAS,KAAK;gBACvB,GACA;YAEJ;YACA,OAAO,KAAK,SAAS;QACvB;IACF;AACF;AAEA,SAAS,kBAAkB,CAAC;IAC1B,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QAC9B,IAAI,OAAO,EAAE,IAAI,CAAC;QAClB,IAAI,KAAK,KAAK,KAAK,YAAY;YAC7B,IAAI,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9B,IAAI,IAAI,SAAS,CAAC,GAAG,SAAS,KAAK,GAAG;YACtC,IAAI,IAAI,SAAS,CAAC;YAClB,IAAI,KAAK,KAAK,CAAC,GAAG;YAClB,IAAI,KAAK,SAAS,MAAM,GAAG;YAC3B,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK;YAC5B,EAAE,UAAU,CAAC;YACb,KAAK,KAAK,CAAC,MAAM,GAAG;gBAClB;oBAAE,GAAG,IAAI,AAAC,IAAI,KAAM;oBAAG,GAAG,IAAI;gBAAG;gBACjC;oBAAE,GAAG,IAAI,AAAC,IAAI,KAAM;oBAAG,GAAG,IAAI;gBAAG;gBACjC;oBAAE,GAAG,IAAI;oBAAI,GAAG;gBAAE;gBAClB;oBAAE,GAAG,IAAI,AAAC,IAAI,KAAM;oBAAG,GAAG,IAAI;gBAAG;gBACjC;oBAAE,GAAG,IAAI,AAAC,IAAI,KAAM;oBAAG,GAAG,IAAI;gBAAG;aAClC;YACD,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;YACrB,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;QACvB;IACF;AACF;AAEA,SAAS,kBAAkB,GAAG,EAAE,KAAK;IACnC,OAAO,CAAA,GAAA,iLAAA,CAAA,YAAW,AAAD,EAAE,CAAA,GAAA,uKAAA,CAAA,OAAM,AAAD,EAAE,KAAK,QAAQ;AACzC;AAEA,SAAS,aAAa,KAAK;IACzB,IAAI,WAAW,CAAC;IAChB,CAAA,GAAA,6KAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAU,CAAC,EAAE,CAAC;QAC7B,QAAQ,CAAC,EAAE,WAAW,GAAG,GAAG;IAC9B;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre/index.js"], "sourcesContent": ["import * as acyclic from './acyclic.js';\nimport { layout } from './layout.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\n\nexport { acyclic, normalize, rank, layout };\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4099, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/graphlib/json.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from './graph.js';\n\nexport { write, read };\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound(),\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g),\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;;;;AAIA,SAAS,MAAM,CAAC;IACd,IAAI,OAAO;QACT,SAAS;YACP,UAAU,EAAE,UAAU;YACtB,YAAY,EAAE,YAAY;YAC1B,UAAU,EAAE,UAAU;QACxB;QACA,OAAO,WAAW;QAClB,OAAO,WAAW;IACpB;IACA,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,EAAE,KAAK,KAAK;QAC7B,KAAK,KAAK,GAAG,CAAA,GAAA,yKAAA,CAAA,QAAO,AAAD,EAAE,EAAE,KAAK;IAC9B;IACA,OAAO;AACT;AAEA,SAAS,WAAW,CAAC;IACnB,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QACjC,IAAI,YAAY,EAAE,IAAI,CAAC;QACvB,IAAI,SAAS,EAAE,MAAM,CAAC;QACtB,IAAI,OAAO;YAAE,GAAG;QAAE;QAClB,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,YAAY;YAC7B,KAAK,KAAK,GAAG;QACf;QACA,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,SAAS;YAC1B,KAAK,MAAM,GAAG;QAChB;QACA,OAAO;IACT;AACF;AAEA,SAAS,WAAW,CAAC;IACnB,OAAO,CAAA,GAAA,qKAAA,CAAA,MAAK,AAAD,EAAE,EAAE,KAAK,IAAI,SAAU,CAAC;QACjC,IAAI,YAAY,EAAE,IAAI,CAAC;QACvB,IAAI,OAAO;YAAE,GAAG,EAAE,CAAC;YAAE,GAAG,EAAE,CAAC;QAAC;QAC5B,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,EAAE,IAAI,GAAG;YAC1B,KAAK,IAAI,GAAG,EAAE,IAAI;QACpB;QACA,IAAI,CAAC,CAAA,GAAA,qLAAA,CAAA,cAAa,AAAD,EAAE,YAAY;YAC7B,KAAK,KAAK,GAAG;QACf;QACA,OAAO;IACT;AACF;AAEA,SAAS,KAAK,IAAI;IAChB,IAAI,IAAI,IAAI,6JAAA,CAAA,QAAK,CAAC,KAAK,OAAO,EAAE,QAAQ,CAAC,KAAK,KAAK;IACnD,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,KAAK,KAAK,EAAE,SAAU,KAAK;QAChC,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,KAAK;QAC9B,IAAI,MAAM,MAAM,EAAE;YAChB,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,MAAM;QACnC;IACF;IACA,CAAA,GAAA,0KAAA,CAAA,OAAM,AAAD,EAAE,KAAK,KAAK,EAAE,SAAU,KAAK;QAChC,EAAE,OAAO,CAAC;YAAE,GAAG,MAAM,CAAC;YAAE,GAAG,MAAM,CAAC;YAAE,MAAM,MAAM,IAAI;QAAC,GAAG,MAAM,KAAK;IACrE;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre-js/util.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\n// Public utility functions\nexport { isSubgraph, edgeToId, applyStyle, applyClass, applyTransition };\n\n/*\n * Returns true if the specified node in the graph is a subgraph node. A\n * subgraph node is one that contains other nodes.\n */\nfunction isSubgraph(g, v) {\n  return !!g.children(v).length;\n}\n\nfunction edgeToId(e) {\n  return escapeId(e.v) + ':' + escapeId(e.w) + ':' + escapeId(e.name);\n}\n\nvar ID_DELIM = /:/g;\nfunction escapeId(str) {\n  return str ? String(str).replace(ID_DELIM, '\\\\:') : '';\n}\n\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\nfunction applyClass(dom, classFn, otherClasses) {\n  if (classFn) {\n    dom.attr('class', classFn).attr('class', otherClasses + ' ' + dom.attr('class'));\n  }\n}\n\nfunction applyTransition(selection, g) {\n  var graph = g.graph();\n\n  if (_.isPlainObject(graph)) {\n    var transition = graph.transition;\n    if (_.isFunction(transition)) {\n      return transition(selection);\n    }\n  }\n\n  return selection;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;;AAKA;;;CAGC,GACD,SAAS,WAAW,CAAC,EAAE,CAAC;IACtB,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM;AAC/B;AAEA,SAAS,SAAS,CAAC;IACjB,OAAO,SAAS,EAAE,CAAC,IAAI,MAAM,SAAS,EAAE,CAAC,IAAI,MAAM,SAAS,EAAE,IAAI;AACpE;AAEA,IAAI,WAAW;AACf,SAAS,SAAS,GAAG;IACnB,OAAO,MAAM,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS;AACtD;AAEA,SAAS,WAAW,GAAG,EAAE,OAAO;IAC9B,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,SAAS;IACpB;AACF;AAEA,SAAS,WAAW,GAAG,EAAE,OAAO,EAAE,YAAY;IAC5C,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,eAAe,MAAM,IAAI,IAAI,CAAC;IACzE;AACF;AAEA,SAAS,gBAAgB,SAAS,EAAE,CAAC;IACnC,IAAI,QAAQ,EAAE,KAAK;IAEnB,IAAI,CAAA,GAAA,yLAAA,CAAA,gBAAe,AAAD,EAAE,QAAQ;QAC1B,IAAI,aAAa,MAAM,UAAU;QACjC,IAAI,CAAA,GAAA,mLAAA,CAAA,aAAY,AAAD,EAAE,aAAa;YAC5B,OAAO,WAAW;QACpB;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/dagre-d3-es/src/dagre-js/label/add-html-label.js"], "sourcesContent": ["import * as util from '../util.js';\n\nexport { addHtmlLabel };\n\nfunction addHtmlLabel(root, node) {\n  var fo = root.append('foreignObject').attr('width', '100000');\n\n  var div = fo.append('xhtml:div');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n\n  var label = node.label;\n  switch (typeof label) {\n    case 'function':\n      div.insert(label);\n      break;\n    case 'object':\n      // Currently we assume this is a DOM object.\n      div.insert(function () {\n        return label;\n      });\n      break;\n    default:\n      div.html(label);\n  }\n\n  util.applyStyle(div, node.labelStyle);\n  div.style('display', 'inline-block');\n  // Fix for firefox\n  div.style('white-space', 'nowrap');\n\n  var client = div.node().getBoundingClientRect();\n  fo.attr('width', client.width).attr('height', client.height);\n\n  return fo;\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAIA,SAAS,aAAa,IAAI,EAAE,IAAI;IAC9B,IAAI,KAAK,KAAK,MAAM,CAAC,iBAAiB,IAAI,CAAC,SAAS;IAEpD,IAAI,MAAM,GAAG,MAAM,CAAC;IACpB,IAAI,IAAI,CAAC,SAAS;IAElB,IAAI,QAAQ,KAAK,KAAK;IACtB,OAAQ,OAAO;QACb,KAAK;YACH,IAAI,MAAM,CAAC;YACX;QACF,KAAK;YACH,4CAA4C;YAC5C,IAAI,MAAM,CAAC;gBACT,OAAO;YACT;YACA;QACF;YACE,IAAI,IAAI,CAAC;IACb;IAEA,CAAA,GAAA,+JAAA,CAAA,aAAe,AAAD,EAAE,KAAK,KAAK,UAAU;IACpC,IAAI,KAAK,CAAC,WAAW;IACrB,kBAAkB;IAClB,IAAI,KAAK,CAAC,eAAe;IAEzB,IAAI,SAAS,IAAI,IAAI,GAAG,qBAAqB;IAC7C,GAAG,IAAI,CAAC,SAAS,OAAO,KAAK,EAAE,IAAI,CAAC,UAAU,OAAO,MAAM;IAE3D,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}