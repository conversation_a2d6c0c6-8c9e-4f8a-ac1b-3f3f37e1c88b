import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

export async function POST(request: NextRequest) {
  try {
    const { message, context, diagramCode } = await request.json();
    
    if (!message) {
      return NextResponse.json({ error: 'No message provided' }, { status: 400 });
    }

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - returning demo response');

      return NextResponse.json({
        success: true,
        response: "Thank you for your feedback! In demo mode, I can't actually modify the diagram, but I understand your request. Please configure your Claude API key to enable real-time diagram improvements.",
        updatedDiagramCode: diagramCode || null,
        message: 'Demo mode - Configure Claude API key for real chat functionality'
      });
    }

    console.log('Processing architecture chat message...');
    console.log(`User message: ${message}`);

    // Create a comprehensive prompt for architecture diagram improvement
    const fullPrompt = `You are a senior solutions architect helping to improve and refine architecture diagrams based on user feedback.

Context: The user is reviewing an architecture diagram that was generated from technical requirements. They may want to:
- Modify components or connections
- Add missing elements
- Improve the layout or organization
- Clarify relationships between components
- Add security layers or compliance elements
- Optimize for performance or scalability

Current diagram context:
${context || 'Architecture diagram for a cloud-native solution'}

Current diagram code (if available):
${diagramCode || 'No diagram code provided'}

User feedback/request:
${message}

Please provide helpful, specific advice and if the user requests changes to the diagram, provide updated Mermaid or PlantUML code that incorporates their feedback.

Be conversational, helpful, and focus on practical architecture improvements. If you provide updated diagram code, wrap it in appropriate code blocks (triple backticks for mermaid or @startuml/@enduml for PlantUML).`;

    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 1500,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude chat API call successful');
    } catch (apiError: any) {
      console.error('Claude Chat API Error:', apiError);

      // Handle specific API errors
      if (apiError.status === 401) {
        return NextResponse.json({
          error: 'Invalid API key. Please check your Anthropic API key configuration.'
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({
          error: 'Rate limit exceeded. Please try again in a few minutes.'
        }, { status: 429 });
      } else {
        return NextResponse.json({
          error: `Claude API error: ${apiError.message || 'Unknown error'}`
        }, { status: 500 });
      }
    }

    const chatResponse = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Check if the response contains updated diagram code
    let updatedDiagramCode = null;
    
    // Look for Mermaid code in the response
    const mermaidMatch = chatResponse.match(/```mermaid\s*([\s\S]*?)\s*```/i);
    if (mermaidMatch) {
      updatedDiagramCode = {
        type: 'mermaid',
        code: mermaidMatch[1].trim()
      };
    }
    
    // Look for PlantUML code in the response
    const plantUMLMatch = chatResponse.match(/@startuml([\s\S]*?)@enduml/i);
    if (plantUMLMatch) {
      updatedDiagramCode = {
        type: 'plantuml',
        code: `@startuml${plantUMLMatch[1]}@enduml`
      };
    }
    
    return NextResponse.json({
      success: true,
      response: chatResponse,
      updatedDiagramCode: updatedDiagramCode,
      message: 'Chat response generated successfully'
    });

  } catch (error) {
    console.error('Error processing chat message:', error);
    return NextResponse.json(
      { error: 'Failed to process chat message' }, 
      { status: 500 }
    );
  }
}
