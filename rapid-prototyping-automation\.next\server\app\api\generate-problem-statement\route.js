const CHUNK_PUBLIC_PATH = "server/app/api/generate-problem-statement/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_9e7d68f0._.js");
runtime.loadChunk("server/chunks/node_modules_@anthropic-ai_sdk_7e568b40._.js");
runtime.loadChunk("server/chunks/node_modules_underscore_modules_cf46b0b6._.js");
runtime.loadChunk("server/chunks/node_modules_bluebird_js_release_a358d141._.js");
runtime.loadChunk("server/chunks/node_modules_mammoth_lib_ae085314._.js");
runtime.loadChunk("server/chunks/node_modules_jszip_lib_f1a3aa10._.js");
runtime.loadChunk("server/chunks/node_modules_pako_d1ebb5fd._.js");
runtime.loadChunk("server/chunks/node_modules_@xmldom_xmldom_lib_9e2441e9._.js");
runtime.loadChunk("server/chunks/node_modules_dingbat-to-unicode_dist_5f852e4c._.js");
runtime.loadChunk("server/chunks/node_modules_d25e4bca._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__f9502dde._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/generate-problem-statement/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-problem-statement/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-problem-statement/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
