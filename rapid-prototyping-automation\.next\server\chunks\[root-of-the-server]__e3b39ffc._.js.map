{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/generate-implementation-prompts/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\n\n// For development environments with SSL certificate issues\nif (process.env.NODE_ENV === 'development') {\n  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n}\n\n// Check if API key is configured\nif (!process.env.ANTHROPIC_API_KEY) {\n  console.error('ANTHROPIC_API_KEY is not configured in environment variables');\n}\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',\n});\n\nconst IMPLEMENTATION_PROMPTS_PROMPT = `You are a senior full-stack developer and DevOps engineer specializing in creating detailed implementation prompts for AI-powered development tools like Loveable AI.\n\nYour task is to analyze the technical requirements document and generate four specific, actionable prompts that can be copy-pasted directly into Loveable AI to build a complete prototype solution.\n\nIMPORTANT: Generate exactly four distinct prompts in the following format:\n\n=== FRONTEND PROMPT ===\n[Detailed frontend implementation prompt for Loveable AI]\n\n=== BACKEND API PROMPT ===\n[Detailed backend API implementation prompt for Loveable AI]\n\n=== DATABASE SCHEMA PROMPT ===\n[Detailed database schema implementation prompt for Loveable AI]\n\n=== DEVOPS DEPLOYMENT ===\n[JSON configuration for Ansible/Terraform deployment]\n\nFor each prompt, follow these guidelines:\n\n**FRONTEND PROMPT:**\n- Specify the exact UI framework (React, Vue, Angular, etc.)\n- Detail all pages, components, and user flows\n- Include styling framework (Tailwind, Material-UI, etc.)\n- Specify responsive design requirements\n- Include authentication and state management\n- Detail API integration points\n- Include accessibility requirements\n\n**BACKEND API PROMPT:**\n- Specify the backend technology stack\n- Detail all API endpoints with methods and parameters\n- Include authentication and authorization logic\n- Specify data validation and error handling\n- Include middleware and security measures\n- Detail database integration\n- Include logging and monitoring\n\n**DATABASE SCHEMA PROMPT:**\n- Specify the database type (PostgreSQL, MongoDB, etc.)\n- Detail all tables/collections with fields and types\n- Include relationships and constraints\n- Specify indexes for performance\n- Include data migration strategies\n- Detail backup and recovery procedures\n\n**DEVOPS DEPLOYMENT:**\n- Provide a complete JSON configuration\n- Include infrastructure as code (Terraform or Ansible)\n- Specify cloud provider and services\n- Include CI/CD pipeline configuration\n- Detail monitoring and logging setup\n- Include security configurations\n- Specify scaling and load balancing\n\nEach prompt should be:\n- Specific and actionable\n- Ready to copy-paste into Loveable AI\n- Include all necessary technical details\n- Follow best practices for the technology stack\n- Include error handling and edge cases\n- Be production-ready focused\n\nAnalyze the technical requirements and create comprehensive implementation prompts.`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { technicalRequirementsContent } = await request.json();\n    \n    if (!technicalRequirementsContent) {\n      return NextResponse.json({ error: 'No technical requirements content provided' }, { status: 400 });\n    }\n\n    // Check if API key is configured\n    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo prompts');\n      \n      // For development, return demo prompts\n      const demoPrompts = {\n        frontend: createDemoFrontendPrompt(),\n        backend: createDemoBackendPrompt(),\n        database: createDemoDatabasePrompt(),\n        devops: createDemoDevOpsConfig()\n      };\n\n      const markdownContent = formatPromptsMarkdown(\n        \"Demo implementation prompts generated. Configure Claude API key for real prompt generation based on your technical requirements.\",\n        demoPrompts.frontend,\n        demoPrompts.backend,\n        demoPrompts.database,\n        demoPrompts.devops\n      );\n\n      return NextResponse.json({\n        success: true,\n        content: markdownContent, // Return formatted markdown content\n        prompts: demoPrompts,\n        message: 'Demo implementation prompts generated. Configure Claude API key for real prompt generation.'\n      });\n    }\n\n    console.log('Generating implementation prompts from technical requirements...');\n    console.log(`Technical requirements content length: ${technicalRequirementsContent.length} characters`);\n\n    // Call Claude API with Sonnet 4\n    console.log('Calling Claude API with Sonnet 4 for implementation prompts...');\n    const fullPrompt = `${IMPLEMENTATION_PROMPTS_PROMPT}\\n\\nTechnical Requirements Document Content:\\n${technicalRequirementsContent}`;\n    console.log(`Full prompt length: ${fullPrompt.length} characters`);\n    \n    let response;\n    try {\n      response = await anthropic.messages.create({\n        model: 'claude-sonnet-4-20250514',\n        max_tokens: 10000,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n      console.log('Claude API call successful for implementation prompts');\n    } catch (apiError: any) {\n      console.error('Claude API Error:', apiError);\n      \n      // Handle specific API errors\n      if (apiError.status === 401) {\n        return NextResponse.json({ \n          error: 'Invalid API key. Please check your Anthropic API key configuration.' \n        }, { status: 401 });\n      } else if (apiError.status === 429) {\n        return NextResponse.json({ \n          error: 'Rate limit exceeded. Please try again in a few minutes.' \n        }, { status: 429 });\n      } else if (apiError.message?.includes('model')) {\n        return NextResponse.json({ \n          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.' \n        }, { status: 400 });\n      } else {\n        return NextResponse.json({ \n          error: `Claude API error: ${apiError.message || 'Unknown error'}` \n        }, { status: 500 });\n      }\n    }\n\n    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    console.log('=== CLAUDE PROMPTS RESPONSE DEBUG ===');\n    console.log('Full response length:', analysisText.length);\n    console.log('Response preview:', analysisText.substring(0, 300));\n    console.log('=== END DEBUG ===');\n    \n    // Parse the response to extract the four prompts\n    const parsedPrompts = parseImplementationPrompts(analysisText);\n    \n    console.log('Parsed prompts:', {\n      frontendLength: parsedPrompts.frontend.length,\n      backendLength: parsedPrompts.backend.length,\n      databaseLength: parsedPrompts.database.length,\n      devopsLength: parsedPrompts.devops.length\n    });\n    \n    // Format as markdown document with prompts\n    const markdownContent = formatPromptsMarkdown(\n      analysisText,\n      parsedPrompts.frontend,\n      parsedPrompts.backend,\n      parsedPrompts.database,\n      parsedPrompts.devops\n    );\n\n    return NextResponse.json({\n      success: true,\n      content: markdownContent, // Return formatted markdown content\n      prompts: parsedPrompts,\n      fullResponse: analysisText,\n      message: 'Implementation prompts generated successfully with Claude Sonnet 4!'\n    });\n\n  } catch (error) {\n    console.error('Error generating implementation prompts:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate implementation prompts' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction formatPromptsMarkdown(\n  analysisText: string,\n  frontendPrompt: string,\n  backendPrompt: string,\n  databasePrompt: string,\n  devopsPrompt: string\n): string {\n  const currentDate = new Date().toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n\n  return `# Implementation Prompts Document\n\n**Date:** ${currentDate}\n**Generated by:** Claude Sonnet 4 AI Analysis\n\n---\n\n${analysisText}\n\n---\n\n## Implementation Prompts for Loveable AI\n\n### 🎨 Frontend Development Prompt\n\n\\`\\`\\`\n${frontendPrompt}\n\\`\\`\\`\n\n### ⚙️ Backend API Development Prompt\n\n\\`\\`\\`\n${backendPrompt}\n\\`\\`\\`\n\n### 🗄️ Database Schema Prompt\n\n\\`\\`\\`\n${databasePrompt}\n\\`\\`\\`\n\n### 🚀 DevOps & Deployment Prompt\n\n\\`\\`\\`\n${devopsPrompt}\n\\`\\`\\`\n\n---\n\n*These implementation prompts were automatically generated using AI analysis. Copy and paste each prompt into Loveable AI for rapid prototyping and development.*`;\n}\n\nfunction parseImplementationPrompts(text: string) {\n  const result = {\n    frontend: '',\n    backend: '',\n    database: '',\n    devops: ''\n  };\n  \n  console.log('Parsing implementation prompts, length:', text.length);\n  \n  // Extract Frontend prompt\n  const frontendMatch = text.match(/=== FRONTEND PROMPT ===\\s*([\\s\\S]*?)(?=\\s*=== [A-Z]|$)/i);\n  if (frontendMatch) {\n    result.frontend = frontendMatch[1].trim();\n  }\n  \n  // Extract Backend API prompt\n  const backendMatch = text.match(/=== BACKEND API PROMPT ===\\s*([\\s\\S]*?)(?=\\s*=== [A-Z]|$)/i);\n  if (backendMatch) {\n    result.backend = backendMatch[1].trim();\n  }\n  \n  // Extract Database Schema prompt\n  const databaseMatch = text.match(/=== DATABASE SCHEMA PROMPT ===\\s*([\\s\\S]*?)(?=\\s*=== [A-Z]|$)/i);\n  if (databaseMatch) {\n    result.database = databaseMatch[1].trim();\n  }\n  \n  // Extract DevOps Deployment\n  const devopsMatch = text.match(/=== DEVOPS DEPLOYMENT ===\\s*([\\s\\S]*?)(?=\\s*=== [A-Z]|$)/i);\n  if (devopsMatch) {\n    result.devops = devopsMatch[1].trim();\n  }\n  \n  // If structured format not found, try to extract any JSON for DevOps\n  if (!result.devops) {\n    const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n    if (jsonMatch) {\n      result.devops = jsonMatch[0];\n    }\n  }\n  \n  console.log('Parsed prompts result:', {\n    frontendLength: result.frontend.length,\n    backendLength: result.backend.length,\n    databaseLength: result.database.length,\n    devopsLength: result.devops.length\n  });\n  \n  return result;\n}\n\n// Demo functions for when API key is not configured\nfunction createDemoFrontendPrompt(): string {\n  return `Create a modern React application with TypeScript and Tailwind CSS for a cloud-native solution dashboard.\n\nRequirements:\n- Landing page with hero section and feature overview\n- User authentication (login/register) with JWT\n- Main dashboard with navigation sidebar\n- Data visualization components using Chart.js\n- Responsive design for mobile and desktop\n- Real-time updates using WebSocket connection\n- Form validation and error handling\n- Loading states and skeleton screens\n- Accessibility compliance (WCAG 2.1)\n\nTech Stack:\n- React 18 with TypeScript\n- Tailwind CSS for styling\n- React Router for navigation\n- React Query for API state management\n- Chart.js for data visualization\n- Socket.io-client for real-time features\n\nConfigure API integration for backend endpoints and implement proper error boundaries.`;\n}\n\nfunction createDemoBackendPrompt(): string {\n  return `Build a Node.js REST API with Express and TypeScript for a cloud-native solution backend.\n\nRequirements:\n- RESTful API with proper HTTP methods and status codes\n- JWT authentication and authorization middleware\n- User management (CRUD operations)\n- Data processing and analytics endpoints\n- File upload and storage capabilities\n- Real-time WebSocket support\n- Input validation and sanitization\n- Error handling and logging\n- API rate limiting and security headers\n- Database integration with connection pooling\n\nTech Stack:\n- Node.js with Express and TypeScript\n- JWT for authentication\n- Bcrypt for password hashing\n- Multer for file uploads\n- Socket.io for real-time features\n- Helmet for security headers\n- Express-rate-limit for rate limiting\n- Winston for logging\n\nImplement proper middleware chain and database integration patterns.`;\n}\n\nfunction createDemoDatabasePrompt(): string {\n  return `Design a PostgreSQL database schema for a cloud-native solution with proper relationships and indexes.\n\nRequirements:\n- User management tables with roles and permissions\n- Data storage tables with proper relationships\n- Audit logging and versioning\n- Performance indexes on frequently queried columns\n- Data validation constraints\n- Backup and recovery procedures\n- Migration scripts for schema updates\n\nSchema Design:\n- Users table with authentication data\n- Profiles table with user details\n- Data tables with foreign key relationships\n- Audit logs table for tracking changes\n- Sessions table for active user sessions\n- Configuration table for application settings\n\nInclude proper data types, constraints, and indexes for optimal performance.`;\n}\n\nfunction createDemoDevOpsConfig(): any {\n  return {\n    \"terraform\": {\n      \"provider\": {\n        \"aws\": {\n          \"region\": \"us-west-2\"\n        }\n      },\n      \"resource\": {\n        \"aws_instance\": {\n          \"web_server\": {\n            \"ami\": \"ami-0c55b159cbfafe1d0\",\n            \"instance_type\": \"t3.micro\",\n            \"tags\": {\n              \"Name\": \"WebServer\",\n              \"Environment\": \"production\"\n            }\n          }\n        },\n        \"aws_rds_instance\": {\n          \"database\": {\n            \"engine\": \"postgres\",\n            \"engine_version\": \"13.7\",\n            \"instance_class\": \"db.t3.micro\",\n            \"allocated_storage\": 20,\n            \"db_name\": \"appdb\",\n            \"username\": \"dbuser\",\n            \"password\": \"changeme123\",\n            \"skip_final_snapshot\": true\n          }\n        }\n      }\n    },\n    \"ansible\": {\n      \"playbook\": [\n        {\n          \"name\": \"Deploy Application\",\n          \"hosts\": \"webservers\",\n          \"tasks\": [\n            {\n              \"name\": \"Install Node.js\",\n              \"yum\": {\n                \"name\": \"nodejs\",\n                \"state\": \"present\"\n              }\n            },\n            {\n              \"name\": \"Deploy application\",\n              \"copy\": {\n                \"src\": \"./app\",\n                \"dest\": \"/opt/app\"\n              }\n            },\n            {\n              \"name\": \"Start application service\",\n              \"systemd\": {\n                \"name\": \"app\",\n                \"state\": \"started\",\n                \"enabled\": true\n              }\n            }\n          ]\n        }\n      ]\n    }\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,2DAA2D;AAC3D,wCAA4C;IAC1C,QAAQ,GAAG,CAAC,4BAA4B,GAAG;AAC7C;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC3C;AAEA,MAAM,gCAAgC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mFA+D4C,CAAC;AAE7E,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,4BAA4B,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3D,IAAI,CAAC,8BAA8B;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6C,GAAG;gBAAE,QAAQ;YAAI;QAClG;QAEA,iCAAiC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;YACrG,QAAQ,KAAK,CAAC;YAEd,uCAAuC;YACvC,MAAM,cAAc;gBAClB,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,QAAQ;YACV;YAEA,MAAM,kBAAkB,sBACtB,oIACA,YAAY,QAAQ,EACpB,YAAY,OAAO,EACnB,YAAY,QAAQ,EACpB,YAAY,MAAM;YAGpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,6BAA6B,MAAM,CAAC,WAAW,CAAC;QAEtG,gCAAgC;QAChC,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,GAAG,8BAA8B,8CAA8C,EAAE,8BAA8B;QAClI,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,MAAM,CAAC,WAAW,CAAC;QAEjE,IAAI;QACJ,IAAI;YACF,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,6BAA6B;YAC7B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,OAAO,EAAE,SAAS,UAAU;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,CAAC,kBAAkB,EAAE,SAAS,OAAO,IAAI,iBAAiB;gBACnE,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,yBAAyB,aAAa,MAAM;QACxD,QAAQ,GAAG,CAAC,qBAAqB,aAAa,SAAS,CAAC,GAAG;QAC3D,QAAQ,GAAG,CAAC;QAEZ,iDAAiD;QACjD,MAAM,gBAAgB,2BAA2B;QAEjD,QAAQ,GAAG,CAAC,mBAAmB;YAC7B,gBAAgB,cAAc,QAAQ,CAAC,MAAM;YAC7C,eAAe,cAAc,OAAO,CAAC,MAAM;YAC3C,gBAAgB,cAAc,QAAQ,CAAC,MAAM;YAC7C,cAAc,cAAc,MAAM,CAAC,MAAM;QAC3C;QAEA,2CAA2C;QAC3C,MAAM,kBAAkB,sBACtB,cACA,cAAc,QAAQ,EACtB,cAAc,OAAO,EACrB,cAAc,QAAQ,EACtB,cAAc,MAAM;QAGtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,SAAS;YACT,cAAc;YACd,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4C,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,sBACP,YAAoB,EACpB,cAAsB,EACtB,aAAqB,EACrB,cAAsB,EACtB,YAAoB;IAEpB,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,OAAO,CAAC;;UAEA,EAAE,YAAY;;;;;AAKxB,EAAE,aAAa;;;;;;;;;AASf,EAAE,eAAe;;;;;;AAMjB,EAAE,cAAc;;;;;;AAMhB,EAAE,eAAe;;;;;;AAMjB,EAAE,aAAa;;;;;iKAKkJ,CAAC;AAClK;AAEA,SAAS,2BAA2B,IAAY;IAC9C,MAAM,SAAS;QACb,UAAU;QACV,SAAS;QACT,UAAU;QACV,QAAQ;IACV;IAEA,QAAQ,GAAG,CAAC,2CAA2C,KAAK,MAAM;IAElE,0BAA0B;IAC1B,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,IAAI,eAAe;QACjB,OAAO,QAAQ,GAAG,aAAa,CAAC,EAAE,CAAC,IAAI;IACzC;IAEA,6BAA6B;IAC7B,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,IAAI,cAAc;QAChB,OAAO,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC,IAAI;IACvC;IAEA,iCAAiC;IACjC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,IAAI,eAAe;QACjB,OAAO,QAAQ,GAAG,aAAa,CAAC,EAAE,CAAC,IAAI;IACzC;IAEA,4BAA4B;IAC5B,MAAM,cAAc,KAAK,KAAK,CAAC;IAC/B,IAAI,aAAa;QACf,OAAO,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,qEAAqE;IACrE,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,IAAI,WAAW;YACb,OAAO,MAAM,GAAG,SAAS,CAAC,EAAE;QAC9B;IACF;IAEA,QAAQ,GAAG,CAAC,0BAA0B;QACpC,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QACtC,eAAe,OAAO,OAAO,CAAC,MAAM;QACpC,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QACtC,cAAc,OAAO,MAAM,CAAC,MAAM;IACpC;IAEA,OAAO;AACT;AAEA,oDAAoD;AACpD,SAAS;IACP,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;sFAqB4E,CAAC;AACvF;AAEA,SAAS;IACP,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;oEAwB0D,CAAC;AACrE;AAEA,SAAS;IACP,OAAO,CAAC;;;;;;;;;;;;;;;;;;;4EAmBkE,CAAC;AAC7E;AAEA,SAAS;IACP,OAAO;QACL,aAAa;YACX,YAAY;gBACV,OAAO;oBACL,UAAU;gBACZ;YACF;YACA,YAAY;gBACV,gBAAgB;oBACd,cAAc;wBACZ,OAAO;wBACP,iBAAiB;wBACjB,QAAQ;4BACN,QAAQ;4BACR,eAAe;wBACjB;oBACF;gBACF;gBACA,oBAAoB;oBAClB,YAAY;wBACV,UAAU;wBACV,kBAAkB;wBAClB,kBAAkB;wBAClB,qBAAqB;wBACrB,WAAW;wBACX,YAAY;wBACZ,YAAY;wBACZ,uBAAuB;oBACzB;gBACF;YACF;QACF;QACA,WAAW;YACT,YAAY;gBACV;oBACE,QAAQ;oBACR,SAAS;oBACT,SAAS;wBACP;4BACE,QAAQ;4BACR,OAAO;gCACL,QAAQ;gCACR,SAAS;4BACX;wBACF;wBACA;4BACE,QAAQ;4BACR,QAAQ;gCACN,OAAO;gCACP,QAAQ;4BACV;wBACF;wBACA;4BACE,QAAQ;4BACR,WAAW;gCACT,QAAQ;gCACR,SAAS;gCACT,WAAW;4BACb;wBACF;qBACD;gBACH;aACD;QACH;IACF;AACF", "debugId": null}}]}