{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/constant.js"], "sourcesContent": ["export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC;IACvB,OAAO,SAAS;QACd,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-path/src/path.js"], "sourcesContent": ["const pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction append(strings) {\n  this._ += strings[0];\n  for (let i = 1, n = strings.length; i < n; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  let d = Math.floor(digits);\n  if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  const k = 10 ** d;\n  return function(strings) {\n    this._ += strings[0];\n    for (let i = 1, n = strings.length; i < n; ++i) {\n      this._ += Math.round(arguments[i] * k) / k + strings[i];\n    }\n  };\n}\n\nexport class Path {\n  constructor(digits) {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n    this._append = digits == null ? append : appendRound(digits);\n  }\n  moveTo(x, y) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._append`Z`;\n    }\n  }\n  lineTo(x, y) {\n    this._append`L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arcTo(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._append`M${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._append`L${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      let x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;\n      }\n\n      this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;\n    }\n  }\n  arc(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._append`M${x0},${y0}`;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._append`L${x0},${y0}`;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;\n    }\n  }\n  rect(x, y, w, h) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;\n  }\n  toString() {\n    return this._;\n  }\n}\n\nexport function path() {\n  return new Path;\n}\n\n// Allow instanceof d3.path\npath.prototype = Path.prototype;\n\nexport function pathRound(digits = 3) {\n  return new Path(+digits);\n}\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,KAAK,KAAK,EAAE,EACd,MAAM,IAAI,IACV,UAAU,MACV,aAAa,MAAM;AAEvB,SAAS,OAAO,OAAO;IACrB,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,EAAE;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;QAC9C,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IACrC;AACF;AAEA,SAAS,YAAY,MAAM;IACzB,IAAI,IAAI,KAAK,KAAK,CAAC;IACnB,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;IAC1D,IAAI,IAAI,IAAI,OAAO;IACnB,MAAM,IAAI,MAAM;IAChB,OAAO,SAAS,OAAO;QACrB,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,EAAE;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;YAC9C,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,KAAK,IAAI,OAAO,CAAC,EAAE;QACzD;IACF;AACF;AAEO,MAAM;IACX,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GACnB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,yBAAyB;QACrD,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,OAAO,GAAG,UAAU,OAAO,SAAS,YAAY;IACvD;IACA,OAAO,CAAC,EAAE,CAAC,EAAE;QACX,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IACxE;IACA,YAAY;QACV,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM;YACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;YACxC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QACjB;IACF;IACA,OAAO,CAAC,EAAE,CAAC,EAAE;QACX,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IAClD;IACA,iBAAiB,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC7B,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IAChE;IACA,cAAc,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAClC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;IAC9E;IACA,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;QACvB,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;QAE7C,iCAAiC;QACjC,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,GAAG;QAElD,IAAI,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;QAE9B,uCAAuC;QACvC,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM;YACrB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAClD,OAGK,IAAI,CAAC,CAAC,QAAQ,OAAO;aAKrB,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK,CAAC,GAAG;YAC3D,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAClD,OAGK;YACH,IAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,IAAI,CAAC,QAChB,MAAM,KAAK,IAAI,CAAC,QAChB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,QAAQ,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,GAAG,EAAE,IAAI,IAC/E,MAAM,IAAI,KACV,MAAM,IAAI;YAEd,gEAAgE;YAChE,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,SAAS;gBAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC;YACpD;YAEA,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,MAAM,IAAI,CAAC;QACpH;IACF;IACA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;QACxB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;QAEhC,iCAAiC;QACjC,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,GAAG;QAElD,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,KAClB,KAAK,IAAI,KAAK,GAAG,CAAC,KAClB,KAAK,IAAI,IACT,KAAK,IAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;QAE9B,uCAAuC;QACvC,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM;YACrB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;QAC5B,OAGK,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,SAAS;YAC/E,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;QAC5B;QAEA,iCAAiC;QACjC,IAAI,CAAC,GAAG;QAER,uDAAuD;QACvD,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM;QAE5B,mEAAmE;QACnE,IAAI,KAAK,YAAY;YACnB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAC9G,OAGK,IAAI,KAAK,SAAS;YACrB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC;QACvH;IACF;IACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACf,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/F;IACA,WAAW;QACT,OAAO,IAAI,CAAC,CAAC;IACf;AACF;AAEO,SAAS;IACd,OAAO,IAAI;AACb;AAEA,2BAA2B;AAC3B,KAAK,SAAS,GAAG,KAAK,SAAS;AAExB,SAAS,UAAU,SAAS,CAAC;IAClC,OAAO,IAAI,KAAK,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/path.js"], "sourcesContent": ["import {Path} from \"d3-path\";\n\nexport function withPath(shape) {\n  let digits = 3;\n\n  shape.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n\n  return () => new Path(digits);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,SAAS,KAAK;IAC5B,IAAI,SAAS;IAEb,MAAM,MAAM,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO;QAC9B,IAAI,KAAK,MAAM;YACb,SAAS;QACX,OAAO;YACL,MAAM,IAAI,KAAK,KAAK,CAAC;YACrB,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,GAAG;YAC1D,SAAS;QACX;QACA,OAAO;IACT;IAEA,OAAO,IAAM,IAAI,yIAAA,CAAA,OAAI,CAAC;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/d3-shape/src/arc.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\nimport {withPath} from \"./path.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null,\n      path = withPath(arc);\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle. If this\n        // intersection fails, it’s probably because the arc is too small, so\n        // disable the corner radius entirely.\n        if (da < pi) {\n          if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {\n            var ax = x01 - oc[0],\n                ay = y01 - oc[1],\n                bx = x11 - oc[0],\n                by = y11 - oc[1],\n                kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n                lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n            rc0 = min(rc, (r0 - lc) / (kc - 1));\n            rc1 = min(rc, (r1 - lc) / (kc + 1));\n          } else {\n            rc0 = rc1 = 0;\n          }\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,eAAe,CAAC;IACvB,OAAO,EAAE,WAAW;AACtB;AAEA,SAAS,eAAe,CAAC;IACvB,OAAO,EAAE,WAAW;AACtB;AAEA,SAAS,cAAc,CAAC;IACtB,OAAO,EAAE,UAAU;AACrB;AAEA,SAAS,YAAY,CAAC;IACpB,OAAO,EAAE,QAAQ;AACnB;AAEA,SAAS,YAAY,CAAC;IACpB,OAAO,KAAK,EAAE,QAAQ,EAAE,kBAAkB;AAC5C;AAEA,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAC/C,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,IAAI,MAAM,MAAM,MAAM;IAC1B,IAAI,IAAI,IAAI,0IAAA,CAAA,UAAO,EAAE;IACrB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI;IAC1C,OAAO;QAAC,KAAK,IAAI;QAAK,KAAK,IAAI;KAAI;AACrC;AAEA,kDAAkD;AAClD,4DAA4D;AAC5D,SAAS,eAAe,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAChD,IAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,IAAI,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,MAAM,MAAM,MAAM,MAC9C,KAAK,KAAK,KACV,KAAK,CAAC,KAAK,KACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,CAAC,MAAM,GAAG,IAAI,GACpB,MAAM,CAAC,MAAM,GAAG,IAAI,GACpB,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,IAAI,KAAK,IACT,IAAI,MAAM,MAAM,MAAM,KACtB,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,GAAG,IAAI,IAAI,KAAK,IAAI,KACrD,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAC1B,MAAM,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAC3B,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAC1B,MAAM,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAC3B,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM;IAEhB,kDAAkD;IAClD,qEAAqE;IACrE,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM;IAEpE,OAAO;QACL,IAAI;QACJ,IAAI;QACJ,KAAK,CAAC;QACN,KAAK,CAAC;QACN,KAAK,MAAM,CAAC,KAAK,IAAI,CAAC;QACtB,KAAK,MAAM,CAAC,KAAK,IAAI,CAAC;IACxB;AACF;AAEe;IACb,IAAI,cAAc,gBACd,cAAc,gBACd,eAAe,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,IACxB,YAAY,MACZ,aAAa,eACb,WAAW,aACX,WAAW,aACX,UAAU,MACV,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;IAEpB,SAAS;QACP,IAAI,QACA,GACA,KAAK,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,YAC9B,KAAK,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,YAC9B,KAAK,WAAW,KAAK,CAAC,IAAI,EAAE,aAAa,0IAAA,CAAA,SAAM,EAC/C,KAAK,SAAS,KAAK,CAAC,IAAI,EAAE,aAAa,0IAAA,CAAA,SAAM,EAC7C,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KACd,KAAK,KAAK;QAEd,IAAI,CAAC,SAAS,UAAU,SAAS;QAEjC,uEAAuE;QACvE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK;QAEnC,iBAAiB;QACjB,IAAI,CAAC,CAAC,KAAK,0IAAA,CAAA,UAAO,GAAG,QAAQ,MAAM,CAAC,GAAG;aAGlC,IAAI,KAAK,0IAAA,CAAA,MAAG,GAAG,0IAAA,CAAA,UAAO,EAAE;YAC3B,QAAQ,MAAM,CAAC,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE;YACtC,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC;YAC/B,IAAI,KAAK,0IAAA,CAAA,UAAO,EAAE;gBAChB,QAAQ,MAAM,CAAC,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE;gBACtC,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI;YAChC;QACF,OAGK;YACH,IAAI,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,KAAK,SAAS,KAAK,CAAC,IAAI,EAAE,aAAa,GACvC,KAAK,AAAC,KAAK,0IAAA,CAAA,UAAO,IAAK,CAAC,YAAY,CAAC,UAAU,KAAK,CAAC,IAAI,EAAE,aAAa,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,KAAK,KAAK,GAAG,GAC/F,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,KAAK,MAAM,GAAG,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE,aACrD,MAAM,IACN,MAAM,IACN,IACA;YAEJ,qDAAqD;YACrD,IAAI,KAAK,0IAAA,CAAA,UAAO,EAAE;gBAChB,IAAI,KAAK,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACxB,KAAK,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE;gBAC5B,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,0IAAA,CAAA,UAAO,EAAE,MAAO,KAAK,IAAI,CAAC,GAAI,OAAO,IAAI,OAAO;qBACjE,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,EAAE,IAAI;gBACtC,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,0IAAA,CAAA,UAAO,EAAE,MAAO,KAAK,IAAI,CAAC,GAAI,OAAO,IAAI,OAAO;qBACjE,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,EAAE,IAAI;YACxC;YAEA,IAAI,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACf,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACf,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACf,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE;YAEnB,yBAAyB;YACzB,IAAI,KAAK,0IAAA,CAAA,UAAO,EAAE;gBAChB,IAAI,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACf,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACf,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACf,MAAM,KAAK,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,MACf;gBAEJ,oEAAoE;gBACpE,qEAAqE;gBACrE,sCAAsC;gBACtC,IAAI,KAAK,0IAAA,CAAA,KAAE,EAAE;oBACX,IAAI,KAAK,UAAU,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;wBAC1D,IAAI,KAAK,MAAM,EAAE,CAAC,EAAE,EAChB,KAAK,MAAM,EAAE,CAAC,EAAE,EAChB,KAAK,MAAM,EAAE,CAAC,EAAE,EAChB,KAAK,MAAM,EAAE,CAAC,EAAE,EAChB,KAAK,IAAI,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,CAAC,KAAK,KAAK,KAAK,EAAE,IAAI,CAAC,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,KAAK,KAAK,MAAM,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,KAAK,KAAK,GAAG,KAAK,IAC/F,KAAK,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;wBAC3C,MAAM,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;wBACjC,MAAM,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;oBACnC,OAAO;wBACL,MAAM,MAAM;oBACd;gBACF;YACF;YAEA,qCAAqC;YACrC,IAAI,CAAC,CAAC,MAAM,0IAAA,CAAA,UAAO,GAAG,QAAQ,MAAM,CAAC,KAAK;iBAGrC,IAAI,MAAM,0IAAA,CAAA,UAAO,EAAE;gBACtB,KAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;gBACjD,KAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;gBAEjD,QAAQ,MAAM,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;gBAE7C,2BAA2B;gBAC3B,IAAI,MAAM,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;qBAGvF;oBACH,QAAQ,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;oBAC9E,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;oBACrG,QAAQ,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;gBAChF;YACF,OAGK,QAAQ,MAAM,CAAC,KAAK,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,KAAK,CAAC;YAEhE,sDAAsD;YACtD,8DAA8D;YAC9D,IAAI,CAAC,CAAC,KAAK,0IAAA,CAAA,UAAO,KAAK,CAAC,CAAC,MAAM,0IAAA,CAAA,UAAO,GAAG,QAAQ,MAAM,CAAC,KAAK;iBAGxD,IAAI,MAAM,0IAAA,CAAA,UAAO,EAAE;gBACtB,KAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK;gBAClD,KAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK;gBAElD,QAAQ,MAAM,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG;gBAE7C,2BAA2B;gBAC3B,IAAI,MAAM,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;qBAGvF;oBACH,QAAQ,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;oBAC9E,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG;oBACpG,QAAQ,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAA,GAAA,0IAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;gBAChF;YACF,OAGK,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,KAAK;QACvC;QAEA,QAAQ,SAAS;QAEjB,IAAI,QAAQ,OAAO,UAAU,MAAM,SAAS,MAAM;IACpD;IAEA,IAAI,QAAQ,GAAG;QACb,IAAI,IAAI,CAAC,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,UAAU,IAAI,GAClF,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,SAAS,KAAK,CAAC,IAAI,EAAE,UAAU,IAAI,IAAI,0IAAA,CAAA,KAAE,GAAG;QAC3F,OAAO;YAAC,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,KAAK;YAAG,CAAA,GAAA,0IAAA,CAAA,MAAG,AAAD,EAAE,KAAK;SAAE;IACjC;IAEA,IAAI,WAAW,GAAG,SAAS,CAAC;QAC1B,OAAO,UAAU,MAAM,GAAG,CAAC,cAAc,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,GAAG,IAAI;IAC9F;IAEA,IAAI,WAAW,GAAG,SAAS,CAAC;QAC1B,OAAO,UAAU,MAAM,GAAG,CAAC,cAAc,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,GAAG,IAAI;IAC9F;IAEA,IAAI,YAAY,GAAG,SAAS,CAAC;QAC3B,OAAO,UAAU,MAAM,GAAG,CAAC,eAAe,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,GAAG,IAAI;IAC/F;IAEA,IAAI,SAAS,GAAG,SAAS,CAAC;QACxB,OAAO,UAAU,MAAM,GAAG,CAAC,YAAY,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,GAAG,IAAI;IAC/G;IAEA,IAAI,UAAU,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,aAAa,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,GAAG,IAAI;IAC7F;IAEA,IAAI,QAAQ,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,GAAG,IAAI;IAC3F;IAEA,IAAI,QAAQ,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,GAAG,IAAI;IAC3F;IAEA,IAAI,OAAO,GAAG,SAAS,CAAC;QACtB,OAAO,UAAU,MAAM,GAAG,CAAC,AAAC,UAAU,KAAK,OAAO,OAAO,GAAI,GAAG,IAAI;IACtE;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/mermaid/dist/timeline-definition-bf702344.js"], "sourcesContent": ["import { L as commonDb, v as clear$1, c as getConfig, l as log, o as setupGraphViewbox } from \"./mermaid-6dc72991.js\";\nimport { select, arc } from \"d3\";\nimport { isDark, lighten, darken } from \"khroma\";\nimport \"ts-dedent\";\nimport \"dayjs\";\nimport \"@braintree/sanitize-url\";\nimport \"dompurify\";\nimport \"lodash-es/memoize.js\";\nimport \"lodash-es/merge.js\";\nimport \"stylis\";\nimport \"lodash-es/isEmpty.js\";\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 20, 21], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 16], $V7 = [1, 17];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"timeline\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"period_statement\": 18, \"event_statement\": 19, \"period\": 20, \"event\": 21, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"timeline\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 20: \"period\", 21: \"event\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 1], [18, 1], [19, 1]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.getCommonDb().setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 15:\n          yy.addTask($$[$0], 0, \"\");\n          this.$ = $$[$0];\n          break;\n        case 16:\n          yy.addEvent($$[$0].substr(2));\n          this.$ = $$[$0];\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 18, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 19] }, { 15: [1, 20] }, o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10])],\n    defaultActions: {},\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: { \"case-insensitive\": true },\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n          case 6:\n            return 11;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n          case 14:\n            return 17;\n          case 15:\n            return 21;\n          case 16:\n            return 20;\n          case 17:\n            return 6;\n          case 18:\n            return \"INVALID\";\n        }\n      },\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:timeline\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^#:\\n;]+)/i, /^(?::\\s[^#:\\n;]+)/i, /^(?:[^#:\\n;]+)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst parser$1 = parser;\nlet currentSection = \"\";\nlet currentTaskId = 0;\nconst sections = [];\nconst tasks = [];\nconst rawTasks = [];\nconst getCommonDb = () => commonDb;\nconst clear = function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  clear$1();\n};\nconst addSection = function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\nconst getSections = function() {\n  return sections;\n};\nconst getTasks = function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n};\nconst addTask = function(period, length, event) {\n  const rawTask = {\n    id: currentTaskId++,\n    section: currentSection,\n    type: currentSection,\n    task: period,\n    score: length ? length : 0,\n    //if event is defined, then add it the events array\n    events: event ? [event] : []\n  };\n  rawTasks.push(rawTask);\n};\nconst addEvent = function(event) {\n  const currentTask = rawTasks.find((task) => task.id === currentTaskId - 1);\n  currentTask.events.push(event);\n};\nconst addTaskOrg = function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n};\nconst compileTasks = function() {\n  const compileTask = function(pos) {\n    return rawTasks[pos].processed;\n  };\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\nconst timelineDb = {\n  clear,\n  getCommonDb,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  addEvent\n};\nconst db = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  addEvent,\n  addSection,\n  addTask,\n  addTaskOrg,\n  clear,\n  default: timelineDb,\n  getCommonDb,\n  getSections,\n  getTasks\n}, Symbol.toStringTag, { value: \"Module\" }));\nconst MAX_SECTIONS = 12;\nconst drawRect = function(elem, rectData) {\n  const rectElem = elem.append(\"rect\");\n  rectElem.attr(\"x\", rectData.x);\n  rectElem.attr(\"y\", rectData.y);\n  rectElem.attr(\"fill\", rectData.fill);\n  rectElem.attr(\"stroke\", rectData.stroke);\n  rectElem.attr(\"width\", rectData.width);\n  rectElem.attr(\"height\", rectData.height);\n  rectElem.attr(\"rx\", rectData.rx);\n  rectElem.attr(\"ry\", rectData.ry);\n  if (rectData.class !== void 0) {\n    rectElem.attr(\"class\", rectData.class);\n  }\n  return rectElem;\n};\nconst drawFace = function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc$1 = arc().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc$1).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  function sad(face2) {\n    const arc$1 = arc().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc$1).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n};\nconst drawCircle = function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n};\nconst drawText = function(elem, textData) {\n  const nText = textData.text.replace(/<br\\s*\\/?>/gi, \" \");\n  const textElem = elem.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class !== void 0) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const span = textElem.append(\"tspan\");\n  span.attr(\"x\", textData.x + textData.textMargin * 2);\n  span.text(nText);\n  return textElem;\n};\nconst drawLabel = function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n};\nconst drawSection = function(elem, section, conf) {\n  const g = elem.append(\"g\");\n  const rect = getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf,\n    section.colour\n  );\n};\nlet taskCount = -1;\nconst drawTask = function(elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  task.x + 14;\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf,\n    task.colour\n  );\n};\nconst drawBackgroundRect = function(elem, bounds) {\n  const rectElem = drawRect(elem, {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    class: \"rect\"\n  });\n  rectElem.lower();\n};\nconst getTextObj = function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    \"text-anchor\": \"start\",\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0\n  };\n};\nconst getNoteRect = function() {\n  return {\n    x: 0,\n    y: 0,\n    width: 100,\n    anchor: \"start\",\n    height: 100,\n    rx: 0,\n    ry: 0\n  };\n};\nconst _drawTextCandidateFunc = function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  return function(conf) {\n    return conf.textPlacement === \"fo\" ? byFo : conf.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nconst initGraphics = function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n};\nfunction wrap(text, width) {\n  text.each(function() {\n    var text2 = select(this), words = text2.text().split(/(\\s+|<br>)/).reverse(), word, line = [], lineHeight = 1.1, y = text2.attr(\"y\"), dy = parseFloat(text2.attr(\"dy\")), tspan = text2.text(null).append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", dy + \"em\");\n    for (let j = 0; j < words.length; j++) {\n      word = words[words.length - 1 - j];\n      line.push(word);\n      tspan.text(line.join(\" \").trim());\n      if (tspan.node().getComputedTextLength() > width || word === \"<br>\") {\n        line.pop();\n        tspan.text(line.join(\" \").trim());\n        if (word === \"<br>\") {\n          line = [\"\"];\n        } else {\n          line = [word];\n        }\n        tspan = text2.append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", lineHeight + \"em\").text(word);\n      }\n    }\n  });\n}\nconst drawNode = function(elem, node, fullSection, conf) {\n  const section = fullSection % MAX_SECTIONS - 1;\n  const nodeElem = elem.append(\"g\");\n  node.section = section;\n  nodeElem.attr(\n    \"class\",\n    (node.class ? node.class + \" \" : \"\") + \"timeline-node \" + (\"section-\" + section)\n  );\n  const bkgElem = nodeElem.append(\"g\");\n  const textElem = nodeElem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize && conf.fontSize.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  node.height = bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n  node.height = Math.max(node.height, node.maxHeight);\n  node.width = node.width + 2 * node.padding;\n  textElem.attr(\"transform\", \"translate(\" + node.width / 2 + \", \" + node.padding / 2 + \")\");\n  defaultBkg(bkgElem, node, section);\n  return node;\n};\nconst getVirtualNodeHeight = function(elem, node, conf) {\n  const textElem = elem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize && conf.fontSize.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  textElem.remove();\n  return bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n};\nconst defaultBkg = function(elem, node, section) {\n  const rd = 5;\n  elem.append(\"path\").attr(\"id\", \"node-\" + node.id).attr(\"class\", \"node-bkg node-\" + node.type).attr(\n    \"d\",\n    `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${node.width - 2 * rd} q5,0 5,5 v${node.height - rd} H0 Z`\n  );\n  elem.append(\"line\").attr(\"class\", \"node-line-\" + section).attr(\"x1\", 0).attr(\"y1\", node.height).attr(\"x2\", node.width).attr(\"y2\", node.height);\n};\nconst svgDraw = {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  getTextObj,\n  getNoteRect,\n  initGraphics,\n  drawNode,\n  getVirtualNodeHeight\n};\nconst draw = function(text, id, version, diagObj) {\n  var _a, _b;\n  const conf = getConfig();\n  const LEFT_MARGIN = conf.leftMargin ?? 50;\n  log.debug(\"timeline\", diagObj.db);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(\"#\" + id);\n  svg.append(\"g\");\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getCommonDb().getDiagramTitle();\n  log.debug(\"task\", tasks2);\n  svgDraw.initGraphics(svg);\n  const sections2 = diagObj.db.getSections();\n  log.debug(\"sections\", sections2);\n  let maxSectionHeight = 0;\n  let maxTaskHeight = 0;\n  let depthY = 0;\n  let sectionBeginY = 0;\n  let masterX = 50 + LEFT_MARGIN;\n  let masterY = 50;\n  sectionBeginY = 50;\n  let sectionNumber = 0;\n  let hasSections = true;\n  sections2.forEach(function(section) {\n    const sectionNode = {\n      number: sectionNumber,\n      descr: section,\n      section: sectionNumber,\n      width: 150,\n      padding: 20,\n      maxHeight: maxSectionHeight\n    };\n    const sectionHeight = svgDraw.getVirtualNodeHeight(svg, sectionNode, conf);\n    log.debug(\"sectionHeight before draw\", sectionHeight);\n    maxSectionHeight = Math.max(maxSectionHeight, sectionHeight + 20);\n  });\n  let maxEventCount = 0;\n  let maxEventLineLength = 0;\n  log.debug(\"tasks.length\", tasks2.length);\n  for (const [i, task] of tasks2.entries()) {\n    const taskNode = {\n      number: i,\n      descr: task,\n      section: task.section,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    const taskHeight = svgDraw.getVirtualNodeHeight(svg, taskNode, conf);\n    log.debug(\"taskHeight before draw\", taskHeight);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight + 20);\n    maxEventCount = Math.max(maxEventCount, task.events.length);\n    let maxEventLineLengthTemp = 0;\n    for (let j = 0; j < task.events.length; j++) {\n      const event = task.events[j];\n      const eventNode = {\n        descr: event,\n        section: task.section,\n        number: task.section,\n        width: 150,\n        padding: 20,\n        maxHeight: 50\n      };\n      maxEventLineLengthTemp += svgDraw.getVirtualNodeHeight(svg, eventNode, conf);\n    }\n    maxEventLineLength = Math.max(maxEventLineLength, maxEventLineLengthTemp);\n  }\n  log.debug(\"maxSectionHeight before draw\", maxSectionHeight);\n  log.debug(\"maxTaskHeight before draw\", maxTaskHeight);\n  if (sections2 && sections2.length > 0) {\n    sections2.forEach((section) => {\n      const tasksForSection = tasks2.filter((task) => task.section === section);\n      const sectionNode = {\n        number: sectionNumber,\n        descr: section,\n        section: sectionNumber,\n        width: 200 * Math.max(tasksForSection.length, 1) - 50,\n        padding: 20,\n        maxHeight: maxSectionHeight\n      };\n      log.debug(\"sectionNode\", sectionNode);\n      const sectionNodeWrapper = svg.append(\"g\");\n      const node = svgDraw.drawNode(sectionNodeWrapper, sectionNode, sectionNumber, conf);\n      log.debug(\"sectionNode output\", node);\n      sectionNodeWrapper.attr(\"transform\", `translate(${masterX}, ${sectionBeginY})`);\n      masterY += maxSectionHeight + 50;\n      if (tasksForSection.length > 0) {\n        drawTasks(\n          svg,\n          tasksForSection,\n          sectionNumber,\n          masterX,\n          masterY,\n          maxTaskHeight,\n          conf,\n          maxEventCount,\n          maxEventLineLength,\n          maxSectionHeight,\n          false\n        );\n      }\n      masterX += 200 * Math.max(tasksForSection.length, 1);\n      masterY = sectionBeginY;\n      sectionNumber++;\n    });\n  } else {\n    hasSections = false;\n    drawTasks(\n      svg,\n      tasks2,\n      sectionNumber,\n      masterX,\n      masterY,\n      maxTaskHeight,\n      conf,\n      maxEventCount,\n      maxEventLineLength,\n      maxSectionHeight,\n      true\n    );\n  }\n  const box = svg.node().getBBox();\n  log.debug(\"bounds\", box);\n  if (title) {\n    svg.append(\"text\").text(title).attr(\"x\", box.width / 2 - LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 20);\n  }\n  depthY = hasSections ? maxSectionHeight + maxTaskHeight + 150 : maxTaskHeight + 100;\n  const lineWrapper = svg.append(\"g\").attr(\"class\", \"lineWrapper\");\n  lineWrapper.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", depthY).attr(\"x2\", box.width + 3 * LEFT_MARGIN).attr(\"y2\", depthY).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  setupGraphViewbox(\n    void 0,\n    svg,\n    ((_a = conf.timeline) == null ? void 0 : _a.padding) ?? 50,\n    ((_b = conf.timeline) == null ? void 0 : _b.useMaxWidth) ?? false\n  );\n};\nconst drawTasks = function(diagram2, tasks2, sectionColor, masterX, masterY, maxTaskHeight, conf, maxEventCount, maxEventLineLength, maxSectionHeight, isWithoutSections) {\n  var _a;\n  for (const task of tasks2) {\n    const taskNode = {\n      descr: task.task,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    log.debug(\"taskNode\", taskNode);\n    const taskWrapper = diagram2.append(\"g\").attr(\"class\", \"taskWrapper\");\n    const node = svgDraw.drawNode(taskWrapper, taskNode, sectionColor, conf);\n    const taskHeight = node.height;\n    log.debug(\"taskHeight after draw\", taskHeight);\n    taskWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight);\n    if (task.events) {\n      const lineWrapper = diagram2.append(\"g\").attr(\"class\", \"lineWrapper\");\n      let lineLength = maxTaskHeight;\n      masterY += 100;\n      lineLength = lineLength + drawEvents(diagram2, task.events, sectionColor, masterX, masterY, conf);\n      masterY -= 100;\n      lineWrapper.append(\"line\").attr(\"x1\", masterX + 190 / 2).attr(\"y1\", masterY + maxTaskHeight).attr(\"x2\", masterX + 190 / 2).attr(\n        \"y2\",\n        masterY + maxTaskHeight + (isWithoutSections ? maxTaskHeight : maxSectionHeight) + maxEventLineLength + 120\n      ).attr(\"stroke-width\", 2).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\").attr(\"stroke-dasharray\", \"5,5\");\n    }\n    masterX = masterX + 200;\n    if (isWithoutSections && !((_a = conf.timeline) == null ? void 0 : _a.disableMulticolor)) {\n      sectionColor++;\n    }\n  }\n  masterY = masterY - 10;\n};\nconst drawEvents = function(diagram2, events, sectionColor, masterX, masterY, conf) {\n  let maxEventHeight = 0;\n  const eventBeginY = masterY;\n  masterY = masterY + 100;\n  for (const event of events) {\n    const eventNode = {\n      descr: event,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: 50\n    };\n    log.debug(\"eventNode\", eventNode);\n    const eventWrapper = diagram2.append(\"g\").attr(\"class\", \"eventWrapper\");\n    const node = svgDraw.drawNode(eventWrapper, eventNode, sectionColor, conf);\n    const eventHeight = node.height;\n    maxEventHeight = maxEventHeight + eventHeight;\n    eventWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    masterY = masterY + 10 + eventHeight;\n  }\n  masterY = eventBeginY;\n  return maxEventHeight;\n};\nconst renderer = {\n  setConf: () => {\n  },\n  draw\n};\nconst genSections = (options) => {\n  let sections2 = \"\";\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options[\"lineColor\" + i] = options[\"lineColor\" + i] || options[\"cScaleInv\" + i];\n    if (isDark(options[\"lineColor\" + i])) {\n      options[\"lineColor\" + i] = lighten(options[\"lineColor\" + i], 20);\n    } else {\n      options[\"lineColor\" + i] = darken(options[\"lineColor\" + i], 20);\n    }\n  }\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = \"\" + (17 - 3 * i);\n    sections2 += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${i - 1} path  {\n      fill: ${options[\"cScale\" + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options[\"cScaleLabel\" + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options[\"cScaleLabel\" + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options[\"cScale\" + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options[\"cScaleInv\" + i]} ;\n      stroke-width: 3;\n    }\n\n    .lineWrapper line{\n      stroke: ${options[\"cScaleLabel\" + i]} ;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections2;\n};\nconst getStyles = (options) => `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .eventWrapper  {\n   filter: brightness(120%);\n  }\n`;\nconst styles = getStyles;\nconst diagram = {\n  db,\n  renderer,\n  parser: parser$1,\n  styles\n};\nexport {\n  diagram\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAKA,IAAI,SAAS;IACX,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;QAC1B,IAAK,KAAK,MAAM,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG;QAElD,OAAO;IACT,GAAG,MAAM;QAAC;QAAG;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG,EAAE,MAAM;QAAC;QAAG;KAAE,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG,EAAE,MAAM;QAAC;QAAG;KAAG;IACvJ,IAAI,UAAU;QACZ,OAAO,SAAS,SAChB;QACA,IAAI,CAAC;QACL,UAAU;YAAE,SAAS;YAAG,SAAS;YAAG,YAAY;YAAG,YAAY;YAAG,OAAO;YAAG,QAAQ;YAAG,SAAS;YAAG,aAAa;YAAG,WAAW;YAAI,SAAS;YAAI,aAAa;YAAI,mBAAmB;YAAI,aAAa;YAAI,mBAAmB;YAAI,6BAA6B;YAAI,WAAW;YAAI,oBAAoB;YAAI,mBAAmB;YAAI,UAAU;YAAI,SAAS;YAAI,WAAW;YAAG,QAAQ;QAAE;QACjX,YAAY;YAAE,GAAG;YAAS,GAAG;YAAY,GAAG;YAAO,GAAG;YAAS,IAAI;YAAW,IAAI;YAAS,IAAI;YAAa,IAAI;YAAmB,IAAI;YAAa,IAAI;YAAmB,IAAI;YAA6B,IAAI;YAAW,IAAI;YAAU,IAAI;QAAQ;QACrP,cAAc;YAAC;YAAG;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAG;aAAE;YAAE;gBAAC;gBAAI;aAAE;YAAE;gBAAC;gBAAI;aAAE;SAAC;QACnJ,eAAe,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;YAC7E,IAAI,KAAK,GAAG,MAAM,GAAG;YACrB,OAAQ;gBACN,KAAK;oBACH,OAAO,EAAE,CAAC,KAAK,EAAE;gBACnB,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE;oBACX;gBACF,KAAK;oBACH,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;oBACtB,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE;oBACnB;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE;oBACX;gBACF,KAAK;oBACH,GAAG,WAAW,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC/C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvB;gBACF,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;oBACpB,GAAG,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;oBACnC;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;oBACpB,GAAG,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACzC;gBACF,KAAK;oBACH,GAAG,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvB;gBACF,KAAK;oBACH,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG;oBACtB,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;gBACF,KAAK;oBACH,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC1B,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;oBACf;YACJ;QACF;QACA,OAAO;YAAC;gBAAE,GAAG;gBAAG,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAG;gBAAE,GAAG;oBAAC;iBAAE;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAE,EAAE;gBAAE,GAAG;YAAE;YAAI;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;gBAAE,GAAG;gBAAG,GAAG;oBAAC;oBAAG;iBAAE;gBAAE,GAAG;gBAAG,IAAI;oBAAC;oBAAG;iBAAE;gBAAE,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;YAAI;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAE,EAAE;gBAAE,GAAG;oBAAC;oBAAG;iBAAE;YAAC;YAAI,EAAE,KAAK;gBAAC;gBAAG;aAAE;YAAG;gBAAE,GAAG;gBAAI,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAK,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAK,IAAI;YAAI;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAE;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAE;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAE;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG;gBAAE,IAAI;oBAAC;oBAAG;iBAAG;YAAC;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAE;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAE;YAAG,EAAE,KAAK;gBAAC;gBAAG;aAAG;SAAE;QACrjB,gBAAgB,CAAC;QACjB,YAAY,SAAS,WAAW,GAAG,EAAE,IAAI;YACvC,IAAI,KAAK,WAAW,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC;YACb,OAAO;gBACL,IAAI,QAAQ,IAAI,MAAM;gBACtB,MAAM,IAAI,GAAG;gBACb,MAAM;YACR;QACF;QACA,OAAO,SAAS,MAAM,KAAK;YACzB,IAAI,OAAO,IAAI,EAAE,QAAQ;gBAAC;aAAE,EAAE,SAAS,EAAE,EAAE,SAAS;gBAAC;aAAK,EAAE,SAAS,EAAE,EAAE,QAAQ,IAAI,CAAC,KAAK,EAAE,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM;YACtJ,IAAI,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW;YACxC,IAAI,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK;YACrC,IAAI,cAAc;gBAAE,IAAI,CAAC;YAAE;YAC3B,IAAK,IAAI,KAAK,IAAI,CAAC,EAAE,CAAE;gBACrB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI;oBACpD,YAAY,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE;gBAChC;YACF;YACA,OAAO,QAAQ,CAAC,OAAO,YAAY,EAAE;YACrC,YAAY,EAAE,CAAC,KAAK,GAAG;YACvB,YAAY,EAAE,CAAC,MAAM,GAAG,IAAI;YAC5B,IAAI,OAAO,OAAO,MAAM,IAAI,aAAa;gBACvC,OAAO,MAAM,GAAG,CAAC;YACnB;YACA,IAAI,QAAQ,OAAO,MAAM;YACzB,OAAO,IAAI,CAAC;YACZ,IAAI,SAAS,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM;YACpD,IAAI,OAAO,YAAY,EAAE,CAAC,UAAU,KAAK,YAAY;gBACnD,IAAI,CAAC,UAAU,GAAG,YAAY,EAAE,CAAC,UAAU;YAC7C,OAAO;gBACL,IAAI,CAAC,UAAU,GAAG,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAC1D;YACA,SAAS;gBACP,IAAI;gBACJ,QAAQ,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM;gBACxC,IAAI,OAAO,UAAU,UAAU;oBAC7B,IAAI,iBAAiB,OAAO;wBAC1B,SAAS;wBACT,QAAQ,OAAO,GAAG;oBACpB;oBACA,QAAQ,KAAK,QAAQ,CAAC,MAAM,IAAI;gBAClC;gBACA,OAAO;YACT;YACA,IAAI,QAAQ,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;YAC5D,MAAO,KAAM;gBACX,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;gBAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;oBAC9B,SAAS,IAAI,CAAC,cAAc,CAAC,MAAM;gBACrC,OAAO;oBACL,IAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;wBACnD,SAAS;oBACX;oBACA,SAAS,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO;gBAC/C;gBACA,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;oBACjE,IAAI,SAAS;oBACb,WAAW,EAAE;oBACb,IAAK,KAAK,KAAK,CAAC,MAAM,CAAE;wBACtB,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,IAAI,QAAQ;4BACpC,SAAS,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG;wBAC3C;oBACF;oBACA,IAAI,OAAO,YAAY,EAAE;wBACvB,SAAS,yBAAyB,CAAC,WAAW,CAAC,IAAI,QAAQ,OAAO,YAAY,KAAK,iBAAiB,SAAS,IAAI,CAAC,QAAQ,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,MAAM,IAAI;oBAC9K,OAAO;wBACL,SAAS,yBAAyB,CAAC,WAAW,CAAC,IAAI,kBAAkB,CAAC,UAAU,MAAM,iBAAiB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;oBACxJ;oBACA,IAAI,CAAC,UAAU,CAAC,QAAQ;wBACtB,MAAM,OAAO,KAAK;wBAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI;wBAClC,MAAM,OAAO,QAAQ;wBACrB,KAAK;wBACL;oBACF;gBACF;gBACA,IAAI,MAAM,CAAC,EAAE,YAAY,SAAS,OAAO,MAAM,GAAG,GAAG;oBACnD,MAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc;gBAC9F;gBACA,OAAQ,MAAM,CAAC,EAAE;oBACf,KAAK;wBACH,MAAM,IAAI,CAAC;wBACX,OAAO,IAAI,CAAC,OAAO,MAAM;wBACzB,OAAO,IAAI,CAAC,OAAO,MAAM;wBACzB,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE;wBACpB,SAAS;wBACT;4BACE,SAAS,OAAO,MAAM;4BACtB,SAAS,OAAO,MAAM;4BACtB,WAAW,OAAO,QAAQ;4BAC1B,QAAQ,OAAO,MAAM;wBACvB;wBACA;oBACF,KAAK;wBACH,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;wBACrC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI;wBACrC,MAAM,EAAE,GAAG;4BACT,YAAY,MAAM,CAAC,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU;4BACzD,WAAW,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,SAAS;4BAC9C,cAAc,MAAM,CAAC,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY;4BAC7D,aAAa,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,WAAW;wBACpD;wBACA,IAAI,QAAQ;4BACV,MAAM,EAAE,CAAC,KAAK,GAAG;gCACf,MAAM,CAAC,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;gCAC3C,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE;6BACnC;wBACH;wBACA,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO;4BAClC;4BACA;4BACA;4BACA,YAAY,EAAE;4BACd,MAAM,CAAC,EAAE;4BACT;4BACA;yBACD,CAAC,MAAM,CAAC;wBACT,IAAI,OAAO,MAAM,aAAa;4BAC5B,OAAO;wBACT;wBACA,IAAI,KAAK;4BACP,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM;4BAClC,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI;4BAC9B,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI;wBAChC;wBACA,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;wBAC1C,OAAO,IAAI,CAAC,MAAM,CAAC;wBACnB,OAAO,IAAI,CAAC,MAAM,EAAE;wBACpB,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;wBAClE,MAAM,IAAI,CAAC;wBACX;oBACF,KAAK;wBACH,OAAO;gBACX;YACF;YACA,OAAO;QACT;IACF;IACA,IAAI,QAAQ;QACV,IAAI,SAAS;YACX,KAAK;YACL,YAAY,SAAS,WAAW,GAAG,EAAE,IAAI;gBACvC,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBAClB,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK;gBACjC,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF;YACA,mCAAmC;YACnC,UAAU,SAAS,KAAK,EAAE,EAAE;gBAC1B,IAAI,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC;gBAC5B,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG;gBAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG;gBAC1C,IAAI,CAAC,cAAc,GAAG;oBAAC;iBAAU;gBACjC,IAAI,CAAC,MAAM,GAAG;oBACZ,YAAY;oBACZ,cAAc;oBACd,WAAW;oBACX,aAAa;gBACf;gBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;wBAAC;wBAAG;qBAAE;gBAC5B;gBACA,IAAI,CAAC,MAAM,GAAG;gBACd,OAAO,IAAI;YACb;YACA,+CAA+C;YAC/C,OAAO;gBACL,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;gBACvB,IAAI,CAAC,MAAM,IAAI;gBACf,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,KAAK,IAAI;gBACd,IAAI,CAAC,OAAO,IAAI;gBAChB,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,IAAI,OAAO;oBACT,IAAI,CAAC,QAAQ;oBACb,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvB,OAAO;oBACL,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;gBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACtB;gBACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChC,OAAO;YACT;YACA,iDAAiD;YACjD,OAAO,SAAS,EAAE;gBAChB,IAAI,MAAM,GAAG,MAAM;gBACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;gBACzD,IAAI,CAAC,MAAM,IAAI;gBACf,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;gBACtD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;gBAC5D,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,IAAI,CAAC,QAAQ,IAAI,MAAM,MAAM,GAAG;gBAClC;gBACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;gBACzB,IAAI,CAAC,MAAM,GAAG;oBACZ,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU;oBAClC,WAAW,IAAI,CAAC,QAAQ,GAAG;oBAC3B,cAAc,IAAI,CAAC,MAAM,CAAC,YAAY;oBACtC,aAAa,QAAQ,CAAC,MAAM,MAAM,KAAK,SAAS,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG;gBAC1L;gBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;wBAAC,CAAC,CAAC,EAAE;wBAAE,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG;qBAAI;gBACtD;gBACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;gBAChC,OAAO,IAAI;YACb;YACA,6EAA6E;YAC7E,MAAM;gBACJ,IAAI,CAAC,KAAK,GAAG;gBACb,OAAO,IAAI;YACb;YACA,kJAAkJ;YAClJ,QAAQ;gBACN,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;oBAChC,IAAI,CAAC,UAAU,GAAG;gBACpB,OAAO;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,qIAAqI,IAAI,CAAC,YAAY,IAAI;wBAChO,MAAM;wBACN,OAAO;wBACP,MAAM,IAAI,CAAC,QAAQ;oBACrB;gBACF;gBACA,OAAO,IAAI;YACb;YACA,yCAAyC;YACzC,MAAM,SAAS,CAAC;gBACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YAC9B;YACA,0DAA0D;YAC1D,WAAW;gBACT,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;gBACzE,OAAO,CAAC,KAAK,MAAM,GAAG,KAAK,QAAQ,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO;YAC3E;YACA,mDAAmD;YACnD,eAAe;gBACb,IAAI,OAAO,IAAI,CAAC,KAAK;gBACrB,IAAI,KAAK,MAAM,GAAG,IAAI;oBACpB,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,KAAK,MAAM;gBAChD;gBACA,OAAO,CAAC,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,GAAG,KAAK,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO;YAC/E;YACA,2FAA2F;YAC3F,cAAc;gBACZ,IAAI,MAAM,IAAI,CAAC,SAAS;gBACxB,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC;gBACvC,OAAO,MAAM,IAAI,CAAC,aAAa,KAAK,OAAO,IAAI;YACjD;YACA,8EAA8E;YAC9E,YAAY,SAAS,KAAK,EAAE,YAAY;gBACtC,IAAI,OAAO,OAAO;gBAClB,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;oBAChC,SAAS;wBACP,UAAU,IAAI,CAAC,QAAQ;wBACvB,QAAQ;4BACN,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU;4BAClC,WAAW,IAAI,CAAC,SAAS;4BACzB,cAAc,IAAI,CAAC,MAAM,CAAC,YAAY;4BACtC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;wBACtC;wBACA,QAAQ,IAAI,CAAC,MAAM;wBACnB,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS,IAAI,CAAC,OAAO;wBACrB,SAAS,IAAI,CAAC,OAAO;wBACrB,QAAQ,IAAI,CAAC,MAAM;wBACnB,QAAQ,IAAI,CAAC,MAAM;wBACnB,OAAO,IAAI,CAAC,KAAK;wBACjB,QAAQ,IAAI,CAAC,MAAM;wBACnB,IAAI,IAAI,CAAC,EAAE;wBACX,gBAAgB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;wBAC1C,MAAM,IAAI,CAAC,IAAI;oBACjB;oBACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;wBACvB,OAAO,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;oBAChD;gBACF;gBACA,QAAQ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;gBACvB,IAAI,OAAO;oBACT,IAAI,CAAC,QAAQ,IAAI,MAAM,MAAM;gBAC/B;gBACA,IAAI,CAAC,MAAM,GAAG;oBACZ,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;oBACjC,WAAW,IAAI,CAAC,QAAQ,GAAG;oBAC3B,cAAc,IAAI,CAAC,MAAM,CAAC,WAAW;oBACrC,aAAa,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;gBACrJ;gBACA,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;gBACvB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE;gBACtB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;gBAChC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;wBAAC,IAAI,CAAC,MAAM;wBAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;qBAAC;gBAC/D;gBACA,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM;gBAC/C,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE;gBACxB,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,cAAc,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE;gBACtH,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;oBAC5B,IAAI,CAAC,IAAI,GAAG;gBACd;gBACA,IAAI,OAAO;oBACT,OAAO;gBACT,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;oBAC1B,IAAK,IAAI,KAAK,OAAQ;wBACpB,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;oBACrB;oBACA,OAAO;gBACT;gBACA,OAAO;YACT;YACA,6BAA6B;YAC7B,MAAM;gBACJ,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,OAAO,IAAI,CAAC,GAAG;gBACjB;gBACA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAChB,IAAI,CAAC,IAAI,GAAG;gBACd;gBACA,IAAI,OAAO,OAAO,WAAW;gBAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;oBACf,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,CAAC,KAAK,GAAG;gBACf;gBACA,IAAI,QAAQ,IAAI,CAAC,aAAa;gBAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClD,IAAI,aAAa,CAAC,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG;wBAClE,QAAQ;wBACR,QAAQ;wBACR,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;4BAChC,QAAQ,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,CAAC,EAAE;4BAC3C,IAAI,UAAU,OAAO;gCACnB,OAAO;4BACT,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;gCAC1B,QAAQ;gCACR;4BACF,OAAO;gCACL,OAAO;4BACT;wBACF,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;4BAC7B;wBACF;oBACF;gBACF;gBACA,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,MAAM;oBAC3C,IAAI,UAAU,OAAO;wBACnB,OAAO;oBACT;oBACA,OAAO;gBACT;gBACA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI;oBACtB,OAAO,IAAI,CAAC,GAAG;gBACjB,OAAO;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,2BAA2B,IAAI,CAAC,YAAY,IAAI;wBACtH,MAAM;wBACN,OAAO;wBACP,MAAM,IAAI,CAAC,QAAQ;oBACrB;gBACF;YACF;YACA,qCAAqC;YACrC,KAAK,SAAS;gBACZ,IAAI,IAAI,IAAI,CAAC,IAAI;gBACjB,IAAI,GAAG;oBACL,OAAO;gBACT,OAAO;oBACL,OAAO,IAAI,CAAC,GAAG;gBACjB;YACF;YACA,wGAAwG;YACxG,OAAO,SAAS,MAAM,SAAS;gBAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YAC3B;YACA,0EAA0E;YAC1E,UAAU,SAAS;gBACjB,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;gBACrC,IAAI,IAAI,GAAG;oBACT,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG;gBAChC,OAAO;oBACL,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE;gBAC/B;YACF;YACA,4FAA4F;YAC5F,eAAe,SAAS;gBACtB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,EAAE;oBACrF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK;gBACnF,OAAO;oBACL,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK;gBACzC;YACF;YACA,oJAAoJ;YACpJ,UAAU,SAAS,SAAS,CAAC;gBAC3B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,KAAK,GAAG,CAAC,KAAK;gBACnD,IAAI,KAAK,GAAG;oBACV,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE;gBAC/B,OAAO;oBACL,OAAO;gBACT;YACF;YACA,6BAA6B;YAC7B,WAAW,SAAS,UAAU,SAAS;gBACrC,IAAI,CAAC,KAAK,CAAC;YACb;YACA,qDAAqD;YACrD,gBAAgB,SAAS;gBACvB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;YACnC;YACA,SAAS;gBAAE,oBAAoB;YAAK;YACpC,eAAe,SAAS,UAAU,EAAE,EAAE,GAAG,EAAE,yBAAyB,EAAE,QAAQ;gBAC5E,OAAQ;oBACN,KAAK;wBACH;oBACF,KAAK;wBACH;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH;oBACF,KAAK;wBACH;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb,OAAO;oBACT,KAAK;wBACH,IAAI,CAAC,KAAK,CAAC;wBACX;oBACF,KAAK;wBACH,IAAI,CAAC,QAAQ;wBACb;oBACF,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;gBACX;YACF;YACA,OAAO;gBAAC;gBAAuB;gBAAuB;gBAAe;gBAAa;gBAAiB;gBAAoB;gBAAyB;gBAAyB;gBAAyB;gBAAyB;gBAAyB;gBAA0B;gBAAc;gBAAgB;gBAA4B;gBAAsB;gBAAmB;gBAAW;aAAU;YACtY,YAAY;gBAAE,uBAAuB;oBAAE,SAAS;wBAAC;wBAAI;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;qBAAG;oBAAE,aAAa;gBAAM;gBAAG,aAAa;oBAAE,SAAS;wBAAC;qBAAE;oBAAE,aAAa;gBAAM;gBAAG,WAAW;oBAAE,SAAS;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;qBAAG;oBAAE,aAAa;gBAAK;YAAE;QACxR;QACA,OAAO;IACT;IACA,QAAQ,KAAK,GAAG;IAChB,SAAS;QACP,IAAI,CAAC,EAAE,GAAG,CAAC;IACb;IACA,OAAO,SAAS,GAAG;IACnB,QAAQ,MAAM,GAAG;IACjB,OAAO,IAAI;AACb;AACA,OAAO,MAAM,GAAG;AAChB,MAAM,WAAW;AACjB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,MAAM,WAAW,EAAE;AACnB,MAAM,QAAQ,EAAE;AAChB,MAAM,WAAW,EAAE;AACnB,MAAM,cAAc,IAAM,sJAAA,CAAA,IAAQ;AAClC,MAAM,QAAQ;IACZ,SAAS,MAAM,GAAG;IAClB,MAAM,MAAM,GAAG;IACf,iBAAiB;IACjB,SAAS,MAAM,GAAG;IAClB,CAAA,GAAA,sJAAA,CAAA,IAAO,AAAD;AACR;AACA,MAAM,aAAa,SAAS,GAAG;IAC7B,iBAAiB;IACjB,SAAS,IAAI,CAAC;AAChB;AACA,MAAM,cAAc;IAClB,OAAO;AACT;AACA,MAAM,WAAW;IACf,IAAI,oBAAoB;IACxB,MAAM,WAAW;IACjB,IAAI,iBAAiB;IACrB,MAAO,CAAC,qBAAqB,iBAAiB,SAAU;QACtD,oBAAoB;QACpB;IACF;IACA,MAAM,IAAI,IAAI;IACd,OAAO;AACT;AACA,MAAM,UAAU,SAAS,MAAM,EAAE,MAAM,EAAE,KAAK;IAC5C,MAAM,UAAU;QACd,IAAI;QACJ,SAAS;QACT,MAAM;QACN,MAAM;QACN,OAAO,SAAS,SAAS;QACzB,mDAAmD;QACnD,QAAQ,QAAQ;YAAC;SAAM,GAAG,EAAE;IAC9B;IACA,SAAS,IAAI,CAAC;AAChB;AACA,MAAM,WAAW,SAAS,KAAK;IAC7B,MAAM,cAAc,SAAS,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK,gBAAgB;IACxE,YAAY,MAAM,CAAC,IAAI,CAAC;AAC1B;AACA,MAAM,aAAa,SAAS,KAAK;IAC/B,MAAM,UAAU;QACd,SAAS;QACT,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS,EAAE;IACb;IACA,MAAM,IAAI,CAAC;AACb;AACA,MAAM,eAAe;IACnB,MAAM,cAAc,SAAS,GAAG;QAC9B,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS;IAChC;IACA,IAAI,eAAe;IACnB,KAAK,MAAM,CAAC,GAAG,QAAQ,IAAI,SAAS,OAAO,GAAI;QAC7C,YAAY;QACZ,eAAe,gBAAgB,QAAQ,SAAS;IAClD;IACA,OAAO;AACT;AACA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;AACA,MAAM,KAAK,aAAa,GAAG,OAAO,MAAM,CAAC,aAAa,GAAG,OAAO,cAAc,CAAC;IAC7E,WAAW;IACX;IACA;IACA;IACA;IACA;IACA,SAAS;IACT;IACA;IACA;AACF,GAAG,OAAO,WAAW,EAAE;IAAE,OAAO;AAAS;AACzC,MAAM,eAAe;AACrB,MAAM,WAAW,SAAS,IAAI,EAAE,QAAQ;IACtC,MAAM,WAAW,KAAK,MAAM,CAAC;IAC7B,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC;IAC7B,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC;IAC7B,SAAS,IAAI,CAAC,QAAQ,SAAS,IAAI;IACnC,SAAS,IAAI,CAAC,UAAU,SAAS,MAAM;IACvC,SAAS,IAAI,CAAC,SAAS,SAAS,KAAK;IACrC,SAAS,IAAI,CAAC,UAAU,SAAS,MAAM;IACvC,SAAS,IAAI,CAAC,MAAM,SAAS,EAAE;IAC/B,SAAS,IAAI,CAAC,MAAM,SAAS,EAAE;IAC/B,IAAI,SAAS,KAAK,KAAK,KAAK,GAAG;QAC7B,SAAS,IAAI,CAAC,SAAS,SAAS,KAAK;IACvC;IACA,OAAO;AACT;AACA,MAAM,WAAW,SAAS,OAAO,EAAE,QAAQ;IACzC,MAAM,SAAS;IACf,MAAM,gBAAgB,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,SAAS,EAAE,EAAE,IAAI,CAAC,MAAM,SAAS,EAAE,EAAE,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY;IAChL,MAAM,OAAO,QAAQ,MAAM,CAAC;IAC5B,KAAK,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,UAAU;IAC3K,KAAK,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,UAAU;IAC3K,SAAS,MAAM,KAAK;QAClB,MAAM,QAAQ,CAAA,GAAA,2KAAA,CAAA,MAAG,AAAD,IAAI,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS;QACrH,MAAM,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,aAAa,eAAe,SAAS,EAAE,GAAG,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI;IACxI;IACA,SAAS,IAAI,KAAK;QAChB,MAAM,QAAQ,CAAA,GAAA,2KAAA,CAAA,MAAG,AAAD,IAAI,UAAU,CAAC,IAAI,KAAK,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS;QACzH,MAAM,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,aAAa,eAAe,SAAS,EAAE,GAAG,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI;IACxI;IACA,SAAS,WAAW,KAAK;QACvB,MAAM,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,UAAU;IAClP;IACA,IAAI,SAAS,KAAK,GAAG,GAAG;QACtB,MAAM;IACR,OAAO,IAAI,SAAS,KAAK,GAAG,GAAG;QAC7B,IAAI;IACN,OAAO;QACL,WAAW;IACb;IACA,OAAO;AACT;AACA,MAAM,aAAa,SAAS,OAAO,EAAE,UAAU;IAC7C,MAAM,gBAAgB,QAAQ,MAAM,CAAC;IACrC,cAAc,IAAI,CAAC,MAAM,WAAW,EAAE;IACtC,cAAc,IAAI,CAAC,MAAM,WAAW,EAAE;IACtC,cAAc,IAAI,CAAC,SAAS,WAAW,WAAW,GAAG;IACrD,cAAc,IAAI,CAAC,QAAQ,WAAW,IAAI;IAC1C,cAAc,IAAI,CAAC,UAAU,WAAW,MAAM;IAC9C,cAAc,IAAI,CAAC,KAAK,WAAW,CAAC;IACpC,IAAI,cAAc,KAAK,KAAK,KAAK,GAAG;QAClC,cAAc,IAAI,CAAC,SAAS,cAAc,KAAK;IACjD;IACA,IAAI,WAAW,KAAK,KAAK,KAAK,GAAG;QAC/B,cAAc,MAAM,CAAC,SAAS,IAAI,CAAC,WAAW,KAAK;IACrD;IACA,OAAO;AACT;AACA,MAAM,WAAW,SAAS,IAAI,EAAE,QAAQ;IACtC,MAAM,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,gBAAgB;IACpD,MAAM,WAAW,KAAK,MAAM,CAAC;IAC7B,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC;IAC7B,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC;IAC7B,SAAS,IAAI,CAAC,SAAS;IACvB,SAAS,KAAK,CAAC,eAAe,SAAS,MAAM;IAC7C,IAAI,SAAS,KAAK,KAAK,KAAK,GAAG;QAC7B,SAAS,IAAI,CAAC,SAAS,SAAS,KAAK;IACvC;IACA,MAAM,OAAO,SAAS,MAAM,CAAC;IAC7B,KAAK,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG,SAAS,UAAU,GAAG;IAClD,KAAK,IAAI,CAAC;IACV,OAAO;AACT;AACA,MAAM,YAAY,SAAS,IAAI,EAAE,SAAS;IACxC,SAAS,UAAU,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;QACzC,OAAO,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS,GAAG,IAAI,MAAM,CAAC,IAAI,QAAQ,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,MAAM;IACpL;IACA,MAAM,UAAU,KAAK,MAAM,CAAC;IAC5B,QAAQ,IAAI,CAAC,UAAU,UAAU,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,IAAI,IAAI;IACnE,QAAQ,IAAI,CAAC,SAAS;IACtB,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,WAAW;IACjD,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,MAAM,UAAU,WAAW;IACvD,SAAS,MAAM;AACjB;AACA,MAAM,cAAc,SAAS,IAAI,EAAE,OAAO,EAAE,IAAI;IAC9C,MAAM,IAAI,KAAK,MAAM,CAAC;IACtB,MAAM,OAAO;IACb,KAAK,CAAC,GAAG,QAAQ,CAAC;IAClB,KAAK,CAAC,GAAG,QAAQ,CAAC;IAClB,KAAK,IAAI,GAAG,QAAQ,IAAI;IACxB,KAAK,KAAK,GAAG,KAAK,KAAK;IACvB,KAAK,MAAM,GAAG,KAAK,MAAM;IACzB,KAAK,KAAK,GAAG,kCAAkC,QAAQ,GAAG;IAC1D,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,SAAS,GAAG;IACZ,uBAAuB,MACrB,QAAQ,IAAI,EACZ,GACA,KAAK,CAAC,EACN,KAAK,CAAC,EACN,KAAK,KAAK,EACV,KAAK,MAAM,EACX;QAAE,OAAO,kCAAkC,QAAQ,GAAG;IAAC,GACvD,MACA,QAAQ,MAAM;AAElB;AACA,IAAI,YAAY,CAAC;AACjB,MAAM,WAAW,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI;IACxC,MAAM,SAAS,KAAK,CAAC,GAAG,KAAK,KAAK,GAAG;IACrC,MAAM,IAAI,KAAK,MAAM,CAAC;IACtB;IACA,MAAM,YAAY,MAAM,IAAI;IAC5B,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,WAAW,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,SAAS,aAAa,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,UAAU;IACrO,SAAS,GAAG;QACV,IAAI;QACJ,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI;QAC7B,OAAO,KAAK,KAAK;IACnB;IACA,MAAM,OAAO;IACb,KAAK,CAAC,GAAG,KAAK,CAAC;IACf,KAAK,CAAC,GAAG,KAAK,CAAC;IACf,KAAK,IAAI,GAAG,KAAK,IAAI;IACrB,KAAK,KAAK,GAAG,KAAK,KAAK;IACvB,KAAK,MAAM,GAAG,KAAK,MAAM;IACzB,KAAK,KAAK,GAAG,oBAAoB,KAAK,GAAG;IACzC,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,SAAS,GAAG;IACZ,KAAK,CAAC,GAAG;IACT,uBAAuB,MACrB,KAAK,IAAI,EACT,GACA,KAAK,CAAC,EACN,KAAK,CAAC,EACN,KAAK,KAAK,EACV,KAAK,MAAM,EACX;QAAE,OAAO;IAAO,GAChB,MACA,KAAK,MAAM;AAEf;AACA,MAAM,qBAAqB,SAAS,IAAI,EAAE,MAAM;IAC9C,MAAM,WAAW,SAAS,MAAM;QAC9B,GAAG,OAAO,MAAM;QAChB,GAAG,OAAO,MAAM;QAChB,OAAO,OAAO,KAAK,GAAG,OAAO,MAAM;QACnC,QAAQ,OAAO,KAAK,GAAG,OAAO,MAAM;QACpC,MAAM,OAAO,IAAI;QACjB,OAAO;IACT;IACA,SAAS,KAAK;AAChB;AACA,MAAM,aAAa;IACjB,OAAO;QACL,GAAG;QACH,GAAG;QACH,MAAM,KAAK;QACX,eAAe;QACf,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,IAAI;QACJ,IAAI;IACN;AACF;AACA,MAAM,cAAc;IAClB,OAAO;QACL,GAAG;QACH,GAAG;QACH,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,IAAI;QACJ,IAAI;IACN;AACF;AACA,MAAM,yBAAyB;IAC7B,SAAS,OAAO,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;QAChE,MAAM,OAAO,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,GAAG,KAAK,CAAC,cAAc,QAAQ,KAAK,CAAC,eAAe,UAAU,IAAI,CAAC;QACrJ,cAAc,MAAM;IACtB;IACA,SAAS,QAAQ,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM;QACvE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG;QACzC,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,KAAK,IAAI,eAAe,eAAe,CAAC,MAAM,MAAM,GAAG,CAAC,IAAI;YAClE,MAAM,OAAO,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,QAAQ,KAAK,CAAC,eAAe,UAAU,KAAK,CAAC,aAAa,cAAc,KAAK,CAAC,eAAe;YAC9K,KAAK,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1E,KAAK,IAAI,CAAC,KAAK,IAAI,SAAS,GAAG,IAAI,CAAC,qBAAqB,WAAW,IAAI,CAAC,sBAAsB;YAC/F,cAAc,MAAM;QACtB;IACF;IACA,SAAS,KAAK,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI;QAC5D,MAAM,OAAO,EAAE,MAAM,CAAC;QACtB,MAAM,IAAI,KAAK,MAAM,CAAC,iBAAiB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,YAAY;QAC9H,MAAM,OAAO,EAAE,MAAM,CAAC,aAAa,KAAK,CAAC,WAAW,SAAS,KAAK,CAAC,UAAU,QAAQ,KAAK,CAAC,SAAS;QACpG,KAAK,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS,SAAS,KAAK,CAAC,WAAW,cAAc,KAAK,CAAC,cAAc,UAAU,KAAK,CAAC,kBAAkB,UAAU,IAAI,CAAC;QAC9I,QAAQ,SAAS,MAAM,GAAG,GAAG,OAAO,QAAQ,WAAW;QACvD,cAAc,MAAM;IACtB;IACA,SAAS,cAAc,MAAM,EAAE,iBAAiB;QAC9C,IAAK,MAAM,OAAO,kBAAmB;YACnC,IAAI,OAAO,mBAAmB;gBAC5B,OAAO,IAAI,CAAC,KAAK,iBAAiB,CAAC,IAAI;YACzC;QACF;IACF;IACA,OAAO,SAAS,IAAI;QAClB,OAAO,KAAK,aAAa,KAAK,OAAO,OAAO,KAAK,aAAa,KAAK,QAAQ,SAAS;IACtF;AACF;AACA,MAAM,eAAe,SAAS,QAAQ;IACpC,SAAS,MAAM,CAAC,QAAQ,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,aAAa,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK;AAClM;AACA,SAAS,KAAK,IAAI,EAAE,KAAK;IACvB,KAAK,IAAI,CAAC;QACR,IAAI,QAAQ,CAAA,GAAA,qLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,GAAG,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,OAAO,IAAI,MAAM,OAAO,EAAE,EAAE,aAAa,KAAK,IAAI,MAAM,IAAI,CAAC,MAAM,KAAK,WAAW,MAAM,IAAI,CAAC,QAAQ,QAAQ,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,KAAK;QAC5P,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,EAAE;YAClC,KAAK,IAAI,CAAC;YACV,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI;YAC9B,IAAI,MAAM,IAAI,GAAG,qBAAqB,KAAK,SAAS,SAAS,QAAQ;gBACnE,KAAK,GAAG;gBACR,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI;gBAC9B,IAAI,SAAS,QAAQ;oBACnB,OAAO;wBAAC;qBAAG;gBACb,OAAO;oBACL,OAAO;wBAAC;qBAAK;gBACf;gBACA,QAAQ,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,aAAa,MAAM,IAAI,CAAC;YAC7F;QACF;IACF;AACF;AACA,MAAM,WAAW,SAAS,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI;IACrD,MAAM,UAAU,cAAc,eAAe;IAC7C,MAAM,WAAW,KAAK,MAAM,CAAC;IAC7B,KAAK,OAAO,GAAG;IACf,SAAS,IAAI,CACX,SACA,CAAC,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,EAAE,IAAI,mBAAmB,CAAC,aAAa,OAAO;IAEjF,MAAM,UAAU,SAAS,MAAM,CAAC;IAChC,MAAM,WAAW,SAAS,MAAM,CAAC;IACjC,MAAM,MAAM,SAAS,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,sBAAsB,UAAU,IAAI,CAAC,qBAAqB,UAAU,IAAI,CAAC,eAAe,UAAU,IAAI,CAAC,MAAM,KAAK,KAAK;IACnM,MAAM,OAAO,IAAI,IAAI,GAAG,OAAO;IAC/B,MAAM,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC,OAAO,CAAC,MAAM,MAAM,KAAK,QAAQ;IACzG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,WAAW,MAAM,MAAM,KAAK,OAAO;IAC/D,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,SAAS;IAClD,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,OAAO;IAC1C,SAAS,IAAI,CAAC,aAAa,eAAe,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,OAAO,GAAG,IAAI;IACrF,WAAW,SAAS,MAAM;IAC1B,OAAO;AACT;AACA,MAAM,uBAAuB,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI;IACpD,MAAM,WAAW,KAAK,MAAM,CAAC;IAC7B,MAAM,MAAM,SAAS,MAAM,CAAC,QAAQ,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,sBAAsB,UAAU,IAAI,CAAC,qBAAqB,UAAU,IAAI,CAAC,eAAe,UAAU,IAAI,CAAC,MAAM,KAAK,KAAK;IACnM,MAAM,OAAO,IAAI,IAAI,GAAG,OAAO;IAC/B,MAAM,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC,OAAO,CAAC,MAAM,MAAM,KAAK,QAAQ;IACzG,SAAS,MAAM;IACf,OAAO,KAAK,MAAM,GAAG,WAAW,MAAM,MAAM,KAAK,OAAO;AAC1D;AACA,MAAM,aAAa,SAAS,IAAI,EAAE,IAAI,EAAE,OAAO;IAC7C,MAAM,KAAK;IACX,KAAK,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,UAAU,KAAK,EAAE,EAAE,IAAI,CAAC,SAAS,mBAAmB,KAAK,IAAI,EAAE,IAAI,CAChG,KACA,CAAC,GAAG,EAAE,KAAK,MAAM,GAAG,GAAG,EAAE,EAAE,CAAC,KAAK,MAAM,GAAG,IAAI,GAAG,aAAa,EAAE,KAAK,KAAK,GAAG,IAAI,GAAG,WAAW,EAAE,KAAK,MAAM,GAAG,GAAG,KAAK,CAAC;IAE1H,KAAK,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,eAAe,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,KAAK,MAAM;AAC/I;AACA,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;AACA,MAAM,OAAO,SAAS,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO;IAC9C,IAAI,IAAI;IACR,MAAM,OAAO,CAAA,GAAA,sJAAA,CAAA,IAAS,AAAD;IACrB,MAAM,cAAc,KAAK,UAAU,IAAI;IACvC,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,YAAY,QAAQ,EAAE;IAChC,MAAM,gBAAgB,KAAK,aAAa;IACxC,IAAI;IACJ,IAAI,kBAAkB,WAAW;QAC/B,iBAAiB,CAAA,GAAA,qLAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACjC;IACA,MAAM,OAAO,kBAAkB,YAAY,CAAA,GAAA,qLAAA,CAAA,SAAM,AAAD,EAAE,eAAe,KAAK,EAAE,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,IAAI,CAAA,GAAA,qLAAA,CAAA,SAAM,AAAD,EAAE;IAC3G,MAAM,MAAM,KAAK,MAAM,CAAC,MAAM;IAC9B,IAAI,MAAM,CAAC;IACX,MAAM,SAAS,QAAQ,EAAE,CAAC,QAAQ;IAClC,MAAM,QAAQ,QAAQ,EAAE,CAAC,WAAW,GAAG,eAAe;IACtD,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,QAAQ;IAClB,QAAQ,YAAY,CAAC;IACrB,MAAM,YAAY,QAAQ,EAAE,CAAC,WAAW;IACxC,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,YAAY;IACtB,IAAI,mBAAmB;IACvB,IAAI,gBAAgB;IACpB,IAAI,SAAS;IACb,IAAI,gBAAgB;IACpB,IAAI,UAAU,KAAK;IACnB,IAAI,UAAU;IACd,gBAAgB;IAChB,IAAI,gBAAgB;IACpB,IAAI,cAAc;IAClB,UAAU,OAAO,CAAC,SAAS,OAAO;QAChC,MAAM,cAAc;YAClB,QAAQ;YACR,OAAO;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,WAAW;QACb;QACA,MAAM,gBAAgB,QAAQ,oBAAoB,CAAC,KAAK,aAAa;QACrE,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,6BAA6B;QACvC,mBAAmB,KAAK,GAAG,CAAC,kBAAkB,gBAAgB;IAChE;IACA,IAAI,gBAAgB;IACpB,IAAI,qBAAqB;IACzB,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,gBAAgB,OAAO,MAAM;IACvC,KAAK,MAAM,CAAC,GAAG,KAAK,IAAI,OAAO,OAAO,GAAI;QACxC,MAAM,WAAW;YACf,QAAQ;YACR,OAAO;YACP,SAAS,KAAK,OAAO;YACrB,OAAO;YACP,SAAS;YACT,WAAW;QACb;QACA,MAAM,aAAa,QAAQ,oBAAoB,CAAC,KAAK,UAAU;QAC/D,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,0BAA0B;QACpC,gBAAgB,KAAK,GAAG,CAAC,eAAe,aAAa;QACrD,gBAAgB,KAAK,GAAG,CAAC,eAAe,KAAK,MAAM,CAAC,MAAM;QAC1D,IAAI,yBAAyB;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE,IAAK;YAC3C,MAAM,QAAQ,KAAK,MAAM,CAAC,EAAE;YAC5B,MAAM,YAAY;gBAChB,OAAO;gBACP,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,OAAO;gBACpB,OAAO;gBACP,SAAS;gBACT,WAAW;YACb;YACA,0BAA0B,QAAQ,oBAAoB,CAAC,KAAK,WAAW;QACzE;QACA,qBAAqB,KAAK,GAAG,CAAC,oBAAoB;IACpD;IACA,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,gCAAgC;IAC1C,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,6BAA6B;IACvC,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;QACrC,UAAU,OAAO,CAAC,CAAC;YACjB,MAAM,kBAAkB,OAAO,MAAM,CAAC,CAAC,OAAS,KAAK,OAAO,KAAK;YACjE,MAAM,cAAc;gBAClB,QAAQ;gBACR,OAAO;gBACP,SAAS;gBACT,OAAO,MAAM,KAAK,GAAG,CAAC,gBAAgB,MAAM,EAAE,KAAK;gBACnD,SAAS;gBACT,WAAW;YACb;YACA,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,eAAe;YACzB,MAAM,qBAAqB,IAAI,MAAM,CAAC;YACtC,MAAM,OAAO,QAAQ,QAAQ,CAAC,oBAAoB,aAAa,eAAe;YAC9E,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,sBAAsB;YAChC,mBAAmB,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE,cAAc,CAAC,CAAC;YAC9E,WAAW,mBAAmB;YAC9B,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,UACE,KACA,iBACA,eACA,SACA,SACA,eACA,MACA,eACA,oBACA,kBACA;YAEJ;YACA,WAAW,MAAM,KAAK,GAAG,CAAC,gBAAgB,MAAM,EAAE;YAClD,UAAU;YACV;QACF;IACF,OAAO;QACL,cAAc;QACd,UACE,KACA,QACA,eACA,SACA,SACA,eACA,MACA,eACA,oBACA,kBACA;IAEJ;IACA,MAAM,MAAM,IAAI,IAAI,GAAG,OAAO;IAC9B,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,UAAU;IACpB,IAAI,OAAO;QACT,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,IAAI,KAAK,GAAG,IAAI,aAAa,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,eAAe,QAAQ,IAAI,CAAC,KAAK;IACvI;IACA,SAAS,cAAc,mBAAmB,gBAAgB,MAAM,gBAAgB;IAChF,MAAM,cAAc,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS;IAClD,YAAY,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,aAAa,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,aAAa,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,SAAS,IAAI,CAAC,cAAc;IACpM,CAAA,GAAA,sJAAA,CAAA,IAAiB,AAAD,EACd,KAAK,GACL,KACA,CAAC,CAAC,KAAK,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,IACxD,CAAC,CAAC,KAAK,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI,GAAG,WAAW,KAAK;AAEhE;AACA,MAAM,YAAY,SAAS,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB;IACtK,IAAI;IACJ,KAAK,MAAM,QAAQ,OAAQ;QACzB,MAAM,WAAW;YACf,OAAO,KAAK,IAAI;YAChB,SAAS;YACT,QAAQ;YACR,OAAO;YACP,SAAS;YACT,WAAW;QACb;QACA,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,YAAY;QACtB,MAAM,cAAc,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS;QACvD,MAAM,OAAO,QAAQ,QAAQ,CAAC,aAAa,UAAU,cAAc;QACnE,MAAM,aAAa,KAAK,MAAM;QAC9B,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,yBAAyB;QACnC,YAAY,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjE,gBAAgB,KAAK,GAAG,CAAC,eAAe;QACxC,IAAI,KAAK,MAAM,EAAE;YACf,MAAM,cAAc,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS;YACvD,IAAI,aAAa;YACjB,WAAW;YACX,aAAa,aAAa,WAAW,UAAU,KAAK,MAAM,EAAE,cAAc,SAAS,SAAS;YAC5F,WAAW;YACX,YAAY,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,UAAU,MAAM,GAAG,IAAI,CAAC,MAAM,UAAU,eAAe,IAAI,CAAC,MAAM,UAAU,MAAM,GAAG,IAAI,CAC7H,MACA,UAAU,gBAAgB,CAAC,oBAAoB,gBAAgB,gBAAgB,IAAI,qBAAqB,KACxG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,SAAS,IAAI,CAAC,cAAc,mBAAmB,IAAI,CAAC,oBAAoB;QACnH;QACA,UAAU,UAAU;QACpB,IAAI,qBAAqB,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI,GAAG,iBAAiB,GAAG;YACxF;QACF;IACF;IACA,UAAU,UAAU;AACtB;AACA,MAAM,aAAa,SAAS,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI;IAChF,IAAI,iBAAiB;IACrB,MAAM,cAAc;IACpB,UAAU,UAAU;IACpB,KAAK,MAAM,SAAS,OAAQ;QAC1B,MAAM,YAAY;YAChB,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;YACP,SAAS;YACT,WAAW;QACb;QACA,sJAAA,CAAA,IAAG,CAAC,KAAK,CAAC,aAAa;QACvB,MAAM,eAAe,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS;QACxD,MAAM,OAAO,QAAQ,QAAQ,CAAC,cAAc,WAAW,cAAc;QACrE,MAAM,cAAc,KAAK,MAAM;QAC/B,iBAAiB,iBAAiB;QAClC,aAAa,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;QAClE,UAAU,UAAU,KAAK;IAC3B;IACA,UAAU;IACV,OAAO;AACT;AACA,MAAM,WAAW;IACf,SAAS,KACT;IACA;AACF;AACA,MAAM,cAAc,CAAC;IACnB,IAAI,YAAY;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,iBAAiB,EAAE,IAAK;QAClD,OAAO,CAAC,cAAc,EAAE,GAAG,OAAO,CAAC,cAAc,EAAE,IAAI,OAAO,CAAC,cAAc,EAAE;QAC/E,IAAI,CAAA,GAAA,yLAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,cAAc,EAAE,GAAG;YACpC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAA,GAAA,0LAAA,CAAA,UAAO,AAAD,EAAE,OAAO,CAAC,cAAc,EAAE,EAAE;QAC/D,OAAO;YACL,OAAO,CAAC,cAAc,EAAE,GAAG,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,cAAc,EAAE,EAAE;QAC9D;IACF;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,iBAAiB,EAAE,IAAK;QAClD,MAAM,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC;QAC3B,aAAa,CAAC;aACL,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE;YACnF,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC;;aAEvB,EAAE,IAAI,EAAE;WACV,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC;;eAEzB,EAAE,IAAI,EAAE;;aAEV,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC;;kBAExB,EAAE,IAAI,EAAE;cACZ,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC;;gBAEtB,EAAE,IAAI,EAAE;oBACJ,EAAE,GAAG;;aAEZ,EAAE,IAAI,EAAE;cACP,EAAE,OAAO,CAAC,cAAc,EAAE,CAAC;;;;;cAK3B,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC;;;;;;;;;IASvC,CAAC;IACH;IACA,OAAO;AACT;AACA,MAAM,YAAY,CAAC,UAAY,CAAC;;;;EAI9B,EAAE,YAAY,SAAS;;UAEf,EAAE,QAAQ,IAAI,CAAC;;;UAGf,EAAE,QAAQ,eAAe,CAAC;;;;;;;;;;;;;;AAcpC,CAAC;AACD,MAAM,SAAS;AACf,MAAM,UAAU;IACd;IACA;IACA,QAAQ;IACR;AACF", "ignoreList": [0], "debugId": null}}]}