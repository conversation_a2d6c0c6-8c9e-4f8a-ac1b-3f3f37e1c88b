import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
  console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

const ARCHITECTURE_DIAGRAM_PROMPT = `1. ROLE AND POINT-OF-<PERSON><PERSON><PERSON>
<PERSON> are a senior solutions architect and technical illustrator trusted to convert written requirements into visual architecture assets.

2. OBJECTIVE
Parse the supplied technical requirements document, identify every relevant component, and deliver a single, professionally formatted solution diagram that would satisfy an enterprise design review.

3. INPUT (VERBATIM)
<<<
[This is the content from the technical requirements document]
>>>

PROCESS (FOLLOW THESE STEPS IN ORDER)
   1. Inventory – list all actors, systems, services, data stores, queues, APIs, protocols, environments, and user roles.
   2. Classify & Group – organise items into logical layers (Presentation, Application, Data, Network, Security, External).
   3. Map Flows – define directional connections, interaction types (REST, gRPC, Message Bus, SFTP, etc.), and cardinalities.
   4. Validate Coverage – ensure every listed requirement from the source appears in the visual model; flag anything missing with "TBD".
   5. Render – produce the diagram in a style equivalent to Microsoft Visio: clean, minimalist, consistent iconography, landscape orientation.
   6. Export Code – output an editable text representation (PlantUML AND Mermaid) so the diagram can be tweaked later.

5. OUTPUT SPECIFICATION
   - Primary: high-resolution SVG (preferred) or PNG graphic, transparent background, max-width 1600 px.
   - Secondary: plain-text blocks containing:
       * PlantUML code wrapped in @startuml / @enduml
       * Mermaid code wrapped in \`\`\`mermaid blocks
   - Legend & Notes: 5-10 bullet points explaining symbols, color codes, and any assumptions.

6. STYLISTIC AND QUALITY GUIDELINES
   - Use standard architecture icons (database cylinder, user silhouette, cloud edge, queue stack, etc.).
   - Label layers with subtle background shading; use sans-serif font 10-12 pt.
   - Arrowheads denote direction; dashed lines are asynchronous; solid lines are synchronous.
   - Follow WCAG AA color-contrast guidelines.

7. CONSTRAINTS AND ERROR HANDLING
   - If critical details are absent, insert a red dashed placeholder box and note it in the legend.
   - Do not invent business logic beyond the document; only infer necessary connections for coherence.
   - Output only the diagram image, the two code blocks, and the legend.

8. DELIVERY FORMAT TEMPLATE
[SVG or PNG IMAGE]

--- begin plantuml ---
@startuml
(generated code)
@enduml
--- end plantuml ---

--- begin mermaid ---
graph LR
(generated code)
--- end mermaid ---

LEGEND AND ASSUMPTIONS
- (list here)`;

export async function POST(request: NextRequest) {
  try {
    const { technicalRequirementsContent } = await request.json();
    
    if (!technicalRequirementsContent) {
      return NextResponse.json({ error: 'No technical requirements content provided' }, { status: 400 });
    }

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo response');
      
      // For development, return a mock response
      return NextResponse.json({
        success: true,
        diagramUrl: null,
        plantUML: `@startuml
!theme plain
title System Architecture Diagram - Demo Mode

package "Presentation Layer" {
  [Web Application] as webapp
  [Mobile App] as mobile
}

package "Application Layer" {
  [API Gateway] as gateway
  [Microservice A] as msA
  [Microservice B] as msB
}

package "Data Layer" {
  database "Primary DB" as db1
  database "Cache" as cache
}

webapp --> gateway
mobile --> gateway
gateway --> msA
gateway --> msB
msA --> db1
msB --> cache

note right of gateway : Demo Mode - Configure OpenAI API key
@enduml`,
        mermaid: `graph TB
    subgraph "Presentation Layer"
        WA[Web Application]
        MA[Mobile App]
    end
    
    subgraph "Application Layer"
        AG[API Gateway]
        MSA[Microservice A]
        MSB[Microservice B]
    end
    
    subgraph "Data Layer"
        DB[(Primary Database)]
        CACHE[(Cache)]
    end
    
    WA --> AG
    MA --> AG
    AG --> MSA
    AG --> MSB
    MSA --> DB
    MSB --> CACHE`,
        legend: [
          "Demo Mode - Claude API not configured",
          "Configure ANTHROPIC_API_KEY to generate real diagrams",
          "This is a sample architecture diagram",
          "Real diagrams will be generated from your technical requirements"
        ],
        message: 'Demo architecture diagram generated. Configure Claude API key for real diagram generation.'
      });
    }

    console.log('Generating architecture diagram from technical requirements...');
    console.log(`Technical requirements content length: ${technicalRequirementsContent.length} characters`);

    // Call Claude API with Sonnet 4
    console.log('Calling Claude API with Sonnet 4 for architecture diagram...');
    const fullPrompt = `${ARCHITECTURE_DIAGRAM_PROMPT}\n\nTechnical Requirements Document Content:\n${technicalRequirementsContent}`;
    console.log(`Full prompt length: ${fullPrompt.length} characters`);

    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 4000,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude API call successful for architecture diagram');
    } catch (apiError: any) {
      console.error('Claude API Error:', apiError);

      // Handle specific API errors
      if (apiError.status === 401) {
        return NextResponse.json({
          error: 'Invalid API key. Please check your Anthropic API key configuration.'
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({
          error: 'Rate limit exceeded. Please try again in a few minutes.'
        }, { status: 429 });
      } else if (apiError.message?.includes('model')) {
        return NextResponse.json({
          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.'
        }, { status: 400 });
      } else {
        return NextResponse.json({
          error: `Claude API error: ${apiError.message || 'Unknown error'}`
        }, { status: 500 });
      }
    }

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Parse the response to extract PlantUML, Mermaid, and legend
    const parsedResponse = parseArchitectureResponse(analysisText);

    console.log('Parsed Mermaid code:', parsedResponse.mermaid);
    console.log('Parsed PlantUML code:', parsedResponse.plantUML);
    console.log('Parsed legend:', parsedResponse.legend);

    // For now, we'll return the text-based diagram codes
    // In a full implementation, you might want to render these to actual images
    return NextResponse.json({
      success: true,
      diagramUrl: null, // Would contain actual image URL in full implementation
      plantUML: parsedResponse.plantUML,
      mermaid: parsedResponse.mermaid,
      legend: parsedResponse.legend,
      fullResponse: analysisText,
      message: 'Architecture diagram generated successfully with Claude Sonnet 4!'
    });

  } catch (error) {
    console.error('Error generating architecture diagram:', error);
    return NextResponse.json(
      { error: 'Failed to generate architecture diagram' }, 
      { status: 500 }
    );
  }
}

function parseArchitectureResponse(text: string) {
  const result = {
    plantUML: '',
    mermaid: '',
    legend: [] as string[]
  };

  console.log('Parsing architecture response, length:', text.length);

  // Extract PlantUML code - try multiple formats
  let plantUMLMatch = text.match(/--- begin plantuml ---\s*([\s\S]*?)\s*--- end plantuml ---/i);
  if (plantUMLMatch) {
    result.plantUML = plantUMLMatch[1].trim();
  } else {
    // Try alternative format with @startuml/@enduml
    plantUMLMatch = text.match(/@startuml([\s\S]*?)@enduml/i);
    if (plantUMLMatch) {
      result.plantUML = `@startuml${plantUMLMatch[1]}@enduml`;
    } else {
      // Try looking for PlantUML in code blocks
      plantUMLMatch = text.match(/```plantuml\s*([\s\S]*?)\s*```/i);
      if (plantUMLMatch) {
        result.plantUML = plantUMLMatch[1].trim();
      }
    }
  }

  // Extract Mermaid code - try multiple formats
  let mermaidMatch = text.match(/--- begin mermaid ---\s*([\s\S]*?)\s*--- end mermaid ---/i);
  if (mermaidMatch) {
    result.mermaid = mermaidMatch[1].trim();
  } else {
    // Try alternative format with ```mermaid - IMPORTANT: Extract content between the markers
    mermaidMatch = text.match(/```mermaid\s*\n([\s\S]*?)\n\s*```/i);
    if (mermaidMatch) {
      result.mermaid = mermaidMatch[1].trim();
    } else {
      // Try without newlines
      mermaidMatch = text.match(/```mermaid\s*([\s\S]*?)\s*```/i);
      if (mermaidMatch) {
        let content = mermaidMatch[1].trim();
        // Remove any remaining ```mermaid or ``` markers
        content = content.replace(/^```mermaid\s*/i, '').replace(/\s*```$/, '');
        result.mermaid = content.trim();
      } else {
        // Try looking for graph/flowchart patterns directly
        mermaidMatch = text.match(/(graph|flowchart)\s+(?:TB|TD|BT|RL|LR)[\s\S]*?(?=\n\n|\n[A-Z]|$)/i);
        if (mermaidMatch) {
          result.mermaid = mermaidMatch[0].trim();
        }
      }
    }
  }

  // Extract legend
  const legendMatch = text.match(/LEGEND AND ASSUMPTIONS\s*([\s\S]*?)(?=\n\n|$)/i);
  if (legendMatch) {
    const legendText = legendMatch[1].trim();
    result.legend = legendText.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && (line.startsWith('-') || line.startsWith('•')))
      .map(line => line.replace(/^[-•]\s*/, '').trim())
      .filter(line => line.length > 0);
  }

  // If no structured formats found, try to extract any diagram-like content
  if (!result.mermaid && !result.plantUML) {
    console.log('No structured diagram found, looking for any diagram content...');

    // Look for any graph-like content
    const graphMatch = text.match(/(graph|flowchart|sequenceDiagram|classDiagram)[\s\S]*?(?=\n\n|$)/i);
    if (graphMatch) {
      result.mermaid = graphMatch[0].trim();
    }
  }

  // Clean up the extracted codes
  if (result.mermaid) {
    result.mermaid = cleanMermaidCode(result.mermaid);
  }
  if (result.plantUML) {
    result.plantUML = cleanPlantUMLCode(result.plantUML);
  }

  console.log('Parsed result:', {
    plantUMLLength: result.plantUML.length,
    mermaidLength: result.mermaid.length,
    legendCount: result.legend.length,
    mermaidPreview: result.mermaid.substring(0, 100)
  });

  return result;
}

function cleanMermaidCode(code: string): string {
  // Remove any markdown code block markers
  let cleaned = code
    .replace(/^```mermaid\s*/i, '')
    .replace(/\s*```$/i, '')
    .replace(/^```\s*/i, '')
    .trim();

  // Remove any extra whitespace and normalize line endings
  cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Ensure it starts with a valid Mermaid diagram type
  if (!cleaned.match(/^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gitgraph|pie|quadrantChart|requirement|mindmap|timeline|sankey|block)/i)) {
    // If it doesn't start with a diagram type, try to detect and add one
    if (cleaned.includes('-->') || cleaned.includes('---')) {
      cleaned = `graph TD\n${cleaned}`;
    }
  }

  return cleaned;
}

function cleanPlantUMLCode(code: string): string {
  // Remove any markdown code block markers
  let cleaned = code
    .replace(/^```plantuml\s*/i, '')
    .replace(/\s*```$/i, '')
    .replace(/^```\s*/i, '')
    .trim();

  // Ensure it has proper @startuml/@enduml markers
  if (!cleaned.startsWith('@startuml')) {
    cleaned = `@startuml\n${cleaned}`;
  }
  if (!cleaned.endsWith('@enduml')) {
    cleaned = `${cleaned}\n@enduml`;
  }

  return cleaned;
}
