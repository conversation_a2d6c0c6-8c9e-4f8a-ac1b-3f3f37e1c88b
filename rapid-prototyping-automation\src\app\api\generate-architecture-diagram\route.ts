import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
  console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

const MERMAID_DIAGRAM_PROMPT = `Generate a Mermaid architecture diagram from the technical requirements.

STRICT SYNTAX RULES:
1. Start with exactly: graph TD
2. Node IDs must be simple: A, B, Frontend, API, DB (no spaces, no hyphens, no special chars)
3. Labels in quotes: A["Frontend Application"]
4. Connections: A --> B
5. Subgraphs: subgraph "Layer Name"

VALID EXAMPLE:
graph TD
    A["Frontend Application"] --> B["API Gateway"]
    B --> C["Authentication Service"]
    B --> D["Business Logic API"]
    D --> E["Primary Database"]
    D --> F["Redis Cache"]

    subgraph "Presentation"
        A
    end

    subgraph "Application"
        B
        C
        D
    end

    subgraph "Data"
        E
        F
    end

OUTPUT ONLY THE MERMAID CODE - NO EXPLANATIONS:`;

export async function POST(request: NextRequest) {
  try {
    const { technicalRequirements } = await request.json();
    
    if (!technicalRequirements) {
      return NextResponse.json({ error: 'Technical requirements are required' }, { status: 400 });
    }

    console.log(`Processing technical requirements: ${technicalRequirements.length} characters`);

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo diagram');
      
      // Return demo Mermaid diagram
      const demoMermaid = `graph TD
    A["Frontend Application"] --> B["API Gateway"]
    B --> C["Authentication Service"]
    B --> D["Business Logic API"]
    D --> E["Primary Database"]
    D --> F["Redis Cache"]
    B --> G["External Services"]

    subgraph "Presentation Layer"
        A
    end

    subgraph "Application Layer"
        B
        C
        D
    end

    subgraph "Data Layer"
        E
        F
    end

    subgraph "External"
        G
    end`;

      return NextResponse.json({
        success: true,
        mermaidCode: demoMermaid,
        message: 'Demo architecture diagram generated. Configure Claude API key for real diagram generation.'
      });
    }

    console.log('Generating architecture diagram with Claude Sonnet 4...');
    
    // Call Claude API with simplified prompt
    const fullPrompt = `${MERMAID_DIAGRAM_PROMPT}\n\nTechnical Requirements:\n${technicalRequirements}`;
    
    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 20000,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude API call successful for architecture diagram');
    } catch (apiError: any) {
      console.error('Claude API Error:', apiError);
      
      if (apiError.status === 401) {
        return NextResponse.json({ 
          error: 'Invalid API key. Please check your Anthropic API key configuration.' 
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({ 
          error: 'Rate limit exceeded. Please try again in a few minutes.' 
        }, { status: 429 });
      } else {
        return NextResponse.json({ 
          error: `Claude API error: ${apiError.message || 'Unknown error'}` 
        }, { status: 500 });
      }
    }

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Clean and validate the Mermaid code
    const mermaidCode = cleanMermaidCode(analysisText);
    
    console.log('Generated Mermaid code:', mermaidCode.substring(0, 200));

    return NextResponse.json({
      success: true,
      mermaidCode: mermaidCode,
      message: 'Architecture diagram generated successfully with Claude Sonnet 4!'
    });

  } catch (error) {
    console.error('Error generating architecture diagram:', error);
    return NextResponse.json(
      { error: 'Failed to generate architecture diagram' }, 
      { status: 500 }
    );
  }
}

function cleanMermaidCode(text: string): string {
  // Remove any markdown code block markers
  let cleaned = text
    .replace(/^```mermaid\s*/i, '')
    .replace(/\s*```$/i, '')
    .replace(/^```\s*/i, '')
    .trim();

  // Remove any extra whitespace and normalize line endings
  cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Ensure it starts with a valid Mermaid diagram type
  if (!cleaned.match(/^(graph|flowchart|sequenceDiagram|classDiagram)/i)) {
    // If it doesn't start with a diagram type, try to detect and add one
    if (cleaned.includes('-->') || cleaned.includes('---')) {
      cleaned = `graph TD\n${cleaned}`;
    }
  }

  // Fix common Mermaid syntax issues
  cleaned = cleaned
    // Fix node labels - ensure they're properly quoted
    .replace(/(\w+)\[([^\]]+)\]/g, (match, nodeId, label) => {
      // If label contains spaces or special chars, wrap in quotes
      if (label.includes(' ') || /[^a-zA-Z0-9]/.test(label)) {
        return `${nodeId}["${label.replace(/"/g, '')}"]`;
      }
      return match;
    })
    // Fix arrows with proper spacing
    .replace(/\s*-->\s*/g, ' --> ')
    // Remove extra spaces but preserve line structure
    .replace(/[ \t]+/g, ' ')
    // Clean up line endings
    .replace(/\n\s+/g, '\n')
    .replace(/\n+/g, '\n')
    .trim();

  return cleaned;
}
