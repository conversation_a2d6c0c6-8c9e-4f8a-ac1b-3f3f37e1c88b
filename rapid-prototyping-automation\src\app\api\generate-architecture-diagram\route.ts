import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
  console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

const ARCHITECTURE_DIAGRAM_PROMPT = `1. ROLE AND POINT-OF-<PERSON><PERSON><PERSON>
<PERSON> are a senior solutions architect and technical illustrator trusted to convert written requirements into visual architecture assets.

2. OBJECTIVE
Parse the supplied technical requirements document, identify every relevant component, and deliver a single, professionally formatted solution diagram that would satisfy an enterprise design review.

3. INPUT (VERBATIM)
<<<
[This is the content from the technical requirements document]
>>>

PROCESS (FOLLOW THESE STEPS IN ORDER)
   1. Inventory – list all actors, systems, services, data stores, queues, APIs, protocols, environments, and user roles.
   2. Classify & Group – organise items into logical layers (Presentation, Application, Data, Network, Security, External).
   3. Map Flows – define directional connections, interaction types (REST, gRPC, Message Bus, SFTP, etc.), and cardinalities.
   4. Validate Coverage – ensure every listed requirement from the source appears in the visual model; flag anything missing with "TBD".
   5. Render – produce the diagram in a style equivalent to Microsoft Visio: clean, minimalist, consistent iconography, landscape orientation.
   6. Export Code – output an editable text representation (PlantUML AND Mermaid) so the diagram can be tweaked later.

5. OUTPUT SPECIFICATION
   - Primary: high-resolution SVG (preferred) or PNG graphic, transparent background, max-width 1600 px.
   - Secondary: plain-text blocks containing:
       * PlantUML code wrapped in @startuml / @enduml
       * Mermaid code wrapped in \`\`\`mermaid blocks
   - Legend & Notes: 5-10 bullet points explaining symbols, color codes, and any assumptions.

6. STYLISTIC AND QUALITY GUIDELINES
   - Use standard architecture icons (database cylinder, user silhouette, cloud edge, queue stack, etc.).
   - Label layers with subtle background shading; use sans-serif font 10-12 pt.
   - Arrowheads denote direction; dashed lines are asynchronous; solid lines are synchronous.
   - Follow WCAG AA color-contrast guidelines.

7. CONSTRAINTS AND ERROR HANDLING
   - If critical details are absent, insert a red dashed placeholder box and note it in the legend.
   - Do not invent business logic beyond the document; only infer necessary connections for coherence.
   - Output only the diagram image, the two code blocks, and the legend.

8. DELIVERY FORMAT TEMPLATE
[SVG or PNG IMAGE]

--- begin plantuml ---
@startuml
(generated code)
@enduml
--- end plantuml ---

--- begin mermaid ---
graph LR
(generated code)
--- end mermaid ---

LEGEND AND ASSUMPTIONS
- (list here)`;

export async function POST(request: NextRequest) {
  try {
    const { technicalRequirementsContent } = await request.json();
    
    if (!technicalRequirementsContent) {
      return NextResponse.json({ error: 'No technical requirements content provided' }, { status: 400 });
    }

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo response');
      
      // For development, return a mock response
      return NextResponse.json({
        success: true,
        diagramUrl: null,
        plantUML: `@startuml
!theme plain
title System Architecture Diagram - Demo Mode

package "Presentation Layer" {
  [Web Application] as webapp
  [Mobile App] as mobile
}

package "Application Layer" {
  [API Gateway] as gateway
  [Microservice A] as msA
  [Microservice B] as msB
}

package "Data Layer" {
  database "Primary DB" as db1
  database "Cache" as cache
}

webapp --> gateway
mobile --> gateway
gateway --> msA
gateway --> msB
msA --> db1
msB --> cache

note right of gateway : Demo Mode - Configure OpenAI API key
@enduml`,
        mermaid: `graph TB
    subgraph "Presentation Layer"
        WA[Web Application]
        MA[Mobile App]
    end
    
    subgraph "Application Layer"
        AG[API Gateway]
        MSA[Microservice A]
        MSB[Microservice B]
    end
    
    subgraph "Data Layer"
        DB[(Primary Database)]
        CACHE[(Cache)]
    end
    
    WA --> AG
    MA --> AG
    AG --> MSA
    AG --> MSB
    MSA --> DB
    MSB --> CACHE`,
        legend: [
          "Demo Mode - Claude API not configured",
          "Configure ANTHROPIC_API_KEY to generate real diagrams",
          "This is a sample architecture diagram",
          "Real diagrams will be generated from your technical requirements"
        ],
        fullResponse: "Demo architecture diagram generated. Configure Claude API key for real diagram generation based on your technical requirements.",
        content: formatArchitectureMarkdown(
          "Demo architecture diagram generated. Configure Claude API key for real diagram generation based on your technical requirements.",
          `graph TB
    subgraph "Presentation Layer"
        WA[Web Application]
        MA[Mobile App]
    end

    subgraph "Application Layer"
        AG[API Gateway]
        MSA[Microservice A]
        MSB[Microservice B]
    end

    subgraph "Data Layer"
        DB[(Primary Database)]
        CACHE[(Cache)]
    end

    WA --> AG
    MA --> AG
    AG --> MSA
    AG --> MSB
    MSA --> DB
    MSB --> CACHE`,
          `@startuml
!theme plain
title System Architecture Diagram - Demo Mode

package "Presentation Layer" {
  [Web Application] as webapp
  [Mobile App] as mobile
}

package "Application Layer" {
  [API Gateway] as gateway
  [Microservice A] as msA
  [Microservice B] as msB
}

package "Data Layer" {
  database "Primary DB" as db1
  database "Cache" as cache
}

webapp --> gateway
mobile --> gateway
gateway --> msA
gateway --> msB
msA --> db1
msB --> cache

note right of gateway : Demo Mode - Configure OpenAI API key
@enduml`,
          [
            "Demo Mode - Claude API not configured",
            "Configure ANTHROPIC_API_KEY to generate real diagrams",
            "This is a sample architecture diagram",
            "Real diagrams will be generated from your technical requirements"
          ]
        ),
        message: 'Demo architecture diagram generated. Configure Claude API key for real diagram generation.'
      });
    }

    console.log('Generating architecture diagram from technical requirements...');
    console.log(`Technical requirements content length: ${technicalRequirementsContent.length} characters`);

    // Call Claude API with Sonnet 4
    console.log('Calling Claude API with Sonnet 4 for architecture diagram...');
    const fullPrompt = `${ARCHITECTURE_DIAGRAM_PROMPT}\n\nTechnical Requirements Document Content:\n${technicalRequirementsContent}`;
    console.log(`Full prompt length: ${fullPrompt.length} characters`);

    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 20000,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude API call successful for architecture diagram');
    } catch (apiError: any) {
      console.error('Claude API Error:', apiError);

      // Handle specific API errors
      if (apiError.status === 401) {
        return NextResponse.json({
          error: 'Invalid API key. Please check your Anthropic API key configuration.'
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({
          error: 'Rate limit exceeded. Please try again in a few minutes.'
        }, { status: 429 });
      } else if (apiError.message?.includes('model')) {
        return NextResponse.json({
          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.'
        }, { status: 400 });
      } else {
        return NextResponse.json({
          error: `Claude API error: ${apiError.message || 'Unknown error'}`
        }, { status: 500 });
      }
    }

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';

    console.log('=== CLAUDE RESPONSE DEBUG ===');
    console.log('Full response length:', analysisText.length);
    console.log('First 500 characters:', analysisText.substring(0, 500));
    console.log('Last 500 characters:', analysisText.substring(Math.max(0, analysisText.length - 500)));
    console.log('Contains "mermaid":', analysisText.toLowerCase().includes('mermaid'));
    console.log('Contains "plantuml":', analysisText.toLowerCase().includes('plantuml'));
    console.log('Contains "graph":', analysisText.toLowerCase().includes('graph'));
    console.log('Contains "flowchart":', analysisText.toLowerCase().includes('flowchart'));
    console.log('=== END DEBUG ===');

    // Parse the response to extract PlantUML, Mermaid, and legend
    const parsedResponse = parseArchitectureResponse(analysisText);

    console.log('Parsed Mermaid code:', parsedResponse.mermaid);
    console.log('Parsed PlantUML code:', parsedResponse.plantUML);
    console.log('Parsed legend:', parsedResponse.legend);

    // Return the parsed diagrams and full response for debugging
    // Format as markdown document with diagram
    const markdownContent = formatArchitectureMarkdown(
      analysisText,
      parsedResponse.mermaid,
      parsedResponse.plantUML,
      parsedResponse.legend
    );

    return NextResponse.json({
      success: true,
      content: markdownContent, // Return formatted markdown content
      diagramUrl: null, // Would contain actual image URL in full implementation
      plantUML: parsedResponse.plantUML,
      mermaid: parsedResponse.mermaid,
      legend: parsedResponse.legend,
      fullResponse: analysisText,
      debug: {
        responseLength: analysisText.length,
        containsMermaid: analysisText.toLowerCase().includes('mermaid'),
        containsPlantUML: analysisText.toLowerCase().includes('plantuml'),
        containsGraph: analysisText.toLowerCase().includes('graph'),
        firstChars: analysisText.substring(0, 200),
        lastChars: analysisText.substring(Math.max(0, analysisText.length - 200))
      },
      message: 'Architecture diagram generated successfully with Claude Sonnet 4!'
    });

  } catch (error) {
    console.error('Error generating architecture diagram:', error);
    return NextResponse.json(
      { error: 'Failed to generate architecture diagram' },
      { status: 500 }
    );
  }
}

function formatArchitectureMarkdown(
  analysisText: string,
  mermaidCode?: string,
  plantUMLCode?: string,
  legend?: string[]
): string {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  let content = `# Architecture Diagram Document

**Date:** ${currentDate}
**Generated by:** Claude Sonnet 4 AI Analysis

---

${analysisText}

---

## Architecture Diagrams

`;

  if (mermaidCode) {
    content += `### Mermaid Diagram

\`\`\`mermaid
${mermaidCode}
\`\`\`

`;
  }

  if (plantUMLCode) {
    content += `### PlantUML Diagram

\`\`\`plantuml
${plantUMLCode}
\`\`\`

`;
  }

  if (legend && legend.length > 0) {
    content += `### Legend

`;
    legend.forEach(item => {
      content += `- ${item}\n`;
    });
    content += '\n';
  }

  content += `---

*This architecture diagram document was automatically generated using AI analysis. Please review and validate the technical architecture before proceeding with implementation.*`;

  return content;
}

function parseArchitectureResponse(text: string) {
  const result = {
    plantUML: '',
    mermaid: '',
    legend: [] as string[]
  };

  console.log('Parsing architecture response, length:', text.length);

  // Extract PlantUML code - try multiple formats
  let plantUMLMatch = text.match(/--- begin plantuml ---\s*([\s\S]*?)\s*--- end plantuml ---/i);
  if (plantUMLMatch) {
    result.plantUML = plantUMLMatch[1].trim();
  } else {
    // Try alternative format with @startuml/@enduml
    plantUMLMatch = text.match(/@startuml([\s\S]*?)@enduml/i);
    if (plantUMLMatch) {
      result.plantUML = `@startuml${plantUMLMatch[1]}@enduml`;
    } else {
      // Try looking for PlantUML in code blocks
      plantUMLMatch = text.match(/```plantuml\s*([\s\S]*?)\s*```/i);
      if (plantUMLMatch) {
        result.plantUML = plantUMLMatch[1].trim();
      }
    }
  }

  // Extract Mermaid code - try multiple formats
  let mermaidMatch = text.match(/--- begin mermaid ---\s*([\s\S]*?)\s*--- end mermaid ---/i);
  if (mermaidMatch) {
    result.mermaid = mermaidMatch[1].trim();
  } else {
    // Try alternative format with ```mermaid - IMPORTANT: Extract content between the markers
    mermaidMatch = text.match(/```mermaid\s*\n([\s\S]*?)\n\s*```/i);
    if (mermaidMatch) {
      result.mermaid = mermaidMatch[1].trim();
    } else {
      // Try without newlines
      mermaidMatch = text.match(/```mermaid\s*([\s\S]*?)\s*```/i);
      if (mermaidMatch) {
        let content = mermaidMatch[1].trim();
        // Remove any remaining ```mermaid or ``` markers
        content = content.replace(/^```mermaid\s*/i, '').replace(/\s*```$/, '');
        result.mermaid = content.trim();
      } else {
        // Try looking for graph/flowchart patterns directly
        mermaidMatch = text.match(/(graph|flowchart)\s+(?:TB|TD|BT|RL|LR)[\s\S]*?(?=\n\n|\n[A-Z]|$)/i);
        if (mermaidMatch) {
          result.mermaid = mermaidMatch[0].trim();
        }
      }
    }
  }

  // Extract legend
  const legendMatch = text.match(/LEGEND AND ASSUMPTIONS\s*([\s\S]*?)(?=\n\n|$)/i);
  if (legendMatch) {
    const legendText = legendMatch[1].trim();
    result.legend = legendText.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && (line.startsWith('-') || line.startsWith('•')))
      .map(line => line.replace(/^[-•]\s*/, '').trim())
      .filter(line => line.length > 0);
  }

  // If no structured formats found, try to extract any diagram-like content
  if (!result.mermaid && !result.plantUML) {
    console.log('No structured diagram found, looking for any diagram content...');

    // Try to find any Mermaid-like content patterns
    const patterns = [
      // Look for graph/flowchart declarations
      /(graph|flowchart)\s+(?:TB|TD|BT|RL|LR)[\s\S]*?(?=\n\n|\n[A-Z][a-z]|\n\*|$)/i,
      // Look for sequence diagrams
      /sequenceDiagram[\s\S]*?(?=\n\n|\n[A-Z][a-z]|\n\*|$)/i,
      // Look for class diagrams
      /classDiagram[\s\S]*?(?=\n\n|\n[A-Z][a-z]|\n\*|$)/i,
      // Look for any content with arrows (common in diagrams)
      /[A-Za-z0-9_\[\]]+\s*(?:-->|---)\s*[A-Za-z0-9_\[\]]+[\s\S]*?(?=\n\n|\n[A-Z][a-z]|\n\*|$)/i
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        result.mermaid = match[0].trim();
        console.log('Found diagram content with pattern:', pattern.source);
        break;
      }
    }

    // If still no Mermaid found, try PlantUML patterns
    if (!result.mermaid && !result.plantUML) {
      const plantUMLPatterns = [
        // Look for @startuml without the closing tag
        /@startuml[\s\S]*?(?=\n\n|\n[A-Z][a-z]|\n\*|$)/i,
        // Look for typical PlantUML content
        /(?:actor|participant|database|node|cloud|package)[\s\S]*?(?=\n\n|\n[A-Z][a-z]|\n\*|$)/i
      ];

      for (const pattern of plantUMLPatterns) {
        const match = text.match(pattern);
        if (match) {
          result.plantUML = match[0].trim();
          if (!result.plantUML.includes('@enduml')) {
            result.plantUML += '\n@enduml';
          }
          console.log('Found PlantUML content with pattern:', pattern.source);
          break;
        }
      }
    }
  }

  // Clean up the extracted codes
  if (result.mermaid) {
    result.mermaid = cleanMermaidCode(result.mermaid);
  }
  if (result.plantUML) {
    result.plantUML = cleanPlantUMLCode(result.plantUML);
  }

  // If still no diagrams found, create a simple fallback based on the text content
  if (!result.mermaid && !result.plantUML) {
    console.log('No diagrams found, creating fallback diagram...');

    // Create a simple Mermaid diagram based on common architecture terms
    const architectureTerms = [
      'frontend', 'backend', 'database', 'api', 'service', 'server', 'client',
      'microservice', 'gateway', 'load balancer', 'cache', 'queue', 'storage'
    ];

    const foundTerms = architectureTerms.filter(term =>
      text.toLowerCase().includes(term)
    );

    if (foundTerms.length > 0) {
      let fallbackDiagram = 'graph TD\n';
      foundTerms.forEach((term, index) => {
        const nodeId = term.replace(/\s+/g, '');
        fallbackDiagram += `    ${nodeId}[${term.charAt(0).toUpperCase() + term.slice(1)}]\n`;
        if (index > 0) {
          const prevNodeId = foundTerms[index - 1].replace(/\s+/g, '');
          fallbackDiagram += `    ${prevNodeId} --> ${nodeId}\n`;
        }
      });

      result.mermaid = fallbackDiagram;
      result.legend.push('Fallback diagram generated from detected architecture terms');
      console.log('Created fallback diagram with terms:', foundTerms);
    } else {
      // Last resort: create a generic architecture diagram
      result.mermaid = `graph TD
    A[User Interface] --> B[API Gateway]
    B --> C[Application Services]
    C --> D[Database]
    B --> E[External Services]
    C --> F[Cache Layer]`;
      result.legend.push('Generic architecture diagram - no specific terms detected');
      console.log('Created generic fallback diagram');
    }
  }

  console.log('Final parsed result:', {
    plantUMLLength: result.plantUML.length,
    mermaidLength: result.mermaid.length,
    legendCount: result.legend.length,
    mermaidPreview: result.mermaid.substring(0, 100)
  });

  return result;
}

function cleanMermaidCode(code: string): string {
  // Remove any markdown code block markers
  let cleaned = code
    .replace(/^```mermaid\s*/i, '')
    .replace(/\s*```$/i, '')
    .replace(/^```\s*/i, '')
    .trim();

  // Remove any extra whitespace and normalize line endings
  cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Ensure it starts with a valid Mermaid diagram type
  if (!cleaned.match(/^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gitgraph|pie|quadrantChart|requirement|mindmap|timeline|sankey|block)/i)) {
    // If it doesn't start with a diagram type, try to detect and add one
    if (cleaned.includes('-->') || cleaned.includes('---')) {
      cleaned = `graph TD\n${cleaned}`;
    }
  }

  return cleaned;
}

function cleanPlantUMLCode(code: string): string {
  // Remove any markdown code block markers
  let cleaned = code
    .replace(/^```plantuml\s*/i, '')
    .replace(/\s*```$/i, '')
    .replace(/^```\s*/i, '')
    .trim();

  // Ensure it has proper @startuml/@enduml markers
  if (!cleaned.startsWith('@startuml')) {
    cleaned = `@startuml\n${cleaned}`;
  }
  if (!cleaned.endsWith('@enduml')) {
    cleaned = `${cleaned}\n@enduml`;
  }

  return cleaned;
}
