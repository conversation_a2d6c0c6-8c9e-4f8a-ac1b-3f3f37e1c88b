import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// For development environments with SSL certificate issues
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
  console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',
});

const MERMAID_DIAGRAM_PROMPT = `You are a senior solutions architect. Generate a clean Mermaid diagram from the technical requirements.

CRITICAL RULES:
1. Output ONLY valid Mermaid code
2. Start with "graph TD" 
3. Use simple alphanumeric node IDs (no spaces, no special characters)
4. Use proper syntax: NodeID[Label] --> OtherNodeID[Other Label]
5. NO markdown blocks, NO explanations, NO extra text

Example format:
graph TD
    Frontend[Frontend App] --> Gateway[API Gateway]
    Gateway --> Auth[Auth Service]
    Gateway --> API[Business API]
    API --> DB[Database]
    API --> Cache[Redis Cache]

Generate the architecture diagram now:`;

export async function POST(request: NextRequest) {
  try {
    const { technicalRequirements } = await request.json();
    
    if (!technicalRequirements) {
      return NextResponse.json({ error: 'Technical requirements are required' }, { status: 400 });
    }

    console.log(`Processing technical requirements: ${technicalRequirements.length} characters`);

    // Check if API key is configured
    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo diagram');
      
      // Return demo Mermaid diagram
      const demoMermaid = `graph TD
    Frontend[Frontend Application] --> Gateway[API Gateway]
    Gateway --> Auth[Authentication Service]
    Gateway --> API[Business Logic API]
    API --> DB[Primary Database]
    API --> Cache[Redis Cache]
    Gateway --> External[External Services]
    
    subgraph "Presentation Layer"
        Frontend
    end
    
    subgraph "Application Layer"
        Gateway
        Auth
        API
    end
    
    subgraph "Data Layer"
        DB
        Cache
    end
    
    subgraph "External"
        External
    end`;

      return NextResponse.json({
        success: true,
        mermaidCode: demoMermaid,
        message: 'Demo architecture diagram generated. Configure Claude API key for real diagram generation.'
      });
    }

    console.log('Generating architecture diagram with Claude Sonnet 4...');
    
    // Call Claude API with simplified prompt
    const fullPrompt = `${MERMAID_DIAGRAM_PROMPT}\n\nTechnical Requirements:\n${technicalRequirements}`;
    
    let response;
    try {
      response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 20000,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });
      console.log('Claude API call successful for architecture diagram');
    } catch (apiError: any) {
      console.error('Claude API Error:', apiError);
      
      if (apiError.status === 401) {
        return NextResponse.json({ 
          error: 'Invalid API key. Please check your Anthropic API key configuration.' 
        }, { status: 401 });
      } else if (apiError.status === 429) {
        return NextResponse.json({ 
          error: 'Rate limit exceeded. Please try again in a few minutes.' 
        }, { status: 429 });
      } else {
        return NextResponse.json({ 
          error: `Claude API error: ${apiError.message || 'Unknown error'}` 
        }, { status: 500 });
      }
    }

    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Clean and validate the Mermaid code
    const mermaidCode = cleanMermaidCode(analysisText);
    
    console.log('Generated Mermaid code:', mermaidCode.substring(0, 200));

    return NextResponse.json({
      success: true,
      mermaidCode: mermaidCode,
      message: 'Architecture diagram generated successfully with Claude Sonnet 4!'
    });

  } catch (error) {
    console.error('Error generating architecture diagram:', error);
    return NextResponse.json(
      { error: 'Failed to generate architecture diagram' }, 
      { status: 500 }
    );
  }
}

function cleanMermaidCode(text: string): string {
  // Remove any markdown code block markers
  let cleaned = text
    .replace(/^```mermaid\s*/i, '')
    .replace(/\s*```$/i, '')
    .replace(/^```\s*/i, '')
    .trim();

  // Remove any extra whitespace and normalize line endings
  cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Ensure it starts with a valid Mermaid diagram type
  if (!cleaned.match(/^(graph|flowchart|sequenceDiagram|classDiagram)/i)) {
    // If it doesn't start with a diagram type, try to detect and add one
    if (cleaned.includes('-->') || cleaned.includes('---')) {
      cleaned = `graph TD\n${cleaned}`;
    }
  }

  // Remove any problematic characters that cause parsing errors
  cleaned = cleaned
    .replace(/[^\w\s\[\](){}|:;.,\->\n"']/g, '') // Remove special chars except allowed ones
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/\n\s+/g, '\n') // Remove leading spaces on lines
    .trim();

  return cleaned;
}
