'use client';

import { useState } from 'react';

export default function TestPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/test-file-upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: 'Failed to upload file' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">File Upload Test</h1>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <label className="block mb-4">
            <span className="text-gray-700">Upload a transcript file:</span>
            <input
              type="file"
              onChange={handleFileUpload}
              accept=".txt,.docx,.pdf,.md"
              className="mt-1 block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-full file:border-0
                file:text-sm file:font-semibold
                file:bg-blue-50 file:text-blue-700
                hover:file:bg-blue-100"
            />
          </label>

          {loading && (
            <div className="text-blue-600">Processing file...</div>
          )}

          {result && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-3">Result:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">Test Instructions:</h3>
          <ol className="list-decimal list-inside text-yellow-700 space-y-1">
            <li>Upload the sample-transcript.txt file from the project root</li>
            <li>Check if the file content is extracted correctly</li>
            <li>Verify the content preview shows the meeting transcript</li>
            <li>If successful, the main Claude integration should work</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
