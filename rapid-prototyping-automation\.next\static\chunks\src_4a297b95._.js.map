{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  gradient?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n}\n\nexport const Card: React.FC<CardProps> = ({\n  children,\n  className,\n  gradient = false,\n  padding = 'md'\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  return (\n    <div\n      className={cn(\n        'card',\n        gradient && 'card-gradient',\n        paddingClasses[padding],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardTitle: React.FC<CardTitleProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <h3 className={cn('text-xl font-semibold gradient-text', className)}>\n      {children}\n    </h3>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContent: React.FC<CardContentProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('text-gray-600', className)}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AACA;;;AASO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,IAAI,EACf;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,YAAY,iBACZ,cAAc,CAAC,QAAQ,EACvB;kBAGD;;;;;;AAGP;KAxBa;AA+BN,MAAM,aAAwC,CAAC,EACpD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;MATa;AAgBN,MAAM,YAAsC,CAAC,EAClD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBACtD;;;;;;AAGP;MATa;AAgBN,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBACjC;;;;;;AAGP;MATa", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'success';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  disabled,\n  children,\n  ...props\n}) => {\n  return (\n    <button\n      className={cn(\n        'btn',\n        `btn-${variant}`,\n        `btn-${size}`,\n        loading && 'opacity-75 cursor-not-allowed',\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-3 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AASO,MAAM,SAAgC,CAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,CAAC,IAAI,EAAE,SAAS,EAChB,CAAC,IAAI,EAAE,MAAM,EACb,WAAW,iCACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA9Ca", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ProgressProps {\n  value: number;\n  max?: number;\n  className?: string;\n  showLabel?: boolean;\n  label?: string;\n}\n\nexport const Progress: React.FC<ProgressProps> = ({\n  value,\n  max = 100,\n  className,\n  showLabel = false,\n  label\n}) => {\n  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);\n\n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between items-center mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">\n            {label || 'Progress'}\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            {Math.round(percentage)}%\n          </span>\n        </div>\n      )}\n      <div className=\"progress-bar\">\n        <div\n          className=\"progress-fill\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,MAAM,GAAG,EACT,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACN;IACC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,SAAS;;;;;;kCAEZ,6LAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAI9B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C;KA7Ba", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/FileUpload.tsx"], "sourcesContent": ["import React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, File, X } from 'lucide-react';\nimport { cn, formatFileSize } from '@/lib/utils';\nimport { Button } from './Button';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  accept?: Record<string, string[]>;\n  maxSize?: number;\n  className?: string;\n}\n\nexport const FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  accept = {\n    'text/*': ['.txt', '.md', '.doc', '.docx'],\n    'application/pdf': ['.pdf']\n  },\n  maxSize = 10 * 1024 * 1024, // 10MB\n  className\n}) => {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [error, setError] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError('');\n    \n    if (rejectedFiles.length > 0) {\n      const rejection = rejectedFiles[0];\n      if (rejection.errors[0]?.code === 'file-too-large') {\n        setError(`File is too large. Maximum size is ${formatFileSize(maxSize)}`);\n      } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n        setError('Invalid file type. Please upload a text document or PDF.');\n      } else {\n        setError('File upload failed. Please try again.');\n      }\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      setSelectedFile(file);\n      onFileSelect(file);\n    }\n  }, [onFileSelect, maxSize]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept,\n    maxSize,\n    multiple: false\n  });\n\n  const removeFile = () => {\n    setSelectedFile(null);\n    setError('');\n  };\n\n  return (\n    <div className={cn('w-full', className)}>\n      {!selectedFile ? (\n        <div\n          {...getRootProps()}\n          className={cn(\n            'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200',\n            isDragActive\n              ? 'border-blue-400 bg-blue-50'\n              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n          )}\n        >\n          <input {...getInputProps()} />\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <p className=\"text-lg font-medium text-gray-700 mb-2\">\n            {isDragActive ? 'Drop the file here' : 'Upload transcript file'}\n          </p>\n          <p className=\"text-sm text-gray-500 mb-4\">\n            Drag and drop your file here, or click to browse\n          </p>\n          <p className=\"text-xs text-gray-400\">\n            Supports: TXT, MD, DOC, DOCX, PDF (max {formatFileSize(maxSize)})\n          </p>\n        </div>\n      ) : (\n        <div className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <File className=\"h-8 w-8 text-blue-500\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">{selectedFile.name}</p>\n                <p className=\"text-sm text-gray-500\">\n                  {formatFileSize(selectedFile.size)}\n                </p>\n              </div>\n            </div>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={removeFile}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      )}\n      \n      {error && (\n        <div className=\"mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2\">\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;;AASO,MAAM,aAAwC,CAAC,EACpD,YAAY,EACZ,SAAS;IACP,UAAU;QAAC;QAAQ;QAAO;QAAQ;KAAQ;IAC1C,mBAAmB;QAAC;KAAO;AAC7B,CAAC,EACD,UAAU,KAAK,OAAO,IAAI,EAC1B,SAAS,EACV;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC,eAAuB;YACjD,SAAS;YAET,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,YAAY,aAAa,CAAC,EAAE;gBAClC,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,kBAAkB;oBAClD,SAAS,CAAC,mCAAmC,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAC1E,OAAO,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,qBAAqB;oBAC5D,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;gBACA;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,gBAAgB;gBAChB,aAAa;YACf;QACF;yCAAG;QAAC;QAAc;KAAQ;IAE1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA;QACA;QACA,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,CAAC,6BACA,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,eACI,+BACA;;kCAGN,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAE,WAAU;kCACV,eAAe,uBAAuB;;;;;;kCAEzC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAE,WAAU;;4BAAwB;4BACK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;4BAAS;;;;;;;;;;;;qCAIpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA6B,aAAa,IAAI;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;GArGa;;QAkC2C,2KAAA,CAAA,cAAW;;;KAlCtD", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ChatInterface.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Button } from './Button';\nimport { Send, Bot, User } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: 'user' | 'bot';\n  timestamp: Date;\n}\n\ninterface ChatInterfaceProps {\n  onFeedback: (feedback: string) => void;\n  className?: string;\n}\n\nexport const ChatInterface: React.FC<ChatInterfaceProps> = ({\n  onFeedback,\n  className\n}) => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: 'I\\'ve generated the architecture diagram based on your requirements. What do you think about the current design?',\n      sender: 'bot',\n      timestamp: new Date()\n    }\n  ]);\n  const [inputValue, setInputValue] = useState('');\n\n  const handleSendMessage = () => {\n    if (!inputValue.trim()) return;\n\n    const newMessage: Message = {\n      id: Date.now().toString(),\n      content: inputValue,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    onFeedback(inputValue);\n    setInputValue('');\n\n    // Simulate bot response\n    setTimeout(() => {\n      const botResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        content: 'Thank you for your feedback! I\\'ll incorporate these changes into the architecture design.',\n        sender: 'bot',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, botResponse]);\n    }, 1000);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className={cn('bg-white border border-gray-200 rounded-lg', className)}>\n      <div className=\"p-4 border-b border-gray-200\">\n        <h4 className=\"font-semibold text-gray-900\">Architecture Feedback</h4>\n        <p className=\"text-sm text-gray-600\">Share your thoughts on the generated architecture</p>\n      </div>\n      \n      <div className=\"h-64 overflow-y-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={cn(\n              'flex items-start space-x-3',\n              message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n            )}\n          >\n            <div className={cn(\n              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',\n              message.sender === 'user' \n                ? 'bg-blue-500 text-white' \n                : 'bg-gray-200 text-gray-600'\n            )}>\n              {message.sender === 'user' ? (\n                <User className=\"w-4 h-4\" />\n              ) : (\n                <Bot className=\"w-4 h-4\" />\n              )}\n            </div>\n            <div className={cn(\n              'flex-1 max-w-xs lg:max-w-md px-4 py-2 rounded-lg',\n              message.sender === 'user'\n                ? 'bg-blue-500 text-white ml-auto'\n                : 'bg-gray-100 text-gray-900'\n            )}>\n              <p className=\"text-sm\">{message.content}</p>\n              <p className={cn(\n                'text-xs mt-1',\n                message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'\n              )}>\n                {message.timestamp.toLocaleTimeString([], { \n                  hour: '2-digit', \n                  minute: '2-digit' \n                })}\n              </p>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex space-x-2\">\n          <textarea\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Share your feedback on the architecture...\"\n            className=\"flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows={2}\n          />\n          <Button\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim()}\n            size=\"sm\"\n            className=\"self-end\"\n          >\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;AAcO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,EACV,SAAS,EACV;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;QACzC,WAAW;QACX,cAAc;QAEd,wBAAwB;QACxB,WAAW;YACT,MAAM,cAAuB;gBAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;QAC5C,GAAG;IACL;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;;0BAC/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,QAAQ,MAAM,KAAK,SAAS,qCAAqC;;0CAGnE,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uEACA,QAAQ,MAAM,KAAK,SACf,2BACA;0CAEH,QAAQ,MAAM,KAAK,uBAClB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oDACA,QAAQ,MAAM,KAAK,SACf,mCACA;;kDAEJ,6LAAC;wCAAE,WAAU;kDAAW,QAAQ,OAAO;;;;;;kDACvC,6LAAC;wCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,gBACA,QAAQ,MAAM,KAAK,SAAS,kBAAkB;kDAE7C,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4CACxC,MAAM;4CACN,QAAQ;wCACV;;;;;;;;;;;;;uBAhCC,QAAQ,EAAE;;;;;;;;;;0BAuCrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,YAAY;4BACZ,aAAY;4BACZ,WAAU;4BACV,MAAM;;;;;;sCAER,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI;4BAC1B,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAtHa;KAAA", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Toast.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { CheckCircle, X, AlertCircle, Info } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport type ToastType = 'success' | 'error' | 'info' | 'warning';\n\ninterface Toast {\n  id: string;\n  message: string;\n  type: ToastType;\n  duration?: number;\n}\n\ninterface ToastProps {\n  toast: Toast;\n  onRemove: (id: string) => void;\n}\n\nconst ToastComponent: React.FC<ToastProps> = ({ toast, onRemove }) => {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id);\n    }, toast.duration || 3000);\n\n    return () => clearTimeout(timer);\n  }, [toast.id, toast.duration, onRemove]);\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5\" />;\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'warning':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5\" />;\n      default:\n        return <Info className=\"h-5 w-5\" />;\n    }\n  };\n\n  const getStyles = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800';\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800';\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800';\n    }\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex items-center justify-between p-4 rounded-lg border shadow-lg animate-slide-up',\n        getStyles()\n      )}\n    >\n      <div className=\"flex items-center space-x-3\">\n        {getIcon()}\n        <span className=\"text-sm font-medium\">{toast.message}</span>\n      </div>\n      <button\n        onClick={() => onRemove(toast.id)}\n        className=\"ml-4 text-current hover:opacity-70 transition-opacity\"\n      >\n        <X className=\"h-4 w-4\" />\n      </button>\n    </div>\n  );\n};\n\ninterface ToastContainerProps {\n  toasts: Toast[];\n  onRemove: (id: string) => void;\n}\n\nexport const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  );\n};\n\n// Hook for managing toasts\nexport const useToast = () => {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n\n  const addToast = (message: string, type: ToastType = 'info', duration?: number) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const newToast: Toast = { id, message, type, duration };\n    setToasts(prev => [...prev, newToast]);\n  };\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success: (message: string, duration?: number) => addToast(message, 'success', duration),\n    error: (message: string, duration?: number) => addToast(message, 'error', duration),\n    info: (message: string, duration?: number) => addToast(message, 'info', duration),\n    warning: (message: string, duration?: number) => addToast(message, 'warning', duration),\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAgBA,MAAM,iBAAuC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,SAAS,MAAM,EAAE;gBACnB;iDAAG,MAAM,QAAQ,IAAI;YAErB;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,YAAY;QAChB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA;;0BAGF,6LAAC;gBAAI,WAAU;;oBACZ;kCACD,6LAAC;wBAAK,WAAU;kCAAuB,MAAM,OAAO;;;;;;;;;;;;0BAEtD,6LAAC;gBACC,SAAS,IAAM,SAAS,MAAM,EAAE;gBAChC,WAAU;0BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GA1DM;KAAA;AAiEC,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChF,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;MARa;AAWN,MAAM,WAAW;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC,SAAiB,OAAkB,MAAM,EAAE;QAC3D,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAkB;YAAE;YAAI;YAAS;YAAM;QAAS;QACtD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;QAC9E,OAAO,CAAC,SAAiB,WAAsB,SAAS,SAAS,SAAS;QAC1E,MAAM,CAAC,SAAiB,WAAsB,SAAS,SAAS,QAAQ;QACxE,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;IAChF;AACF;IAtBa", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/RapidPrototypingApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { Progress } from '@/components/ui/Progress';\nimport { FileUpload } from '@/components/ui/FileUpload';\nimport { ChatInterface } from '@/components/ui/ChatInterface';\nimport { ToastContainer, useToast } from '@/components/ui/Toast';\nimport { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';\n\ntype Step = 1 | 2 | 3 | 4 | 5;\n\ninterface AppState {\n  currentStep: Step;\n  uploadedFile: File | null;\n  isProcessing: boolean;\n  documents: {\n    problemStatement: string | null;\n    technicalRequirements: string | null;\n    architectureDiagram: string | null;\n    prompts: string[] | null;\n  };\n  validations: {\n    problemStatement: boolean | null;\n    technicalRequirements: boolean | null;\n    architectureDiagram: boolean | null;\n  };\n}\n\nconst STEPS = [\n  { id: 1, title: 'Upload Transcript', icon: FileText, description: 'Upload your conversation transcript' },\n  { id: 2, title: 'Problem Statement', icon: CheckCircle, description: 'Review and validate problem statement' },\n  { id: 3, title: 'Technical Requirements', icon: Settings, description: 'Review technical requirements document' },\n  { id: 4, title: 'Architecture Diagram', icon: Image, description: 'Review system architecture' },\n  { id: 5, title: 'Generated Prompts', icon: Code, description: 'Copy implementation prompts' }\n];\n\nexport const RapidPrototypingApp: React.FC = () => {\n  const { toasts, removeToast, success, error, info } = useToast();\n  const [state, setState] = useState<AppState>({\n    currentStep: 1,\n    uploadedFile: null,\n    isProcessing: false,\n    documents: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null,\n      prompts: null\n    },\n    validations: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null\n    }\n  });\n\n  const handleFileUpload = async (file: File) => {\n    setState(prev => ({ ...prev, uploadedFile: file, isProcessing: true }));\n    info('Processing your transcript...', 2000);\n\n    // Simulate processing\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    setState(prev => ({\n      ...prev,\n      isProcessing: false,\n      currentStep: 2,\n      documents: {\n        ...prev.documents,\n        problemStatement: 'Generated problem statement content...'\n      }\n    }));\n\n    success('Problem statement document generated successfully!');\n  };\n\n  const handleValidation = async (documentType: keyof AppState['validations'], isValid: boolean) => {\n    setState(prev => ({\n      ...prev,\n      validations: {\n        ...prev.validations,\n        [documentType]: isValid\n      }\n    }));\n\n    if (isValid) {\n      // Simulate generating next document\n      setState(prev => ({ ...prev, isProcessing: true }));\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      const nextStep = state.currentStep + 1;\n      if (nextStep <= 5) {\n        let newDocuments = { ...state.documents };\n\n        if (nextStep === 3) {\n          newDocuments.technicalRequirements = 'Generated technical requirements content...';\n        } else if (nextStep === 4) {\n          newDocuments.architectureDiagram = 'Generated architecture diagram...';\n        } else if (nextStep === 5) {\n          newDocuments.prompts = ['Prompt 1...', 'Prompt 2...', 'Prompt 3...'];\n        }\n\n        setState(prev => ({\n          ...prev,\n          currentStep: nextStep as Step,\n          documents: newDocuments,\n          isProcessing: false\n        }));\n      }\n    }\n  };\n\n  const currentStepData = STEPS.find(step => step.id === state.currentStep);\n  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <ToastContainer toasts={toasts} onRemove={removeToast} />\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold gradient-text mb-4\">\n            Rapid Prototyping Automation\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Transform your conversation transcripts into comprehensive technical documentation and implementation guides\n          </p>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <Progress \n            value={progress} \n            showLabel \n            label={`Step ${state.currentStep} of ${STEPS.length}: ${currentStepData?.title}`}\n            className=\"max-w-2xl mx-auto\"\n          />\n        </div>\n\n        {/* Steps Navigation */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"flex space-x-4 overflow-x-auto pb-2\">\n            {STEPS.map((step) => {\n              const Icon = step.icon;\n              const isActive = step.id === state.currentStep;\n              const isCompleted = step.id < state.currentStep;\n              \n              return (\n                <div\n                  key={step.id}\n                  className={`flex flex-col items-center min-w-[120px] p-3 rounded-lg transition-all ${\n                    isActive \n                      ? 'bg-blue-100 border-2 border-blue-300' \n                      : isCompleted \n                        ? 'bg-green-100 border-2 border-green-300'\n                        : 'bg-gray-100 border-2 border-gray-200'\n                  }`}\n                >\n                  <Icon \n                    className={`h-6 w-6 mb-2 ${\n                      isActive \n                        ? 'text-blue-600' \n                        : isCompleted \n                          ? 'text-green-600'\n                          : 'text-gray-400'\n                    }`} \n                  />\n                  <span className={`text-sm font-medium text-center ${\n                    isActive \n                      ? 'text-blue-800' \n                      : isCompleted \n                        ? 'text-green-800'\n                        : 'text-gray-600'\n                  }`}>\n                    {step.title}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-4xl mx-auto\">\n          {state.currentStep === 1 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Upload Your Conversation Transcript</CardTitle>\n              </CardHeader>\n              <CardContent>\n                {!state.isProcessing ? (\n                  <FileUpload onFileSelect={handleFileUpload} />\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <div className=\"animate-pulse-slow mb-4\">\n                      <FileText className=\"h-16 w-16 text-blue-500 mx-auto\" />\n                    </div>\n                    <h3 className=\"text-xl font-semibold mb-2\">Processing Your Transcript</h3>\n                    <p className=\"text-gray-600\">\n                      We're analyzing your conversation and preparing the documents...\n                    </p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 2 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Problem Statement Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Generated Problem Statement</h4>\n                    <div className=\"prose max-w-none\">\n                      <p className=\"text-gray-700 leading-relaxed\">\n                        Based on your conversation transcript, we've identified the core problem that needs to be solved.\n                        This document outlines the key challenges, objectives, and success criteria for your project.\n                      </p>\n                      <div className=\"mt-4 p-4 bg-blue-50 rounded-lg\">\n                        <h5 className=\"font-medium text-blue-900 mb-2\">Key Problem Areas:</h5>\n                        <ul className=\"list-disc list-inside text-blue-800 space-y-1\">\n                          <li>User experience optimization</li>\n                          <li>System performance bottlenecks</li>\n                          <li>Integration complexity</li>\n                          <li>Scalability requirements</li>\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\n                    <Button\n                      variant=\"success\"\n                      size=\"lg\"\n                      onClick={() => {\n                        // Simulate document download\n                        const element = document.createElement('a');\n                        const file = new Blob(['Problem Statement Document Content...'], {type: 'text/plain'});\n                        element.href = URL.createObjectURL(file);\n                        element.download = 'problem-statement.txt';\n                        document.body.appendChild(element);\n                        element.click();\n                        document.body.removeChild(element);\n                      }}\n                      className=\"flex-1\"\n                    >\n                      Download Problem Statement\n                    </Button>\n                  </div>\n\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                    <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                    <p className=\"text-yellow-700 mb-4\">\n                      Please review the problem statement document and confirm if it accurately captures your requirements.\n                    </p>\n                    <div className=\"flex gap-3\">\n                      <Button\n                        variant=\"success\"\n                        onClick={() => handleValidation('problemStatement', true)}\n                      >\n                        YES - Continue\n                      </Button>\n                      <Button\n                        variant=\"secondary\"\n                        onClick={() => handleValidation('problemStatement', false)}\n                      >\n                        NO - Needs Feedback\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 3 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Technical Requirements Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Generated Technical Requirements</h4>\n                    <div className=\"prose max-w-none\">\n                      <p className=\"text-gray-700 leading-relaxed mb-4\">\n                        This document outlines the technical specifications, system requirements, and implementation details\n                        needed to solve the identified problems.\n                      </p>\n                      <div className=\"grid md:grid-cols-2 gap-4\">\n                        <div className=\"p-4 bg-green-50 rounded-lg\">\n                          <h5 className=\"font-medium text-green-900 mb-2\">Functional Requirements</h5>\n                          <ul className=\"list-disc list-inside text-green-800 space-y-1 text-sm\">\n                            <li>User authentication system</li>\n                            <li>Real-time data processing</li>\n                            <li>API integration capabilities</li>\n                            <li>Responsive user interface</li>\n                          </ul>\n                        </div>\n                        <div className=\"p-4 bg-purple-50 rounded-lg\">\n                          <h5 className=\"font-medium text-purple-900 mb-2\">Non-Functional Requirements</h5>\n                          <ul className=\"list-disc list-inside text-purple-800 space-y-1 text-sm\">\n                            <li>99.9% uptime availability</li>\n                            <li>Sub-second response times</li>\n                            <li>GDPR compliance</li>\n                            <li>Horizontal scalability</li>\n                          </ul>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\n                    <Button\n                      variant=\"success\"\n                      size=\"lg\"\n                      onClick={() => {\n                        const element = document.createElement('a');\n                        const file = new Blob(['Technical Requirements Document Content...'], {type: 'text/plain'});\n                        element.href = URL.createObjectURL(file);\n                        element.download = 'technical-requirements.txt';\n                        document.body.appendChild(element);\n                        element.click();\n                        document.body.removeChild(element);\n                      }}\n                      className=\"flex-1\"\n                    >\n                      Download Technical Requirements\n                    </Button>\n                  </div>\n\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                    <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                    <p className=\"text-yellow-700 mb-4\">\n                      Please review the technical requirements document and confirm if it meets your technical specifications.\n                    </p>\n                    <div className=\"flex gap-3\">\n                      <Button\n                        variant=\"success\"\n                        onClick={() => handleValidation('technicalRequirements', true)}\n                      >\n                        YES - Continue\n                      </Button>\n                      <Button\n                        variant=\"secondary\"\n                        onClick={() => handleValidation('technicalRequirements', false)}\n                      >\n                        NO - Needs Feedback\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 4 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>System Architecture Diagram</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Generated Architecture Diagram</h4>\n                    <div className=\"bg-gray-100 rounded-lg p-8 text-center\">\n                      <Image className=\"h-24 w-24 text-gray-400 mx-auto mb-4\" />\n                      <p className=\"text-gray-600 mb-2\">Complex System Architecture</p>\n                      <p className=\"text-sm text-gray-500\">\n                        A comprehensive diagram showing system components, data flow, and integration points\n                      </p>\n                    </div>\n                    <div className=\"mt-4 grid md:grid-cols-3 gap-4\">\n                      <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\n                        <h5 className=\"font-medium text-blue-900 mb-1\">Frontend Layer</h5>\n                        <p className=\"text-sm text-blue-700\">React, TypeScript, Tailwind</p>\n                      </div>\n                      <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                        <h5 className=\"font-medium text-green-900 mb-1\">Backend Services</h5>\n                        <p className=\"text-sm text-green-700\">Node.js, Express, PostgreSQL</p>\n                      </div>\n                      <div className=\"text-center p-3 bg-purple-50 rounded-lg\">\n                        <h5 className=\"font-medium text-purple-900 mb-1\">Infrastructure</h5>\n                        <p className=\"text-sm text-purple-700\">AWS, Docker, Kubernetes</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-blue-800 mb-3\">Quick Review</h4>\n                      <p className=\"text-blue-700 mb-4\">\n                        Does the architecture diagram accurately represent your system design requirements?\n                      </p>\n                      <div className=\"flex gap-3\">\n                        <Button\n                          variant=\"success\"\n                          onClick={() => handleValidation('architectureDiagram', true)}\n                        >\n                          YES - Looks Good\n                        </Button>\n                        <Button\n                          variant=\"secondary\"\n                          onClick={() => handleValidation('architectureDiagram', false)}\n                        >\n                          NO - Needs Changes\n                        </Button>\n                      </div>\n                    </div>\n\n                    <ChatInterface\n                      onFeedback={(feedback) => {\n                        console.log('Architecture feedback:', feedback);\n                        // Handle feedback processing here\n                      }}\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {state.currentStep === 5 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Implementation Prompts</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">Copy-Paste Ready Prompts</h4>\n                    <p className=\"text-gray-600 mb-6\">\n                      Here are the generated prompts for implementing your solution. Click to copy each prompt.\n                    </p>\n\n                    <div className=\"space-y-4\">\n                      {[\n                        {\n                          title: \"Frontend Development Prompt\",\n                          content: \"Create a React TypeScript application with the following components: user authentication, dashboard interface, real-time data visualization, and responsive design using Tailwind CSS...\"\n                        },\n                        {\n                          title: \"Backend API Development Prompt\",\n                          content: \"Build a Node.js Express API with the following endpoints: user management, data processing, real-time WebSocket connections, and PostgreSQL database integration...\"\n                        },\n                        {\n                          title: \"Database Schema Prompt\",\n                          content: \"Design a PostgreSQL database schema with the following tables: users, projects, data_points, and audit_logs. Include proper indexing and relationships...\"\n                        },\n                        {\n                          title: \"DevOps & Deployment Prompt\",\n                          content: \"Create Docker containers and Kubernetes deployment configurations for a scalable application with load balancing, auto-scaling, and monitoring...\"\n                        }\n                      ].map((prompt, index) => (\n                        <div key={index} className=\"border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors\">\n                          <div className=\"flex justify-between items-start mb-2\">\n                            <h5 className=\"font-medium text-gray-900\">{prompt.title}</h5>\n                            <Button\n                              size=\"sm\"\n                              variant=\"secondary\"\n                              onClick={() => {\n                                navigator.clipboard.writeText(prompt.content);\n                                success('Prompt copied to clipboard!');\n                              }}\n                            >\n                              Copy\n                            </Button>\n                          </div>\n                          <p className=\"text-sm text-gray-600 line-clamp-3\">\n                            {prompt.content}\n                          </p>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 text-center\">\n                    <CheckCircle className=\"h-12 w-12 text-green-500 mx-auto mb-3\" />\n                    <h4 className=\"font-semibold text-green-800 mb-2\">Process Complete!</h4>\n                    <p className=\"text-green-700\">\n                      Your rapid prototyping automation is complete. Use the prompts above to implement your solution.\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AA8BA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,iNAAA,CAAA,WAAQ;QAAE,aAAa;IAAsC;IACxG;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,8NAAA,CAAA,cAAW;QAAE,aAAa;IAAwC;IAC7G;QAAE,IAAI;QAAG,OAAO;QAA0B,MAAM,6MAAA,CAAA,WAAQ;QAAE,aAAa;IAAyC;IAChH;QAAE,IAAI;QAAG,OAAO;QAAwB,MAAM,uMAAA,CAAA,QAAK;QAAE,aAAa;IAA6B;IAC/F;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,qMAAA,CAAA,OAAI;QAAE,aAAa;IAA8B;CAC7F;AAEM,MAAM,sBAAgC;;IAC3C,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC3C,aAAa;QACb,cAAc;QACd,cAAc;QACd,WAAW;YACT,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;YACrB,SAAS;QACX;QACA,aAAa;YACX,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,cAAc;YAAK,CAAC;QACrE,KAAK,iCAAiC;QAEtC,sBAAsB;QACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,aAAa;gBACb,WAAW;oBACT,GAAG,KAAK,SAAS;oBACjB,kBAAkB;gBACpB;YACF,CAAC;QAED,QAAQ;IACV;IAEA,MAAM,mBAAmB,OAAO,cAA6C;QAC3E,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;oBACX,GAAG,KAAK,WAAW;oBACnB,CAAC,aAAa,EAAE;gBAClB;YACF,CAAC;QAED,IAAI,SAAS;YACX,oCAAoC;YACpC,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAK,CAAC;YACjD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,WAAW,MAAM,WAAW,GAAG;YACrC,IAAI,YAAY,GAAG;gBACjB,IAAI,eAAe;oBAAE,GAAG,MAAM,SAAS;gBAAC;gBAExC,IAAI,aAAa,GAAG;oBAClB,aAAa,qBAAqB,GAAG;gBACvC,OAAO,IAAI,aAAa,GAAG;oBACzB,aAAa,mBAAmB,GAAG;gBACrC,OAAO,IAAI,aAAa,GAAG;oBACzB,aAAa,OAAO,GAAG;wBAAC;wBAAe;wBAAe;qBAAc;gBACtE;gBAEA,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,aAAa;wBACb,WAAW;wBACX,cAAc;oBAChB,CAAC;YACH;QACF;IACF;IAEA,MAAM,kBAAkB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,WAAW;IACxE,MAAM,WAAW,AAAC,CAAC,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK;IAElE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,UAAU;;;;;;0BAC1C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,SAAS;4BACT,OAAO,CAAC,KAAK,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,iBAAiB,OAAO;4BAChF,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC;gCACV,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,KAAK,EAAE,KAAK,MAAM,WAAW;gCAC9C,MAAM,cAAc,KAAK,EAAE,GAAG,MAAM,WAAW;gCAE/C,qBACE,6LAAC;oCAEC,WAAW,CAAC,uEAAuE,EACjF,WACI,yCACA,cACE,2CACA,wCACN;;sDAEF,6LAAC;4CACC,WAAW,CAAC,aAAa,EACvB,WACI,kBACA,cACE,mBACA,iBACN;;;;;;sDAEJ,6LAAC;4CAAK,WAAW,CAAC,gCAAgC,EAChD,WACI,kBACA,cACE,mBACA,iBACN;sDACC,KAAK,KAAK;;;;;;;mCAzBR,KAAK,EAAE;;;;;4BA6BlB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,WAAW,KAAK,mBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACT,CAAC,MAAM,YAAY,iBAClB,6LAAC,yIAAA,CAAA,aAAU;4CAAC,cAAc;;;;;iEAE1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;4BAStC,MAAM,WAAW,KAAK,mBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAI7C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,6LAAC;4EAAG,WAAU;;8FACZ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;8FACJ,6LAAC;8FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAMZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,6BAA6B;4DAC7B,MAAM,UAAU,SAAS,aAAa,CAAC;4DACvC,MAAM,OAAO,IAAI,KAAK;gEAAC;6DAAwC,EAAE;gEAAC,MAAM;4DAAY;4DACpF,QAAQ,IAAI,GAAG,IAAI,eAAe,CAAC;4DACnC,QAAQ,QAAQ,GAAG;4DACnB,SAAS,IAAI,CAAC,WAAW,CAAC;4DAC1B,QAAQ,KAAK;4DACb,SAAS,IAAI,CAAC,WAAW,CAAC;wDAC5B;wDACA,WAAU;kEACX;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;8EAGD,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUZ,MAAM,WAAW,KAAK,mBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAqC;;;;;;8EAIlD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAkC;;;;;;8FAChD,6LAAC;oFAAG,WAAU;;sGACZ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;;;;;;;;;;;;;sFAGR,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAmC;;;;;;8FACjD,6LAAC;oFAAG,WAAU;;sGACZ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;sGACJ,6LAAC;sGAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,MAAM,UAAU,SAAS,aAAa,CAAC;4DACvC,MAAM,OAAO,IAAI,KAAK;gEAAC;6DAA6C,EAAE;gEAAC,MAAM;4DAAY;4DACzF,QAAQ,IAAI,GAAG,IAAI,eAAe,CAAC;4DACnC,QAAQ,QAAQ,GAAG;4DACnB,SAAS,IAAI,CAAC,WAAW,CAAC;4DAC1B,QAAQ,KAAK;4DACb,SAAS,IAAI,CAAC,WAAW,CAAC;wDAC5B;wDACA,WAAU;kEACX;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;8EAGD,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUZ,MAAM,WAAW,KAAK,mBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAClC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAIvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAkC;;;;;;sFAChD,6LAAC;4EAAE,WAAU;sFAAyB;;;;;;;;;;;;8EAExC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAmC;;;;;;sFACjD,6LAAC;4EAAE,WAAU;sFAA0B;;;;;;;;;;;;;;;;;;;;;;;;8DAK7C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAGlC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;sFAGD,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;;;;;;;;;;;;;sEAML,6LAAC,4IAAA,CAAA,gBAAa;4DACZ,YAAY,CAAC;gEACX,QAAQ,GAAG,CAAC,0BAA0B;4DACtC,kCAAkC;4DACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQX,MAAM,WAAW,KAAK,mBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAE,WAAU;sEAAqB;;;;;;sEAIlC,6LAAC;4DAAI,WAAU;sEACZ;gEACC;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;gEACA;oEACE,OAAO;oEACP,SAAS;gEACX;6DACD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,6LAAC;oEAAgB,WAAU;;sFACzB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAA6B,OAAO,KAAK;;;;;;8FACvD,6LAAC,qIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS;wFACP,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;wFAC5C,QAAQ;oFACV;8FACD;;;;;;;;;;;;sFAIH,6LAAC;4EAAE,WAAU;sFACV,OAAO,OAAO;;;;;;;mEAfT;;;;;;;;;;;;;;;;8DAsBhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlD;GAzca;;QAC2C,oIAAA,CAAA,WAAQ;;;KADnD", "debugId": null}}, {"offset": {"line": 2146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { RapidPrototypingApp } from '@/components/RapidPrototypingApp';\n\nexport default function Home() {\n  return <RapidPrototypingApp />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,6LAAC,4IAAA,CAAA,sBAAmB;;;;;AAC7B;KAFwB", "debugId": null}}]}