{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/chat-architecture/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\n\n// For development environments with SSL certificate issues\nif (process.env.NODE_ENV === 'development') {\n  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n}\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { message, context, diagramCode } = await request.json();\n    \n    if (!message) {\n      return NextResponse.json({ error: 'No message provided' }, { status: 400 });\n    }\n\n    // Check if API key is configured\n    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n      console.error('ANTHROPIC_API_KEY is not properly configured - returning demo response');\n\n      return NextResponse.json({\n        success: true,\n        response: \"Thank you for your feedback! In demo mode, I can't actually modify the diagram, but I understand your request. Please configure your Claude API key to enable real-time diagram improvements.\",\n        updatedDiagramCode: diagramCode || null,\n        message: 'Demo mode - Configure Claude API key for real chat functionality'\n      });\n    }\n\n    console.log('Processing architecture chat message...');\n    console.log(`User message: ${message}`);\n\n    // Create a comprehensive prompt for architecture diagram improvement\n    const fullPrompt = `You are a senior solutions architect helping to improve and refine architecture diagrams based on user feedback.\n\nContext: The user is reviewing an architecture diagram that was generated from technical requirements. They may want to:\n- Modify components or connections\n- Add missing elements\n- Improve the layout or organization\n- Clarify relationships between components\n- Add security layers or compliance elements\n- Optimize for performance or scalability\n\nCurrent diagram context:\n${context || 'Architecture diagram for a cloud-native solution'}\n\nCurrent diagram code (if available):\n${diagramCode || 'No diagram code provided'}\n\nUser feedback/request:\n${message}\n\nPlease provide helpful, specific advice and if the user requests changes to the diagram, provide updated Mermaid or PlantUML code that incorporates their feedback.\n\nBe conversational, helpful, and focus on practical architecture improvements. If you provide updated diagram code, wrap it in appropriate code blocks (triple backticks for mermaid or @startuml/@enduml for PlantUML).`;\n\n    let response;\n    try {\n      response = await anthropic.messages.create({\n        model: 'claude-sonnet-4-20250514',\n        max_tokens: 1500,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n      console.log('Claude chat API call successful');\n    } catch (apiError: any) {\n      console.error('Claude Chat API Error:', apiError);\n\n      // Handle specific API errors\n      if (apiError.status === 401) {\n        return NextResponse.json({\n          error: 'Invalid API key. Please check your Anthropic API key configuration.'\n        }, { status: 401 });\n      } else if (apiError.status === 429) {\n        return NextResponse.json({\n          error: 'Rate limit exceeded. Please try again in a few minutes.'\n        }, { status: 429 });\n      } else {\n        return NextResponse.json({\n          error: `Claude API error: ${apiError.message || 'Unknown error'}`\n        }, { status: 500 });\n      }\n    }\n\n    const chatResponse = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    // Check if the response contains updated diagram code\n    let updatedDiagramCode = null;\n    \n    // Look for Mermaid code in the response\n    const mermaidMatch = chatResponse.match(/```mermaid\\s*([\\s\\S]*?)\\s*```/i);\n    if (mermaidMatch) {\n      updatedDiagramCode = {\n        type: 'mermaid',\n        code: mermaidMatch[1].trim()\n      };\n    }\n    \n    // Look for PlantUML code in the response\n    const plantUMLMatch = chatResponse.match(/@startuml([\\s\\S]*?)@enduml/i);\n    if (plantUMLMatch) {\n      updatedDiagramCode = {\n        type: 'plantuml',\n        code: `@startuml${plantUMLMatch[1]}@enduml`\n      };\n    }\n    \n    return NextResponse.json({\n      success: true,\n      response: chatResponse,\n      updatedDiagramCode: updatedDiagramCode,\n      message: 'Chat response generated successfully'\n    });\n\n  } catch (error) {\n    console.error('Error processing chat message:', error);\n    return NextResponse.json(\n      { error: 'Failed to process chat message' }, \n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,2DAA2D;AAC3D,wCAA4C;IAC1C,QAAQ,GAAG,CAAC,4BAA4B,GAAG;AAC7C;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC3C;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE5D,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAsB,GAAG;gBAAE,QAAQ;YAAI;QAC3E;QAEA,iCAAiC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;YACrG,QAAQ,KAAK,CAAC;YAEd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,UAAU;gBACV,oBAAoB,eAAe;gBACnC,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS;QAEtC,qEAAqE;QACrE,MAAM,aAAa,CAAC;;;;;;;;;;;AAWxB,EAAE,WAAW,mDAAmD;;;AAGhE,EAAE,eAAe,2BAA2B;;;AAG5C,EAAE,QAAQ;;;;uNAI6M,CAAC;QAEpN,IAAI;QACJ,IAAI;YACF,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,6BAA6B;YAC7B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,CAAC,kBAAkB,EAAE,SAAS,OAAO,IAAI,iBAAiB;gBACnE,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,sDAAsD;QACtD,IAAI,qBAAqB;QAEzB,wCAAwC;QACxC,MAAM,eAAe,aAAa,KAAK,CAAC;QACxC,IAAI,cAAc;YAChB,qBAAqB;gBACnB,MAAM;gBACN,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI;YAC5B;QACF;QAEA,yCAAyC;QACzC,MAAM,gBAAgB,aAAa,KAAK,CAAC;QACzC,IAAI,eAAe;YACjB,qBAAqB;gBACnB,MAAM;gBACN,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC;YAC7C;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,UAAU;YACV,oBAAoB;YACpB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}