module.exports = {

"[project]/.next-internal/server/app/api/generate-problem-statement/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/app/api/generate-problem-statement/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@anthropic-ai/sdk/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Anthropic__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/@anthropic-ai/sdk/client.mjs [app-route] (ecmascript) <export Anthropic as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/docx/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mammoth$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mammoth/lib/index.js [app-route] (ecmascript)");
;
;
;
;
// For development environments with SSL certificate issues
if ("TURBOPACK compile-time truthy", 1) {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}
// Check if API key is configured
if (!process.env.ANTHROPIC_API_KEY) {
    console.error('ANTHROPIC_API_KEY is not configured in environment variables');
}
const anthropic = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$anthropic$2d$ai$2f$sdk$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Anthropic__as__default$3e$__["default"]({
    apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development'
});
const PROBLEM_STATEMENT_PROMPT = `You are a top 0.1% senior innovation consultant and professional technical writer. 

1. Load & Analyze  
   - Open and read the entire transcript file attached.  
   - Strip out all timestamps; focus only on speaker content, context, and meaning.

2. Deep Content Extraction
   - Identify every meeting attendee from the transcript  
   - Identify every business challenge the customer articulates.  
   - Capture direct quotes (paraphrased or exact) that illustrate those pain points.  
   - List all attendees from DXC proposed solution ideas, including any technical requirements mentioned.  
   - Note CTO's facilitation questions, concerns, and prioritization recommendations.

3. Craft a Polished Analysis
   - Build a structured analysis with these sections:
     1. Title and Participants
        - Infer the title from the intended solution outlined in the transcript and propose a catchy title  
        - List all attendees and their roles
     2. Executive Summary (1 paragraph)  
        - Summarize the workshop goal and outcome in 3–4 sentences—no timestamps.  
     3. Background & Context (½ page)  
        - Why this session was convened, the user's strategic imperatives, and desired outcomes.  
     4. Key Business Challenges  
        - For each challenge, include:  
          - Name of Challenge  
          - Description (2–3 sentences) with a representative quote.  
     5. Core User Needs & Pain Points  
        - List of needs and pain points with supporting details or quotes
     6. How Might We… Problem Statement  
        - Craft one compelling HMW sentence that unifies all the challenges and needs.  
     7. Constraints & Success Criteria  
        - List constraints and corresponding success criteria
     8. Next Steps & Recommendations (1 page)  
        - Three to five actionable recommendations to guide prototype/MVP design, each with rationale.  
     9. Key Insights & Quotes  
        - A list of the 5–7 most critical quotes or insights—no timestamps, just speaker name and content.

4. Output Format
   - Provide the response in structured JSON format with clear sections
   - No timestamps or file metadata in the response
   - Focus on actionable insights and clear problem articulation

Please analyze the transcript and provide a comprehensive problem statement analysis.`;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // Check if API key is configured
        if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
            console.error('ANTHROPIC_API_KEY is not properly configured - creating demo document');
            // For development, create a mock document
            const mockDoc = await createMockDocument(file.name);
            const buffer = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Packer"].toBuffer(mockDoc);
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](buffer, {
                headers: {
                    'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'Content-Disposition': 'attachment; filename="Problem_Statement_Demo.docx"'
                }
            });
        }
        // Read file content
        let transcriptContent = '';
        console.log(`Processing file: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);
        try {
            if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                console.log('Reading as plain text file');
                transcriptContent = await file.text();
            } else if (file.name.endsWith('.docx')) {
                console.log('Reading as DOCX file');
                const arrayBuffer = await file.arrayBuffer();
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mammoth$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].extractRawText({
                    buffer: Buffer.from(arrayBuffer)
                });
                transcriptContent = result.value;
            } else {
                console.log('Reading as generic text file');
                // For other formats, try to read as text
                transcriptContent = await file.text();
            }
        } catch (fileError) {
            console.error('Error reading file content:', fileError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to read file content'
            }, {
                status: 400
            });
        }
        console.log(`Extracted content length: ${transcriptContent.length} characters`);
        console.log(`Content preview: ${transcriptContent.substring(0, 200)}...`);
        if (!transcriptContent.trim()) {
            console.error('File content is empty after extraction');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File appears to be empty or unreadable'
            }, {
                status: 400
            });
        }
        // Call Claude API
        console.log('Preparing to call Claude API...');
        const fullPrompt = `${PROBLEM_STATEMENT_PROMPT}\n\nTranscript Content:\n${transcriptContent}`;
        console.log(`Full prompt length: ${fullPrompt.length} characters`);
        let response;
        try {
            console.log('Calling Claude API with Sonnet 4...');
            response = await anthropic.messages.create({
                model: 'claude-sonnet-4-20250514',
                max_tokens: 10000,
                messages: [
                    {
                        role: 'user',
                        content: fullPrompt
                    }
                ]
            });
            console.log('Claude API call successful');
        } catch (apiError) {
            console.error('Claude API Error:', apiError);
            // Handle specific API errors
            if (apiError.status === 401) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid API key. Please check your Anthropic API key configuration.'
                }, {
                    status: 401
                });
            } else if (apiError.status === 429) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Rate limit exceeded. Please try again in a few minutes.'
                }, {
                    status: 429
                });
            } else if (apiError.message?.includes('model')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Model not available. Your API key may not have access to Claude Sonnet 4.'
                }, {
                    status: 400
                });
            } else if (apiError.code === 'UNABLE_TO_GET_ISSUER_CERT_LOCALLY') {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'SSL certificate issue. This is a development environment issue.'
                }, {
                    status: 500
                });
            } else {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: `Claude API error: ${apiError.message || 'Unknown error'}`
                }, {
                    status: 500
                });
            }
        }
        const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';
        // Parse the analysis and create Word document
        const doc = await createWordDocument(analysisText, file.name);
        // Generate the document buffer
        const buffer = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Packer"].toBuffer(doc);
        // Return JSON with both document URL and content for next steps
        const base64Document = buffer.toString('base64');
        const documentUrl = `data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,${base64Document}`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            documentUrl: documentUrl,
            content: analysisText,
            message: 'Problem statement document generated successfully with Claude Sonnet 4!'
        });
    } catch (error) {
        console.error('Error generating problem statement:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate problem statement'
        }, {
            status: 500
        });
    }
}
async function createWordDocument(analysisText, originalFileName) {
    // Parse the analysis text to extract structured information
    const sections = parseAnalysisText(analysisText);
    const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"]({
        sections: [
            {
                properties: {},
                children: [
                    // Title Page
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.title || "Problem Statement Analysis",
                                bold: true,
                                size: 32,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].TITLE,
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 400
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: `Analysis Date: ${new Date().toLocaleDateString()}`,
                                size: 24
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: `Source: ${originalFileName}`,
                                size: 20,
                                italics: true
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 800
                        }
                    }),
                    // Executive Summary
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Executive Summary",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.executiveSummary || "Executive summary will be generated based on transcript analysis.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Background & Context
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Background & Context",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.background || "Background and context will be extracted from the transcript analysis.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Key Business Challenges
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Key Business Challenges",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.challenges || "Business challenges will be identified and detailed from the transcript.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Problem Statement
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "How Might We… Problem Statement",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.problemStatement || "How might we address the core challenges identified in this analysis?",
                                size: 22,
                                bold: true,
                                color: "DC2626"
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Next Steps
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Next Steps & Recommendations",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: sections.nextSteps || "Actionable recommendations will be provided based on the analysis.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Raw Analysis
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Detailed Analysis",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: analysisText,
                                size: 20
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    })
                ]
            }
        ]
    });
    return doc;
}
function parseAnalysisText(text) {
    // Simple parsing logic - in a real implementation, you'd want more sophisticated parsing
    const sections = {};
    // Try to extract key sections from the Claude response
    const titleMatch = text.match(/title[:\s]*([^\n]+)/i);
    sections.title = titleMatch ? titleMatch[1].trim() : null;
    const summaryMatch = text.match(/executive summary[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.executiveSummary = summaryMatch ? summaryMatch[1].trim() : null;
    const backgroundMatch = text.match(/background[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.background = backgroundMatch ? backgroundMatch[1].trim() : null;
    const challengesMatch = text.match(/challenges[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.challenges = challengesMatch ? challengesMatch[1].trim() : null;
    const problemMatch = text.match(/how might we[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.problemStatement = problemMatch ? problemMatch[1].trim() : null;
    const stepsMatch = text.match(/next steps[:\s]*([^]*?)(?=\n\n|\n[A-Z]|$)/i);
    sections.nextSteps = stepsMatch ? stepsMatch[1].trim() : null;
    return sections;
}
async function createMockDocument(originalFileName) {
    const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Document"]({
        sections: [
            {
                properties: {},
                children: [
                    // Title Page
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Problem Statement Analysis - Demo",
                                bold: true,
                                size: 32,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].TITLE,
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 400
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: `Analysis Date: ${new Date().toLocaleDateString()}`,
                                size: 24
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: `Source: ${originalFileName}`,
                                size: 20,
                                italics: true
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 800
                        }
                    }),
                    // Notice
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "⚠️ Demo Mode - Claude API Not Configured",
                                bold: true,
                                size: 24,
                                color: "DC2626"
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 400
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "This is a demonstration document. To generate real AI-powered problem statements, please configure your Anthropic API key in the .env.local file.",
                                size: 22,
                                color: "DC2626"
                            })
                        ],
                        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlignmentType"].CENTER,
                        spacing: {
                            after: 600
                        }
                    }),
                    // Sample Content
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Executive Summary",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "This is a sample problem statement document. When Claude AI is properly configured, this section will contain a comprehensive analysis of your conversation transcript, including identified challenges, stakeholder needs, and strategic recommendations.",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    }),
                    // Setup Instructions
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "Setup Instructions",
                                bold: true,
                                size: 28,
                                color: "2563EB"
                            })
                        ],
                        heading: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeadingLevel"].HEADING_1,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Paragraph"]({
                        children: [
                            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$docx$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TextRun"]({
                                text: "1. Get your Anthropic API key from https://console.anthropic.com/\n2. Create a .env.local file in your project root\n3. Add: ANTHROPIC_API_KEY=your-api-key-here\n4. Restart your development server\n5. Upload a transcript to generate real AI analysis",
                                size: 22
                            })
                        ],
                        spacing: {
                            after: 400
                        }
                    })
                ]
            }
        ]
    });
    return doc;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ad865d81._.js.map