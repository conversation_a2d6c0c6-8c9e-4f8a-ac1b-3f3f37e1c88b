import React from 'react';
import { cn } from '@/lib/utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'neutral' | 'success' | 'warning' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  className,
  disabled,
  children,
  ...props
}) => {
  // Map variants to DXC classes
  const getVariantClass = () => {
    switch (variant) {
      case 'primary':
        return 'dxc-button-primary';
      case 'secondary':
        return 'dxc-button-secondary';
      case 'neutral':
        return 'dxc-button-neutral';
      case 'success':
        return 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-300';
      case 'warning':
        return 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-300';
      case 'destructive':
        return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-300';
      default:
        return 'dxc-button-primary';
    }
  };

  // Map sizes to DXC spacing
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'h-8 px-4 py-2 text-sm';
      case 'lg':
        return 'h-12 px-8 py-4 text-lg';
      default:
        return 'h-10 px-6 py-3';
    }
  };

  return (
    <button
      className={cn(
        'dxc-button',
        getVariantClass(),
        getSizeClass(),
        loading && 'opacity-75 cursor-not-allowed',
        className
      )}
      disabled={disabled || loading}
      aria-label={props['aria-label'] || (typeof children === 'string' ? children : undefined)}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-3 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      )}
      {children}
    </button>
  );
};
