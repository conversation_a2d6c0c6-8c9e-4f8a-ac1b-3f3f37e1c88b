import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON> } from './Button';
import { Copy, Download, Code, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ArchitectureDiagramProps {
  mermaidCode?: string;
  onCopyCode?: (code: string, type: 'mermaid') => void;
  className?: string;
}

export const ArchitectureDiagram: React.FC<ArchitectureDiagramProps> = ({
  mermaidCode,
  onCopyCode,
  className
}) => {
  const mermaidRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mermaidInstance, setMermaidInstance] = useState<any>(null);

  useEffect(() => {
    // Load Mermaid library
    const loadMermaid = async () => {
      try {
        const mermaid = await import('mermaid');
        mermaid.default.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          themeVariables: {
            primaryColor: '#3B82F6',
            primaryTextColor: '#1F2937',
            primaryBorderColor: '#2563EB',
            lineColor: '#6B7280',
            secondaryColor: '#F3F4F6',
            tertiaryColor: '#F9FAFB'
          }
        });
        setMermaidInstance(mermaid.default);
      } catch (error) {
        console.error('Failed to load Mermaid:', error);
        setError('Failed to load Mermaid library');
      }
    };

    loadMermaid();
  }, []);

  useEffect(() => {
    if (mermaidCode && mermaidInstance && mermaidRef.current) {
      renderMermaidDiagram();
    }
  }, [mermaidCode, mermaidInstance]);

  const cleanMermaidCode = (code: string): string => {
    // Remove any markdown code block markers that might have slipped through
    let cleaned = code
      .replace(/^```mermaid\s*/i, '')
      .replace(/\s*```$/i, '')
      .replace(/^```\s*/i, '')
      .trim();

    // Remove any extra whitespace and normalize line endings
    cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // Log the cleaning process
    if (code !== cleaned) {
      console.log('Cleaned Mermaid code:', { original: code.substring(0, 50), cleaned: cleaned.substring(0, 50) });
    }

    return cleaned;
  };

  const renderMermaidDiagram = async () => {
    if (!mermaidCode || !mermaidInstance || !mermaidRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Clear previous content
      mermaidRef.current.innerHTML = '';

      // Create a unique ID for this diagram
      const diagramId = `mermaid-diagram-${Date.now()}`;

      // Clean the mermaid code thoroughly
      const cleanCode = cleanMermaidCode(mermaidCode);

      console.log('Rendering Mermaid diagram:', cleanCode);

      // Render the diagram
      const { svg } = await mermaidInstance.render(diagramId, cleanCode);

      if (mermaidRef.current) {
        mermaidRef.current.innerHTML = svg;
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Mermaid rendering error:', error);
      setError(`Diagram rendering failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsLoading(false);

      if (mermaidRef.current) {
        mermaidRef.current.innerHTML = `
          <div class="text-red-600 p-4 border border-red-200 rounded-lg bg-red-50">
            <p class="font-semibold">Diagram Rendering Error</p>
            <p class="text-sm mt-1">${error instanceof Error ? error.message : 'Please check the diagram code syntax.'}</p>
            <details class="mt-2">
              <summary class="cursor-pointer text-xs">Show raw code</summary>
              <pre class="text-xs mt-1 p-2 bg-gray-100 rounded overflow-auto">${mermaidCode}</pre>
            </details>
          </div>
        `;
      }
    }
  };

  const handleCopyCode = (code: string, type: 'mermaid') => {
    navigator.clipboard.writeText(code);
    if (onCopyCode) {
      onCopyCode(code, type);
    }
  };

  const downloadMermaidCode = () => {
    if (!mermaidCode) return;

    const blob = new Blob([mermaidCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const element = document.createElement('a');
    element.href = url;
    element.download = 'architecture-diagram.mmd';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    URL.revokeObjectURL(url);
  };

  const handleDownloadDiagram = () => {
    try {
      const svgElement = mermaidRef.current?.querySelector('svg');
      if (!svgElement) {
        alert('No diagram available to download. Please ensure the diagram has rendered successfully.');
        return;
      }

      // Clone the SVG to avoid modifying the original
      const clonedSvg = svgElement.cloneNode(true) as SVGElement;

      // Ensure the SVG has proper dimensions
      if (!clonedSvg.getAttribute('width')) {
        clonedSvg.setAttribute('width', '800');
      }
      if (!clonedSvg.getAttribute('height')) {
        clonedSvg.setAttribute('height', '600');
      }

      // Add XML declaration and DOCTYPE
      const svgData = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
${new XMLSerializer().serializeToString(clonedSvg)}`;

      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `architecture-diagram-${new Date().toISOString().split('T')[0]}.svg`;
      downloadLink.style.display = 'none';

      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      // Clean up the URL object
      setTimeout(() => URL.revokeObjectURL(svgUrl), 100);

      console.log('SVG download initiated successfully');
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download diagram. Please try again.');
    }
  };

  const handleRetryRender = () => {
    if (mermaidCode) {
      renderMermaidDiagram();
    }
  };



  return (
    <div className={cn('space-y-6', className)}>
      {/* Diagram Display */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h4 className="font-semibold text-lg text-gray-900">System Architecture Diagram</h4>
          <div className="flex gap-2">
            {error && (
              <Button
                variant="secondary"
                size="sm"
                onClick={handleRetryRender}
                disabled={isLoading}
              >
                <RefreshCw className={cn("w-4 h-4 mr-1", isLoading && "animate-spin")} />
                Retry
              </Button>
            )}
            <Button
              variant="secondary"
              size="sm"
              onClick={downloadMermaidCode}
              disabled={!mermaidCode}
            >
              <Download className="w-4 h-4 mr-1" />
              Download Code
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleDownloadDiagram}
              disabled={!mermaidCode || error !== null || isLoading}
            >
              <Download className="w-4 h-4 mr-1" />
              Download SVG
            </Button>
          </div>
        </div>

        {/* Status Messages */}
        {isLoading && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800 text-sm">
              <RefreshCw className="inline w-4 h-4 mr-1 animate-spin" />
              Rendering diagram...
            </p>
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 text-sm font-medium">Mermaid Rendering Error</p>
            <p className="text-red-700 text-sm mt-1">{error}</p>
            <div className="mt-3">
              <p className="text-red-700 text-sm mb-2">Please try regenerating the diagram or check the Mermaid code syntax.</p>
            </div>
          </div>
        )}

        {/* Mermaid Diagram */}
        <div className="border border-gray-100 rounded-lg p-4 bg-gray-50 min-h-[400px] flex items-center justify-center">
          <div ref={mermaidRef} className="w-full max-w-full overflow-auto">
            {!mermaidCode && !isLoading && (
              <div className="text-center text-gray-500">
                <Code className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <p>Architecture diagram will appear here</p>
                <p className="text-sm mt-1">Generate from technical requirements to see the diagram</p>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* Code Blocks */}
      <div className="grid md:grid-cols-2 gap-4">
        {/* Mermaid Code */}
        {mermaidCode && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <h5 className="font-semibold text-gray-900">Mermaid Code</h5>
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => handleCopyCode(cleanMermaidCode(mermaidCode), 'mermaid')}
                >
                  <Copy className="w-4 h-4 mr-1" />
                  Copy Clean
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => handleCopyCode(mermaidCode, 'mermaid')}
                >
                  <Copy className="w-4 h-4 mr-1" />
                  Copy Raw
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <div>
                <p className="text-xs text-gray-600 mb-1">Cleaned Code (for rendering):</p>
                <pre className="text-xs bg-white border rounded p-3 overflow-x-auto max-h-32">
                  <code>{cleanMermaidCode(mermaidCode)}</code>
                </pre>
              </div>
              <details>
                <summary className="text-xs text-gray-600 cursor-pointer">Show raw code</summary>
                <pre className="text-xs bg-gray-100 border rounded p-3 overflow-x-auto max-h-32 mt-1">
                  <code>{mermaidCode}</code>
                </pre>
              </details>
            </div>
          </div>
        )}


      </div>


    </div>
  );
};
