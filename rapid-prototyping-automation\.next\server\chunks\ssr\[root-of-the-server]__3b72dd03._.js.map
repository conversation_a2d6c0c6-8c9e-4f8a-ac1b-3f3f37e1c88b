{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  gradient?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n}\n\nexport const Card: React.FC<CardProps> = ({\n  children,\n  className,\n  gradient = false,\n  padding = 'md'\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  return (\n    <div\n      className={cn(\n        'card',\n        gradient && 'card-gradient',\n        paddingClasses[padding],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardTitle: React.FC<CardTitleProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <h3 className={cn('text-xl font-semibold gradient-text', className)}>\n      {children}\n    </h3>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContent: React.FC<CardContentProps> = ({\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('text-gray-600', className)}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AACA;;;AASO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,IAAI,EACf;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,YAAY,iBACZ,cAAc,CAAC,QAAQ,EACvB;kBAGD;;;;;;AAGP;AAOO,MAAM,aAAwC,CAAC,EACpD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;AAOO,MAAM,YAAsC,CAAC,EAClD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBACtD;;;;;;AAGP;AAOO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBACjC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'success';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  disabled,\n  children,\n  ...props\n}) => {\n  return (\n    <button\n      className={cn(\n        'btn',\n        `btn-${variant}`,\n        `btn-${size}`,\n        loading && 'opacity-75 cursor-not-allowed',\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-3 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AASO,MAAM,SAAgC,CAAC,EAC5C,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,CAAC,IAAI,EAAE,SAAS,EAChB,CAAC,IAAI,EAAE,MAAM,EACb,WAAW,iCACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ProgressProps {\n  value: number;\n  max?: number;\n  className?: string;\n  showLabel?: boolean;\n  label?: string;\n}\n\nexport const Progress: React.FC<ProgressProps> = ({\n  value,\n  max = 100,\n  className,\n  showLabel = false,\n  label\n}) => {\n  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);\n\n  return (\n    <div className={cn('w-full', className)}>\n      {showLabel && (\n        <div className=\"flex justify-between items-center mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">\n            {label || 'Progress'}\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            {Math.round(percentage)}%\n          </span>\n        </div>\n      )}\n      <div className=\"progress-bar\">\n        <div\n          className=\"progress-fill\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,MAAM,GAAG,EACT,SAAS,EACT,YAAY,KAAK,EACjB,KAAK,EACN;IACC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,SAAS;;;;;;kCAEZ,8OAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAI9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/FileUpload.tsx"], "sourcesContent": ["import React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, File, X } from 'lucide-react';\nimport { cn, formatFileSize } from '@/lib/utils';\nimport { Button } from './Button';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  accept?: Record<string, string[]>;\n  maxSize?: number;\n  className?: string;\n}\n\nexport const FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  accept = {\n    'text/*': ['.txt', '.md', '.doc', '.docx'],\n    'application/pdf': ['.pdf']\n  },\n  maxSize = 10 * 1024 * 1024, // 10MB\n  className\n}) => {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [error, setError] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError('');\n    \n    if (rejectedFiles.length > 0) {\n      const rejection = rejectedFiles[0];\n      if (rejection.errors[0]?.code === 'file-too-large') {\n        setError(`File is too large. Maximum size is ${formatFileSize(maxSize)}`);\n      } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n        setError('Invalid file type. Please upload a text document or PDF.');\n      } else {\n        setError('File upload failed. Please try again.');\n      }\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      setSelectedFile(file);\n      onFileSelect(file);\n    }\n  }, [onFileSelect, maxSize]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept,\n    maxSize,\n    multiple: false\n  });\n\n  const removeFile = () => {\n    setSelectedFile(null);\n    setError('');\n  };\n\n  return (\n    <div className={cn('w-full', className)}>\n      {!selectedFile ? (\n        <div\n          {...getRootProps()}\n          className={cn(\n            'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200',\n            isDragActive\n              ? 'border-blue-400 bg-blue-50'\n              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n          )}\n        >\n          <input {...getInputProps()} />\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <p className=\"text-lg font-medium text-gray-700 mb-2\">\n            {isDragActive ? 'Drop the file here' : 'Upload transcript file'}\n          </p>\n          <p className=\"text-sm text-gray-500 mb-4\">\n            Drag and drop your file here, or click to browse\n          </p>\n          <p className=\"text-xs text-gray-400\">\n            Supports: TXT, MD, DOC, DOCX, PDF (max {formatFileSize(maxSize)})\n          </p>\n        </div>\n      ) : (\n        <div className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <File className=\"h-8 w-8 text-blue-500\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">{selectedFile.name}</p>\n                <p className=\"text-sm text-gray-500\">\n                  {formatFileSize(selectedFile.size)}\n                </p>\n              </div>\n            </div>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={removeFile}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      )}\n      \n      {error && (\n        <div className=\"mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2\">\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;AASO,MAAM,aAAwC,CAAC,EACpD,YAAY,EACZ,SAAS;IACP,UAAU;QAAC;QAAQ;QAAO;QAAQ;KAAQ;IAC1C,mBAAmB;QAAC;KAAO;AAC7B,CAAC,EACD,UAAU,KAAK,OAAO,IAAI,EAC1B,SAAS,EACV;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,eAAuB;QACjD,SAAS;QAET,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,YAAY,aAAa,CAAC,EAAE;YAClC,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,kBAAkB;gBAClD,SAAS,CAAC,mCAAmC,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YAC1E,OAAO,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,qBAAqB;gBAC5D,SAAS;YACX,OAAO;gBACL,SAAS;YACX;YACA;QACF;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,gBAAgB;YAChB,aAAa;QACf;IACF,GAAG;QAAC;QAAc;KAAQ;IAE1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA;QACA;QACA,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,CAAC,6BACA,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,eACI,+BACA;;kCAGN,8OAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAE,WAAU;kCACV,eAAe,uBAAuB;;;;;;kCAEzC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAE,WAAU;;4BAAwB;4BACK,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;4BAAS;;;;;;;;;;;;qCAIpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAA6B,aAAa,IAAI;;;;;;sDAC3D,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;sCAIvC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ChatInterface.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Button } from './Button';\nimport { Send, Bot, User } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: 'user' | 'bot';\n  timestamp: Date;\n}\n\ninterface ChatInterfaceProps {\n  onFeedback: (feedback: string) => void;\n  context?: string;\n  diagramCode?: string;\n  onDiagramUpdate?: (updatedCode: any) => void;\n  className?: string;\n}\n\nexport const ChatInterface: React.FC<ChatInterfaceProps> = ({\n  onFeedback,\n  context = '',\n  diagramCode = '',\n  onDiagramUpdate,\n  className\n}) => {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: 'I\\'ve generated the architecture diagram based on your technical requirements. What do you think about the current design? You can ask me to modify components, add security layers, improve performance, or make any other architectural changes.',\n      sender: 'bot',\n      timestamp: new Date()\n    }\n  ]);\n  const [inputValue, setInputValue] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: inputValue,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    onFeedback(inputValue);\n\n    const currentInput = inputValue;\n    setInputValue('');\n    setIsLoading(true);\n\n    try {\n      // Call the chat API\n      const response = await fetch('/api/chat-architecture', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: currentInput,\n          context: context,\n          diagramCode: diagramCode\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get chat response');\n      }\n\n      const data = await response.json();\n\n      const botResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        content: data.response || 'I understand your feedback. Let me help you improve the architecture.',\n        sender: 'bot',\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, botResponse]);\n\n      // If there's an updated diagram code, notify the parent component\n      if (data.updatedDiagramCode && onDiagramUpdate) {\n        onDiagramUpdate(data.updatedDiagramCode);\n      }\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      const errorResponse: Message = {\n        id: (Date.now() + 2).toString(),\n        content: 'I apologize, but I\\'m having trouble processing your request right now. Please try again or check if the Claude API is properly configured.',\n        sender: 'bot',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className={cn('bg-white border border-gray-200 rounded-lg', className)}>\n      <div className=\"p-4 border-b border-gray-200\">\n        <h4 className=\"font-semibold text-gray-900\">Architecture Feedback</h4>\n        <p className=\"text-sm text-gray-600\">Share your thoughts on the generated architecture</p>\n      </div>\n      \n      <div className=\"h-64 overflow-y-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={cn(\n              'flex items-start space-x-3',\n              message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n            )}\n          >\n            <div className={cn(\n              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',\n              message.sender === 'user' \n                ? 'bg-blue-500 text-white' \n                : 'bg-gray-200 text-gray-600'\n            )}>\n              {message.sender === 'user' ? (\n                <User className=\"w-4 h-4\" />\n              ) : (\n                <Bot className=\"w-4 h-4\" />\n              )}\n            </div>\n            <div className={cn(\n              'flex-1 max-w-xs lg:max-w-md px-4 py-2 rounded-lg',\n              message.sender === 'user'\n                ? 'bg-blue-500 text-white ml-auto'\n                : 'bg-gray-100 text-gray-900'\n            )}>\n              <p className=\"text-sm\">{message.content}</p>\n              <p className={cn(\n                'text-xs mt-1',\n                message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'\n              )}>\n                {message.timestamp.toLocaleTimeString([], { \n                  hour: '2-digit', \n                  minute: '2-digit' \n                })}\n              </p>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex space-x-2\">\n          <textarea\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Share your feedback on the architecture...\"\n            className=\"flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows={2}\n          />\n          <Button\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim() || isLoading}\n            loading={isLoading}\n            size=\"sm\"\n            className=\"self-end\"\n          >\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAiBO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,EACV,UAAU,EAAE,EACZ,cAAc,EAAE,EAChB,eAAe,EACf,SAAS,EACV;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,WAAW;QAErC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,WAAW;QAEX,MAAM,eAAe;QACrB,cAAc;QACd,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,cAAuB;gBAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS,KAAK,QAAQ,IAAI;gBAC1B,QAAQ;gBACR,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAE1C,kEAAkE;YAClE,IAAI,KAAK,kBAAkB,IAAI,iBAAiB;gBAC9C,gBAAgB,KAAK,kBAAkB;YACzC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,gBAAyB;gBAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;;0BAC/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,QAAQ,MAAM,KAAK,SAAS,qCAAqC;;0CAGnE,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uEACA,QAAQ,MAAM,KAAK,SACf,2BACA;0CAEH,QAAQ,MAAM,KAAK,uBAClB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAGnB,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,oDACA,QAAQ,MAAM,KAAK,SACf,mCACA;;kDAEJ,8OAAC;wCAAE,WAAU;kDAAW,QAAQ,OAAO;;;;;;kDACvC,8OAAC;wCAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,gBACA,QAAQ,MAAM,KAAK,SAAS,kBAAkB;kDAE7C,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4CACxC,MAAM;4CACN,QAAQ;wCACV;;;;;;;;;;;;;uBAhCC,QAAQ,EAAE;;;;;;;;;;0BAuCrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,YAAY;4BACZ,aAAY;4BACZ,WAAU;4BACV,MAAM;;;;;;sCAER,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI,MAAM;4BAChC,SAAS;4BACT,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/Toast.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { CheckCircle, X, AlertCircle, Info } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport type ToastType = 'success' | 'error' | 'info' | 'warning';\n\ninterface Toast {\n  id: string;\n  message: string;\n  type: ToastType;\n  duration?: number;\n}\n\ninterface ToastProps {\n  toast: Toast;\n  onRemove: (id: string) => void;\n}\n\nconst ToastComponent: React.FC<ToastProps> = ({ toast, onRemove }) => {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id);\n    }, toast.duration || 3000);\n\n    return () => clearTimeout(timer);\n  }, [toast.id, toast.duration, onRemove]);\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5\" />;\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'warning':\n        return <AlertCircle className=\"h-5 w-5\" />;\n      case 'info':\n        return <Info className=\"h-5 w-5\" />;\n      default:\n        return <Info className=\"h-5 w-5\" />;\n    }\n  };\n\n  const getStyles = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800';\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800';\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800';\n    }\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex items-center justify-between p-4 rounded-lg border shadow-lg animate-slide-up',\n        getStyles()\n      )}\n    >\n      <div className=\"flex items-center space-x-3\">\n        {getIcon()}\n        <span className=\"text-sm font-medium\">{toast.message}</span>\n      </div>\n      <button\n        onClick={() => onRemove(toast.id)}\n        className=\"ml-4 text-current hover:opacity-70 transition-opacity\"\n      >\n        <X className=\"h-4 w-4\" />\n      </button>\n    </div>\n  );\n};\n\ninterface ToastContainerProps {\n  toasts: Toast[];\n  onRemove: (id: string) => void;\n}\n\nexport const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  );\n};\n\n// Hook for managing toasts\nexport const useToast = () => {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n\n  const addToast = (message: string, type: ToastType = 'info', duration?: number) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const newToast: Toast = { id, message, type, duration };\n    setToasts(prev => [...prev, newToast]);\n  };\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success: (message: string, duration?: number) => addToast(message, 'success', duration),\n    error: (message: string, duration?: number) => addToast(message, 'error', duration),\n    info: (message: string, duration?: number) => addToast(message, 'info', duration),\n    warning: (message: string, duration?: number) => addToast(message, 'warning', duration),\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAgBA,MAAM,iBAAuC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,SAAS,MAAM,EAAE;QACnB,GAAG,MAAM,QAAQ,IAAI;QAErB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,YAAY;QAChB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA;;0BAGF,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBAAK,WAAU;kCAAuB,MAAM,OAAO;;;;;;;;;;;;0BAEtD,8OAAC;gBACC,SAAS,IAAM,SAAS,MAAM,EAAE;gBAChC,WAAU;0BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;AAOO,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChF,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;AAGO,MAAM,WAAW;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC,SAAiB,OAAkB,MAAM,EAAE;QAC3D,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAkB;YAAE;YAAI;YAAS;YAAM;QAAS;QACtD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;QAC9E,OAAO,CAAC,SAAiB,WAAsB,SAAS,SAAS,SAAS;QAC1E,MAAM,CAAC,SAAiB,WAAsB,SAAS,SAAS,QAAQ;QACxE,SAAS,CAAC,SAAiB,WAAsB,SAAS,SAAS,WAAW;IAChF;AACF", "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ThinkingIndicator.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Brain, Lightbulb, Zap, C<PERSON>, Sparkles } from 'lucide-react';\n\ninterface ThinkingIndicatorProps {\n  step: 'transcript' | 'technical' | 'architecture' | 'prompts';\n  className?: string;\n}\n\nconst THINKING_MESSAGES = {\n  transcript: [\n    \"🧠 Reading your conversation transcript...\",\n    \"🔍 Identifying key stakeholders and pain points...\",\n    \"💡 Extracting business challenges...\",\n    \"📝 Crafting executive summary...\",\n    \"🎯 Formulating 'How Might We' statements...\",\n    \"✨ Polishing the problem statement...\",\n    \"🚀 Almost done with the analysis...\"\n  ],\n  technical: [\n    \"🏗️ Analyzing problem statement architecture needs...\",\n    \"⚡ Designing cloud-native solution components...\",\n    \"🔧 Selecting optimal technology stack...\",\n    \"🌐 Mapping data flow and integration patterns...\",\n    \"🔒 Defining security and compliance framework...\",\n    \"📊 Calculating scalability requirements...\",\n    \"🎯 Scoping MVP features and timeline...\",\n    \"📋 Generating implementation roadmap...\"\n  ],\n  architecture: [\n    \"🎨 Analyzing technical requirements for architecture...\",\n    \"🏗️ Sketching system architecture diagrams...\",\n    \"🔗 Connecting microservices and APIs...\",\n    \"☁️ Optimizing cloud infrastructure layout...\",\n    \"🔄 Designing data pipelines and flows...\",\n    \"🛡️ Adding security layers and compliance...\",\n    \"📈 Planning for scale and performance...\",\n    \"🎯 Generating Mermaid and PlantUML code...\",\n    \"✨ Finalizing architecture visualization...\"\n  ],\n  prompts: [\n    \"🎯 Analyzing technical requirements for implementation...\",\n    \"💻 Crafting frontend development prompts for Loveable AI...\",\n    \"🔧 Building backend API specifications and endpoints...\",\n    \"🗄️ Designing database schemas with relationships...\",\n    \"☁️ Creating DevOps deployment configs in JSON format...\",\n    \"📝 Optimizing prompts for AI development tools...\",\n    \"🚀 Generating copy-paste ready implementation guides...\",\n    \"✨ Finalizing Loveable AI integration prompts...\"\n  ]\n};\n\nconst THINKING_ICONS = [Brain, Lightbulb, Zap, Cpu, Sparkles];\n\nexport const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ \n  step, \n  className = '' \n}) => {\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\n  const [currentIconIndex, setCurrentIconIndex] = useState(0);\n  const [dots, setDots] = useState('');\n\n  const messages = THINKING_MESSAGES[step];\n  const CurrentIcon = THINKING_ICONS[currentIconIndex];\n\n  useEffect(() => {\n    // Rotate messages every 2.5 seconds\n    const messageInterval = setInterval(() => {\n      setCurrentMessageIndex((prev) => (prev + 1) % messages.length);\n    }, 2500);\n\n    // Rotate icons every 1.5 seconds\n    const iconInterval = setInterval(() => {\n      setCurrentIconIndex((prev) => (prev + 1) % THINKING_ICONS.length);\n    }, 1500);\n\n    // Animate dots every 500ms\n    const dotsInterval = setInterval(() => {\n      setDots((prev) => {\n        if (prev === '...') return '';\n        return prev + '.';\n      });\n    }, 500);\n\n    return () => {\n      clearInterval(messageInterval);\n      clearInterval(iconInterval);\n      clearInterval(dotsInterval);\n    };\n  }, [messages.length]);\n\n  return (\n    <div className={`text-center py-8 ${className}`}>\n      {/* Animated Icon */}\n      <div className=\"relative mb-6\">\n        <div className=\"animate-pulse-slow mb-4\">\n          <CurrentIcon className=\"h-16 w-16 text-blue-500 mx-auto animate-bounce\" />\n        </div>\n        \n        {/* Thinking bubbles animation */}\n        <div className=\"absolute -top-2 -right-2\">\n          <div className=\"flex space-x-1\">\n            <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-ping\" style={{ animationDelay: '0ms' }}></div>\n            <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-ping\" style={{ animationDelay: '200ms' }}></div>\n            <div className=\"w-2 h-2 bg-pink-400 rounded-full animate-ping\" style={{ animationDelay: '400ms' }}></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main thinking message */}\n      <h3 className=\"text-xl font-semibold mb-3 text-gray-800\">\n        Claude is thinking{dots}\n      </h3>\n\n      {/* Dynamic message */}\n      <div className=\"min-h-[60px] flex items-center justify-center\">\n        <p className=\"text-gray-600 text-lg animate-fade-in max-w-md mx-auto\">\n          {messages[currentMessageIndex]}\n        </p>\n      </div>\n\n      {/* Progress indicator */}\n      <div className=\"mt-6 max-w-md mx-auto\">\n        <div className=\"flex justify-between text-xs text-gray-500 mb-2\">\n          <span>Processing...</span>\n          <span>{Math.round(((currentMessageIndex + 1) / messages.length) * 100)}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div \n            className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000 ease-out\"\n            style={{ width: `${((currentMessageIndex + 1) / messages.length) * 100}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* Fun fact */}\n      <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-lg mx-auto\">\n        <p className=\"text-blue-800 text-sm\">\n          <Sparkles className=\"inline h-4 w-4 mr-1\" />\n          <strong>Did you know?</strong> Claude processes thousands of tokens per second to understand context and generate human-like responses!\n        </p>\n      </div>\n\n      {/* Estimated time */}\n      <p className=\"text-sm text-gray-500 mt-4\">\n        ⏱️ This usually takes 30-60 seconds depending on content complexity\n      </p>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAOA,MAAM,oBAAoB;IACxB,YAAY;QACV;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,iBAAiB;IAAC,oMAAA,CAAA,QAAK;IAAE,4MAAA,CAAA,YAAS;IAAE,gMAAA,CAAA,MAAG;IAAE,gMAAA,CAAA,MAAG;IAAE,0MAAA,CAAA,WAAQ;CAAC;AAEtD,MAAM,oBAAsD,CAAC,EAClE,IAAI,EACJ,YAAY,EAAE,EACf;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,WAAW,iBAAiB,CAAC,KAAK;IACxC,MAAM,cAAc,cAAc,CAAC,iBAAiB;IAEpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,kBAAkB,YAAY;YAClC,uBAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,SAAS,MAAM;QAC/D,GAAG;QAEH,iCAAiC;QACjC,MAAM,eAAe,YAAY;YAC/B,oBAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,eAAe,MAAM;QAClE,GAAG;QAEH,2BAA2B;QAC3B,MAAM,eAAe,YAAY;YAC/B,QAAQ,CAAC;gBACP,IAAI,SAAS,OAAO,OAAO;gBAC3B,OAAO,OAAO;YAChB;QACF,GAAG;QAEH,OAAO;YACL,cAAc;YACd,cAAc;YACd,cAAc;QAChB;IACF,GAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,qBACE,8OAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;;0BAE7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAY,WAAU;;;;;;;;;;;kCAIzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAgD,OAAO;wCAAE,gBAAgB;oCAAM;;;;;;8CAC9F,8OAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;8CAClG,8OAAC;oCAAI,WAAU;oCAAgD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAMtG,8OAAC;gBAAG,WAAU;;oBAA2C;oBACpC;;;;;;;0BAIrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,QAAQ,CAAC,oBAAoB;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;;oCAAM,KAAK,KAAK,CAAC,AAAC,CAAC,sBAAsB,CAAC,IAAI,SAAS,MAAM,GAAI;oCAAK;;;;;;;;;;;;;kCAEzE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,AAAC,CAAC,sBAAsB,CAAC,IAAI,SAAS,MAAM,GAAI,IAAI,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAMhF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;sCAAO;;;;;;wBAAsB;;;;;;;;;;;;0BAKlC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ArchitectureDiagram.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { But<PERSON> } from './Button';\nimport { Copy, Download, Code, RefreshCw } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface ArchitectureDiagramProps {\n  mermaidCode?: string;\n  onCopyCode?: (code: string, type: 'mermaid') => void;\n  className?: string;\n}\n\nexport const ArchitectureDiagram: React.FC<ArchitectureDiagramProps> = ({\n  mermaidCode,\n  onCopyCode,\n  className\n}) => {\n  const mermaidRef = useRef<HTMLDivElement>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [mermaidInstance, setMermaidInstance] = useState<any>(null);\n\n  useEffect(() => {\n    // Load Mermaid library\n    const loadMermaid = async () => {\n      try {\n        const mermaid = await import('mermaid');\n        mermaid.default.initialize({\n          startOnLoad: false,\n          theme: 'default',\n          securityLevel: 'loose',\n          themeVariables: {\n            primaryColor: '#3B82F6',\n            primaryTextColor: '#1F2937',\n            primaryBorderColor: '#2563EB',\n            lineColor: '#6B7280',\n            secondaryColor: '#F3F4F6',\n            tertiaryColor: '#F9FAFB'\n          }\n        });\n        setMermaidInstance(mermaid.default);\n      } catch (error) {\n        console.error('Failed to load Mermaid:', error);\n        setError('Failed to load Mermaid library');\n      }\n    };\n\n    loadMermaid();\n  }, []);\n\n  useEffect(() => {\n    if (mermaidCode && mermaidInstance && mermaidRef.current) {\n      renderMermaidDiagram();\n    }\n  }, [mermaidCode, mermaidInstance]);\n\n  const cleanMermaidCode = (code: string): string => {\n    // Remove any markdown code block markers that might have slipped through\n    let cleaned = code\n      .replace(/^```mermaid\\s*/i, '')\n      .replace(/\\s*```$/i, '')\n      .replace(/^```\\s*/i, '')\n      .trim();\n\n    // Remove any extra whitespace and normalize line endings\n    cleaned = cleaned.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n\n    // Log the cleaning process\n    if (code !== cleaned) {\n      console.log('Cleaned Mermaid code:', { original: code.substring(0, 50), cleaned: cleaned.substring(0, 50) });\n    }\n\n    return cleaned;\n  };\n\n  const renderMermaidDiagram = async () => {\n    if (!mermaidCode || !mermaidInstance || !mermaidRef.current) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Clear previous content\n      mermaidRef.current.innerHTML = '';\n\n      // Create a unique ID for this diagram\n      const diagramId = `mermaid-diagram-${Date.now()}`;\n\n      // Clean the mermaid code thoroughly\n      const cleanCode = cleanMermaidCode(mermaidCode);\n\n      console.log('Rendering Mermaid diagram:', cleanCode);\n\n      // Render the diagram\n      const { svg } = await mermaidInstance.render(diagramId, cleanCode);\n\n      if (mermaidRef.current) {\n        mermaidRef.current.innerHTML = svg;\n      }\n\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Mermaid rendering error:', error);\n      setError(`Diagram rendering failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      setIsLoading(false);\n\n      if (mermaidRef.current) {\n        mermaidRef.current.innerHTML = `\n          <div class=\"text-red-600 p-4 border border-red-200 rounded-lg bg-red-50\">\n            <p class=\"font-semibold\">Diagram Rendering Error</p>\n            <p class=\"text-sm mt-1\">${error instanceof Error ? error.message : 'Please check the diagram code syntax.'}</p>\n            <details class=\"mt-2\">\n              <summary class=\"cursor-pointer text-xs\">Show raw code</summary>\n              <pre class=\"text-xs mt-1 p-2 bg-gray-100 rounded overflow-auto\">${mermaidCode}</pre>\n            </details>\n          </div>\n        `;\n      }\n    }\n  };\n\n  const handleCopyCode = (code: string, type: 'mermaid') => {\n    navigator.clipboard.writeText(code);\n    if (onCopyCode) {\n      onCopyCode(code, type);\n    }\n  };\n\n  const downloadMermaidCode = () => {\n    if (!mermaidCode) return;\n\n    const blob = new Blob([mermaidCode], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const element = document.createElement('a');\n    element.href = url;\n    element.download = 'architecture-diagram.mmd';\n    document.body.appendChild(element);\n    element.click();\n    document.body.removeChild(element);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleDownloadDiagram = () => {\n    try {\n      const svgElement = mermaidRef.current?.querySelector('svg');\n      if (!svgElement) {\n        alert('No diagram available to download. Please ensure the diagram has rendered successfully.');\n        return;\n      }\n\n      // Clone the SVG to avoid modifying the original\n      const clonedSvg = svgElement.cloneNode(true) as SVGElement;\n\n      // Ensure the SVG has proper dimensions\n      if (!clonedSvg.getAttribute('width')) {\n        clonedSvg.setAttribute('width', '800');\n      }\n      if (!clonedSvg.getAttribute('height')) {\n        clonedSvg.setAttribute('height', '600');\n      }\n\n      // Add XML declaration and DOCTYPE\n      const svgData = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n${new XMLSerializer().serializeToString(clonedSvg)}`;\n\n      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });\n      const svgUrl = URL.createObjectURL(svgBlob);\n\n      const downloadLink = document.createElement('a');\n      downloadLink.href = svgUrl;\n      downloadLink.download = `architecture-diagram-${new Date().toISOString().split('T')[0]}.svg`;\n      downloadLink.style.display = 'none';\n\n      document.body.appendChild(downloadLink);\n      downloadLink.click();\n      document.body.removeChild(downloadLink);\n\n      // Clean up the URL object\n      setTimeout(() => URL.revokeObjectURL(svgUrl), 100);\n\n      console.log('SVG download initiated successfully');\n    } catch (error) {\n      console.error('Download failed:', error);\n      alert('Failed to download diagram. Please try again.');\n    }\n  };\n\n  const handleRetryRender = () => {\n    if (mermaidCode) {\n      renderMermaidDiagram();\n    }\n  };\n\n\n\n  return (\n    <div className={cn('space-y-6', className)}>\n      {/* Diagram Display */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h4 className=\"font-semibold text-lg text-gray-900\">System Architecture Diagram</h4>\n          <div className=\"flex gap-2\">\n            {error && (\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={handleRetryRender}\n                disabled={isLoading}\n              >\n                <RefreshCw className={cn(\"w-4 h-4 mr-1\", isLoading && \"animate-spin\")} />\n                Retry\n              </Button>\n            )}\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={downloadMermaidCode}\n              disabled={!mermaidCode}\n            >\n              <Download className=\"w-4 h-4 mr-1\" />\n              Download Code\n            </Button>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={handleDownloadDiagram}\n              disabled={!mermaidCode || error !== null || isLoading}\n            >\n              <Download className=\"w-4 h-4 mr-1\" />\n              Download SVG\n            </Button>\n          </div>\n        </div>\n\n        {/* Status Messages */}\n        {isLoading && (\n          <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n            <p className=\"text-blue-800 text-sm\">\n              <RefreshCw className=\"inline w-4 h-4 mr-1 animate-spin\" />\n              Rendering diagram...\n            </p>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-800 text-sm font-medium\">Mermaid Rendering Error</p>\n            <p className=\"text-red-700 text-sm mt-1\">{error}</p>\n            <div className=\"mt-3\">\n              <p className=\"text-red-700 text-sm mb-2\">Please try regenerating the diagram or check the Mermaid code syntax.</p>\n            </div>\n          </div>\n        )}\n\n        {/* Mermaid Diagram */}\n        <div className=\"border border-gray-100 rounded-lg p-4 bg-gray-50 min-h-[400px] flex items-center justify-center\">\n          <div ref={mermaidRef} className=\"w-full max-w-full overflow-auto\">\n            {!mermaidCode && !isLoading && (\n              <div className=\"text-center text-gray-500\">\n                <Code className=\"w-16 h-16 mx-auto mb-4 text-gray-300\" />\n                <p>Architecture diagram will appear here</p>\n                <p className=\"text-sm mt-1\">Generate from technical requirements to see the diagram</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* Code Blocks */}\n      <div className=\"grid md:grid-cols-2 gap-4\">\n        {/* Mermaid Code */}\n        {mermaidCode && (\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <h5 className=\"font-semibold text-gray-900\">Mermaid Code</h5>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={() => handleCopyCode(cleanMermaidCode(mermaidCode), 'mermaid')}\n                >\n                  <Copy className=\"w-4 h-4 mr-1\" />\n                  Copy Clean\n                </Button>\n                <Button\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  onClick={() => handleCopyCode(mermaidCode, 'mermaid')}\n                >\n                  <Copy className=\"w-4 h-4 mr-1\" />\n                  Copy Raw\n                </Button>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <div>\n                <p className=\"text-xs text-gray-600 mb-1\">Cleaned Code (for rendering):</p>\n                <pre className=\"text-xs bg-white border rounded p-3 overflow-x-auto max-h-32\">\n                  <code>{cleanMermaidCode(mermaidCode)}</code>\n                </pre>\n              </div>\n              <details>\n                <summary className=\"text-xs text-gray-600 cursor-pointer\">Show raw code</summary>\n                <pre className=\"text-xs bg-gray-100 border rounded p-3 overflow-x-auto max-h-32 mt-1\">\n                  <code>{mermaidCode}</code>\n                </pre>\n              </details>\n            </div>\n          </div>\n        )}\n\n\n      </div>\n\n\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAQO,MAAM,sBAA0D,CAAC,EACtE,WAAW,EACX,UAAU,EACV,SAAS,EACV;IACC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uBAAuB;QACvB,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,UAAU;gBAChB,QAAQ,OAAO,CAAC,UAAU,CAAC;oBACzB,aAAa;oBACb,OAAO;oBACP,eAAe;oBACf,gBAAgB;wBACd,cAAc;wBACd,kBAAkB;wBAClB,oBAAoB;wBACpB,WAAW;wBACX,gBAAgB;wBAChB,eAAe;oBACjB;gBACF;gBACA,mBAAmB,QAAQ,OAAO;YACpC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS;YACX;QACF;QAEA;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,mBAAmB,WAAW,OAAO,EAAE;YACxD;QACF;IACF,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,mBAAmB,CAAC;QACxB,yEAAyE;QACzE,IAAI,UAAU,KACX,OAAO,CAAC,mBAAmB,IAC3B,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,YAAY,IACpB,IAAI;QAEP,yDAAyD;QACzD,UAAU,QAAQ,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,OAAO;QAExD,2BAA2B;QAC3B,IAAI,SAAS,SAAS;YACpB,QAAQ,GAAG,CAAC,yBAAyB;gBAAE,UAAU,KAAK,SAAS,CAAC,GAAG;gBAAK,SAAS,QAAQ,SAAS,CAAC,GAAG;YAAI;QAC5G;QAEA,OAAO;IACT;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,WAAW,OAAO,EAAE;QAE7D,aAAa;QACb,SAAS;QAET,IAAI;YACF,yBAAyB;YACzB,WAAW,OAAO,CAAC,SAAS,GAAG;YAE/B,sCAAsC;YACtC,MAAM,YAAY,CAAC,gBAAgB,EAAE,KAAK,GAAG,IAAI;YAEjD,oCAAoC;YACpC,MAAM,YAAY,iBAAiB;YAEnC,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,qBAAqB;YACrB,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,gBAAgB,MAAM,CAAC,WAAW;YAExD,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC,SAAS,GAAG;YACjC;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,CAAC,0BAA0B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAChG,aAAa;YAEb,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO,CAAC,SAAS,GAAG,CAAC;;;oCAGJ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,wCAAwC;;;8EAGzC,EAAE,YAAY;;;QAGpF,CAAC;YACH;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC,MAAc;QACpC,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,IAAI,YAAY;YACd,WAAW,MAAM;QACnB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,aAAa;QAElB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAa;QAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,IAAI,GAAG;QACf,QAAQ,QAAQ,GAAG;QACnB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,QAAQ,KAAK;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,aAAa,WAAW,OAAO,EAAE,cAAc;YACrD,IAAI,CAAC,YAAY;gBACf,MAAM;gBACN;YACF;YAEA,gDAAgD;YAChD,MAAM,YAAY,WAAW,SAAS,CAAC;YAEvC,uCAAuC;YACvC,IAAI,CAAC,UAAU,YAAY,CAAC,UAAU;gBACpC,UAAU,YAAY,CAAC,SAAS;YAClC;YACA,IAAI,CAAC,UAAU,YAAY,CAAC,WAAW;gBACrC,UAAU,YAAY,CAAC,UAAU;YACnC;YAEA,kCAAkC;YAClC,MAAM,UAAU,CAAC;;AAEvB,EAAE,IAAI,gBAAgB,iBAAiB,CAAC,YAAY;YAE9C,MAAM,UAAU,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAAE,MAAM;YAA8B;YAC1E,MAAM,SAAS,IAAI,eAAe,CAAC;YAEnC,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,IAAI,GAAG;YACpB,aAAa,QAAQ,GAAG,CAAC,qBAAqB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC5F,aAAa,KAAK,CAAC,OAAO,GAAG;YAE7B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,aAAa,KAAK;YAClB,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,0BAA0B;YAC1B,WAAW,IAAM,IAAI,eAAe,CAAC,SAAS;YAE9C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,aAAa;YACf;QACF;IACF;IAIA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;;oCACZ,uBACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,aAAa;;;;;;4CAAmB;;;;;;;kDAI7E,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC;;0DAEX,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,eAAe,UAAU,QAAQ;;0DAE5C,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAO1C,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;8CACX,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAqC;;;;;;;;;;;;oBAM/D,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAM/C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,KAAK;4BAAY,WAAU;sCAC7B,CAAC,eAAe,CAAC,2BAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtC,8OAAC;gBAAI,WAAU;0BAEZ,6BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,eAAe,iBAAiB,cAAc;;8DAE7D,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,eAAe,aAAa;;8DAE3C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAM,iBAAiB;;;;;;;;;;;;;;;;;8CAG5B,8OAAC;;sDACC,8OAAC;4CAAQ,WAAU;sDAAuC;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazB", "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/ImplementationPrompts.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { But<PERSON> } from './Button';\nimport { Copy, Download, Code, Database, Server, Cloud, Monitor } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface ImplementationPromptsProps {\n  prompts?: {\n    frontend: string;\n    backend: string;\n    database: string;\n    devops: string | any;\n  };\n  onCopyPrompt?: (prompt: string, type: string) => void;\n  className?: string;\n}\n\nexport const ImplementationPrompts: React.FC<ImplementationPromptsProps> = ({\n  prompts,\n  onCopyPrompt,\n  className\n}) => {\n  const [activeTab, setActiveTab] = useState<'frontend' | 'backend' | 'database' | 'devops'>('frontend');\n\n  const handleCopyPrompt = (prompt: string, type: string) => {\n    navigator.clipboard.writeText(prompt);\n    if (onCopyPrompt) {\n      onCopyPrompt(prompt, type);\n    }\n  };\n\n  const handleDownloadPrompt = (content: string, filename: string, type: string = 'text/plain') => {\n    const blob = new Blob([content], { type });\n    const url = URL.createObjectURL(blob);\n    const element = document.createElement('a');\n    element.href = url;\n    element.download = filename;\n    document.body.appendChild(element);\n    element.click();\n    document.body.removeChild(element);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleDownloadDevOps = () => {\n    if (!prompts?.devops) return;\n    \n    let content = prompts.devops;\n    let filename = 'devops-config.json';\n    \n    // If it's already an object, stringify it\n    if (typeof content === 'object') {\n      content = JSON.stringify(content, null, 2);\n    } else {\n      // Try to parse and reformat if it's a string\n      try {\n        const parsed = JSON.parse(content);\n        content = JSON.stringify(parsed, null, 2);\n      } catch (e) {\n        // If not valid JSON, keep as is\n        filename = 'devops-config.txt';\n      }\n    }\n    \n    handleDownloadPrompt(content, filename, 'application/json');\n  };\n\n  const tabs = [\n    {\n      id: 'frontend' as const,\n      label: 'Frontend',\n      icon: Monitor,\n      color: 'blue',\n      description: 'React/Vue UI Components'\n    },\n    {\n      id: 'backend' as const,\n      label: 'Backend API',\n      icon: Server,\n      color: 'green',\n      description: 'REST API & Services'\n    },\n    {\n      id: 'database' as const,\n      label: 'Database',\n      icon: Database,\n      color: 'purple',\n      description: 'Schema & Migrations'\n    },\n    {\n      id: 'devops' as const,\n      label: 'DevOps',\n      icon: Cloud,\n      color: 'orange',\n      description: 'Infrastructure as Code'\n    }\n  ];\n\n  const getPromptContent = (type: string) => {\n    if (!prompts) return '';\n    \n    switch (type) {\n      case 'frontend':\n        return prompts.frontend;\n      case 'backend':\n        return prompts.backend;\n      case 'database':\n        return prompts.database;\n      case 'devops':\n        return typeof prompts.devops === 'object' \n          ? JSON.stringify(prompts.devops, null, 2)\n          : prompts.devops;\n      default:\n        return '';\n    }\n  };\n\n  if (!prompts) {\n    return (\n      <div className={cn('text-center py-8', className)}>\n        <Code className=\"w-16 h-16 mx-auto mb-4 text-gray-300\" />\n        <p className=\"text-gray-500\">Implementation prompts will appear here</p>\n        <p className=\"text-sm text-gray-400 mt-1\">Generate from technical requirements to see the prompts</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn('space-y-6', className)}>\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            const isActive = activeTab === tab.id;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={cn(\n                  'group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm',\n                  isActive\n                    ? `border-${tab.color}-500 text-${tab.color}-600`\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                )}\n              >\n                <Icon className={cn(\n                  'mr-2 h-5 w-5',\n                  isActive ? `text-${tab.color}-500` : 'text-gray-400 group-hover:text-gray-500'\n                )} />\n                <div className=\"text-left\">\n                  <div>{tab.label}</div>\n                  <div className=\"text-xs text-gray-400\">{tab.description}</div>\n                </div>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"bg-white border border-gray-200 rounded-lg\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-start mb-4\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                {tabs.find(t => t.id === activeTab)?.label} Implementation Prompt\n              </h3>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Copy this prompt and paste it into Loveable AI to generate the {activeTab} implementation\n              </p>\n            </div>\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={() => handleCopyPrompt(getPromptContent(activeTab), activeTab)}\n              >\n                <Copy className=\"w-4 h-4 mr-1\" />\n                Copy Prompt\n              </Button>\n              \n              {activeTab === 'devops' ? (\n                <Button\n                  variant=\"primary\"\n                  size=\"sm\"\n                  onClick={handleDownloadDevOps}\n                >\n                  <Download className=\"w-4 h-4 mr-1\" />\n                  Download JSON\n                </Button>\n              ) : (\n                <Button\n                  variant=\"primary\"\n                  size=\"sm\"\n                  onClick={() => handleDownloadPrompt(\n                    getPromptContent(activeTab),\n                    `${activeTab}-prompt.txt`\n                  )}\n                >\n                  <Download className=\"w-4 h-4 mr-1\" />\n                  Download\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* Prompt Content */}\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n            <pre className=\"text-sm text-gray-800 whitespace-pre-wrap overflow-auto max-h-96 leading-relaxed\">\n              {getPromptContent(activeTab)}\n            </pre>\n          </div>\n\n          {/* Special DevOps Info */}\n          {activeTab === 'devops' && (\n            <div className=\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n              <div className=\"flex items-start\">\n                <Cloud className=\"w-5 h-5 text-blue-500 mt-0.5 mr-2\" />\n                <div>\n                  <h4 className=\"font-medium text-blue-900\">Infrastructure as Code</h4>\n                  <p className=\"text-blue-700 text-sm mt-1\">\n                    This JSON configuration can be used with Terraform or Ansible for automated deployment.\n                    Download the file and customize it for your specific cloud provider and requirements.\n                  </p>\n                  <div className=\"mt-2 flex gap-4 text-xs text-blue-600\">\n                    <span>• Terraform: terraform apply</span>\n                    <span>• Ansible: ansible-playbook</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Loveable AI Integration Info */}\n          <div className=\"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg\">\n            <div className=\"flex items-start\">\n              <Code className=\"w-5 h-5 text-green-500 mt-0.5 mr-2\" />\n              <div>\n                <h4 className=\"font-medium text-green-900\">Loveable AI Integration</h4>\n                <p className=\"text-green-700 text-sm mt-1\">\n                  Copy the prompt above and paste it directly into Loveable AI. The prompt includes all necessary\n                  technical specifications, best practices, and implementation details for rapid prototyping.\n                </p>\n                <div className=\"mt-2 text-xs text-green-600\">\n                  💡 Tip: Each prompt is optimized for AI-powered development tools\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <div key={tab.id} className=\"bg-white border border-gray-200 rounded-lg p-4 text-center\">\n              <Icon className={cn('w-8 h-8 mx-auto mb-2', `text-${tab.color}-500`)} />\n              <h4 className=\"font-medium text-gray-900 text-sm\">{tab.label}</h4>\n              <p className=\"text-xs text-gray-500 mt-1\">{tab.description}</p>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"mt-3 w-full\"\n                onClick={() => {\n                  setActiveTab(tab.id);\n                  handleCopyPrompt(getPromptContent(tab.id), tab.id);\n                }}\n              >\n                <Copy className=\"w-3 h-3 mr-1\" />\n                Copy\n              </Button>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAaO,MAAM,wBAA8D,CAAC,EAC1E,OAAO,EACP,YAAY,EACZ,SAAS,EACV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IAE3F,MAAM,mBAAmB,CAAC,QAAgB;QACxC,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,IAAI,cAAc;YAChB,aAAa,QAAQ;QACvB;IACF;IAEA,MAAM,uBAAuB,CAAC,SAAiB,UAAkB,OAAe,YAAY;QAC1F,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE;QAAK;QACxC,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,IAAI,GAAG;QACf,QAAQ,QAAQ,GAAG;QACnB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,QAAQ,KAAK;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,SAAS,QAAQ;QAEtB,IAAI,UAAU,QAAQ,MAAM;QAC5B,IAAI,WAAW;QAEf,0CAA0C;QAC1C,IAAI,OAAO,YAAY,UAAU;YAC/B,UAAU,KAAK,SAAS,CAAC,SAAS,MAAM;QAC1C,OAAO;YACL,6CAA6C;YAC7C,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,UAAU,KAAK,SAAS,CAAC,QAAQ,MAAM;YACzC,EAAE,OAAO,GAAG;gBACV,gCAAgC;gBAChC,WAAW;YACb;QACF;QAEA,qBAAqB,SAAS,UAAU;IAC1C;IAEA,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,SAAS,OAAO;QAErB,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,QAAQ;YACzB,KAAK;gBACH,OAAO,QAAQ,OAAO;YACxB,KAAK;gBACH,OAAO,QAAQ,QAAQ;YACzB,KAAK;gBACH,OAAO,OAAO,QAAQ,MAAM,KAAK,WAC7B,KAAK,SAAS,CAAC,QAAQ,MAAM,EAAE,MAAM,KACrC,QAAQ,MAAM;YACpB;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;;8BACrC,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;8BAChB,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;8BAC7B,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAGhD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBACrC,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2EACA,WACI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,GAC/C;;8CAGN,8OAAC;oCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,gBACA,WAAW,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK,IAAI,KAAK;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDAAyB,IAAI,WAAW;;;;;;;;;;;;;2BAfpD,IAAI,EAAE;;;;;oBAmBjB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDACX,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY;gDAAM;;;;;;;sDAE7C,8OAAC;4CAAE,WAAU;;gDAA6B;gDACwB;gDAAU;;;;;;;;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,iBAAiB,iBAAiB,YAAY;;8DAE7D,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAIlC,cAAc,yBACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;;8DAET,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;iEAIvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,qBACb,iBAAiB,YACjB,GAAG,UAAU,WAAW,CAAC;;8DAG3B,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAQ7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB;;;;;;;;;;;wBAKrB,cAAc,0BACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAI1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,8OAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC;oBACT,MAAM,OAAO,IAAI,IAAI;oBACrB,qBACE,8OAAC;wBAAiB,WAAU;;0CAC1B,8OAAC;gCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC;;;;;;0CACnE,8OAAC;gCAAG,WAAU;0CAAqC,IAAI,KAAK;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAA8B,IAAI,WAAW;;;;;;0CAC1D,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;oCACP,aAAa,IAAI,EAAE;oCACnB,iBAAiB,iBAAiB,IAAI,EAAE,GAAG,IAAI,EAAE;gCACnD;;kDAEA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;uBAb3B,IAAI,EAAE;;;;;gBAkBpB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/DocumentViewer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Button } from './Button';\nimport { Download, Copy } from 'lucide-react';\n\ninterface DocumentViewerProps {\n  content?: string;\n  title?: string;\n  className?: string;\n  downloadFileName?: string;\n  onCopy?: () => void;\n}\n\nexport const DocumentViewer: React.FC<DocumentViewerProps> = ({\n  content,\n  title = \"Document\",\n  className = \"\",\n  downloadFileName,\n  onCopy\n}) => {\n  if (!content) {\n    return (\n      <div className={`p-6 bg-gray-50 rounded-lg border border-gray-200 ${className}`}>\n        <p className=\"text-gray-500 text-center\">No document content available</p>\n      </div>\n    );\n  }\n\n  const handleDownload = () => {\n    if (!content || !downloadFileName) return;\n    \n    const blob = new Blob([content], { type: 'text/markdown' });\n    const url = URL.createObjectURL(blob);\n    const element = document.createElement('a');\n    element.href = url;\n    element.download = downloadFileName;\n    document.body.appendChild(element);\n    element.click();\n    document.body.removeChild(element);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleCopy = () => {\n    if (!content) return;\n    \n    navigator.clipboard.writeText(content).then(() => {\n      onCopy?.();\n    }).catch(err => {\n      console.error('Failed to copy content:', err);\n    });\n  };\n\n  // Simple markdown rendering for display\n  const renderMarkdown = (text: string) => {\n    return text\n      .replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold mb-4 text-gray-900\">$1</h1>')\n      .replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-semibold mb-3 text-gray-800\">$1</h2>')\n      .replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-medium mb-2 text-gray-700\">$1</h3>')\n      .replace(/^\\* (.*$)/gim, '<li class=\"ml-4 mb-1\">$1</li>')\n      .replace(/^\\- (.*$)/gim, '<li class=\"ml-4 mb-1\">$1</li>')\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-semibold\">$1</strong>')\n      .replace(/\\*(.*?)\\*/g, '<em class=\"italic\">$1</em>')\n      .replace(/\\n\\n/g, '</p><p class=\"mb-4\">')\n      .replace(/\\n/g, '<br/>');\n  };\n\n  return (\n    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>\n      <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        <div className=\"flex gap-2\">\n          <Button\n            variant=\"secondary\"\n            size=\"sm\"\n            onClick={handleCopy}\n            className=\"flex items-center gap-2\"\n          >\n            <Copy className=\"h-4 w-4\" />\n            Copy\n          </Button>\n          {downloadFileName && (\n            <Button\n              variant=\"primary\"\n              size=\"sm\"\n              onClick={handleDownload}\n              className=\"flex items-center gap-2\"\n            >\n              <Download className=\"h-4 w-4\" />\n              Download\n            </Button>\n          )}\n        </div>\n      </div>\n      <div className=\"p-6 max-h-96 overflow-y-auto\">\n        <div \n          className=\"prose max-w-none text-gray-700 leading-relaxed\"\n          dangerouslySetInnerHTML={{ \n            __html: `<p class=\"mb-4\">${renderMarkdown(content)}</p>` \n          }}\n        />\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAcO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,EACP,QAAQ,UAAU,EAClB,YAAY,EAAE,EACd,gBAAgB,EAChB,MAAM,EACP;IACC,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAW,CAAC,iDAAiD,EAAE,WAAW;sBAC7E,cAAA,8OAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;IAG/C;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,CAAC,kBAAkB;QAEnC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAgB;QACzD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,IAAI,GAAG;QACf,QAAQ,QAAQ,GAAG;QACnB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,QAAQ,KAAK;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;QAEd,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC;YAC1C;QACF,GAAG,KAAK,CAAC,CAAA;YACP,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,wCAAwC;IACxC,MAAM,iBAAiB,CAAC;QACtB,OAAO,KACJ,OAAO,CAAC,eAAe,6DACvB,OAAO,CAAC,gBAAgB,gEACxB,OAAO,CAAC,iBAAiB,8DACzB,OAAO,CAAC,gBAAgB,iCACxB,OAAO,CAAC,gBAAgB,iCACxB,OAAO,CAAC,kBAAkB,6CAC1B,OAAO,CAAC,cAAc,8BACtB,OAAO,CAAC,SAAS,wBACjB,OAAO,CAAC,OAAO;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BACjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAG7B,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;0BAMxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,yBAAyB;wBACvB,QAAQ,CAAC,gBAAgB,EAAE,eAAe,SAAS,IAAI,CAAC;oBAC1D;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 2410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/RapidPrototypingApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { Progress } from '@/components/ui/Progress';\nimport { FileUpload } from '@/components/ui/FileUpload';\nimport { ChatInterface } from '@/components/ui/ChatInterface';\nimport { ToastContainer, useToast } from '@/components/ui/Toast';\nimport { ThinkingIndicator } from '@/components/ui/ThinkingIndicator';\nimport { ArchitectureDiagram } from '@/components/ui/ArchitectureDiagram';\nimport { ImplementationPrompts } from '@/components/ui/ImplementationPrompts';\nimport { DocumentViewer } from '@/components/ui/DocumentViewer';\nimport { CheckCircle, FileText, Settings, Image, Code } from 'lucide-react';\n\ntype Step = 1 | 2 | 3 | 4 | 5;\n\ninterface AppState {\n  currentStep: Step;\n  uploadedFile: File | null;\n  isProcessing: boolean;\n  processingStep: string;\n  error: string | null;\n  isTransitioning: boolean;\n  transitionType: 'transcript' | 'technical' | 'architecture' | 'prompts' | null;\n  documents: {\n    problemStatement: string | null; // Markdown content\n    technicalRequirements: string | null; // Markdown content\n    architectureDiagram: {\n      mermaidCode?: string;\n      plantUMLCode?: string;\n      legend?: string[];\n      fullResponse?: string;\n      debug?: any;\n    } | null;\n    prompts: {\n      frontend: string;\n      backend: string;\n      database: string;\n      devops: string | any;\n    } | null;\n  };\n  validations: {\n    problemStatement: boolean | null;\n    technicalRequirements: boolean | null;\n    architectureDiagram: boolean | null;\n  };\n}\n\nconst STEPS = [\n  { id: 1, title: 'Upload Transcript', icon: FileText, description: 'Upload your conversation transcript' },\n  { id: 2, title: 'Problem Statement', icon: CheckCircle, description: 'Review and validate problem statement' },\n  { id: 3, title: 'Technical Requirements', icon: Settings, description: 'Review technical requirements document' },\n  { id: 4, title: 'Architecture Diagram', icon: Image, description: 'Review system architecture' },\n  { id: 5, title: 'Generated Prompts', icon: Code, description: 'Copy implementation prompts' }\n];\n\n// Helper function to extract text content from uploaded files\nconst extractTextFromFile = async (file: File): Promise<string> => {\n  return new Promise((resolve) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const content = e.target?.result as string;\n      resolve(content || '');\n    };\n    reader.onerror = () => {\n      resolve(''); // Return empty string on error\n    };\n    reader.readAsText(file);\n  });\n};\n\nexport const RapidPrototypingApp: React.FC = () => {\n  const { toasts, removeToast, success, error, info } = useToast();\n  const [state, setState] = useState<AppState>({\n    currentStep: 1,\n    uploadedFile: null,\n    isProcessing: false,\n    processingStep: '',\n    error: null,\n    isTransitioning: false,\n    transitionType: null,\n    documents: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null,\n      prompts: null\n    },\n    validations: {\n      problemStatement: null,\n      technicalRequirements: null,\n      architectureDiagram: null\n    }\n  });\n\n  const handleFileUpload = async (file: File) => {\n    setState(prev => ({\n      ...prev,\n      uploadedFile: file,\n      isProcessing: true,\n      processingStep: 'Uploading transcript...',\n      error: null\n    }));\n\n    info('Processing your transcript with Claude AI...', 5000);\n\n    try {\n      setState(prev => ({ ...prev, processingStep: 'Analyzing transcript with Claude AI...' }));\n\n      // Create FormData to send file to API\n      const formData = new FormData();\n      formData.append('file', file);\n\n      setState(prev => ({ ...prev, processingStep: 'Generating problem statement document...' }));\n\n      // Call the API to generate problem statement\n      const response = await fetch('/api/generate-problem-statement', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate problem statement`;\n        throw new Error(errorMessage);\n      }\n\n      setState(prev => ({ ...prev, processingStep: 'Finalizing document...' }));\n\n      // Response contains markdown content\n      const data = await response.json();\n      const content = data.content || null;\n\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        currentStep: 2,\n        error: null,\n        documents: {\n          ...prev.documents,\n          problemStatement: content\n        }\n      }));\n\n      success('Problem statement document generated successfully with Claude AI!');\n    } catch (err) {\n      console.error('Error generating problem statement:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate problem statement. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isProcessing: false,\n        processingStep: '',\n        error: errorMessage\n      }));\n    }\n  };\n\n  const handleValidation = async (documentType: keyof AppState['validations'], isValid: boolean) => {\n    setState(prev => ({\n      ...prev,\n      validations: {\n        ...prev.validations,\n        [documentType]: isValid\n      }\n    }));\n\n    if (isValid) {\n      const nextStep = state.currentStep + 1;\n\n      if (nextStep === 3 && documentType === 'problemStatement') {\n        // Show thinking indicator for technical requirements generation\n        setState(prev => ({\n          ...prev,\n          isTransitioning: true,\n          transitionType: 'technical',\n          error: null\n        }));\n\n        // Small delay to show the thinking indicator\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Generate technical requirements from problem statement\n        await generateTechnicalRequirements();\n      } else if (nextStep === 4 && documentType === 'technicalRequirements') {\n        // Generate architecture diagram from technical requirements\n        setState(prev => ({\n          ...prev,\n          isTransitioning: true,\n          transitionType: 'architecture',\n          error: null\n        }));\n\n        // Small delay to show the thinking indicator\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Generate architecture diagram\n        await generateArchitectureDiagram();\n      } else if (nextStep === 5 && documentType === 'architectureDiagram') {\n        // Generate implementation prompts from technical requirements\n        setState(prev => ({\n          ...prev,\n          isTransitioning: true,\n          transitionType: 'prompts',\n          error: null\n        }));\n\n        // Small delay to show the thinking indicator\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Generate implementation prompts\n        await generateImplementationPrompts();\n      }\n    }\n  };\n\n  const generateTechnicalRequirements = async () => {\n    try {\n      // Use actual problem statement content\n      const problemStatementContent = state.documents.problemStatement ||\n        'No problem statement content available. Please ensure the problem statement was generated successfully.';\n\n      // Call the technical requirements API\n      const response = await fetch('/api/generate-technical-requirements', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          problemStatementContent: problemStatementContent\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate technical requirements`;\n        throw new Error(errorMessage);\n      }\n\n      // Response contains markdown content\n      const data = await response.json();\n      const content = data.content || null;\n\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        currentStep: 3,\n        error: null,\n        documents: {\n          ...prev.documents,\n          technicalRequirements: content\n        }\n      }));\n\n      success('Technical requirements document generated successfully with Claude AI!');\n    } catch (err) {\n      console.error('Error generating technical requirements:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate technical requirements. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        error: errorMessage\n      }));\n    }\n  };\n\n  const generateArchitectureDiagram = async () => {\n    try {\n      // Use actual technical requirements content\n      const technicalRequirementsContent = state.documents.technicalRequirements ||\n        'No technical requirements content available. Please ensure the technical requirements were generated successfully.';\n\n      // Call the architecture diagram API\n      const response = await fetch('/api/generate-architecture-diagram', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          technicalRequirements: technicalRequirementsContent\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate architecture diagram`;\n        throw new Error(errorMessage);\n      }\n\n      const data = await response.json();\n\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        currentStep: 4,\n        error: null,\n        documents: {\n          ...prev.documents,\n          architectureDiagram: {\n            mermaidCode: data.mermaidCode,\n            fullResponse: data.mermaidCode // Store the Mermaid code as the response\n          }\n        }\n      }));\n\n      success('Architecture diagram generated successfully with Claude Sonnet 4!');\n    } catch (err) {\n      console.error('Error generating architecture diagram:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate architecture diagram. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        error: errorMessage\n      }));\n    }\n  };\n\n  const generateImplementationPrompts = async () => {\n    try {\n      // Use actual technical requirements content\n      const technicalRequirementsContent = state.documents.technicalRequirements ||\n        'No technical requirements content available. Please ensure the technical requirements were generated successfully.';\n\n      // Call the implementation prompts API\n      const response = await fetch('/api/generate-implementation-prompts', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          technicalRequirementsContent: technicalRequirementsContent\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        const errorMessage = errorData.error || `Server error (${response.status}): Failed to generate implementation prompts`;\n        throw new Error(errorMessage);\n      }\n\n      const data = await response.json();\n\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        currentStep: 5,\n        error: null,\n        documents: {\n          ...prev.documents,\n          prompts: data.content || data.prompts // Use markdown content or fallback to prompts object\n        }\n      }));\n\n      success('Implementation prompts generated successfully with Claude Sonnet 4!');\n    } catch (err) {\n      console.error('Error generating implementation prompts:', err);\n      const errorMessage = err instanceof Error ? err.message : 'Failed to generate implementation prompts. Please try again.';\n      error(errorMessage);\n      setState(prev => ({\n        ...prev,\n        isTransitioning: false,\n        transitionType: null,\n        error: errorMessage\n      }));\n    }\n  };\n\n  const currentStepData = STEPS.find(step => step.id === state.currentStep);\n  const progress = ((state.currentStep - 1) / (STEPS.length - 1)) * 100;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <ToastContainer toasts={toasts} onRemove={removeToast} />\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold gradient-text mb-4\">\n            Rapid Prototyping Automation\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Transform your conversation transcripts into comprehensive technical documentation and implementation guides\n          </p>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <Progress \n            value={progress} \n            showLabel \n            label={`Step ${state.currentStep} of ${STEPS.length}: ${currentStepData?.title}`}\n            className=\"max-w-2xl mx-auto\"\n          />\n        </div>\n\n        {/* Steps Navigation - Now Clickable */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"flex space-x-4 overflow-x-auto pb-2\">\n            {STEPS.map((step) => {\n              const Icon = step.icon;\n              const isActive = step.id === state.currentStep;\n              const isCompleted = step.id < state.currentStep;\n              const isAvailable = step.id <= state.currentStep || isCompleted;\n\n              return (\n                <button\n                  key={step.id}\n                  onClick={() => {\n                    if (isAvailable && !state.isTransitioning) {\n                      setState(prev => ({ ...prev, currentStep: step.id as Step }));\n                    }\n                  }}\n                  disabled={!isAvailable || state.isTransitioning}\n                  className={`flex flex-col items-center min-w-[120px] p-3 rounded-lg transition-all cursor-pointer hover:shadow-md disabled:cursor-not-allowed ${\n                    isActive\n                      ? 'bg-blue-100 border-2 border-blue-300 shadow-md'\n                      : isCompleted\n                        ? 'bg-green-100 border-2 border-green-300 hover:bg-green-200'\n                        : isAvailable\n                          ? 'bg-purple-100 border-2 border-purple-300 hover:bg-purple-200'\n                          : 'bg-gray-100 border-2 border-gray-200 opacity-50'\n                  }`}\n                >\n                  <Icon\n                    className={`h-6 w-6 mb-2 ${\n                      isActive\n                        ? 'text-blue-600'\n                        : isCompleted\n                          ? 'text-green-600'\n                          : isAvailable\n                            ? 'text-purple-600'\n                            : 'text-gray-400'\n                    }`}\n                  />\n                  <span className={`text-sm font-medium text-center ${\n                    isActive\n                      ? 'text-blue-800'\n                      : isCompleted\n                        ? 'text-green-800'\n                        : isAvailable\n                          ? 'text-purple-800'\n                          : 'text-gray-600'\n                  }`}>\n                    {step.title}\n                  </span>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Show thinking indicator when transitioning between steps */}\n          {state.isTransitioning && state.transitionType && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardContent>\n                <ThinkingIndicator step={state.transitionType} />\n                {state.error && (\n                  <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg max-w-md mx-auto\">\n                    <p className=\"text-red-700 text-sm\">{state.error}</p>\n                    <Button\n                      variant=\"secondary\"\n                      size=\"sm\"\n                      className=\"mt-2\"\n                      onClick={() => setState(prev => ({\n                        ...prev,\n                        error: null,\n                        isTransitioning: false,\n                        transitionType: null\n                      }))}\n                    >\n                      Try Again\n                    </Button>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 1 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Upload Your Conversation Transcript</CardTitle>\n              </CardHeader>\n              <CardContent>\n                {!state.isProcessing ? (\n                  <FileUpload onFileSelect={handleFileUpload} />\n                ) : (\n                  <div>\n                    <ThinkingIndicator step=\"transcript\" />\n                    {state.error && (\n                      <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg max-w-md mx-auto\">\n                        <p className=\"text-red-700 text-sm\">{state.error}</p>\n                        <Button\n                          variant=\"secondary\"\n                          size=\"sm\"\n                          className=\"mt-2\"\n                          onClick={() => setState(prev => ({ ...prev, error: null, isProcessing: false }))}\n                        >\n                          Try Again\n                        </Button>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 2 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Problem Statement Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  {state.documents.problemStatement ? (\n                    <DocumentViewer\n                      content={state.documents.problemStatement}\n                      title=\"AI-Generated Problem Statement\"\n                      downloadFileName=\"Problem_Statement.md\"\n                      onCopy={() => success('Problem statement copied to clipboard!')}\n                    />\n                  ) : (\n                    <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                      <h4 className=\"font-semibold text-lg mb-4\">AI-Generated Problem Statement</h4>\n                      <div className=\"prose max-w-none\">\n                        <p className=\"text-gray-700 leading-relaxed\">\n                          Claude AI has analyzed your conversation transcript and generated a comprehensive problem statement document.\n                          This professional document includes executive summary, business challenges, user needs, and actionable recommendations.\n                        </p>\n                        <div className=\"mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\">\n                          <h5 className=\"font-medium text-blue-900 mb-2\">Document Includes:</h5>\n                          <ul className=\"list-disc list-inside text-blue-800 space-y-1\">\n                            <li>Executive Summary & Background Context</li>\n                            <li>Key Business Challenges with Supporting Quotes</li>\n                            <li>Core User Needs & Pain Points Analysis</li>\n                            <li>\"How Might We\" Problem Statement</li>\n                            <li>Constraints & Success Criteria</li>\n                            <li>Next Steps & Recommendations</li>\n                            <li>Key Insights & Critical Quotes</li>\n                          </ul>\n                        </div>\n                        <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                          <p className=\"text-green-800 text-sm\">\n                            ✅ Generated using Claude Sonnet 4 - Latest AI model for professional document creation\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {state.documents.problemStatement && (\n                    <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                      <p className=\"text-yellow-700 mb-4\">\n                        Please review the problem statement document and confirm if it accurately captures your requirements.\n                      </p>\n                      <div className=\"flex gap-3\">\n                        <Button\n                          variant=\"success\"\n                          onClick={() => handleValidation('problemStatement', true)}\n                        >\n                          YES - Continue\n                        </Button>\n                        <Button\n                          variant=\"secondary\"\n                          onClick={() => handleValidation('problemStatement', false)}\n                        >\n                          NO - Needs Feedback\n                        </Button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 3 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Technical Requirements Document</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  {state.documents.technicalRequirements ? (\n                    <DocumentViewer\n                      content={state.documents.technicalRequirements}\n                      title=\"AI-Generated Technical Solution Document\"\n                      downloadFileName=\"Technical_Solution_Document.md\"\n                      onCopy={() => success('Technical requirements copied to clipboard!')}\n                    />\n                  ) : (\n                    <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                      <h4 className=\"font-semibold text-lg mb-4\">AI-Generated Technical Solution Document</h4>\n                      <div className=\"prose max-w-none\">\n                        <p className=\"text-gray-700 leading-relaxed mb-4\">\n                          Claude AI has analyzed your problem statement and generated a comprehensive technical solution document.\n                          This professional document includes solution architecture, component design, and implementation roadmap.\n                        </p>\n                        <div className=\"grid md:grid-cols-2 gap-4\">\n                          <div className=\"p-4 bg-blue-50 rounded-lg\">\n                            <h5 className=\"font-medium text-blue-900 mb-2\">Architecture & Design</h5>\n                            <ul className=\"list-disc list-inside text-blue-800 space-y-1 text-sm\">\n                              <li>Solution Architecture Overview</li>\n                              <li>Component Design & Technology Choices</li>\n                              <li>Data Flow & Integration Patterns</li>\n                              <li>Cloud-Native Implementation Strategy</li>\n                            </ul>\n                          </div>\n                          <div className=\"p-4 bg-purple-50 rounded-lg\">\n                            <h5 className=\"font-medium text-purple-900 mb-2\">Requirements & Implementation</h5>\n                            <ul className=\"list-disc list-inside text-purple-800 space-y-1 text-sm\">\n                              <li>Security & Compliance Framework</li>\n                              <li>Non-Functional Requirements</li>\n                              <li>Prototype Scope & MVP Features</li>\n                              <li>Implementation Roadmap</li>\n                            </ul>\n                          </div>\n                        </div>\n                        <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                          <p className=\"text-green-800 text-sm\">\n                            ✅ Generated using Claude Sonnet 4 - Based on your problem statement analysis\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {state.documents.technicalRequirements && (\n                    <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-yellow-800 mb-3\">Validation Required</h4>\n                      <p className=\"text-yellow-700 mb-4\">\n                        Please review the technical requirements document and confirm if it meets your technical specifications.\n                      </p>\n                      <div className=\"flex gap-3\">\n                        <Button\n                          variant=\"success\"\n                          onClick={() => handleValidation('technicalRequirements', true)}\n                        >\n                          YES - Continue\n                        </Button>\n                        <Button\n                          variant=\"secondary\"\n                          onClick={() => handleValidation('technicalRequirements', false)}\n                        >\n                          NO - Needs Feedback\n                        </Button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 4 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>AI-Generated System Architecture</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                    <h4 className=\"font-semibold text-lg mb-4\">System Architecture Diagram</h4>\n                    <div className=\"prose max-w-none mb-4\">\n                      <p className=\"text-gray-700 leading-relaxed\">\n                        Claude Sonnet 4 has analyzed your technical requirements and generated a comprehensive system architecture diagram.\n                        This includes component relationships, data flows, and integration patterns.\n                      </p>\n                      <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                        <p className=\"text-green-800 text-sm\">\n                          ✅ Generated using Claude Sonnet 4 - Based on your technical solution document\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Architecture Diagram Component */}\n                  <ArchitectureDiagram\n                    mermaidCode={state.documents.architectureDiagram?.mermaidCode}\n                    onCopyCode={(code, type) => {\n                      success('Mermaid code copied to clipboard!');\n                    }}\n                  />\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-blue-800 mb-3\">Quick Review</h4>\n                      <p className=\"text-blue-700 mb-4\">\n                        Does the architecture diagram accurately represent your system design requirements?\n                      </p>\n                      <div className=\"flex gap-3\">\n                        <Button\n                          variant=\"success\"\n                          onClick={() => handleValidation('architectureDiagram', true)}\n                        >\n                          YES - Looks Good\n                        </Button>\n                        <Button\n                          variant=\"secondary\"\n                          onClick={() => handleValidation('architectureDiagram', false)}\n                        >\n                          NO - Needs Changes\n                        </Button>\n                      </div>\n                    </div>\n\n                    <ChatInterface\n                      context=\"System architecture diagram generated from technical requirements\"\n                      diagramCode={state.documents.architectureDiagram?.mermaidCode || ''}\n                      onFeedback={(feedback) => {\n                        console.log('Architecture feedback:', feedback);\n                      }}\n                      onDiagramUpdate={(updatedCode) => {\n                        console.log('Diagram update:', updatedCode);\n                        // Update the diagram with new code\n                        setState(prev => ({\n                          ...prev,\n                          documents: {\n                            ...prev.documents,\n                            architectureDiagram: {\n                              ...prev.documents.architectureDiagram,\n                              [updatedCode.type === 'mermaid' ? 'mermaidCode' : 'plantUMLCode']: updatedCode.code\n                            }\n                          }\n                        }));\n                        success('Architecture diagram updated based on your feedback!');\n                      }}\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {!state.isTransitioning && state.currentStep === 5 && (\n            <Card gradient padding=\"lg\" className=\"animate-fade-in\">\n              <CardHeader>\n                <CardTitle>Loveable AI Implementation Prompts</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  {state.documents.prompts ? (\n                    <DocumentViewer\n                      content={typeof state.documents.prompts === 'string' ? state.documents.prompts : JSON.stringify(state.documents.prompts, null, 2)}\n                      title=\"Loveable AI Implementation Prompts\"\n                      downloadFileName=\"Implementation_Prompts.md\"\n                      onCopy={() => success('Implementation prompts copied to clipboard!')}\n                    />\n                  ) : (\n                    <div className=\"bg-white p-6 rounded-lg border border-gray-200\">\n                      <h4 className=\"font-semibold text-lg mb-4\">Claude Sonnet 4 Generated Prompts</h4>\n                      <div className=\"prose max-w-none mb-4\">\n                        <p className=\"text-gray-700 leading-relaxed\">\n                          Claude Sonnet 4 has analyzed your technical requirements and generated four comprehensive implementation prompts.\n                          Each prompt is optimized for Loveable AI and includes detailed specifications, best practices, and ready-to-use code instructions.\n                        </p>\n                        <div className=\"mt-4 p-3 bg-green-50 rounded-lg border border-green-200\">\n                          <p className=\"text-green-800 text-sm\">\n                            ✅ Generated using Claude Sonnet 4 - Ready for Loveable AI copy-paste implementation\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Implementation Prompts Component */}\n                  {state.documents.prompts && typeof state.documents.prompts === 'object' && (\n                    <ImplementationPrompts\n                      prompts={state.documents.prompts}\n                      onCopyPrompt={(prompt, type) => {\n                        success(`${type.charAt(0).toUpperCase() + type.slice(1)} prompt copied to clipboard!`);\n                      }}\n                    />\n                  )}\n\n                  <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 text-center\">\n                    <CheckCircle className=\"h-12 w-12 text-green-500 mx-auto mb-3\" />\n                    <h4 className=\"font-semibold text-green-800 mb-2\">Rapid Prototyping Complete!</h4>\n                    <p className=\"text-green-700 mb-3\">\n                      Your complete AI-powered prototyping workflow is finished. You now have:\n                    </p>\n                    <div className=\"grid md:grid-cols-2 gap-4 text-left\">\n                      <div className=\"bg-white rounded-lg p-3\">\n                        <h5 className=\"font-medium text-green-800 mb-1\">📋 Documents Generated</h5>\n                        <ul className=\"text-sm text-green-700 space-y-1\">\n                          <li>• Problem Statement (Word)</li>\n                          <li>• Technical Requirements (Word)</li>\n                          <li>• Architecture Diagrams (SVG)</li>\n                        </ul>\n                      </div>\n                      <div className=\"bg-white rounded-lg p-3\">\n                        <h5 className=\"font-medium text-green-800 mb-1\">🚀 Implementation Ready</h5>\n                        <ul className=\"text-sm text-green-700 space-y-1\">\n                          <li>• Frontend Prompt (Loveable AI)</li>\n                          <li>• Backend API Prompt (Loveable AI)</li>\n                          <li>• Database Schema (Loveable AI)</li>\n                          <li>• DevOps Config (JSON Download)</li>\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;;;;;AAiDA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,8MAAA,CAAA,WAAQ;QAAE,aAAa;IAAsC;IACxG;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,2NAAA,CAAA,cAAW;QAAE,aAAa;IAAwC;IAC7G;QAAE,IAAI;QAAG,OAAO;QAA0B,MAAM,0MAAA,CAAA,WAAQ;QAAE,aAAa;IAAyC;IAChH;QAAE,IAAI;QAAG,OAAO;QAAwB,MAAM,oMAAA,CAAA,QAAK;QAAE,aAAa;IAA6B;IAC/F;QAAE,IAAI;QAAG,OAAO;QAAqB,MAAM,kMAAA,CAAA,OAAI;QAAE,aAAa;IAA8B;CAC7F;AAED,8DAA8D;AAC9D,MAAM,sBAAsB,OAAO;IACjC,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,EAAE,MAAM,EAAE;YAC1B,QAAQ,WAAW;QACrB;QACA,OAAO,OAAO,GAAG;YACf,QAAQ,KAAK,+BAA+B;QAC9C;QACA,OAAO,UAAU,CAAC;IACpB;AACF;AAEO,MAAM,sBAAgC;IAC3C,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAC3C,aAAa;QACb,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,OAAO;QACP,iBAAiB;QACjB,gBAAgB;QAChB,WAAW;YACT,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;YACrB,SAAS;QACX;QACA,aAAa;YACX,kBAAkB;YAClB,uBAAuB;YACvB,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,cAAc;gBACd,gBAAgB;gBAChB,OAAO;YACT,CAAC;QAED,KAAK,gDAAgD;QAErD,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAyC,CAAC;YAEvF,sCAAsC;YACtC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAA2C,CAAC;YAEzF,6CAA6C;YAC7C,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,uCAAuC,CAAC;gBACjH,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAyB,CAAC;YAEvE,qCAAqC;YACrC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,IAAI;YAEhC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,kBAAkB;oBACpB;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,cAAc;oBACd,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,OAAO,cAA6C;QAC3E,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;oBACX,GAAG,KAAK,WAAW;oBACnB,CAAC,aAAa,EAAE;gBAClB;YACF,CAAC;QAED,IAAI,SAAS;YACX,MAAM,WAAW,MAAM,WAAW,GAAG;YAErC,IAAI,aAAa,KAAK,iBAAiB,oBAAoB;gBACzD,gEAAgE;gBAChE,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;wBACjB,gBAAgB;wBAChB,OAAO;oBACT,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,yDAAyD;gBACzD,MAAM;YACR,OAAO,IAAI,aAAa,KAAK,iBAAiB,yBAAyB;gBACrE,4DAA4D;gBAC5D,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;wBACjB,gBAAgB;wBAChB,OAAO;oBACT,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,gCAAgC;gBAChC,MAAM;YACR,OAAO,IAAI,aAAa,KAAK,iBAAiB,uBAAuB;gBACnE,8DAA8D;gBAC9D,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;wBACjB,gBAAgB;wBAChB,OAAO;oBACT,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,kCAAkC;gBAClC,MAAM;YACR;QACF;IACF;IAEA,MAAM,gCAAgC;QACpC,IAAI;YACF,uCAAuC;YACvC,MAAM,0BAA0B,MAAM,SAAS,CAAC,gBAAgB,IAC9D;YAEF,sCAAsC;YACtC,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,yBAAyB;gBAC3B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,4CAA4C,CAAC;gBACtH,MAAM,IAAI,MAAM;YAClB;YAEA,qCAAqC;YACrC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO,IAAI;YAEhC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,uBAAuB;oBACzB;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,8BAA8B;QAClC,IAAI;YACF,4CAA4C;YAC5C,MAAM,+BAA+B,MAAM,SAAS,CAAC,qBAAqB,IACxE;YAEF,oCAAoC;YACpC,MAAM,WAAW,MAAM,MAAM,sCAAsC;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,uBAAuB;gBACzB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,0CAA0C,CAAC;gBACpH,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,qBAAqB;4BACnB,aAAa,KAAK,WAAW;4BAC7B,cAAc,KAAK,WAAW,CAAC,yCAAyC;wBAC1E;oBACF;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,gCAAgC;QACpC,IAAI;YACF,4CAA4C;YAC5C,MAAM,+BAA+B,MAAM,SAAS,CAAC,qBAAqB,IACxE;YAEF,sCAAsC;YACtC,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,8BAA8B;gBAChC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,eAAe,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,4CAA4C,CAAC;gBACtH,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,GAAG,KAAK,SAAS;wBACjB,SAAS,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,qDAAqD;oBAC7F;gBACF,CAAC;YAED,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;oBACjB,gBAAgB;oBAChB,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,kBAAkB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,WAAW;IACxE,MAAM,WAAW,AAAC,CAAC,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK;IAElE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,UAAU;;;;;;0BAC1C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,SAAS;4BACT,OAAO,CAAC,KAAK,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,iBAAiB,OAAO;4BAChF,WAAU;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC;gCACV,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,KAAK,EAAE,KAAK,MAAM,WAAW;gCAC9C,MAAM,cAAc,KAAK,EAAE,GAAG,MAAM,WAAW;gCAC/C,MAAM,cAAc,KAAK,EAAE,IAAI,MAAM,WAAW,IAAI;gCAEpD,qBACE,8OAAC;oCAEC,SAAS;wCACP,IAAI,eAAe,CAAC,MAAM,eAAe,EAAE;4CACzC,SAAS,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,KAAK,EAAE;gDAAS,CAAC;wCAC7D;oCACF;oCACA,UAAU,CAAC,eAAe,MAAM,eAAe;oCAC/C,WAAW,CAAC,kIAAkI,EAC5I,WACI,mDACA,cACE,8DACA,cACE,iEACA,mDACR;;sDAEF,8OAAC;4CACC,WAAW,CAAC,aAAa,EACvB,WACI,kBACA,cACE,mBACA,cACE,oBACA,iBACR;;;;;;sDAEJ,8OAAC;4CAAK,WAAW,CAAC,gCAAgC,EAChD,WACI,kBACA,cACE,mBACA,cACE,oBACA,iBACR;sDACC,KAAK,KAAK;;;;;;;mCArCR,KAAK,EAAE;;;;;4BAyClB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;4BAEZ,MAAM,eAAe,IAAI,MAAM,cAAc,kBAC5C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;0CACpC,cAAA,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC,6IAAA,CAAA,oBAAiB;4CAAC,MAAM,MAAM,cAAc;;;;;;wCAC5C,MAAM,KAAK,kBACV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAwB,MAAM,KAAK;;;;;;8DAChD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,SAAS,CAAA,OAAQ,CAAC;gEAC/B,GAAG,IAAI;gEACP,OAAO;gEACP,iBAAiB;gEACjB,gBAAgB;4DAClB,CAAC;8DACF;;;;;;;;;;;;;;;;;;;;;;;4BASV,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACT,CAAC,MAAM,YAAY,iBAClB,8OAAC,sIAAA,CAAA,aAAU;4CAAC,cAAc;;;;;iEAE1B,8OAAC;;8DACC,8OAAC,6IAAA,CAAA,oBAAiB;oDAAC,MAAK;;;;;;gDACvB,MAAM,KAAK,kBACV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB,MAAM,KAAK;;;;;;sEAChD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,SAAS,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO;wEAAM,cAAc;oEAAM,CAAC;sEAC/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAWd,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,SAAS,CAAC,gBAAgB,iBAC/B,8OAAC,0IAAA,CAAA,iBAAc;oDACb,SAAS,MAAM,SAAS,CAAC,gBAAgB;oDACzC,OAAM;oDACN,kBAAiB;oDACjB,QAAQ,IAAM,QAAQ;;;;;yEAGxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAI7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;;;;;;;;;;;;;8EAGR,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;gDAQ7C,MAAM,SAAS,CAAC,gBAAgB,kBAC/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;8EAGD,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,oBAAoB;8EACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAWd,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,SAAS,CAAC,qBAAqB,iBACpC,8OAAC,0IAAA,CAAA,iBAAc;oDACb,SAAS,MAAM,SAAS,CAAC,qBAAqB;oDAC9C,OAAM;oDACN,kBAAiB;oDACjB,QAAQ,IAAM,QAAQ;;;;;yEAGxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAqC;;;;;;8EAIlD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAAiC;;;;;;8FAC/C,8OAAC;oFAAG,WAAU;;sGACZ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;;;;;;;;;;;;;sFAGR,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAAmC;;;;;;8FACjD,8OAAC;oFAAG,WAAU;;sGACZ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;sGACJ,8OAAC;sGAAG;;;;;;;;;;;;;;;;;;;;;;;;8EAIV,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;gDAQ7C,MAAM,SAAS,CAAC,qBAAqB,kBACpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAuB;;;;;;sEAGpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;8EAGD,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,SAAS,IAAM,iBAAiB,yBAAyB;8EAC1D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAWd,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAI7C,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;8DAQ5C,8OAAC,+IAAA,CAAA,sBAAmB;oDAClB,aAAa,MAAM,SAAS,CAAC,mBAAmB,EAAE;oDAClD,YAAY,CAAC,MAAM;wDACjB,QAAQ;oDACV;;;;;;8DAGF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAGlC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;sFAGD,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,iBAAiB,uBAAuB;sFACxD;;;;;;;;;;;;;;;;;;sEAML,8OAAC,yIAAA,CAAA,gBAAa;4DACZ,SAAQ;4DACR,aAAa,MAAM,SAAS,CAAC,mBAAmB,EAAE,eAAe;4DACjE,YAAY,CAAC;gEACX,QAAQ,GAAG,CAAC,0BAA0B;4DACxC;4DACA,iBAAiB,CAAC;gEAChB,QAAQ,GAAG,CAAC,mBAAmB;gEAC/B,mCAAmC;gEACnC,SAAS,CAAA,OAAQ,CAAC;wEAChB,GAAG,IAAI;wEACP,WAAW;4EACT,GAAG,KAAK,SAAS;4EACjB,qBAAqB;gFACnB,GAAG,KAAK,SAAS,CAAC,mBAAmB;gFACrC,CAAC,YAAY,IAAI,KAAK,YAAY,gBAAgB,eAAe,EAAE,YAAY,IAAI;4EACrF;wEACF;oEACF,CAAC;gEACD,QAAQ;4DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQX,CAAC,MAAM,eAAe,IAAI,MAAM,WAAW,KAAK,mBAC/C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,QAAQ;gCAAC,SAAQ;gCAAK,WAAU;;kDACpC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,SAAS,CAAC,OAAO,iBACtB,8OAAC,0IAAA,CAAA,iBAAc;oDACb,SAAS,OAAO,MAAM,SAAS,CAAC,OAAO,KAAK,WAAW,MAAM,SAAS,CAAC,OAAO,GAAG,KAAK,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,MAAM;oDAC/H,OAAM;oDACN,kBAAiB;oDACjB,QAAQ,IAAM,QAAQ;;;;;yEAGxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;8EAI7C,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;gDAS7C,MAAM,SAAS,CAAC,OAAO,IAAI,OAAO,MAAM,SAAS,CAAC,OAAO,KAAK,0BAC7D,8OAAC,iJAAA,CAAA,wBAAqB;oDACpB,SAAS,MAAM,SAAS,CAAC,OAAO;oDAChC,cAAc,CAAC,QAAQ;wDACrB,QAAQ,GAAG,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,4BAA4B,CAAC;oDACvF;;;;;;8DAIJ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAsB;;;;;;sEAGnC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAkC;;;;;;sFAChD,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;;;;;;;;;;;;;8EAGR,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAkC;;;;;;sFAChD,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;8FACJ,8OAAC;8FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9B", "debugId": null}}, {"offset": {"line": 3963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { RapidPrototypingApp } from '@/components/RapidPrototypingApp';\n\nexport default function Home() {\n  return <RapidPrototypingApp />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,yIAAA,CAAA,sBAAmB;;;;;AAC7B", "debugId": null}}]}