{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/api/generate-problem-statement/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\nimport mammoth from 'mammoth';\n\n// For development environments with SSL certificate issues\nif (process.env.NODE_ENV === 'development') {\n  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n}\n\n// Check if API key is configured\nif (!process.env.ANTHROPIC_API_KEY) {\n  console.error('ANTHROPIC_API_KEY is not configured in environment variables');\n}\n\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY || 'dummy-key-for-development',\n});\n\nconst PROBLEM_STATEMENT_PROMPT = `You are a senior business analyst and product strategist specializing in creating comprehensive problem statement documents from conversation transcripts.\n\nYour task is to analyze the conversation transcript and generate a professional problem statement document that includes:\n\n1. **Executive Summary** - High-level overview of the business challenge\n2. **Background & Context** - Setting and circumstances leading to this workshop\n3. **Key Business Challenges** - Specific problems identified with supporting quotes\n4. **Core User Needs & Pain Points** - What users are struggling with\n5. **How Might We Problem Statement** - Clear, actionable problem framing\n6. **Constraints & Assumptions** - Technical, business, and resource limitations\n7. **Success Criteria** - How we'll measure if the solution works\n8. **Next Steps & Recommendations** - Immediate actions to take\n9. **Key Insights & Critical Quotes** - Important verbatim statements from stakeholders\n\nFormat the output as a professional business document with clear sections, bullet points, and direct quotes from the transcript where relevant. Use a tone that's professional yet accessible, suitable for both technical and business stakeholders.\n\nFocus on extracting actionable insights and creating a clear problem statement that can guide solution development.`;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n    \n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    console.log(`Processing file: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);\n\n    // Extract text content from the uploaded file\n    let textContent = '';\n    \n    if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n      // Handle .docx files\n      const arrayBuffer = await file.arrayBuffer();\n      const result = await mammoth.extractRawText({ arrayBuffer });\n      textContent = result.value;\n    } else if (file.type === 'text/plain' || file.name.endsWith('.txt') || file.name.endsWith('.md')) {\n      // Handle text files\n      textContent = await file.text();\n    } else {\n      return NextResponse.json({ \n        error: 'Unsupported file type. Please upload a .txt, .md, or .docx file.' \n      }, { status: 400 });\n    }\n\n    if (!textContent.trim()) {\n      return NextResponse.json({ \n        error: 'No text content found in the uploaded file.' \n      }, { status: 400 });\n    }\n\n    console.log(`Extracted text content: ${textContent.length} characters`);\n\n    // Check if API key is configured\n    if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {\n      console.error('ANTHROPIC_API_KEY is not properly configured - creating demo document');\n      \n      // For development, return demo content\n      const demoContent = createDemoMarkdownDocument(file.name);\n      return NextResponse.json({\n        success: true,\n        content: demoContent,\n        message: 'Demo problem statement generated. Configure Claude API key for real document generation.'\n      });\n    }\n\n    console.log('Generating problem statement from transcript...');\n    console.log(`Transcript content length: ${textContent.length} characters`);\n\n    // Call Claude API with Sonnet 4\n    console.log('Calling Claude API with Sonnet 4 for problem statement...');\n    const fullPrompt = `${PROBLEM_STATEMENT_PROMPT}\\n\\nConversation Transcript Content:\\n${textContent}`;\n    console.log(`Full prompt length: ${fullPrompt.length} characters`);\n    \n    let response;\n    try {\n      response = await anthropic.messages.create({\n        model: 'claude-sonnet-4-20250514',\n        max_tokens: 10000,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n      console.log('Claude API call successful for problem statement');\n    } catch (apiError: any) {\n      console.error('Claude API Error:', apiError);\n      \n      // Handle specific API errors\n      if (apiError.status === 401) {\n        return NextResponse.json({ \n          error: 'Invalid API key. Please check your Anthropic API key configuration.' \n        }, { status: 401 });\n      } else if (apiError.status === 429) {\n        return NextResponse.json({ \n          error: 'Rate limit exceeded. Please try again in a few minutes.' \n        }, { status: 429 });\n      } else if (apiError.message?.includes('model')) {\n        return NextResponse.json({ \n          error: 'Model not available. Your API key may not have access to Claude Sonnet 4.' \n        }, { status: 400 });\n      } else {\n        return NextResponse.json({ \n          error: `Claude API error: ${apiError.message || 'Unknown error'}` \n        }, { status: 500 });\n      }\n    }\n\n    const analysisText = response.content[0].type === 'text' ? response.content[0].text : '';\n    \n    // Format the analysis as Markdown document\n    const markdownContent = formatAsMarkdownDocument(analysisText, file.name);\n    \n    return NextResponse.json({\n      success: true,\n      content: markdownContent, // Return formatted markdown content\n      message: 'Problem statement document generated successfully with Claude Sonnet 4!'\n    });\n\n  } catch (error) {\n    console.error('Error generating problem statement:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate problem statement' }, \n      { status: 500 }\n    );\n  }\n}\n\nfunction formatAsMarkdownDocument(analysisText: string, originalFileName: string): string {\n  const currentDate = new Date().toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n\n  return `# Problem Statement Document\n\n**Generated from:** ${originalFileName}  \n**Date:** ${currentDate}  \n**Generated by:** Claude Sonnet 4 AI Analysis  \n\n---\n\n${analysisText}\n\n---\n\n*This document was automatically generated using AI analysis of the provided conversation transcript. Please review and validate the content before proceeding with implementation.*`;\n}\n\nfunction createDemoMarkdownDocument(originalFileName: string): string {\n  const currentDate = new Date().toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n\n  return `# Problem Statement Document\n\n**Generated from:** ${originalFileName}  \n**Date:** ${currentDate}  \n**Generated by:** Demo Mode (Configure Claude API key for real analysis)  \n\n---\n\n## Executive Summary\n\nThis is a demo problem statement document. To generate real problem statements from your conversation transcripts, please configure your Claude API key in the environment variables.\n\n## Key Business Challenges\n\n- **Demo Challenge 1**: Sample business challenge that would be extracted from your transcript\n- **Demo Challenge 2**: Another example challenge with supporting context\n- **Demo Challenge 3**: Technical or operational challenge identified\n\n## Core User Needs & Pain Points\n\n- Users need a streamlined solution for their workflow\n- Current processes are manual and time-consuming\n- Integration between systems is lacking\n\n## How Might We Problem Statement\n\n**How might we** create a solution that addresses the core business challenges while providing a seamless user experience and scalable technical architecture?\n\n## Success Criteria\n\n- Improved user satisfaction scores\n- Reduced processing time\n- Increased system reliability\n- Better integration capabilities\n\n## Next Steps & Recommendations\n\n1. Configure Claude API key for real document generation\n2. Upload your conversation transcript\n3. Review and validate the generated problem statement\n4. Proceed to technical requirements generation\n\n---\n\n*This is a demo document. Configure your Claude API key to generate real problem statements from conversation transcripts.*`;\n}\n\n// Removed Word document functions - now using Markdown format\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEA,2DAA2D;AAC3D,wCAA4C;IAC1C,QAAQ,GAAG,CAAC,4BAA4B,GAAG;AAC7C;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAC3C;AAEA,MAAM,2BAA2B,CAAC;;;;;;;;;;;;;;;;mHAgBiF,CAAC;AAE7G,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QAEzF,8CAA8C;QAC9C,IAAI,cAAc;QAElB,IAAI,KAAK,IAAI,KAAK,2EAA2E;YAC3F,qBAAqB;YACrB,MAAM,cAAc,MAAM,KAAK,WAAW;YAC1C,MAAM,SAAS,MAAM,yIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAAE;YAAY;YAC1D,cAAc,OAAO,KAAK;QAC5B,OAAO,IAAI,KAAK,IAAI,KAAK,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ;YAChG,oBAAoB;YACpB,cAAc,MAAM,KAAK,IAAI;QAC/B,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,YAAY,MAAM,CAAC,WAAW,CAAC;QAEtE,iCAAiC;QACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK,+BAA+B;YACrG,QAAQ,KAAK,CAAC;YAEd,uCAAuC;YACvC,MAAM,cAAc,2BAA2B,KAAK,IAAI;YACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,SAAS;YACX;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,YAAY,MAAM,CAAC,WAAW,CAAC;QAEzE,gCAAgC;QAChC,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,GAAG,yBAAyB,sCAAsC,EAAE,aAAa;QACpG,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,MAAM,CAAC,WAAW,CAAC;QAEjE,IAAI;QACJ,IAAI;YACF,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YACA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,6BAA6B;YAC7B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO,IAAI,SAAS,OAAO,EAAE,SAAS,UAAU;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB,OAAO;gBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO,CAAC,kBAAkB,EAAE,SAAS,OAAO,IAAI,iBAAiB;gBACnE,GAAG;oBAAE,QAAQ;gBAAI;YACnB;QACF;QAEA,MAAM,eAAe,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG;QAEtF,2CAA2C;QAC3C,MAAM,kBAAkB,yBAAyB,cAAc,KAAK,IAAI;QAExE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,yBAAyB,YAAoB,EAAE,gBAAwB;IAC9E,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,OAAO,CAAC;;oBAEU,EAAE,iBAAiB;UAC7B,EAAE,YAAY;;;;;AAKxB,EAAE,aAAa;;;;oLAIqK,CAAC;AACrL;AAEA,SAAS,2BAA2B,gBAAwB;IAC1D,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,OAAO,CAAC;;oBAEU,EAAE,iBAAiB;UAC7B,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2HAyCmG,CAAC;AAC5H,EAEA,8DAA8D", "debugId": null}}]}