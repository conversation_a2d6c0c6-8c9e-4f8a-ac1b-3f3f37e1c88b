module.exports = {

"[project]/node_modules/d3-array/src/ticks.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ticks),
    "tickIncrement": (()=>tickIncrement),
    "tickStep": (()=>tickStep)
});
const e10 = Math.sqrt(50), e5 = Math.sqrt(10), e2 = Math.sqrt(2);
function tickSpec(start, stop, count) {
    const step = (stop - start) / Math.max(0, count), power = Math.floor(Math.log10(step)), error = step / Math.pow(10, power), factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;
    let i1, i2, inc;
    if (power < 0) {
        inc = Math.pow(10, -power) / factor;
        i1 = Math.round(start * inc);
        i2 = Math.round(stop * inc);
        if (i1 / inc < start) ++i1;
        if (i2 / inc > stop) --i2;
        inc = -inc;
    } else {
        inc = Math.pow(10, power) * factor;
        i1 = Math.round(start / inc);
        i2 = Math.round(stop / inc);
        if (i1 * inc < start) ++i1;
        if (i2 * inc > stop) --i2;
    }
    if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);
    return [
        i1,
        i2,
        inc
    ];
}
function ticks(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    if (!(count > 0)) return [];
    if (start === stop) return [
        start
    ];
    const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);
    if (!(i2 >= i1)) return [];
    const n = i2 - i1 + 1, ticks = new Array(n);
    if (reverse) {
        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) / -inc;
        else for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) * inc;
    } else {
        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) / -inc;
        else for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) * inc;
    }
    return ticks;
}
function tickIncrement(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    return tickSpec(start, stop, count)[2];
}
function tickStep(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);
    return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);
}
}}),
"[project]/node_modules/d3-array/src/ticks.js [app-ssr] (ecmascript) <export default as ticks>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ticks": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/d3-array/src/ascending.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ascending)
});
function ascending(a, b) {
    return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
}
}}),
"[project]/node_modules/d3-array/src/descending.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>descending)
});
function descending(a, b) {
    return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;
}
}}),
"[project]/node_modules/d3-array/src/bisector.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>bisector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ascending.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$descending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/descending.js [app-ssr] (ecmascript)");
;
;
function bisector(f) {
    let compare1, compare2, delta;
    // If an accessor is specified, promote it to a comparator. In this case we
    // can test whether the search value is (self-) comparable. We can’t do this
    // for a comparator (except for specific, known comparators) because we can’t
    // tell if the comparator is symmetric, and an asymmetric comparator can’t be
    // used to test whether a single value is comparable.
    if (f.length !== 2) {
        compare1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
        compare2 = (d, x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(f(d), x);
        delta = (d, x)=>f(d) - x;
    } else {
        compare1 = f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$descending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] ? f : zero;
        compare2 = f;
        delta = f;
    }
    function left(a, x, lo = 0, hi = a.length) {
        if (lo < hi) {
            if (compare1(x, x) !== 0) return hi;
            do {
                const mid = lo + hi >>> 1;
                if (compare2(a[mid], x) < 0) lo = mid + 1;
                else hi = mid;
            }while (lo < hi)
        }
        return lo;
    }
    function right(a, x, lo = 0, hi = a.length) {
        if (lo < hi) {
            if (compare1(x, x) !== 0) return hi;
            do {
                const mid = lo + hi >>> 1;
                if (compare2(a[mid], x) <= 0) lo = mid + 1;
                else hi = mid;
            }while (lo < hi)
        }
        return lo;
    }
    function center(a, x, lo = 0, hi = a.length) {
        const i = left(a, x, lo, hi - 1);
        return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;
    }
    return {
        left,
        center,
        right
    };
}
function zero() {
    return 0;
}
}}),
"[project]/node_modules/d3-array/src/number.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>number),
    "numbers": (()=>numbers)
});
function number(x) {
    return x === null ? NaN : +x;
}
function* numbers(values, valueof) {
    if (valueof === undefined) {
        for (let value of values){
            if (value != null && (value = +value) >= value) {
                yield value;
            }
        }
    } else {
        let index = -1;
        for (let value of values){
            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {
                yield value;
            }
        }
    }
}
}}),
"[project]/node_modules/d3-array/src/bisect.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bisectCenter": (()=>bisectCenter),
    "bisectLeft": (()=>bisectLeft),
    "bisectRight": (()=>bisectRight),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ascending.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/number.js [app-ssr] (ecmascript)");
;
;
;
const ascendingBisect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const bisectRight = ascendingBisect.right;
const bisectLeft = ascendingBisect.left;
const bisectCenter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).center;
const __TURBOPACK__default__export__ = bisectRight;
}}),
"[project]/node_modules/d3-array/src/bisect.js [app-ssr] (ecmascript) <export default as bisect>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bisect": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisect.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/d3-interpolate/src/round.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function __TURBOPACK__default__export__(a, b) {
    return a = +a, b = +b, function(t) {
        return Math.round(a * (1 - t) + b * t);
    };
}
}}),
"[project]/node_modules/d3-interpolate/src/round.js [app-ssr] (ecmascript) <export default as interpolateRound>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "interpolateRound": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/round.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/d3-scale/src/constant.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>constants)
});
function constants(x) {
    return function() {
        return x;
    };
}
}}),
"[project]/node_modules/d3-scale/src/number.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>number)
});
function number(x) {
    return +x;
}
}}),
"[project]/node_modules/d3-scale/src/continuous.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "copy": (()=>copy),
    "default": (()=>continuous),
    "identity": (()=>identity),
    "transformer": (()=>transformer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__bisect$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisect.js [app-ssr] (ecmascript) <export default as bisect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolate$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/value.js [app-ssr] (ecmascript) <export default as interpolate>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/number.js [app-ssr] (ecmascript) <export default as interpolateNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateRound$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/round.js [app-ssr] (ecmascript) <export default as interpolateRound>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/constant.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/number.js [app-ssr] (ecmascript)");
;
;
;
;
var unit = [
    0,
    1
];
function identity(x) {
    return x;
}
function normalize(a, b) {
    return (b -= a = +a) ? function(x) {
        return (x - a) / b;
    } : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(isNaN(b) ? NaN : 0.5);
}
function clamper(a, b) {
    var t;
    if (a > b) t = a, a = b, b = t;
    return function(x) {
        return Math.max(a, Math.min(b, x));
    };
}
// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].
// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].
function bimap(domain, range, interpolate) {
    var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];
    if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);
    else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);
    return function(x) {
        return r0(d0(x));
    };
}
function polymap(domain, range, interpolate) {
    var j = Math.min(domain.length, range.length) - 1, d = new Array(j), r = new Array(j), i = -1;
    // Reverse descending domains.
    if (domain[j] < domain[0]) {
        domain = domain.slice().reverse();
        range = range.slice().reverse();
    }
    while(++i < j){
        d[i] = normalize(domain[i], domain[i + 1]);
        r[i] = interpolate(range[i], range[i + 1]);
    }
    return function(x) {
        var i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__bisect$3e$__["bisect"])(domain, x, 1, j) - 1;
        return r[i](d[i](x));
    };
}
function copy(source, target) {
    return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());
}
function transformer() {
    var domain = unit, range = unit, interpolate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolate$3e$__["interpolate"], transform, untransform, unknown, clamp = identity, piecewise, output, input;
    function rescale() {
        var n = Math.min(domain.length, range.length);
        if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);
        piecewise = n > 2 ? polymap : bimap;
        output = input = null;
        return scale;
    }
    function scale(x) {
        return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));
    }
    scale.invert = function(y) {
        return clamp(untransform((input || (input = piecewise(range, domain.map(transform), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateNumber$3e$__["interpolateNumber"])))(y)));
    };
    scale.domain = function(_) {
        return arguments.length ? (domain = Array.from(_, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]), rescale()) : domain.slice();
    };
    scale.range = function(_) {
        return arguments.length ? (range = Array.from(_), rescale()) : range.slice();
    };
    scale.rangeRound = function(_) {
        return range = Array.from(_), interpolate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateRound$3e$__["interpolateRound"], rescale();
    };
    scale.clamp = function(_) {
        return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;
    };
    scale.interpolate = function(_) {
        return arguments.length ? (interpolate = _, rescale()) : interpolate;
    };
    scale.unknown = function(_) {
        return arguments.length ? (unknown = _, scale) : unknown;
    };
    return function(t, u) {
        transform = t, untransform = u;
        return rescale();
    };
}
function continuous() {
    return transformer()(identity, identity);
}
}}),
"[project]/node_modules/d3-scale/src/init.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "initInterpolator": (()=>initInterpolator),
    "initRange": (()=>initRange)
});
function initRange(domain, range) {
    switch(arguments.length){
        case 0:
            break;
        case 1:
            this.range(domain);
            break;
        default:
            this.range(range).domain(domain);
            break;
    }
    return this;
}
function initInterpolator(domain, interpolator) {
    switch(arguments.length){
        case 0:
            break;
        case 1:
            {
                if (typeof domain === "function") this.interpolator(domain);
                else this.range(domain);
                break;
            }
        default:
            {
                this.domain(domain);
                if (typeof interpolator === "function") this.interpolator(interpolator);
                else this.range(interpolator);
                break;
            }
    }
    return this;
}
}}),
"[project]/node_modules/d3-format/src/formatDecimal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "formatDecimalParts": (()=>formatDecimalParts)
});
function __TURBOPACK__default__export__(x) {
    return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString("en").replace(/,/g, "") : x.toString(10);
}
function formatDecimalParts(x, p) {
    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf("e")) < 0) return null; // NaN, ±Infinity
    var i, coefficient = x.slice(0, i);
    // The string returned by toExponential either has the form \d\.\d+e[-+]\d+
    // (e.g., 1.2e+3) or the form \de[-+]\d+ (e.g., 1e+3).
    return [
        coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
        +x.slice(i + 1)
    ];
}
}}),
"[project]/node_modules/d3-format/src/exponent.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-ssr] (ecmascript)");
;
function __TURBOPACK__default__export__(x) {
    return x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDecimalParts"])(Math.abs(x)), x ? x[1] : NaN;
}
}}),
"[project]/node_modules/d3-format/src/formatGroup.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function __TURBOPACK__default__export__(grouping, thousands) {
    return function(value, width) {
        var i = value.length, t = [], j = 0, g = grouping[0], length = 0;
        while(i > 0 && g > 0){
            if (length + g + 1 > width) g = Math.max(1, width - length);
            t.push(value.substring(i -= g, i + g));
            if ((length += g + 1) > width) break;
            g = grouping[j = (j + 1) % grouping.length];
        }
        return t.reverse().join(thousands);
    };
}
}}),
"[project]/node_modules/d3-format/src/formatNumerals.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function __TURBOPACK__default__export__(numerals) {
    return function(value) {
        return value.replace(/[0-9]/g, function(i) {
            return numerals[+i];
        });
    };
}
}}),
"[project]/node_modules/d3-format/src/formatSpecifier.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// [[fill]align][sign][symbol][0][width][,][.precision][~][type]
__turbopack_context__.s({
    "FormatSpecifier": (()=>FormatSpecifier),
    "default": (()=>formatSpecifier)
});
var re = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
function formatSpecifier(specifier) {
    if (!(match = re.exec(specifier))) throw new Error("invalid format: " + specifier);
    var match;
    return new FormatSpecifier({
        fill: match[1],
        align: match[2],
        sign: match[3],
        symbol: match[4],
        zero: match[5],
        width: match[6],
        comma: match[7],
        precision: match[8] && match[8].slice(1),
        trim: match[9],
        type: match[10]
    });
}
formatSpecifier.prototype = FormatSpecifier.prototype; // instanceof
function FormatSpecifier(specifier) {
    this.fill = specifier.fill === undefined ? " " : specifier.fill + "";
    this.align = specifier.align === undefined ? ">" : specifier.align + "";
    this.sign = specifier.sign === undefined ? "-" : specifier.sign + "";
    this.symbol = specifier.symbol === undefined ? "" : specifier.symbol + "";
    this.zero = !!specifier.zero;
    this.width = specifier.width === undefined ? undefined : +specifier.width;
    this.comma = !!specifier.comma;
    this.precision = specifier.precision === undefined ? undefined : +specifier.precision;
    this.trim = !!specifier.trim;
    this.type = specifier.type === undefined ? "" : specifier.type + "";
}
FormatSpecifier.prototype.toString = function() {
    return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (this.width === undefined ? "" : Math.max(1, this.width | 0)) + (this.comma ? "," : "") + (this.precision === undefined ? "" : "." + Math.max(0, this.precision | 0)) + (this.trim ? "~" : "") + this.type;
};
}}),
"[project]/node_modules/d3-format/src/formatTrim.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function __TURBOPACK__default__export__(s) {
    out: for(var n = s.length, i = 1, i0 = -1, i1; i < n; ++i){
        switch(s[i]){
            case ".":
                i0 = i1 = i;
                break;
            case "0":
                if (i0 === 0) i0 = i;
                i1 = i;
                break;
            default:
                if (!+s[i]) break out;
                if (i0 > 0) i0 = 0;
                break;
        }
    }
    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;
}
}}),
"[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prefixExponent": (()=>prefixExponent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-ssr] (ecmascript)");
;
var prefixExponent;
function __TURBOPACK__default__export__(x, p) {
    var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, p);
    if (!d) return x + "";
    var coefficient = d[0], exponent = d[1], i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n = coefficient.length;
    return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join("0") : i > 0 ? coefficient.slice(0, i) + "." + coefficient.slice(i) : "0." + new Array(1 - i).join("0") + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, Math.max(0, p + i - 1))[0]; // less than 1y!
}
}}),
"[project]/node_modules/d3-format/src/formatRounded.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-ssr] (ecmascript)");
;
function __TURBOPACK__default__export__(x, p) {
    var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, p);
    if (!d) return x + "";
    var coefficient = d[0], exponent = d[1];
    return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join("0");
}
}}),
"[project]/node_modules/d3-format/src/formatTypes.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatRounded.js [app-ssr] (ecmascript)");
;
;
;
const __TURBOPACK__default__export__ = {
    "%": (x, p)=>(x * 100).toFixed(p),
    "b": (x)=>Math.round(x).toString(2),
    "c": (x)=>x + "",
    "d": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    "e": (x, p)=>x.toExponential(p),
    "f": (x, p)=>x.toFixed(p),
    "g": (x, p)=>x.toPrecision(p),
    "o": (x)=>Math.round(x).toString(8),
    "p": (x, p)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(x * 100, p),
    "r": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    "s": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    "X": (x)=>Math.round(x).toString(16).toUpperCase(),
    "x": (x)=>Math.round(x).toString(16)
};
}}),
"[project]/node_modules/d3-format/src/identity.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function __TURBOPACK__default__export__(x) {
    return x;
}
}}),
"[project]/node_modules/d3-format/src/locale.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatGroup.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatNumerals$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatNumerals.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTrim$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatTrim.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatTypes.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/identity.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
var map = Array.prototype.map, prefixes = [
    "y",
    "z",
    "a",
    "f",
    "p",
    "n",
    "µ",
    "m",
    "",
    "k",
    "M",
    "G",
    "T",
    "P",
    "E",
    "Z",
    "Y"
];
function __TURBOPACK__default__export__(locale) {
    var group = locale.grouping === undefined || locale.thousands === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(map.call(locale.grouping, Number), locale.thousands + ""), currencyPrefix = locale.currency === undefined ? "" : locale.currency[0] + "", currencySuffix = locale.currency === undefined ? "" : locale.currency[1] + "", decimal = locale.decimal === undefined ? "." : locale.decimal + "", numerals = locale.numerals === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatNumerals$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(map.call(locale.numerals, String)), percent = locale.percent === undefined ? "%" : locale.percent + "", minus = locale.minus === undefined ? "−" : locale.minus + "", nan = locale.nan === undefined ? "NaN" : locale.nan + "";
    function newFormat(specifier) {
        specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(specifier);
        var fill = specifier.fill, align = specifier.align, sign = specifier.sign, symbol = specifier.symbol, zero = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;
        // The "n" type is an alias for ",g".
        if (type === "n") comma = true, type = "g";
        else if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"][type]) precision === undefined && (precision = 12), trim = true, type = "g";
        // If zero fill is specified, padding goes after sign and before digits.
        if (zero || fill === "0" && align === "=") zero = true, fill = "0", align = "=";
        // Compute the prefix and suffix.
        // For SI-prefix, the suffix is lazily computed.
        var prefix = symbol === "$" ? currencyPrefix : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "", suffix = symbol === "$" ? currencySuffix : /[%p]/.test(type) ? percent : "";
        // What format function should we use?
        // Is this an integer type?
        // Can this type generate exponential notation?
        var formatType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"][type], maybeSuffix = /[defgprs%]/.test(type);
        // Set the default precision if not specified,
        // or clamp the specified precision to the supported range.
        // For significant precision, it must be in [1, 21].
        // For fixed precision, it must be in [0, 20].
        precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));
        function format(value) {
            var valuePrefix = prefix, valueSuffix = suffix, i, n, c;
            if (type === "c") {
                valueSuffix = formatType(value) + valueSuffix;
                value = "";
            } else {
                value = +value;
                // Determine the sign. -0 is not less than 0, but 1 / -0 is!
                var valueNegative = value < 0 || 1 / value < 0;
                // Perform the initial formatting.
                value = isNaN(value) ? nan : formatType(Math.abs(value), precision);
                // Trim insignificant zeros.
                if (trim) value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTrim$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(value);
                // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.
                if (valueNegative && +value === 0 && sign !== "+") valueNegative = false;
                // Compute the prefix and suffix.
                valuePrefix = (valueNegative ? sign === "(" ? sign : minus : sign === "-" || sign === "(" ? "" : sign) + valuePrefix;
                valueSuffix = (type === "s" ? prefixes[8 + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["prefixExponent"] / 3] : "") + valueSuffix + (valueNegative && sign === "(" ? ")" : "");
                // Break the formatted value into the integer “value” part that can be
                // grouped, and fractional or exponential “suffix” part that is not.
                if (maybeSuffix) {
                    i = -1, n = value.length;
                    while(++i < n){
                        if (c = value.charCodeAt(i), 48 > c || c > 57) {
                            valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;
                            value = value.slice(0, i);
                            break;
                        }
                    }
                }
            }
            // If the fill character is not "0", grouping is applied before padding.
            if (comma && !zero) value = group(value, Infinity);
            // Compute the padding.
            var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : "";
            // If the fill character is "0", grouping is applied after padding.
            if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";
            // Reconstruct the final output based on the desired alignment.
            switch(align){
                case "<":
                    value = valuePrefix + value + valueSuffix + padding;
                    break;
                case "=":
                    value = valuePrefix + padding + value + valueSuffix;
                    break;
                case "^":
                    value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);
                    break;
                default:
                    value = padding + valuePrefix + value + valueSuffix;
                    break;
            }
            return numerals(value);
        }
        format.toString = function() {
            return specifier + "";
        };
        return format;
    }
    function formatPrefix(specifier, value) {
        var f = newFormat((specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(specifier), specifier.type = "f", specifier)), e = Math.max(-8, Math.min(8, Math.floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(value) / 3))) * 3, k = Math.pow(10, -e), prefix = prefixes[8 + e / 3];
        return function(value) {
            return f(k * value) + prefix;
        };
    }
    return {
        format: newFormat,
        formatPrefix: formatPrefix
    };
}
}}),
"[project]/node_modules/d3-format/src/defaultLocale.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>defaultLocale),
    "format": (()=>format),
    "formatPrefix": (()=>formatPrefix)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/locale.js [app-ssr] (ecmascript)");
;
var locale;
var format;
var formatPrefix;
defaultLocale({
    thousands: ",",
    grouping: [
        3
    ],
    currency: [
        "$",
        ""
    ]
});
function defaultLocale(definition) {
    locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(definition);
    format = locale.format;
    formatPrefix = locale.formatPrefix;
    return locale;
}
}}),
"[project]/node_modules/d3-format/src/formatSpecifier.js [app-ssr] (ecmascript) <export default as formatSpecifier>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatSpecifier": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/d3-format/src/precisionFixed.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-ssr] (ecmascript)");
;
function __TURBOPACK__default__export__(step) {
    return Math.max(0, -(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Math.abs(step)));
}
}}),
"[project]/node_modules/d3-format/src/precisionFixed.js [app-ssr] (ecmascript) <export default as precisionFixed>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "precisionFixed": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionFixed.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/d3-format/src/precisionPrefix.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-ssr] (ecmascript)");
;
function __TURBOPACK__default__export__(step, value) {
    return Math.max(0, Math.max(-8, Math.min(8, Math.floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(value) / 3))) * 3 - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Math.abs(step)));
}
}}),
"[project]/node_modules/d3-format/src/precisionPrefix.js [app-ssr] (ecmascript) <export default as precisionPrefix>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "precisionPrefix": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionPrefix.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/d3-format/src/precisionRound.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-ssr] (ecmascript)");
;
function __TURBOPACK__default__export__(step, max) {
    step = Math.abs(step), max = Math.abs(max) - step;
    return Math.max(0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(max) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(step)) + 1;
}
}}),
"[project]/node_modules/d3-format/src/precisionRound.js [app-ssr] (ecmascript) <export default as precisionRound>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "precisionRound": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionRound.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/d3-scale/src/tickFormat.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>tickFormat)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/defaultLocale.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__formatSpecifier$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-ssr] (ecmascript) <export default as formatSpecifier>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionFixed$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionFixed.js [app-ssr] (ecmascript) <export default as precisionFixed>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionPrefix$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionPrefix.js [app-ssr] (ecmascript) <export default as precisionPrefix>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionRound$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionRound.js [app-ssr] (ecmascript) <export default as precisionRound>");
;
;
function tickFormat(start, stop, count, specifier) {
    var step = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tickStep"])(start, stop, count), precision;
    specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__formatSpecifier$3e$__["formatSpecifier"])(specifier == null ? ",f" : specifier);
    switch(specifier.type){
        case "s":
            {
                var value = Math.max(Math.abs(start), Math.abs(stop));
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionPrefix$3e$__["precisionPrefix"])(step, value))) specifier.precision = precision;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatPrefix"])(specifier, value);
            }
        case "":
        case "e":
        case "g":
        case "p":
        case "r":
            {
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionRound$3e$__["precisionRound"])(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === "e");
                break;
            }
        case "f":
        case "%":
            {
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionFixed$3e$__["precisionFixed"])(step))) specifier.precision = precision - (specifier.type === "%") * 2;
                break;
            }
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["format"])(specifier);
}
}}),
"[project]/node_modules/d3-scale/src/linear.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>linear),
    "linearish": (()=>linearish)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ticks$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-ssr] (ecmascript) <export default as ticks>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/continuous.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/init.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$tickFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/tickFormat.js [app-ssr] (ecmascript)");
;
;
;
;
function linearish(scale) {
    var domain = scale.domain;
    scale.ticks = function(count) {
        var d = domain();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ticks$3e$__["ticks"])(d[0], d[d.length - 1], count == null ? 10 : count);
    };
    scale.tickFormat = function(count, specifier) {
        var d = domain();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$tickFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(d[0], d[d.length - 1], count == null ? 10 : count, specifier);
    };
    scale.nice = function(count) {
        if (count == null) count = 10;
        var d = domain();
        var i0 = 0;
        var i1 = d.length - 1;
        var start = d[i0];
        var stop = d[i1];
        var prestep;
        var step;
        var maxIter = 10;
        if (stop < start) {
            step = start, start = stop, stop = step;
            step = i0, i0 = i1, i1 = step;
        }
        while(maxIter-- > 0){
            step = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tickIncrement"])(start, stop, count);
            if (step === prestep) {
                d[i0] = start;
                d[i1] = stop;
                return domain(d);
            } else if (step > 0) {
                start = Math.floor(start / step) * step;
                stop = Math.ceil(stop / step) * step;
            } else if (step < 0) {
                start = Math.ceil(start * step) / step;
                stop = Math.floor(stop * step) / step;
            } else {
                break;
            }
            prestep = step;
        }
        return scale;
    };
    return scale;
}
function linear() {
    var scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    scale.copy = function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["copy"])(scale, linear());
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initRange"].apply(scale, arguments);
    return linearish(scale);
}
}}),
"[project]/node_modules/d3-scale/src/linear.js [app-ssr] (ecmascript) <export default as scaleLinear>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "scaleLinear": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/linear.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/mermaid/dist/quadrantDiagram-c759a472.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "diagram": (()=>diagram)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/mermaid-6dc72991.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/linear.js [app-ssr] (ecmascript) <export default as scaleLinear>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-ssr] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ts$2d$dedent$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ts-dedent/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@braintree/sanitize-url/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dompurify/dist/purify.es.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
var parser = function() {
    var o = function(k, v, o2, l) {
        for(o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);
        return o2;
    }, $V0 = [
        1,
        3
    ], $V1 = [
        1,
        4
    ], $V2 = [
        1,
        5
    ], $V3 = [
        1,
        6
    ], $V4 = [
        1,
        7
    ], $V5 = [
        1,
        5,
        13,
        15,
        17,
        19,
        20,
        25,
        27,
        28,
        29,
        30,
        31,
        32,
        33,
        34,
        37,
        38,
        40,
        41,
        42,
        43,
        44,
        45,
        46,
        47,
        48,
        49,
        50
    ], $V6 = [
        1,
        5,
        6,
        13,
        15,
        17,
        19,
        20,
        25,
        27,
        28,
        29,
        30,
        31,
        32,
        33,
        34,
        37,
        38,
        40,
        41,
        42,
        43,
        44,
        45,
        46,
        47,
        48,
        49,
        50
    ], $V7 = [
        32,
        33,
        34
    ], $V8 = [
        2,
        7
    ], $V9 = [
        1,
        13
    ], $Va = [
        1,
        17
    ], $Vb = [
        1,
        18
    ], $Vc = [
        1,
        19
    ], $Vd = [
        1,
        20
    ], $Ve = [
        1,
        21
    ], $Vf = [
        1,
        22
    ], $Vg = [
        1,
        23
    ], $Vh = [
        1,
        24
    ], $Vi = [
        1,
        25
    ], $Vj = [
        1,
        26
    ], $Vk = [
        1,
        27
    ], $Vl = [
        1,
        30
    ], $Vm = [
        1,
        31
    ], $Vn = [
        1,
        32
    ], $Vo = [
        1,
        33
    ], $Vp = [
        1,
        34
    ], $Vq = [
        1,
        35
    ], $Vr = [
        1,
        36
    ], $Vs = [
        1,
        37
    ], $Vt = [
        1,
        38
    ], $Vu = [
        1,
        39
    ], $Vv = [
        1,
        40
    ], $Vw = [
        1,
        41
    ], $Vx = [
        1,
        42
    ], $Vy = [
        1,
        57
    ], $Vz = [
        1,
        58
    ], $VA = [
        5,
        22,
        26,
        32,
        33,
        34,
        40,
        41,
        42,
        43,
        44,
        45,
        46,
        47,
        48,
        49,
        50,
        51
    ];
    var parser2 = {
        trace: function trace() {},
        yy: {},
        symbols_: {
            "error": 2,
            "start": 3,
            "eol": 4,
            "SPACE": 5,
            "QUADRANT": 6,
            "document": 7,
            "line": 8,
            "statement": 9,
            "axisDetails": 10,
            "quadrantDetails": 11,
            "points": 12,
            "title": 13,
            "title_value": 14,
            "acc_title": 15,
            "acc_title_value": 16,
            "acc_descr": 17,
            "acc_descr_value": 18,
            "acc_descr_multiline_value": 19,
            "section": 20,
            "text": 21,
            "point_start": 22,
            "point_x": 23,
            "point_y": 24,
            "X-AXIS": 25,
            "AXIS-TEXT-DELIMITER": 26,
            "Y-AXIS": 27,
            "QUADRANT_1": 28,
            "QUADRANT_2": 29,
            "QUADRANT_3": 30,
            "QUADRANT_4": 31,
            "NEWLINE": 32,
            "SEMI": 33,
            "EOF": 34,
            "alphaNumToken": 35,
            "textNoTagsToken": 36,
            "STR": 37,
            "MD_STR": 38,
            "alphaNum": 39,
            "PUNCTUATION": 40,
            "AMP": 41,
            "NUM": 42,
            "ALPHA": 43,
            "COMMA": 44,
            "PLUS": 45,
            "EQUALS": 46,
            "MULT": 47,
            "DOT": 48,
            "BRKT": 49,
            "UNDERSCORE": 50,
            "MINUS": 51,
            "$accept": 0,
            "$end": 1
        },
        terminals_: {
            2: "error",
            5: "SPACE",
            6: "QUADRANT",
            13: "title",
            14: "title_value",
            15: "acc_title",
            16: "acc_title_value",
            17: "acc_descr",
            18: "acc_descr_value",
            19: "acc_descr_multiline_value",
            20: "section",
            22: "point_start",
            23: "point_x",
            24: "point_y",
            25: "X-AXIS",
            26: "AXIS-TEXT-DELIMITER",
            27: "Y-AXIS",
            28: "QUADRANT_1",
            29: "QUADRANT_2",
            30: "QUADRANT_3",
            31: "QUADRANT_4",
            32: "NEWLINE",
            33: "SEMI",
            34: "EOF",
            37: "STR",
            38: "MD_STR",
            40: "PUNCTUATION",
            41: "AMP",
            42: "NUM",
            43: "ALPHA",
            44: "COMMA",
            45: "PLUS",
            46: "EQUALS",
            47: "MULT",
            48: "DOT",
            49: "BRKT",
            50: "UNDERSCORE",
            51: "MINUS"
        },
        productions_: [
            0,
            [
                3,
                2
            ],
            [
                3,
                2
            ],
            [
                3,
                2
            ],
            [
                7,
                0
            ],
            [
                7,
                2
            ],
            [
                8,
                2
            ],
            [
                9,
                0
            ],
            [
                9,
                2
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                12,
                4
            ],
            [
                10,
                4
            ],
            [
                10,
                3
            ],
            [
                10,
                2
            ],
            [
                10,
                4
            ],
            [
                10,
                3
            ],
            [
                10,
                2
            ],
            [
                11,
                2
            ],
            [
                11,
                2
            ],
            [
                11,
                2
            ],
            [
                11,
                2
            ],
            [
                4,
                1
            ],
            [
                4,
                1
            ],
            [
                4,
                1
            ],
            [
                21,
                1
            ],
            [
                21,
                2
            ],
            [
                21,
                1
            ],
            [
                21,
                1
            ],
            [
                39,
                1
            ],
            [
                39,
                2
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                35,
                1
            ],
            [
                36,
                1
            ],
            [
                36,
                1
            ],
            [
                36,
                1
            ]
        ],
        performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {
            var $0 = $$.length - 1;
            switch(yystate){
                case 12:
                    this.$ = $$[$0].trim();
                    yy.setDiagramTitle(this.$);
                    break;
                case 13:
                    this.$ = $$[$0].trim();
                    yy.setAccTitle(this.$);
                    break;
                case 14:
                case 15:
                    this.$ = $$[$0].trim();
                    yy.setAccDescription(this.$);
                    break;
                case 16:
                    yy.addSection($$[$0].substr(8));
                    this.$ = $$[$0].substr(8);
                    break;
                case 17:
                    yy.addPoint($$[$0 - 3], $$[$0 - 1], $$[$0]);
                    break;
                case 18:
                    yy.setXAxisLeftText($$[$0 - 2]);
                    yy.setXAxisRightText($$[$0]);
                    break;
                case 19:
                    $$[$0 - 1].text += " ⟶ ";
                    yy.setXAxisLeftText($$[$0 - 1]);
                    break;
                case 20:
                    yy.setXAxisLeftText($$[$0]);
                    break;
                case 21:
                    yy.setYAxisBottomText($$[$0 - 2]);
                    yy.setYAxisTopText($$[$0]);
                    break;
                case 22:
                    $$[$0 - 1].text += " ⟶ ";
                    yy.setYAxisBottomText($$[$0 - 1]);
                    break;
                case 23:
                    yy.setYAxisBottomText($$[$0]);
                    break;
                case 24:
                    yy.setQuadrant1Text($$[$0]);
                    break;
                case 25:
                    yy.setQuadrant2Text($$[$0]);
                    break;
                case 26:
                    yy.setQuadrant3Text($$[$0]);
                    break;
                case 27:
                    yy.setQuadrant4Text($$[$0]);
                    break;
                case 31:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 32:
                    this.$ = {
                        text: $$[$0 - 1].text + "" + $$[$0],
                        type: $$[$0 - 1].type
                    };
                    break;
                case 33:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 34:
                    this.$ = {
                        text: $$[$0],
                        type: "markdown"
                    };
                    break;
                case 35:
                    this.$ = $$[$0];
                    break;
                case 36:
                    this.$ = $$[$0 - 1] + "" + $$[$0];
                    break;
            }
        },
        table: [
            {
                3: 1,
                4: 2,
                5: $V0,
                6: $V1,
                32: $V2,
                33: $V3,
                34: $V4
            },
            {
                1: [
                    3
                ]
            },
            {
                3: 8,
                4: 2,
                5: $V0,
                6: $V1,
                32: $V2,
                33: $V3,
                34: $V4
            },
            {
                3: 9,
                4: 2,
                5: $V0,
                6: $V1,
                32: $V2,
                33: $V3,
                34: $V4
            },
            o($V5, [
                2,
                4
            ], {
                7: 10
            }),
            o($V6, [
                2,
                28
            ]),
            o($V6, [
                2,
                29
            ]),
            o($V6, [
                2,
                30
            ]),
            {
                1: [
                    2,
                    1
                ]
            },
            {
                1: [
                    2,
                    2
                ]
            },
            o($V7, $V8, {
                8: 11,
                9: 12,
                10: 14,
                11: 15,
                12: 16,
                21: 28,
                35: 29,
                1: [
                    2,
                    3
                ],
                5: $V9,
                13: $Va,
                15: $Vb,
                17: $Vc,
                19: $Vd,
                20: $Ve,
                25: $Vf,
                27: $Vg,
                28: $Vh,
                29: $Vi,
                30: $Vj,
                31: $Vk,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            }),
            o($V5, [
                2,
                5
            ]),
            {
                4: 43,
                32: $V2,
                33: $V3,
                34: $V4
            },
            o($V7, $V8, {
                10: 14,
                11: 15,
                12: 16,
                21: 28,
                35: 29,
                9: 44,
                5: $V9,
                13: $Va,
                15: $Vb,
                17: $Vc,
                19: $Vd,
                20: $Ve,
                25: $Vf,
                27: $Vg,
                28: $Vh,
                29: $Vi,
                30: $Vj,
                31: $Vk,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            }),
            o($V7, [
                2,
                9
            ]),
            o($V7, [
                2,
                10
            ]),
            o($V7, [
                2,
                11
            ]),
            {
                14: [
                    1,
                    45
                ]
            },
            {
                16: [
                    1,
                    46
                ]
            },
            {
                18: [
                    1,
                    47
                ]
            },
            o($V7, [
                2,
                15
            ]),
            o($V7, [
                2,
                16
            ]),
            {
                21: 48,
                35: 29,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            },
            {
                21: 49,
                35: 29,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            },
            {
                21: 50,
                35: 29,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            },
            {
                21: 51,
                35: 29,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            },
            {
                21: 52,
                35: 29,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            },
            {
                21: 53,
                35: 29,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            },
            {
                5: $Vy,
                22: [
                    1,
                    54
                ],
                35: 56,
                36: 55,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            },
            o($VA, [
                2,
                31
            ]),
            o($VA, [
                2,
                33
            ]),
            o($VA, [
                2,
                34
            ]),
            o($VA, [
                2,
                37
            ]),
            o($VA, [
                2,
                38
            ]),
            o($VA, [
                2,
                39
            ]),
            o($VA, [
                2,
                40
            ]),
            o($VA, [
                2,
                41
            ]),
            o($VA, [
                2,
                42
            ]),
            o($VA, [
                2,
                43
            ]),
            o($VA, [
                2,
                44
            ]),
            o($VA, [
                2,
                45
            ]),
            o($VA, [
                2,
                46
            ]),
            o($VA, [
                2,
                47
            ]),
            o($V5, [
                2,
                6
            ]),
            o($V7, [
                2,
                8
            ]),
            o($V7, [
                2,
                12
            ]),
            o($V7, [
                2,
                13
            ]),
            o($V7, [
                2,
                14
            ]),
            o($V7, [
                2,
                20
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                26: [
                    1,
                    59
                ],
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            o($V7, [
                2,
                23
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                26: [
                    1,
                    60
                ],
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            o($V7, [
                2,
                24
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            o($V7, [
                2,
                25
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            o($V7, [
                2,
                26
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            o($V7, [
                2,
                27
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            {
                23: [
                    1,
                    61
                ]
            },
            o($VA, [
                2,
                32
            ]),
            o($VA, [
                2,
                48
            ]),
            o($VA, [
                2,
                49
            ]),
            o($VA, [
                2,
                50
            ]),
            o($V7, [
                2,
                19
            ], {
                35: 29,
                21: 62,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            }),
            o($V7, [
                2,
                22
            ], {
                35: 29,
                21: 63,
                37: $Vl,
                38: $Vm,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx
            }),
            {
                24: [
                    1,
                    64
                ]
            },
            o($V7, [
                2,
                18
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            o($V7, [
                2,
                21
            ], {
                36: 55,
                35: 56,
                5: $Vy,
                40: $Vn,
                41: $Vo,
                42: $Vp,
                43: $Vq,
                44: $Vr,
                45: $Vs,
                46: $Vt,
                47: $Vu,
                48: $Vv,
                49: $Vw,
                50: $Vx,
                51: $Vz
            }),
            o($V7, [
                2,
                17
            ])
        ],
        defaultActions: {
            8: [
                2,
                1
            ],
            9: [
                2,
                2
            ]
        },
        parseError: function parseError(str, hash) {
            if (hash.recoverable) {
                this.trace(str);
            } else {
                var error = new Error(str);
                error.hash = hash;
                throw error;
            }
        },
        parse: function parse(input) {
            var self = this, stack = [
                0
            ], tstack = [], vstack = [
                null
            ], lstack = [], table = this.table, yytext = "", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;
            var args = lstack.slice.call(arguments, 1);
            var lexer2 = Object.create(this.lexer);
            var sharedState = {
                yy: {}
            };
            for(var k in this.yy){
                if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
                    sharedState.yy[k] = this.yy[k];
                }
            }
            lexer2.setInput(input, sharedState.yy);
            sharedState.yy.lexer = lexer2;
            sharedState.yy.parser = this;
            if (typeof lexer2.yylloc == "undefined") {
                lexer2.yylloc = {};
            }
            var yyloc = lexer2.yylloc;
            lstack.push(yyloc);
            var ranges = lexer2.options && lexer2.options.ranges;
            if (typeof sharedState.yy.parseError === "function") {
                this.parseError = sharedState.yy.parseError;
            } else {
                this.parseError = Object.getPrototypeOf(this).parseError;
            }
            function lex() {
                var token;
                token = tstack.pop() || lexer2.lex() || EOF;
                if (typeof token !== "number") {
                    if (token instanceof Array) {
                        tstack = token;
                        token = tstack.pop();
                    }
                    token = self.symbols_[token] || token;
                }
                return token;
            }
            var symbol, state, action, r, yyval = {}, p, len, newState, expected;
            while(true){
                state = stack[stack.length - 1];
                if (this.defaultActions[state]) {
                    action = this.defaultActions[state];
                } else {
                    if (symbol === null || typeof symbol == "undefined") {
                        symbol = lex();
                    }
                    action = table[state] && table[state][symbol];
                }
                if (typeof action === "undefined" || !action.length || !action[0]) {
                    var errStr = "";
                    expected = [];
                    for(p in table[state]){
                        if (this.terminals_[p] && p > TERROR) {
                            expected.push("'" + this.terminals_[p] + "'");
                        }
                    }
                    if (lexer2.showPosition) {
                        errStr = "Parse error on line " + (yylineno + 1) + ":\n" + lexer2.showPosition() + "\nExpecting " + expected.join(", ") + ", got '" + (this.terminals_[symbol] || symbol) + "'";
                    } else {
                        errStr = "Parse error on line " + (yylineno + 1) + ": Unexpected " + (symbol == EOF ? "end of input" : "'" + (this.terminals_[symbol] || symbol) + "'");
                    }
                    this.parseError(errStr, {
                        text: lexer2.match,
                        token: this.terminals_[symbol] || symbol,
                        line: lexer2.yylineno,
                        loc: yyloc,
                        expected
                    });
                }
                if (action[0] instanceof Array && action.length > 1) {
                    throw new Error("Parse Error: multiple actions possible at state: " + state + ", token: " + symbol);
                }
                switch(action[0]){
                    case 1:
                        stack.push(symbol);
                        vstack.push(lexer2.yytext);
                        lstack.push(lexer2.yylloc);
                        stack.push(action[1]);
                        symbol = null;
                        {
                            yyleng = lexer2.yyleng;
                            yytext = lexer2.yytext;
                            yylineno = lexer2.yylineno;
                            yyloc = lexer2.yylloc;
                        }
                        break;
                    case 2:
                        len = this.productions_[action[1]][1];
                        yyval.$ = vstack[vstack.length - len];
                        yyval._$ = {
                            first_line: lstack[lstack.length - (len || 1)].first_line,
                            last_line: lstack[lstack.length - 1].last_line,
                            first_column: lstack[lstack.length - (len || 1)].first_column,
                            last_column: lstack[lstack.length - 1].last_column
                        };
                        if (ranges) {
                            yyval._$.range = [
                                lstack[lstack.length - (len || 1)].range[0],
                                lstack[lstack.length - 1].range[1]
                            ];
                        }
                        r = this.performAction.apply(yyval, [
                            yytext,
                            yyleng,
                            yylineno,
                            sharedState.yy,
                            action[1],
                            vstack,
                            lstack
                        ].concat(args));
                        if (typeof r !== "undefined") {
                            return r;
                        }
                        if (len) {
                            stack = stack.slice(0, -1 * len * 2);
                            vstack = vstack.slice(0, -1 * len);
                            lstack = lstack.slice(0, -1 * len);
                        }
                        stack.push(this.productions_[action[1]][0]);
                        vstack.push(yyval.$);
                        lstack.push(yyval._$);
                        newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
                        stack.push(newState);
                        break;
                    case 3:
                        return true;
                }
            }
            return true;
        }
    };
    var lexer = function() {
        var lexer2 = {
            EOF: 1,
            parseError: function parseError(str, hash) {
                if (this.yy.parser) {
                    this.yy.parser.parseError(str, hash);
                } else {
                    throw new Error(str);
                }
            },
            // resets the lexer, sets new input
            setInput: function(input, yy) {
                this.yy = yy || this.yy || {};
                this._input = input;
                this._more = this._backtrack = this.done = false;
                this.yylineno = this.yyleng = 0;
                this.yytext = this.matched = this.match = "";
                this.conditionStack = [
                    "INITIAL"
                ];
                this.yylloc = {
                    first_line: 1,
                    first_column: 0,
                    last_line: 1,
                    last_column: 0
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        0,
                        0
                    ];
                }
                this.offset = 0;
                return this;
            },
            // consumes and returns one char from the input
            input: function() {
                var ch = this._input[0];
                this.yytext += ch;
                this.yyleng++;
                this.offset++;
                this.match += ch;
                this.matched += ch;
                var lines = ch.match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno++;
                    this.yylloc.last_line++;
                } else {
                    this.yylloc.last_column++;
                }
                if (this.options.ranges) {
                    this.yylloc.range[1]++;
                }
                this._input = this._input.slice(1);
                return ch;
            },
            // unshifts one char (or a string) into the input
            unput: function(ch) {
                var len = ch.length;
                var lines = ch.split(/(?:\r\n?|\n)/g);
                this._input = ch + this._input;
                this.yytext = this.yytext.substr(0, this.yytext.length - len);
                this.offset -= len;
                var oldLines = this.match.split(/(?:\r\n?|\n)/g);
                this.match = this.match.substr(0, this.match.length - 1);
                this.matched = this.matched.substr(0, this.matched.length - 1);
                if (lines.length - 1) {
                    this.yylineno -= lines.length - 1;
                }
                var r = this.yylloc.range;
                this.yylloc = {
                    first_line: this.yylloc.first_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.first_column,
                    last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        r[0],
                        r[0] + this.yyleng - len
                    ];
                }
                this.yyleng = this.yytext.length;
                return this;
            },
            // When called from action, caches matched text and appends it on next action
            more: function() {
                this._more = true;
                return this;
            },
            // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
            reject: function() {
                if (this.options.backtrack_lexer) {
                    this._backtrack = true;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
                return this;
            },
            // retain first n characters of the match
            less: function(n) {
                this.unput(this.match.slice(n));
            },
            // displays already matched input, i.e. for error messages
            pastInput: function() {
                var past = this.matched.substr(0, this.matched.length - this.match.length);
                return (past.length > 20 ? "..." : "") + past.substr(-20).replace(/\n/g, "");
            },
            // displays upcoming input, i.e. for error messages
            upcomingInput: function() {
                var next = this.match;
                if (next.length < 20) {
                    next += this._input.substr(0, 20 - next.length);
                }
                return (next.substr(0, 20) + (next.length > 20 ? "..." : "")).replace(/\n/g, "");
            },
            // displays the character position where the lexing error occurred, i.e. for error messages
            showPosition: function() {
                var pre = this.pastInput();
                var c = new Array(pre.length + 1).join("-");
                return pre + this.upcomingInput() + "\n" + c + "^";
            },
            // test the lexed token: return FALSE when not a match, otherwise return token
            test_match: function(match, indexed_rule) {
                var token, lines, backup;
                if (this.options.backtrack_lexer) {
                    backup = {
                        yylineno: this.yylineno,
                        yylloc: {
                            first_line: this.yylloc.first_line,
                            last_line: this.last_line,
                            first_column: this.yylloc.first_column,
                            last_column: this.yylloc.last_column
                        },
                        yytext: this.yytext,
                        match: this.match,
                        matches: this.matches,
                        matched: this.matched,
                        yyleng: this.yyleng,
                        offset: this.offset,
                        _more: this._more,
                        _input: this._input,
                        yy: this.yy,
                        conditionStack: this.conditionStack.slice(0),
                        done: this.done
                    };
                    if (this.options.ranges) {
                        backup.yylloc.range = this.yylloc.range.slice(0);
                    }
                }
                lines = match[0].match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno += lines.length;
                }
                this.yylloc = {
                    first_line: this.yylloc.last_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.last_column,
                    last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + match[0].length
                };
                this.yytext += match[0];
                this.match += match[0];
                this.matches = match;
                this.yyleng = this.yytext.length;
                if (this.options.ranges) {
                    this.yylloc.range = [
                        this.offset,
                        this.offset += this.yyleng
                    ];
                }
                this._more = false;
                this._backtrack = false;
                this._input = this._input.slice(match[0].length);
                this.matched += match[0];
                token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
                if (this.done && this._input) {
                    this.done = false;
                }
                if (token) {
                    return token;
                } else if (this._backtrack) {
                    for(var k in backup){
                        this[k] = backup[k];
                    }
                    return false;
                }
                return false;
            },
            // return next match in input
            next: function() {
                if (this.done) {
                    return this.EOF;
                }
                if (!this._input) {
                    this.done = true;
                }
                var token, match, tempMatch, index;
                if (!this._more) {
                    this.yytext = "";
                    this.match = "";
                }
                var rules = this._currentRules();
                for(var i = 0; i < rules.length; i++){
                    tempMatch = this._input.match(this.rules[rules[i]]);
                    if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                        match = tempMatch;
                        index = i;
                        if (this.options.backtrack_lexer) {
                            token = this.test_match(tempMatch, rules[i]);
                            if (token !== false) {
                                return token;
                            } else if (this._backtrack) {
                                match = false;
                                continue;
                            } else {
                                return false;
                            }
                        } else if (!this.options.flex) {
                            break;
                        }
                    }
                }
                if (match) {
                    token = this.test_match(match, rules[index]);
                    if (token !== false) {
                        return token;
                    }
                    return false;
                }
                if (this._input === "") {
                    return this.EOF;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". Unrecognized text.\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
            },
            // return next match that has a token
            lex: function lex() {
                var r = this.next();
                if (r) {
                    return r;
                } else {
                    return this.lex();
                }
            },
            // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
            begin: function begin(condition) {
                this.conditionStack.push(condition);
            },
            // pop the previously active lexer condition state off the condition stack
            popState: function popState() {
                var n = this.conditionStack.length - 1;
                if (n > 0) {
                    return this.conditionStack.pop();
                } else {
                    return this.conditionStack[0];
                }
            },
            // produce the lexer rule set which is active for the currently active lexer condition state
            _currentRules: function _currentRules() {
                if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
                    return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
                } else {
                    return this.conditions["INITIAL"].rules;
                }
            },
            // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
            topState: function topState(n) {
                n = this.conditionStack.length - 1 - Math.abs(n || 0);
                if (n >= 0) {
                    return this.conditionStack[n];
                } else {
                    return "INITIAL";
                }
            },
            // alias for begin(condition)
            pushState: function pushState(condition) {
                this.begin(condition);
            },
            // return the number of states currently on the stack
            stateStackSize: function stateStackSize() {
                return this.conditionStack.length;
            },
            options: {
                "case-insensitive": true
            },
            performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {
                switch($avoiding_name_collisions){
                    case 0:
                        break;
                    case 1:
                        break;
                    case 2:
                        return 32;
                    case 3:
                        break;
                    case 4:
                        this.begin("title");
                        return 13;
                    case 5:
                        this.popState();
                        return "title_value";
                    case 6:
                        this.begin("acc_title");
                        return 15;
                    case 7:
                        this.popState();
                        return "acc_title_value";
                    case 8:
                        this.begin("acc_descr");
                        return 17;
                    case 9:
                        this.popState();
                        return "acc_descr_value";
                    case 10:
                        this.begin("acc_descr_multiline");
                        break;
                    case 11:
                        this.popState();
                        break;
                    case 12:
                        return "acc_descr_multiline_value";
                    case 13:
                        return 25;
                    case 14:
                        return 27;
                    case 15:
                        return 26;
                    case 16:
                        return 28;
                    case 17:
                        return 29;
                    case 18:
                        return 30;
                    case 19:
                        return 31;
                    case 20:
                        this.begin("md_string");
                        break;
                    case 21:
                        return "MD_STR";
                    case 22:
                        this.popState();
                        break;
                    case 23:
                        this.begin("string");
                        break;
                    case 24:
                        this.popState();
                        break;
                    case 25:
                        return "STR";
                    case 26:
                        this.begin("point_start");
                        return 22;
                    case 27:
                        this.begin("point_x");
                        return 23;
                    case 28:
                        this.popState();
                        break;
                    case 29:
                        this.popState();
                        this.begin("point_y");
                        break;
                    case 30:
                        this.popState();
                        return 24;
                    case 31:
                        return 6;
                    case 32:
                        return 43;
                    case 33:
                        return "COLON";
                    case 34:
                        return 45;
                    case 35:
                        return 44;
                    case 36:
                        return 46;
                    case 37:
                        return 46;
                    case 38:
                        return 47;
                    case 39:
                        return 49;
                    case 40:
                        return 50;
                    case 41:
                        return 48;
                    case 42:
                        return 41;
                    case 43:
                        return 51;
                    case 44:
                        return 42;
                    case 45:
                        return 5;
                    case 46:
                        return 33;
                    case 47:
                        return 40;
                    case 48:
                        return 34;
                }
            },
            rules: [
                /^(?:%%(?!\{)[^\n]*)/i,
                /^(?:[^\}]%%[^\n]*)/i,
                /^(?:[\n\r]+)/i,
                /^(?:%%[^\n]*)/i,
                /^(?:title\b)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accTitle\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*\{\s*)/i,
                /^(?:[\}])/i,
                /^(?:[^\}]*)/i,
                /^(?: *x-axis *)/i,
                /^(?: *y-axis *)/i,
                /^(?: *--+> *)/i,
                /^(?: *quadrant-1 *)/i,
                /^(?: *quadrant-2 *)/i,
                /^(?: *quadrant-3 *)/i,
                /^(?: *quadrant-4 *)/i,
                /^(?:["][`])/i,
                /^(?:[^`"]+)/i,
                /^(?:[`]["])/i,
                /^(?:["])/i,
                /^(?:["])/i,
                /^(?:[^"]*)/i,
                /^(?:\s*:\s*\[\s*)/i,
                /^(?:(1)|(0(.\d+)?))/i,
                /^(?:\s*\] *)/i,
                /^(?:\s*,\s*)/i,
                /^(?:(1)|(0(.\d+)?))/i,
                /^(?: *quadrantChart *)/i,
                /^(?:[A-Za-z]+)/i,
                /^(?::)/i,
                /^(?:\+)/i,
                /^(?:,)/i,
                /^(?:=)/i,
                /^(?:=)/i,
                /^(?:\*)/i,
                /^(?:#)/i,
                /^(?:[\_])/i,
                /^(?:\.)/i,
                /^(?:&)/i,
                /^(?:-)/i,
                /^(?:[0-9]+)/i,
                /^(?:\s)/i,
                /^(?:;)/i,
                /^(?:[!"#$%&'*+,-.`?\\_/])/i,
                /^(?:$)/i
            ],
            conditions: {
                "point_y": {
                    "rules": [
                        30
                    ],
                    "inclusive": false
                },
                "point_x": {
                    "rules": [
                        29
                    ],
                    "inclusive": false
                },
                "point_start": {
                    "rules": [
                        27,
                        28
                    ],
                    "inclusive": false
                },
                "acc_descr_multiline": {
                    "rules": [
                        11,
                        12
                    ],
                    "inclusive": false
                },
                "acc_descr": {
                    "rules": [
                        9
                    ],
                    "inclusive": false
                },
                "acc_title": {
                    "rules": [
                        7
                    ],
                    "inclusive": false
                },
                "title": {
                    "rules": [
                        5
                    ],
                    "inclusive": false
                },
                "md_string": {
                    "rules": [
                        21,
                        22
                    ],
                    "inclusive": false
                },
                "string": {
                    "rules": [
                        24,
                        25
                    ],
                    "inclusive": false
                },
                "INITIAL": {
                    "rules": [
                        0,
                        1,
                        2,
                        3,
                        4,
                        6,
                        8,
                        10,
                        13,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19,
                        20,
                        23,
                        26,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45,
                        46,
                        47,
                        48
                    ],
                    "inclusive": true
                }
            }
        };
        return lexer2;
    }();
    parser2.lexer = lexer;
    function Parser() {
        this.yy = {};
    }
    Parser.prototype = parser2;
    parser2.Parser = Parser;
    return new Parser();
}();
parser.parser = parser;
const parser$1 = parser;
const defaultThemeVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["E"])();
class QuadrantBuilder {
    constructor(){
        this.config = this.getDefaultConfig();
        this.themeConfig = this.getDefaultThemeConfig();
        this.data = this.getDefaultData();
    }
    getDefaultData() {
        return {
            titleText: "",
            quadrant1Text: "",
            quadrant2Text: "",
            quadrant3Text: "",
            quadrant4Text: "",
            xAxisLeftText: "",
            xAxisRightText: "",
            yAxisBottomText: "",
            yAxisTopText: "",
            points: []
        };
    }
    getDefaultConfig() {
        var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r;
        return {
            showXAxis: true,
            showYAxis: true,
            showTitle: true,
            chartHeight: ((_a = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _a.chartWidth) || 500,
            chartWidth: ((_b = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _b.chartHeight) || 500,
            titlePadding: ((_c = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _c.titlePadding) || 10,
            titleFontSize: ((_d = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _d.titleFontSize) || 20,
            quadrantPadding: ((_e = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _e.quadrantPadding) || 5,
            xAxisLabelPadding: ((_f = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _f.xAxisLabelPadding) || 5,
            yAxisLabelPadding: ((_g = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _g.yAxisLabelPadding) || 5,
            xAxisLabelFontSize: ((_h = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _h.xAxisLabelFontSize) || 16,
            yAxisLabelFontSize: ((_i = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _i.yAxisLabelFontSize) || 16,
            quadrantLabelFontSize: ((_j = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _j.quadrantLabelFontSize) || 16,
            quadrantTextTopPadding: ((_k = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _k.quadrantTextTopPadding) || 5,
            pointTextPadding: ((_l = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _l.pointTextPadding) || 5,
            pointLabelFontSize: ((_m = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _m.pointLabelFontSize) || 12,
            pointRadius: ((_n = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _n.pointRadius) || 5,
            xAxisPosition: ((_o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _o.xAxisPosition) || "top",
            yAxisPosition: ((_p = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _p.yAxisPosition) || "left",
            quadrantInternalBorderStrokeWidth: ((_q = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _q.quadrantInternalBorderStrokeWidth) || 1,
            quadrantExternalBorderStrokeWidth: ((_r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["B"].quadrantChart) == null ? void 0 : _r.quadrantExternalBorderStrokeWidth) || 2
        };
    }
    getDefaultThemeConfig() {
        return {
            quadrant1Fill: defaultThemeVariables.quadrant1Fill,
            quadrant2Fill: defaultThemeVariables.quadrant2Fill,
            quadrant3Fill: defaultThemeVariables.quadrant3Fill,
            quadrant4Fill: defaultThemeVariables.quadrant4Fill,
            quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,
            quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,
            quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,
            quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,
            quadrantPointFill: defaultThemeVariables.quadrantPointFill,
            quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,
            quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,
            quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,
            quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,
            quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,
            quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill
        };
    }
    clear() {
        this.config = this.getDefaultConfig();
        this.themeConfig = this.getDefaultThemeConfig();
        this.data = this.getDefaultData();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["l"].info("clear called");
    }
    setData(data) {
        this.data = {
            ...this.data,
            ...data
        };
    }
    addPoints(points) {
        this.data.points = [
            ...points,
            ...this.data.points
        ];
    }
    setConfig(config2) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["l"].trace("setConfig called with: ", config2);
        this.config = {
            ...this.config,
            ...config2
        };
    }
    setThemeConfig(themeConfig) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["l"].trace("setThemeConfig called with: ", themeConfig);
        this.themeConfig = {
            ...this.themeConfig,
            ...themeConfig
        };
    }
    calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle) {
        const xAxisSpaceCalculation = this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;
        const xAxisSpace = {
            top: xAxisPosition === "top" && showXAxis ? xAxisSpaceCalculation : 0,
            bottom: xAxisPosition === "bottom" && showXAxis ? xAxisSpaceCalculation : 0
        };
        const yAxisSpaceCalculation = this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;
        const yAxisSpace = {
            left: this.config.yAxisPosition === "left" && showYAxis ? yAxisSpaceCalculation : 0,
            right: this.config.yAxisPosition === "right" && showYAxis ? yAxisSpaceCalculation : 0
        };
        const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;
        const titleSpace = {
            top: showTitle ? titleSpaceCalculation : 0
        };
        const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;
        const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;
        const quadrantWidth = this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;
        const quadrantHeight = this.config.chartHeight - this.config.quadrantPadding * 2 - xAxisSpace.top - xAxisSpace.bottom - titleSpace.top;
        const quadrantHalfWidth = quadrantWidth / 2;
        const quadrantHalfHeight = quadrantHeight / 2;
        const quadrantSpace = {
            quadrantLeft,
            quadrantTop,
            quadrantWidth,
            quadrantHalfWidth,
            quadrantHeight,
            quadrantHalfHeight
        };
        return {
            xAxisSpace,
            yAxisSpace,
            titleSpace,
            quadrantSpace
        };
    }
    getAxisLabels(xAxisPosition, showXAxis, showYAxis, spaceData) {
        const { quadrantSpace, titleSpace } = spaceData;
        const { quadrantHalfHeight, quadrantHeight, quadrantLeft, quadrantHalfWidth, quadrantTop, quadrantWidth } = quadrantSpace;
        const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);
        const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);
        const axisLabels = [];
        if (this.data.xAxisLeftText && showXAxis) {
            axisLabels.push({
                text: this.data.xAxisLeftText,
                fill: this.themeConfig.quadrantXAxisTextFill,
                x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),
                y: xAxisPosition === "top" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,
                fontSize: this.config.xAxisLabelFontSize,
                verticalPos: drawXAxisLabelsInMiddle ? "center" : "left",
                horizontalPos: "top",
                rotation: 0
            });
        }
        if (this.data.xAxisRightText && showXAxis) {
            axisLabels.push({
                text: this.data.xAxisRightText,
                fill: this.themeConfig.quadrantXAxisTextFill,
                x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),
                y: xAxisPosition === "top" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,
                fontSize: this.config.xAxisLabelFontSize,
                verticalPos: drawXAxisLabelsInMiddle ? "center" : "left",
                horizontalPos: "top",
                rotation: 0
            });
        }
        if (this.data.yAxisBottomText && showYAxis) {
            axisLabels.push({
                text: this.data.yAxisBottomText,
                fill: this.themeConfig.quadrantYAxisTextFill,
                x: this.config.yAxisPosition === "left" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,
                y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),
                fontSize: this.config.yAxisLabelFontSize,
                verticalPos: drawYAxisLabelsInMiddle ? "center" : "left",
                horizontalPos: "top",
                rotation: -90
            });
        }
        if (this.data.yAxisTopText && showYAxis) {
            axisLabels.push({
                text: this.data.yAxisTopText,
                fill: this.themeConfig.quadrantYAxisTextFill,
                x: this.config.yAxisPosition === "left" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,
                y: quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),
                fontSize: this.config.yAxisLabelFontSize,
                verticalPos: drawYAxisLabelsInMiddle ? "center" : "left",
                horizontalPos: "top",
                rotation: -90
            });
        }
        return axisLabels;
    }
    getQuadrants(spaceData) {
        const { quadrantSpace } = spaceData;
        const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;
        const quadrants = [
            {
                text: {
                    text: this.data.quadrant1Text,
                    fill: this.themeConfig.quadrant1TextFill,
                    x: 0,
                    y: 0,
                    fontSize: this.config.quadrantLabelFontSize,
                    verticalPos: "center",
                    horizontalPos: "middle",
                    rotation: 0
                },
                x: quadrantLeft + quadrantHalfWidth,
                y: quadrantTop,
                width: quadrantHalfWidth,
                height: quadrantHalfHeight,
                fill: this.themeConfig.quadrant1Fill
            },
            {
                text: {
                    text: this.data.quadrant2Text,
                    fill: this.themeConfig.quadrant2TextFill,
                    x: 0,
                    y: 0,
                    fontSize: this.config.quadrantLabelFontSize,
                    verticalPos: "center",
                    horizontalPos: "middle",
                    rotation: 0
                },
                x: quadrantLeft,
                y: quadrantTop,
                width: quadrantHalfWidth,
                height: quadrantHalfHeight,
                fill: this.themeConfig.quadrant2Fill
            },
            {
                text: {
                    text: this.data.quadrant3Text,
                    fill: this.themeConfig.quadrant3TextFill,
                    x: 0,
                    y: 0,
                    fontSize: this.config.quadrantLabelFontSize,
                    verticalPos: "center",
                    horizontalPos: "middle",
                    rotation: 0
                },
                x: quadrantLeft,
                y: quadrantTop + quadrantHalfHeight,
                width: quadrantHalfWidth,
                height: quadrantHalfHeight,
                fill: this.themeConfig.quadrant3Fill
            },
            {
                text: {
                    text: this.data.quadrant4Text,
                    fill: this.themeConfig.quadrant4TextFill,
                    x: 0,
                    y: 0,
                    fontSize: this.config.quadrantLabelFontSize,
                    verticalPos: "center",
                    horizontalPos: "middle",
                    rotation: 0
                },
                x: quadrantLeft + quadrantHalfWidth,
                y: quadrantTop + quadrantHalfHeight,
                width: quadrantHalfWidth,
                height: quadrantHalfHeight,
                fill: this.themeConfig.quadrant4Fill
            }
        ];
        for (const quadrant of quadrants){
            quadrant.text.x = quadrant.x + quadrant.width / 2;
            if (this.data.points.length === 0) {
                quadrant.text.y = quadrant.y + quadrant.height / 2;
                quadrant.text.horizontalPos = "middle";
            } else {
                quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;
                quadrant.text.horizontalPos = "top";
            }
        }
        return quadrants;
    }
    getQuadrantPoints(spaceData) {
        const { quadrantSpace } = spaceData;
        const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;
        const xAxis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__["scaleLinear"])().domain([
            0,
            1
        ]).range([
            quadrantLeft,
            quadrantWidth + quadrantLeft
        ]);
        const yAxis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__["scaleLinear"])().domain([
            0,
            1
        ]).range([
            quadrantHeight + quadrantTop,
            quadrantTop
        ]);
        const points = this.data.points.map((point)=>{
            const props = {
                x: xAxis(point.x),
                y: yAxis(point.y),
                fill: this.themeConfig.quadrantPointFill,
                radius: this.config.pointRadius,
                text: {
                    text: point.text,
                    fill: this.themeConfig.quadrantPointTextFill,
                    x: xAxis(point.x),
                    y: yAxis(point.y) + this.config.pointTextPadding,
                    verticalPos: "center",
                    horizontalPos: "top",
                    fontSize: this.config.pointLabelFontSize,
                    rotation: 0
                }
            };
            return props;
        });
        return points;
    }
    getBorders(spaceData) {
        const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;
        const { quadrantSpace } = spaceData;
        const { quadrantHalfHeight, quadrantHeight, quadrantLeft, quadrantHalfWidth, quadrantTop, quadrantWidth } = quadrantSpace;
        const borderLines = [
            // top border
            {
                strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,
                strokeWidth: this.config.quadrantExternalBorderStrokeWidth,
                x1: quadrantLeft - halfExternalBorderWidth,
                y1: quadrantTop,
                x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,
                y2: quadrantTop
            },
            // right border
            {
                strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,
                strokeWidth: this.config.quadrantExternalBorderStrokeWidth,
                x1: quadrantLeft + quadrantWidth,
                y1: quadrantTop + halfExternalBorderWidth,
                x2: quadrantLeft + quadrantWidth,
                y2: quadrantTop + quadrantHeight - halfExternalBorderWidth
            },
            // bottom border
            {
                strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,
                strokeWidth: this.config.quadrantExternalBorderStrokeWidth,
                x1: quadrantLeft - halfExternalBorderWidth,
                y1: quadrantTop + quadrantHeight,
                x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,
                y2: quadrantTop + quadrantHeight
            },
            // left border
            {
                strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,
                strokeWidth: this.config.quadrantExternalBorderStrokeWidth,
                x1: quadrantLeft,
                y1: quadrantTop + halfExternalBorderWidth,
                x2: quadrantLeft,
                y2: quadrantTop + quadrantHeight - halfExternalBorderWidth
            },
            // vertical inner border
            {
                strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,
                strokeWidth: this.config.quadrantInternalBorderStrokeWidth,
                x1: quadrantLeft + quadrantHalfWidth,
                y1: quadrantTop + halfExternalBorderWidth,
                x2: quadrantLeft + quadrantHalfWidth,
                y2: quadrantTop + quadrantHeight - halfExternalBorderWidth
            },
            // horizontal inner border
            {
                strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,
                strokeWidth: this.config.quadrantInternalBorderStrokeWidth,
                x1: quadrantLeft + halfExternalBorderWidth,
                y1: quadrantTop + quadrantHalfHeight,
                x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,
                y2: quadrantTop + quadrantHalfHeight
            }
        ];
        return borderLines;
    }
    getTitle(showTitle) {
        if (showTitle) {
            return {
                text: this.data.titleText,
                fill: this.themeConfig.quadrantTitleFill,
                fontSize: this.config.titleFontSize,
                horizontalPos: "top",
                verticalPos: "center",
                rotation: 0,
                y: this.config.titlePadding,
                x: this.config.chartWidth / 2
            };
        }
        return;
    }
    build() {
        const showXAxis = this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);
        const showYAxis = this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);
        const showTitle = this.config.showTitle && !!this.data.titleText;
        const xAxisPosition = this.data.points.length > 0 ? "bottom" : this.config.xAxisPosition;
        const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);
        return {
            points: this.getQuadrantPoints(calculatedSpace),
            quadrants: this.getQuadrants(calculatedSpace),
            axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),
            borderLines: this.getBorders(calculatedSpace),
            title: this.getTitle(showTitle)
        };
    }
}
const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])();
function textSanitizer(text) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["d"])(text.trim(), config);
}
const quadrantBuilder = new QuadrantBuilder();
function setQuadrant1Text(textObj) {
    quadrantBuilder.setData({
        quadrant1Text: textSanitizer(textObj.text)
    });
}
function setQuadrant2Text(textObj) {
    quadrantBuilder.setData({
        quadrant2Text: textSanitizer(textObj.text)
    });
}
function setQuadrant3Text(textObj) {
    quadrantBuilder.setData({
        quadrant3Text: textSanitizer(textObj.text)
    });
}
function setQuadrant4Text(textObj) {
    quadrantBuilder.setData({
        quadrant4Text: textSanitizer(textObj.text)
    });
}
function setXAxisLeftText(textObj) {
    quadrantBuilder.setData({
        xAxisLeftText: textSanitizer(textObj.text)
    });
}
function setXAxisRightText(textObj) {
    quadrantBuilder.setData({
        xAxisRightText: textSanitizer(textObj.text)
    });
}
function setYAxisTopText(textObj) {
    quadrantBuilder.setData({
        yAxisTopText: textSanitizer(textObj.text)
    });
}
function setYAxisBottomText(textObj) {
    quadrantBuilder.setData({
        yAxisBottomText: textSanitizer(textObj.text)
    });
}
function addPoint(textObj, x, y) {
    quadrantBuilder.addPoints([
        {
            x,
            y,
            text: textSanitizer(textObj.text)
        }
    ]);
}
function setWidth(width) {
    quadrantBuilder.setConfig({
        chartWidth: width
    });
}
function setHeight(height) {
    quadrantBuilder.setConfig({
        chartHeight: height
    });
}
function getQuadrantData() {
    const config2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])();
    const { themeVariables, quadrantChart: quadrantChartConfig } = config2;
    if (quadrantChartConfig) {
        quadrantBuilder.setConfig(quadrantChartConfig);
    }
    quadrantBuilder.setThemeConfig({
        quadrant1Fill: themeVariables.quadrant1Fill,
        quadrant2Fill: themeVariables.quadrant2Fill,
        quadrant3Fill: themeVariables.quadrant3Fill,
        quadrant4Fill: themeVariables.quadrant4Fill,
        quadrant1TextFill: themeVariables.quadrant1TextFill,
        quadrant2TextFill: themeVariables.quadrant2TextFill,
        quadrant3TextFill: themeVariables.quadrant3TextFill,
        quadrant4TextFill: themeVariables.quadrant4TextFill,
        quadrantPointFill: themeVariables.quadrantPointFill,
        quadrantPointTextFill: themeVariables.quadrantPointTextFill,
        quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,
        quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,
        quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,
        quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,
        quadrantTitleFill: themeVariables.quadrantTitleFill
    });
    quadrantBuilder.setData({
        titleText: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["t"])()
    });
    return quadrantBuilder.build();
}
const clear = function() {
    quadrantBuilder.clear();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["v"])();
};
const db = {
    setWidth,
    setHeight,
    setQuadrant1Text,
    setQuadrant2Text,
    setQuadrant3Text,
    setQuadrant4Text,
    setXAxisLeftText,
    setXAxisRightText,
    setYAxisTopText,
    setYAxisBottomText,
    addPoint,
    getQuadrantData,
    clear,
    setAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["s"],
    getAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["g"],
    setDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["q"],
    getDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["t"],
    getAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["a"],
    setAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["b"]
};
const draw = (txt, id, _version, diagObj)=>{
    var _a, _b, _c;
    function getDominantBaseLine(horizontalPos) {
        return horizontalPos === "top" ? "hanging" : "middle";
    }
    function getTextAnchor(verticalPos) {
        return verticalPos === "left" ? "start" : "middle";
    }
    function getTransformation(data) {
        return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;
    }
    const conf = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["c"])();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["l"].debug("Rendering quadrant chart\n" + txt);
    const securityLevel = conf.securityLevel;
    let sandboxElement;
    if (securityLevel === "sandbox") {
        sandboxElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("#i" + id);
    }
    const root = securityLevel === "sandbox" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(sandboxElement.nodes()[0].contentDocument.body) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("body");
    const svg = root.select(`[id="${id}"]`);
    const group = svg.append("g").attr("class", "main");
    const width = ((_a = conf.quadrantChart) == null ? void 0 : _a.chartWidth) || 500;
    const height = ((_b = conf.quadrantChart) == null ? void 0 : _b.chartHeight) || 500;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2d$6dc72991$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["i"])(svg, height, width, ((_c = conf.quadrantChart) == null ? void 0 : _c.useMaxWidth) || true);
    svg.attr("viewBox", "0 0 " + width + " " + height);
    diagObj.db.setHeight(height);
    diagObj.db.setWidth(width);
    const quadrantData = diagObj.db.getQuadrantData();
    const quadrantsGroup = group.append("g").attr("class", "quadrants");
    const borderGroup = group.append("g").attr("class", "border");
    const dataPointGroup = group.append("g").attr("class", "data-points");
    const labelGroup = group.append("g").attr("class", "labels");
    const titleGroup = group.append("g").attr("class", "title");
    if (quadrantData.title) {
        titleGroup.append("text").attr("x", 0).attr("y", 0).attr("fill", quadrantData.title.fill).attr("font-size", quadrantData.title.fontSize).attr("dominant-baseline", getDominantBaseLine(quadrantData.title.horizontalPos)).attr("text-anchor", getTextAnchor(quadrantData.title.verticalPos)).attr("transform", getTransformation(quadrantData.title)).text(quadrantData.title.text);
    }
    if (quadrantData.borderLines) {
        borderGroup.selectAll("line").data(quadrantData.borderLines).enter().append("line").attr("x1", (data)=>data.x1).attr("y1", (data)=>data.y1).attr("x2", (data)=>data.x2).attr("y2", (data)=>data.y2).style("stroke", (data)=>data.strokeFill).style("stroke-width", (data)=>data.strokeWidth);
    }
    const quadrants = quadrantsGroup.selectAll("g.quadrant").data(quadrantData.quadrants).enter().append("g").attr("class", "quadrant");
    quadrants.append("rect").attr("x", (data)=>data.x).attr("y", (data)=>data.y).attr("width", (data)=>data.width).attr("height", (data)=>data.height).attr("fill", (data)=>data.fill);
    quadrants.append("text").attr("x", 0).attr("y", 0).attr("fill", (data)=>data.text.fill).attr("font-size", (data)=>data.text.fontSize).attr("dominant-baseline", (data)=>getDominantBaseLine(data.text.horizontalPos)).attr("text-anchor", (data)=>getTextAnchor(data.text.verticalPos)).attr("transform", (data)=>getTransformation(data.text)).text((data)=>data.text.text);
    const labels = labelGroup.selectAll("g.label").data(quadrantData.axisLabels).enter().append("g").attr("class", "label");
    labels.append("text").attr("x", 0).attr("y", 0).text((data)=>data.text).attr("fill", (data)=>data.fill).attr("font-size", (data)=>data.fontSize).attr("dominant-baseline", (data)=>getDominantBaseLine(data.horizontalPos)).attr("text-anchor", (data)=>getTextAnchor(data.verticalPos)).attr("transform", (data)=>getTransformation(data));
    const dataPoints = dataPointGroup.selectAll("g.data-point").data(quadrantData.points).enter().append("g").attr("class", "data-point");
    dataPoints.append("circle").attr("cx", (data)=>data.x).attr("cy", (data)=>data.y).attr("r", (data)=>data.radius).attr("fill", (data)=>data.fill);
    dataPoints.append("text").attr("x", 0).attr("y", 0).text((data)=>data.text.text).attr("fill", (data)=>data.text.fill).attr("font-size", (data)=>data.text.fontSize).attr("dominant-baseline", (data)=>getDominantBaseLine(data.text.horizontalPos)).attr("text-anchor", (data)=>getTextAnchor(data.text.verticalPos)).attr("transform", (data)=>getTransformation(data.text));
};
const renderer = {
    draw
};
const diagram = {
    parser: parser$1,
    db,
    renderer,
    styles: ()=>""
};
;
}}),

};

//# sourceMappingURL=node_modules_d6f71a59._.js.map