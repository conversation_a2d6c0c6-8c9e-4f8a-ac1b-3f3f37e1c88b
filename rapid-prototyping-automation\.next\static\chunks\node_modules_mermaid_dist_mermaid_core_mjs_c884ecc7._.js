(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/mermaid/dist/mermaid.core.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_9c17eaec._.js",
  "static/chunks/node_modules_2e17a2ac._.js",
  "static/chunks/node_modules_mermaid_dist_mermaid_core_mjs_d5a41d6a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/mermaid/dist/mermaid.core.mjs [app-client] (ecmascript)");
    });
});
}}),
}]);