module.exports = {

"[project]/node_modules/mermaid/dist/mermaid.core.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_9a2edf48._.js",
  "server/chunks/ssr/node_modules_e2e7cfca._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/mermaid/dist/mermaid.core.mjs [app-ssr] (ecmascript)");
    });
});
}}),

};