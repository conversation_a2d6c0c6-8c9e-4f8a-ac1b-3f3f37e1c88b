{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/ui/DXCHeader.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\n\ninterface DXCHeaderProps {\n  className?: string;\n}\n\nexport const DXCHeader: React.FC<DXCHeaderProps> = ({ className }) => {\n  return (\n    <header className={cn('dxc-header', className)}>\n      <div className=\"dxc-container\">\n        <div className=\"flex items-center justify-between\">\n          <Link \n            href=\"/\" \n            className=\"dxc-logo-link\"\n            aria-label=\"DXC Technology - Go to homepage\"\n          >\n            {/* DXC Logo SVG - Inline for better control */}\n            <svg \n              className=\"dxc-logo\" \n              width=\"120\" \n              height=\"40\" \n              viewBox=\"0 0 120 40\" \n              fill=\"none\" \n              xmlns=\"http://www.w3.org/2000/svg\"\n              role=\"img\"\n              aria-labelledby=\"dxc-logo-title\"\n            >\n              <title id=\"dxc-logo-title\">DXC Technology</title>\n              \n              {/* DXC Logo - Simplified version */}\n              <g fill=\"var(--dxc-grey-900)\">\n                {/* D */}\n                <path d=\"M0 8h8.5c6.9 0 12.5 5.6 12.5 12.5S15.4 33 8.5 33H0V8zm6 20h2.5c3.6 0 6.5-2.9 6.5-6.5S12.1 15 8.5 15H6v13z\"/>\n                \n                {/* X */}\n                <path d=\"M35 8l6 8.5L47 8h7l-9 12.5L54 33h-7l-6-8.5L35 33h-7l9-12.5L28 8h7z\"/>\n                \n                {/* C */}\n                <path d=\"M70 15c-1.7-1.7-4-2.7-6.5-2.7-5.2 0-9.5 4.3-9.5 9.5s4.3 9.5 9.5 9.5c2.5 0 4.8-1 6.5-2.7l4.2 4.2c-2.8 2.8-6.7 4.5-10.7 4.5-8.3 0-15-6.7-15-15s6.7-15 15-15c4 0 7.9 1.7 10.7 4.5L70 15z\"/>\n              </g>\n              \n              {/* Technology text */}\n              <g fill=\"var(--dxc-grey-700)\" fontSize=\"8\" fontFamily=\"Open Sans, sans-serif\">\n                <text x=\"0\" y=\"40\" fontSize=\"6\">Technology</text>\n              </g>\n            </svg>\n          </Link>\n          \n          {/* Navigation could go here */}\n          <nav className=\"hidden md:flex items-center space-x-8\" role=\"navigation\" aria-label=\"Main navigation\">\n            {/* Navigation items would go here */}\n          </nav>\n        </div>\n      </div>\n    </header>\n  );\n};\n\n// Alternative component for when you have the actual DXC logo file\ninterface DXCLogoProps {\n  className?: string;\n  width?: number;\n  height?: number;\n}\n\nexport const DXCLogo: React.FC<DXCLogoProps> = ({ \n  className, \n  width = 120, \n  height = 40 \n}) => {\n  return (\n    <Link \n      href=\"/\" \n      className={cn('dxc-logo-link', className)}\n      aria-label=\"DXC Technology - Go to homepage\"\n    >\n      {/* When you have the actual dxc-logo-black.svg file, use this: */}\n      {/* <img \n        src=\"/images/dxc-logo-black.svg\" \n        alt=\"DXC Technology\" \n        className=\"dxc-logo\"\n        width={width}\n        height={height}\n      /> */}\n      \n      {/* Fallback SVG logo for now */}\n      <svg \n        className=\"dxc-logo\" \n        width={width} \n        height={height} \n        viewBox=\"0 0 120 40\" \n        fill=\"none\" \n        xmlns=\"http://www.w3.org/2000/svg\"\n        role=\"img\"\n        aria-labelledby=\"dxc-logo-title-2\"\n      >\n        <title id=\"dxc-logo-title-2\">DXC Technology</title>\n        \n        {/* DXC Logo */}\n        <g fill=\"var(--dxc-grey-900)\">\n          {/* D */}\n          <path d=\"M0 8h8.5c6.9 0 12.5 5.6 12.5 12.5S15.4 33 8.5 33H0V8zm6 20h2.5c3.6 0 6.5-2.9 6.5-6.5S12.1 15 8.5 15H6v13z\"/>\n          \n          {/* X */}\n          <path d=\"M35 8l6 8.5L47 8h7l-9 12.5L54 33h-7l-6-8.5L35 33h-7l9-12.5L28 8h7z\"/>\n          \n          {/* C */}\n          <path d=\"M70 15c-1.7-1.7-4-2.7-6.5-2.7-5.2 0-9.5 4.3-9.5 9.5s4.3 9.5 9.5 9.5c2.5 0 4.8-1 6.5-2.7l4.2 4.2c-2.8 2.8-6.7 4.5-10.7 4.5-8.3 0-15-6.7-15-15s6.7-15 15-15c4 0 7.9 1.7 10.7 4.5L70 15z\"/>\n        </g>\n        \n        {/* Technology text */}\n        <g fill=\"var(--dxc-grey-700)\">\n          <text x=\"0\" y=\"40\" fontSize=\"6\" fontFamily=\"Open Sans, sans-serif\">Technology</text>\n        </g>\n      </svg>\n    </Link>\n  );\n};\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAMO,MAAM,YAAsC,CAAC,EAAE,SAAS,EAAE;IAC/D,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAClC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;wBACV,cAAW;kCAGX,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,OAAM;4BACN,MAAK;4BACL,mBAAgB;;8CAEhB,8OAAC;oCAAM,IAAG;8CAAiB;;;;;;8CAG3B,8OAAC;oCAAE,MAAK;;sDAEN,8OAAC;4CAAK,GAAE;;;;;;sDAGR,8OAAC;4CAAK,GAAE;;;;;;sDAGR,8OAAC;4CAAK,GAAE;;;;;;;;;;;;8CAIV,8OAAC;oCAAE,MAAK;oCAAsB,UAAS;oCAAI,YAAW;8CACpD,cAAA,8OAAC;wCAAK,GAAE;wCAAI,GAAE;wCAAK,UAAS;kDAAI;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,WAAU;wBAAwC,MAAK;wBAAa,cAAW;;;;;;;;;;;;;;;;;;;;;;AAO9F;AASO,MAAM,UAAkC,CAAC,EAC9C,SAAS,EACT,QAAQ,GAAG,EACX,SAAS,EAAE,EACZ;IACC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,cAAW;kBAYX,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAQ;YACR,MAAK;YACL,OAAM;YACN,MAAK;YACL,mBAAgB;;8BAEhB,8OAAC;oBAAM,IAAG;8BAAmB;;;;;;8BAG7B,8OAAC;oBAAE,MAAK;;sCAEN,8OAAC;4BAAK,GAAE;;;;;;sCAGR,8OAAC;4BAAK,GAAE;;;;;;sCAGR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;8BAIV,8OAAC;oBAAE,MAAK;8BACN,cAAA,8OAAC;wBAAK,GAAE;wBAAI,GAAE;wBAAK,UAAS;wBAAI,YAAW;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAK7E", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/components/RapidPrototypingApp-backup.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { DXCHeader } from '@/components/ui/DXCHeader';\n\nexport const RapidPrototypingApp: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <DXCHeader />\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-6\">\n            Rapid Prototyping Automation\n          </h1>\n          <p className=\"text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed\">\n            Transform your conversation transcripts into comprehensive technical documentation and implementation guides powered by DXC Technology's AI solutions\n          </p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <h2 className=\"text-2xl font-semibold mb-4\">Getting Started</h2>\n          <p className=\"text-gray-600\">\n            This is a simplified version of the Rapid Prototyping Automation app with DXC Technology branding.\n          </p>\n        </div>\n      </main>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,MAAM,sBAAgC;IAC3C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qIAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAKzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8B;;;;;;0CAC5C,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { RapidPrototypingApp } from '@/components/RapidPrototypingApp-backup';\n\nexport default function Home() {\n  return <RapidPrototypingApp />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,mJAAA,CAAA,sBAAmB;;;;;AAC7B", "debugId": null}}]}