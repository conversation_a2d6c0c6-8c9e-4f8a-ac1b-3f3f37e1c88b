{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/base64-js/index.js"], "sourcesContent": ["'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,WAAW,GAAG;AACtB,QAAQ,aAAa,GAAG;AAExB,IAAI,SAAS,EAAE;AACf,IAAI,YAAY,EAAE;AAClB,IAAI,MAAM,OAAO,eAAe,cAAc,aAAa;AAE3D,IAAI,OAAO;AACX,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;IAC/C,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;IACnB,SAAS,CAAC,KAAK,UAAU,CAAC,GAAG,GAAG;AAClC;AAEA,6DAA6D;AAC7D,6DAA6D;AAC7D,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG;AAC/B,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG;AAE/B,SAAS,QAAS,GAAG;IACnB,IAAI,MAAM,IAAI,MAAM;IAEpB,IAAI,MAAM,IAAI,GAAG;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,yDAAyD;IACzD,yDAAyD;IACzD,IAAI,WAAW,IAAI,OAAO,CAAC;IAC3B,IAAI,aAAa,CAAC,GAAG,WAAW;IAEhC,IAAI,kBAAkB,aAAa,MAC/B,IACA,IAAK,WAAW;IAEpB,OAAO;QAAC;QAAU;KAAgB;AACpC;AAEA,4DAA4D;AAC5D,SAAS,WAAY,GAAG;IACtB,IAAI,OAAO,QAAQ;IACnB,IAAI,WAAW,IAAI,CAAC,EAAE;IACtB,IAAI,kBAAkB,IAAI,CAAC,EAAE;IAC7B,OAAO,AAAC,CAAC,WAAW,eAAe,IAAI,IAAI,IAAK;AAClD;AAEA,SAAS,YAAa,GAAG,EAAE,QAAQ,EAAE,eAAe;IAClD,OAAO,AAAC,CAAC,WAAW,eAAe,IAAI,IAAI,IAAK;AAClD;AAEA,SAAS,YAAa,GAAG;IACvB,IAAI;IACJ,IAAI,OAAO,QAAQ;IACnB,IAAI,WAAW,IAAI,CAAC,EAAE;IACtB,IAAI,kBAAkB,IAAI,CAAC,EAAE;IAE7B,IAAI,MAAM,IAAI,IAAI,YAAY,KAAK,UAAU;IAE7C,IAAI,UAAU;IAEd,sEAAsE;IACtE,IAAI,MAAM,kBAAkB,IACxB,WAAW,IACX;IAEJ,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QAC3B,MACE,AAAC,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,KAChC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,KACpC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,IACrC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG;QAClC,GAAG,CAAC,UAAU,GAAG,AAAC,OAAO,KAAM;QAC/B,GAAG,CAAC,UAAU,GAAG,AAAC,OAAO,IAAK;QAC9B,GAAG,CAAC,UAAU,GAAG,MAAM;IACzB;IAEA,IAAI,oBAAoB,GAAG;QACzB,MACE,AAAC,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,IAChC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI;QACvC,GAAG,CAAC,UAAU,GAAG,MAAM;IACzB;IAEA,IAAI,oBAAoB,GAAG;QACzB,MACE,AAAC,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,KAChC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,IACpC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI;QACvC,GAAG,CAAC,UAAU,GAAG,AAAC,OAAO,IAAK;QAC9B,GAAG,CAAC,UAAU,GAAG,MAAM;IACzB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAiB,GAAG;IAC3B,OAAO,MAAM,CAAC,OAAO,KAAK,KAAK,GAC7B,MAAM,CAAC,OAAO,KAAK,KAAK,GACxB,MAAM,CAAC,OAAO,IAAI,KAAK,GACvB,MAAM,CAAC,MAAM,KAAK;AACtB;AAEA,SAAS,YAAa,KAAK,EAAE,KAAK,EAAE,GAAG;IACrC,IAAI;IACJ,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,KAAK,EAAG;QACnC,MACE,CAAC,AAAC,KAAK,CAAC,EAAE,IAAI,KAAM,QAAQ,IAC5B,CAAC,AAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAK,MAAM,IAC7B,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,IAAI;QACtB,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IACA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA,SAAS,cAAe,KAAK;IAC3B,IAAI;IACJ,IAAI,MAAM,MAAM,MAAM;IACtB,IAAI,aAAa,MAAM,EAAE,sCAAsC;;IAC/D,IAAI,QAAQ,EAAE;IACd,IAAI,iBAAiB,MAAM,wBAAwB;;IAEnD,+EAA+E;IAC/E,IAAK,IAAI,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,eAAgB;QACtE,MAAM,IAAI,CAAC,YAAY,OAAO,GAAG,AAAC,IAAI,iBAAkB,OAAO,OAAQ,IAAI;IAC7E;IAEA,sEAAsE;IACtE,IAAI,eAAe,GAAG;QACpB,MAAM,KAAK,CAAC,MAAM,EAAE;QACpB,MAAM,IAAI,CACR,MAAM,CAAC,OAAO,EAAE,GAChB,MAAM,CAAC,AAAC,OAAO,IAAK,KAAK,GACzB;IAEJ,OAAO,IAAI,eAAe,GAAG;QAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE;QAC5C,MAAM,IAAI,CACR,MAAM,CAAC,OAAO,GAAG,GACjB,MAAM,CAAC,AAAC,OAAO,IAAK,KAAK,GACzB,MAAM,CAAC,AAAC,OAAO,IAAK,KAAK,GACzB;IAEJ;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/process-nextick-args/index.js"], "sourcesContent": ["'use strict';\n\nif (typeof process === 'undefined' ||\n    !process.version ||\n    process.version.indexOf('v0.') === 0 ||\n    process.version.indexOf('v1.') === 0 && process.version.indexOf('v1.8.') !== 0) {\n  module.exports = { nextTick: nextTick };\n} else {\n  module.exports = process\n}\n\nfunction nextTick(fn, arg1, arg2, arg3) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('\"callback\" argument must be a function');\n  }\n  var len = arguments.length;\n  var args, i;\n  switch (len) {\n  case 0:\n  case 1:\n    return process.nextTick(fn);\n  case 2:\n    return process.nextTick(function afterTickOne() {\n      fn.call(null, arg1);\n    });\n  case 3:\n    return process.nextTick(function afterTickTwo() {\n      fn.call(null, arg1, arg2);\n    });\n  case 4:\n    return process.nextTick(function afterTickThree() {\n      fn.call(null, arg1, arg2, arg3);\n    });\n  default:\n    args = new Array(len - 1);\n    i = 0;\n    while (i < args.length) {\n      args[i++] = arguments[i];\n    }\n    return process.nextTick(function afterTick() {\n      fn.apply(null, args);\n    });\n  }\n}\n\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,OAAO,YAAY,eACnB,CAAC,QAAQ,OAAO,IAChB,QAAQ,OAAO,CAAC,OAAO,CAAC,WAAW,KACnC,QAAQ,OAAO,CAAC,OAAO,CAAC,WAAW,KAAK,QAAQ,OAAO,CAAC,OAAO,CAAC,aAAa,GAAG;IAClF,OAAO,OAAO,GAAG;QAAE,UAAU;IAAS;AACxC,OAAO;IACL,OAAO,OAAO,GAAG;AACnB;AAEA,SAAS,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IACpC,IAAI,OAAO,OAAO,YAAY;QAC5B,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,MAAM,UAAU,MAAM;IAC1B,IAAI,MAAM;IACV,OAAQ;QACR,KAAK;QACL,KAAK;YACH,OAAO,QAAQ,QAAQ,CAAC;QAC1B,KAAK;YACH,OAAO,QAAQ,QAAQ,CAAC,SAAS;gBAC/B,GAAG,IAAI,CAAC,MAAM;YAChB;QACF,KAAK;YACH,OAAO,QAAQ,QAAQ,CAAC,SAAS;gBAC/B,GAAG,IAAI,CAAC,MAAM,MAAM;YACtB;QACF,KAAK;YACH,OAAO,QAAQ,QAAQ,CAAC,SAAS;gBAC/B,GAAG,IAAI,CAAC,MAAM,MAAM,MAAM;YAC5B;QACF;YACE,OAAO,IAAI,MAAM,MAAM;YACvB,IAAI;YACJ,MAAO,IAAI,KAAK,MAAM,CAAE;gBACtB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,EAAE;YAC1B;YACA,OAAO,QAAQ,QAAQ,CAAC,SAAS;gBAC/B,GAAG,KAAK,CAAC,MAAM;YACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/node_modules/isarray/index.js"], "sourcesContent": ["var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n"], "names": [], "mappings": "AAAA,IAAI,WAAW,CAAC,EAAE,QAAQ;AAE1B,OAAO,OAAO,GAAG,MAAM,OAAO,IAAI,SAAU,GAAG;IAC7C,OAAO,SAAS,IAAI,CAAC,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/internal/streams/stream.js"], "sourcesContent": ["module.exports = require('stream');\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/internal/streams/BufferList.js"], "sourcesContent": ["'use strict';\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Buffer = require('safe-buffer').Buffer;\nvar util = require('util');\n\nfunction copyBuffer(src, target, offset) {\n  src.copy(target, offset);\n}\n\nmodule.exports = function () {\n  function BufferList() {\n    _classCallCheck(this, BufferList);\n\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  BufferList.prototype.push = function push(v) {\n    var entry = { data: v, next: null };\n    if (this.length > 0) this.tail.next = entry;else this.head = entry;\n    this.tail = entry;\n    ++this.length;\n  };\n\n  BufferList.prototype.unshift = function unshift(v) {\n    var entry = { data: v, next: this.head };\n    if (this.length === 0) this.tail = entry;\n    this.head = entry;\n    ++this.length;\n  };\n\n  BufferList.prototype.shift = function shift() {\n    if (this.length === 0) return;\n    var ret = this.head.data;\n    if (this.length === 1) this.head = this.tail = null;else this.head = this.head.next;\n    --this.length;\n    return ret;\n  };\n\n  BufferList.prototype.clear = function clear() {\n    this.head = this.tail = null;\n    this.length = 0;\n  };\n\n  BufferList.prototype.join = function join(s) {\n    if (this.length === 0) return '';\n    var p = this.head;\n    var ret = '' + p.data;\n    while (p = p.next) {\n      ret += s + p.data;\n    }return ret;\n  };\n\n  BufferList.prototype.concat = function concat(n) {\n    if (this.length === 0) return Buffer.alloc(0);\n    var ret = Buffer.allocUnsafe(n >>> 0);\n    var p = this.head;\n    var i = 0;\n    while (p) {\n      copyBuffer(p.data, ret, i);\n      i += p.data.length;\n      p = p.next;\n    }\n    return ret;\n  };\n\n  return BufferList;\n}();\n\nif (util && util.inspect && util.inspect.custom) {\n  module.exports.prototype[util.inspect.custom] = function () {\n    var obj = util.inspect({ length: this.length });\n    return this.constructor.name + ' ' + obj;\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,SAAS,gBAAgB,QAAQ,EAAE,WAAW;IAAI,IAAI,CAAC,CAAC,oBAAoB,WAAW,GAAG;QAAE,MAAM,IAAI,UAAU;IAAsC;AAAE;AAExJ,IAAI,SAAS,gGAAuB,MAAM;AAC1C,IAAI;AAEJ,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,MAAM;IACrC,IAAI,IAAI,CAAC,QAAQ;AACnB;AAEA,OAAO,OAAO,GAAG;IACf,SAAS;QACP,gBAAgB,IAAI,EAAE;QAEtB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,CAAC;QACzC,IAAI,QAAQ;YAAE,MAAM;YAAG,MAAM;QAAK;QAClC,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;aAAW,IAAI,CAAC,IAAI,GAAG;QAC7D,IAAI,CAAC,IAAI,GAAG;QACZ,EAAE,IAAI,CAAC,MAAM;IACf;IAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,CAAC;QAC/C,IAAI,QAAQ;YAAE,MAAM;YAAG,MAAM,IAAI,CAAC,IAAI;QAAC;QACvC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG;QACnC,IAAI,CAAC,IAAI,GAAG;QACZ,EAAE,IAAI,CAAC,MAAM;IACf;IAEA,WAAW,SAAS,CAAC,KAAK,GAAG,SAAS;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG;QACvB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;QACxB,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;aAAU,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QACnF,EAAE,IAAI,CAAC,MAAM;QACb,OAAO;IACT;IAEA,WAAW,SAAS,CAAC,KAAK,GAAG,SAAS;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,CAAC;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;QAC9B,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,MAAM,KAAK,EAAE,IAAI;QACrB,MAAO,IAAI,EAAE,IAAI,CAAE;YACjB,OAAO,IAAI,EAAE,IAAI;QACnB;QAAC,OAAO;IACV;IAEA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,CAAC;QAC7C,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,CAAC;QAC3C,IAAI,MAAM,OAAO,WAAW,CAAC,MAAM;QACnC,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,IAAI;QACR,MAAO,EAAG;YACR,WAAW,EAAE,IAAI,EAAE,KAAK;YACxB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,IAAI,EAAE,IAAI;QACZ;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,IAAI,QAAQ,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,EAAE;IAC/C,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,MAAM,CAAC,GAAG;QAC9C,IAAI,MAAM,KAAK,OAAO,CAAC;YAAE,QAAQ,IAAI,CAAC,MAAM;QAAC;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,MAAM;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/internal/streams/destroy.js"], "sourcesContent": ["'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\n// undocumented cb() API, needed for core, not for public API\nfunction destroy(err, cb) {\n  var _this = this;\n\n  var readableDestroyed = this._readableState && this._readableState.destroyed;\n  var writableDestroyed = this._writableState && this._writableState.destroyed;\n\n  if (readableDestroyed || writableDestroyed) {\n    if (cb) {\n      cb(err);\n    } else if (err) {\n      if (!this._writableState) {\n        pna.nextTick(emitErrorNT, this, err);\n      } else if (!this._writableState.errorEmitted) {\n        this._writableState.errorEmitted = true;\n        pna.nextTick(emitErrorNT, this, err);\n      }\n    }\n\n    return this;\n  }\n\n  // we set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n\n  if (this._readableState) {\n    this._readableState.destroyed = true;\n  }\n\n  // if this is a duplex stream mark the writable part as destroyed as well\n  if (this._writableState) {\n    this._writableState.destroyed = true;\n  }\n\n  this._destroy(err || null, function (err) {\n    if (!cb && err) {\n      if (!_this._writableState) {\n        pna.nextTick(emitErrorNT, _this, err);\n      } else if (!_this._writableState.errorEmitted) {\n        _this._writableState.errorEmitted = true;\n        pna.nextTick(emitErrorNT, _this, err);\n      }\n    } else if (cb) {\n      cb(err);\n    }\n  });\n\n  return this;\n}\n\nfunction undestroy() {\n  if (this._readableState) {\n    this._readableState.destroyed = false;\n    this._readableState.reading = false;\n    this._readableState.ended = false;\n    this._readableState.endEmitted = false;\n  }\n\n  if (this._writableState) {\n    this._writableState.destroyed = false;\n    this._writableState.ended = false;\n    this._writableState.ending = false;\n    this._writableState.finalCalled = false;\n    this._writableState.prefinished = false;\n    this._writableState.finished = false;\n    this._writableState.errorEmitted = false;\n  }\n}\n\nfunction emitErrorNT(self, err) {\n  self.emit('error', err);\n}\n\nmodule.exports = {\n  destroy: destroy,\n  undestroy: undestroy\n};"], "names": [], "mappings": "AAAA;AAEA,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,6DAA6D;AAC7D,SAAS,QAAQ,GAAG,EAAE,EAAE;IACtB,IAAI,QAAQ,IAAI;IAEhB,IAAI,oBAAoB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAC5E,IAAI,oBAAoB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAE5E,IAAI,qBAAqB,mBAAmB;QAC1C,IAAI,IAAI;YACN,GAAG;QACL,OAAO,IAAI,KAAK;YACd,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,QAAQ,CAAC,aAAa,IAAI,EAAE;YAClC,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;gBAC5C,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG;gBACnC,IAAI,QAAQ,CAAC,aAAa,IAAI,EAAE;YAClC;QACF;QAEA,OAAO,IAAI;IACb;IAEA,kEAAkE;IAClE,2EAA2E;IAE3E,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;IAEA,yEAAyE;IACzE,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;IAEA,IAAI,CAAC,QAAQ,CAAC,OAAO,MAAM,SAAU,GAAG;QACtC,IAAI,CAAC,MAAM,KAAK;YACd,IAAI,CAAC,MAAM,cAAc,EAAE;gBACzB,IAAI,QAAQ,CAAC,aAAa,OAAO;YACnC,OAAO,IAAI,CAAC,MAAM,cAAc,CAAC,YAAY,EAAE;gBAC7C,MAAM,cAAc,CAAC,YAAY,GAAG;gBACpC,IAAI,QAAQ,CAAC,aAAa,OAAO;YACnC;QACF,OAAO,IAAI,IAAI;YACb,GAAG;QACL;IACF;IAEA,OAAO,IAAI;AACb;AAEA,SAAS;IACP,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAChC,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC9B,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;QAC5B,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;IACnC;IAEA,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAChC,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;QAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;QAC7B,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG;QAClC,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG;QAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;QAC/B,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG;IACrC;AACF;AAEA,SAAS,YAAY,IAAI,EAAE,GAAG;IAC5B,KAAK,IAAI,CAAC,SAAS;AACrB;AAEA,OAAO,OAAO,GAAG;IACf,SAAS;IACT,WAAW;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/_stream_writable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n\n'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\nmodule.exports = Writable;\n\n/* <replacement> */\nfunction WriteReq(chunk, encoding, cb) {\n  this.chunk = chunk;\n  this.encoding = encoding;\n  this.callback = cb;\n  this.next = null;\n}\n\n// It seems a linked list but it is not\n// there will be only 2 of these for each stream\nfunction CorkedRequest(state) {\n  var _this = this;\n\n  this.next = null;\n  this.entry = null;\n  this.finish = function () {\n    onCorkedFinish(_this, state);\n  };\n}\n/* </replacement> */\n\n/*<replacement>*/\nvar asyncWrite = !process.browser && ['v0.10', 'v0.9.'].indexOf(process.version.slice(0, 5)) > -1 ? setImmediate : pna.nextTick;\n/*</replacement>*/\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nWritable.WritableState = WritableState;\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\n/*<replacement>*/\nvar internalUtil = {\n  deprecate: require('util-deprecate')\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\n/*</replacement>*/\n\nvar destroyImpl = require('./internal/streams/destroy');\n\nutil.inherits(Writable, Stream);\n\nfunction nop() {}\n\nfunction WritableState(options, stream) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  var isDuplex = stream instanceof Duplex;\n\n  // object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n  this.objectMode = !!options.objectMode;\n\n  if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode;\n\n  // the point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write()\n  var hwm = options.highWaterMark;\n  var writableHwm = options.writableHighWaterMark;\n  var defaultHwm = this.objectMode ? 16 : 16 * 1024;\n\n  if (hwm || hwm === 0) this.highWaterMark = hwm;else if (isDuplex && (writableHwm || writableHwm === 0)) this.highWaterMark = writableHwm;else this.highWaterMark = defaultHwm;\n\n  // cast to ints.\n  this.highWaterMark = Math.floor(this.highWaterMark);\n\n  // if _final has been called\n  this.finalCalled = false;\n\n  // drain event flag.\n  this.needDrain = false;\n  // at the start of calling end()\n  this.ending = false;\n  // when end() has been called, and returned\n  this.ended = false;\n  // when 'finish' is emitted\n  this.finished = false;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n  var noDecode = options.decodeStrings === false;\n  this.decodeStrings = !noDecode;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n  this.length = 0;\n\n  // a flag to see when we're in the middle of a write.\n  this.writing = false;\n\n  // when true all writes will be buffered until .uncork() call\n  this.corked = 0;\n\n  // a flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n  this.sync = true;\n\n  // a flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n  this.bufferProcessing = false;\n\n  // the callback that's passed to _write(chunk,cb)\n  this.onwrite = function (er) {\n    onwrite(stream, er);\n  };\n\n  // the callback that the user supplies to write(chunk,encoding,cb)\n  this.writecb = null;\n\n  // the amount that is being written when _write is called.\n  this.writelen = 0;\n\n  this.bufferedRequest = null;\n  this.lastBufferedRequest = null;\n\n  // number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted\n  this.pendingcb = 0;\n\n  // emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams\n  this.prefinished = false;\n\n  // True if the error was already emitted and should not be thrown again\n  this.errorEmitted = false;\n\n  // count buffered requests\n  this.bufferedRequestCount = 0;\n\n  // allocate the first CorkedRequest, there is always\n  // one allocated and free to use, and we maintain at most two\n  this.corkedRequestsFree = new CorkedRequest(this);\n}\n\nWritableState.prototype.getBuffer = function getBuffer() {\n  var current = this.bufferedRequest;\n  var out = [];\n  while (current) {\n    out.push(current);\n    current = current.next;\n  }\n  return out;\n};\n\n(function () {\n  try {\n    Object.defineProperty(WritableState.prototype, 'buffer', {\n      get: internalUtil.deprecate(function () {\n        return this.getBuffer();\n      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')\n    });\n  } catch (_) {}\n})();\n\n// Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\nvar realHasInstance;\nif (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {\n  realHasInstance = Function.prototype[Symbol.hasInstance];\n  Object.defineProperty(Writable, Symbol.hasInstance, {\n    value: function (object) {\n      if (realHasInstance.call(this, object)) return true;\n      if (this !== Writable) return false;\n\n      return object && object._writableState instanceof WritableState;\n    }\n  });\n} else {\n  realHasInstance = function (object) {\n    return object instanceof this;\n  };\n}\n\nfunction Writable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n  if (!realHasInstance.call(Writable, this) && !(this instanceof Duplex)) {\n    return new Writable(options);\n  }\n\n  this._writableState = new WritableState(options, this);\n\n  // legacy.\n  this.writable = true;\n\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write;\n\n    if (typeof options.writev === 'function') this._writev = options.writev;\n\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n\n    if (typeof options.final === 'function') this._final = options.final;\n  }\n\n  Stream.call(this);\n}\n\n// Otherwise people can pipe Writable streams, which is just wrong.\nWritable.prototype.pipe = function () {\n  this.emit('error', new Error('Cannot pipe, not readable'));\n};\n\nfunction writeAfterEnd(stream, cb) {\n  var er = new Error('write after end');\n  // TODO: defer error events consistently everywhere, not just the cb\n  stream.emit('error', er);\n  pna.nextTick(cb, er);\n}\n\n// Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\nfunction validChunk(stream, state, chunk, cb) {\n  var valid = true;\n  var er = false;\n\n  if (chunk === null) {\n    er = new TypeError('May not write null values to stream');\n  } else if (typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new TypeError('Invalid non-string/buffer chunk');\n  }\n  if (er) {\n    stream.emit('error', er);\n    pna.nextTick(cb, er);\n    valid = false;\n  }\n  return valid;\n}\n\nWritable.prototype.write = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  var ret = false;\n  var isBuf = !state.objectMode && _isUint8Array(chunk);\n\n  if (isBuf && !Buffer.isBuffer(chunk)) {\n    chunk = _uint8ArrayToBuffer(chunk);\n  }\n\n  if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;\n\n  if (typeof cb !== 'function') cb = nop;\n\n  if (state.ended) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {\n    state.pendingcb++;\n    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n  }\n\n  return ret;\n};\n\nWritable.prototype.cork = function () {\n  var state = this._writableState;\n\n  state.corked++;\n};\n\nWritable.prototype.uncork = function () {\n  var state = this._writableState;\n\n  if (state.corked) {\n    state.corked--;\n\n    if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n  }\n};\n\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = encoding.toLowerCase();\n  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new TypeError('Unknown encoding: ' + encoding);\n  this._writableState.defaultEncoding = encoding;\n  return this;\n};\n\nfunction decodeChunk(state, chunk, encoding) {\n  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {\n    chunk = Buffer.from(chunk, encoding);\n  }\n  return chunk;\n}\n\nObject.defineProperty(Writable.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function () {\n    return this._writableState.highWaterMark;\n  }\n});\n\n// if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n  if (!isBuf) {\n    var newChunk = decodeChunk(state, chunk, encoding);\n    if (chunk !== newChunk) {\n      isBuf = true;\n      encoding = 'buffer';\n      chunk = newChunk;\n    }\n  }\n  var len = state.objectMode ? 1 : chunk.length;\n\n  state.length += len;\n\n  var ret = state.length < state.highWaterMark;\n  // we must ensure that previous needDrain will not be reset to false.\n  if (!ret) state.needDrain = true;\n\n  if (state.writing || state.corked) {\n    var last = state.lastBufferedRequest;\n    state.lastBufferedRequest = {\n      chunk: chunk,\n      encoding: encoding,\n      isBuf: isBuf,\n      callback: cb,\n      next: null\n    };\n    if (last) {\n      last.next = state.lastBufferedRequest;\n    } else {\n      state.bufferedRequest = state.lastBufferedRequest;\n    }\n    state.bufferedRequestCount += 1;\n  } else {\n    doWrite(stream, state, false, len, chunk, encoding, cb);\n  }\n\n  return ret;\n}\n\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len;\n  state.writecb = cb;\n  state.writing = true;\n  state.sync = true;\n  if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);\n  state.sync = false;\n}\n\nfunction onwriteError(stream, state, sync, er, cb) {\n  --state.pendingcb;\n\n  if (sync) {\n    // defer the callback if we are being called synchronously\n    // to avoid piling up things on the stack\n    pna.nextTick(cb, er);\n    // this can emit finish, and it will always happen\n    // after error\n    pna.nextTick(finishMaybe, stream, state);\n    stream._writableState.errorEmitted = true;\n    stream.emit('error', er);\n  } else {\n    // the caller expect this to happen before if\n    // it is async\n    cb(er);\n    stream._writableState.errorEmitted = true;\n    stream.emit('error', er);\n    // this can emit finish, but finish must\n    // always follow error\n    finishMaybe(stream, state);\n  }\n}\n\nfunction onwriteStateUpdate(state) {\n  state.writing = false;\n  state.writecb = null;\n  state.length -= state.writelen;\n  state.writelen = 0;\n}\n\nfunction onwrite(stream, er) {\n  var state = stream._writableState;\n  var sync = state.sync;\n  var cb = state.writecb;\n\n  onwriteStateUpdate(state);\n\n  if (er) onwriteError(stream, state, sync, er, cb);else {\n    // Check if we're actually ready to finish, but don't emit yet\n    var finished = needFinish(state);\n\n    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n      clearBuffer(stream, state);\n    }\n\n    if (sync) {\n      /*<replacement>*/\n      asyncWrite(afterWrite, stream, state, finished, cb);\n      /*</replacement>*/\n    } else {\n      afterWrite(stream, state, finished, cb);\n    }\n  }\n}\n\nfunction afterWrite(stream, state, finished, cb) {\n  if (!finished) onwriteDrain(stream, state);\n  state.pendingcb--;\n  cb();\n  finishMaybe(stream, state);\n}\n\n// Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\nfunction onwriteDrain(stream, state) {\n  if (state.length === 0 && state.needDrain) {\n    state.needDrain = false;\n    stream.emit('drain');\n  }\n}\n\n// if there's something in the buffer waiting, then process it\nfunction clearBuffer(stream, state) {\n  state.bufferProcessing = true;\n  var entry = state.bufferedRequest;\n\n  if (stream._writev && entry && entry.next) {\n    // Fast case, write everything using _writev()\n    var l = state.bufferedRequestCount;\n    var buffer = new Array(l);\n    var holder = state.corkedRequestsFree;\n    holder.entry = entry;\n\n    var count = 0;\n    var allBuffers = true;\n    while (entry) {\n      buffer[count] = entry;\n      if (!entry.isBuf) allBuffers = false;\n      entry = entry.next;\n      count += 1;\n    }\n    buffer.allBuffers = allBuffers;\n\n    doWrite(stream, state, true, state.length, buffer, '', holder.finish);\n\n    // doWrite is almost always async, defer these to save a bit of time\n    // as the hot path ends with doWrite\n    state.pendingcb++;\n    state.lastBufferedRequest = null;\n    if (holder.next) {\n      state.corkedRequestsFree = holder.next;\n      holder.next = null;\n    } else {\n      state.corkedRequestsFree = new CorkedRequest(state);\n    }\n    state.bufferedRequestCount = 0;\n  } else {\n    // Slow case, write chunks one-by-one\n    while (entry) {\n      var chunk = entry.chunk;\n      var encoding = entry.encoding;\n      var cb = entry.callback;\n      var len = state.objectMode ? 1 : chunk.length;\n\n      doWrite(stream, state, false, len, chunk, encoding, cb);\n      entry = entry.next;\n      state.bufferedRequestCount--;\n      // if we didn't call the onwrite immediately, then\n      // it means that we need to wait until it does.\n      // also, that means that the chunk and cb are currently\n      // being processed, so move the buffer counter past them.\n      if (state.writing) {\n        break;\n      }\n    }\n\n    if (entry === null) state.lastBufferedRequest = null;\n  }\n\n  state.bufferedRequest = entry;\n  state.bufferProcessing = false;\n}\n\nWritable.prototype._write = function (chunk, encoding, cb) {\n  cb(new Error('_write() is not implemented'));\n};\n\nWritable.prototype._writev = null;\n\nWritable.prototype.end = function (chunk, encoding, cb) {\n  var state = this._writableState;\n\n  if (typeof chunk === 'function') {\n    cb = chunk;\n    chunk = null;\n    encoding = null;\n  } else if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);\n\n  // .end() fully uncorks\n  if (state.corked) {\n    state.corked = 1;\n    this.uncork();\n  }\n\n  // ignore unnecessary end() calls.\n  if (!state.ending) endWritable(this, state, cb);\n};\n\nfunction needFinish(state) {\n  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\nfunction callFinal(stream, state) {\n  stream._final(function (err) {\n    state.pendingcb--;\n    if (err) {\n      stream.emit('error', err);\n    }\n    state.prefinished = true;\n    stream.emit('prefinish');\n    finishMaybe(stream, state);\n  });\n}\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function') {\n      state.pendingcb++;\n      state.finalCalled = true;\n      pna.nextTick(callFinal, stream, state);\n    } else {\n      state.prefinished = true;\n      stream.emit('prefinish');\n    }\n  }\n}\n\nfunction finishMaybe(stream, state) {\n  var need = needFinish(state);\n  if (need) {\n    prefinish(stream, state);\n    if (state.pendingcb === 0) {\n      state.finished = true;\n      stream.emit('finish');\n    }\n  }\n  return need;\n}\n\nfunction endWritable(stream, state, cb) {\n  state.ending = true;\n  finishMaybe(stream, state);\n  if (cb) {\n    if (state.finished) pna.nextTick(cb);else stream.once('finish', cb);\n  }\n  state.ended = true;\n  stream.writable = false;\n}\n\nfunction onCorkedFinish(corkReq, state, err) {\n  var entry = corkReq.entry;\n  corkReq.entry = null;\n  while (entry) {\n    var cb = entry.callback;\n    state.pendingcb--;\n    cb(err);\n    entry = entry.next;\n  }\n\n  // reuse the free corkReq.\n  state.corkedRequestsFree.next = corkReq;\n}\n\nObject.defineProperty(Writable.prototype, 'destroyed', {\n  get: function () {\n    if (this._writableState === undefined) {\n      return false;\n    }\n    return this._writableState.destroyed;\n  },\n  set: function (value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._writableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._writableState.destroyed = value;\n  }\n});\n\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\nWritable.prototype._destroy = function (err, cb) {\n  this.end();\n  cb(err);\n};"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,uCAAuC;AACvC,wEAAwE;AACxE,0CAA0C;AAE1C;AAEA,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,OAAO,OAAO,GAAG;AAEjB,iBAAiB,GACjB,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,EAAE;IACnC,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,uCAAuC;AACvC,gDAAgD;AAChD,SAAS,cAAc,KAAK;IAC1B,IAAI,QAAQ,IAAI;IAEhB,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,MAAM,GAAG;QACZ,eAAe,OAAO;IACxB;AACF;AACA,kBAAkB,GAElB,eAAe,GACf,IAAI,aAAa,4CAAoB;IAAC;IAAS;CAAQ,CAAC,OAAO,CAAC,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,eAAe,IAAI,QAAQ;AAC/H,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,SAAS,aAAa,GAAG;AAEzB,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,eAAe,GACf,IAAI,eAAe;IACjB,SAAS;AACX;AACA,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GAEf,IAAI,SAAS,gGAAuB,MAAM;AAC1C,IAAI,gBAAgB,CAAC,OAAO,WAAW,cAAc,SAAS,6EAAyC,OAAO,SAAS,cAAc,OAAO,CAAC,CAAC,EAAE,UAAU,IAAI,YAAa;AAC3K,SAAS,oBAAoB,KAAK;IAChC,OAAO,OAAO,IAAI,CAAC;AACrB;AACA,SAAS,cAAc,GAAG;IACxB,OAAO,OAAO,QAAQ,CAAC,QAAQ,eAAe;AAChD;AAEA,gBAAgB,GAEhB,IAAI;AAEJ,KAAK,QAAQ,CAAC,UAAU;AAExB,SAAS,OAAO;AAEhB,SAAS,cAAc,OAAO,EAAE,MAAM;IACpC,SAAS;IAET,UAAU,WAAW,CAAC;IAEtB,2DAA2D;IAC3D,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,2EAA2E;IAC3E,IAAI,WAAW,kBAAkB;IAEjC,4DAA4D;IAC5D,+BAA+B;IAC/B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;IAEtC,IAAI,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,QAAQ,kBAAkB;IAE/E,oDAAoD;IACpD,iEAAiE;IACjE,0DAA0D;IAC1D,IAAI,MAAM,QAAQ,aAAa;IAC/B,IAAI,cAAc,QAAQ,qBAAqB;IAC/C,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG,KAAK,KAAK;IAE7C,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG;SAAS,IAAI,YAAY,CAAC,eAAe,gBAAgB,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG;SAAiB,IAAI,CAAC,aAAa,GAAG;IAEnK,gBAAgB;IAChB,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,aAAa;IAElD,4BAA4B;IAC5B,IAAI,CAAC,WAAW,GAAG;IAEnB,oBAAoB;IACpB,IAAI,CAAC,SAAS,GAAG;IACjB,gCAAgC;IAChC,IAAI,CAAC,MAAM,GAAG;IACd,2CAA2C;IAC3C,IAAI,CAAC,KAAK,GAAG;IACb,2BAA2B;IAC3B,IAAI,CAAC,QAAQ,GAAG;IAEhB,wBAAwB;IACxB,IAAI,CAAC,SAAS,GAAG;IAEjB,kEAAkE;IAClE,kEAAkE;IAClE,6BAA6B;IAC7B,IAAI,WAAW,QAAQ,aAAa,KAAK;IACzC,IAAI,CAAC,aAAa,GAAG,CAAC;IAEtB,sEAAsE;IACtE,6DAA6D;IAC7D,uDAAuD;IACvD,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI;IAElD,2DAA2D;IAC3D,6DAA6D;IAC7D,kBAAkB;IAClB,IAAI,CAAC,MAAM,GAAG;IAEd,qDAAqD;IACrD,IAAI,CAAC,OAAO,GAAG;IAEf,6DAA6D;IAC7D,IAAI,CAAC,MAAM,GAAG;IAEd,qEAAqE;IACrE,iEAAiE;IACjE,oEAAoE;IACpE,0CAA0C;IAC1C,IAAI,CAAC,IAAI,GAAG;IAEZ,sEAAsE;IACtE,oEAAoE;IACpE,6CAA6C;IAC7C,IAAI,CAAC,gBAAgB,GAAG;IAExB,iDAAiD;IACjD,IAAI,CAAC,OAAO,GAAG,SAAU,EAAE;QACzB,QAAQ,QAAQ;IAClB;IAEA,kEAAkE;IAClE,IAAI,CAAC,OAAO,GAAG;IAEf,0DAA0D;IAC1D,IAAI,CAAC,QAAQ,GAAG;IAEhB,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,mBAAmB,GAAG;IAE3B,kDAAkD;IAClD,gDAAgD;IAChD,IAAI,CAAC,SAAS,GAAG;IAEjB,mEAAmE;IACnE,qDAAqD;IACrD,IAAI,CAAC,WAAW,GAAG;IAEnB,uEAAuE;IACvE,IAAI,CAAC,YAAY,GAAG;IAEpB,0BAA0B;IAC1B,IAAI,CAAC,oBAAoB,GAAG;IAE5B,oDAAoD;IACpD,6DAA6D;IAC7D,IAAI,CAAC,kBAAkB,GAAG,IAAI,cAAc,IAAI;AAClD;AAEA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAS;IAC3C,IAAI,UAAU,IAAI,CAAC,eAAe;IAClC,IAAI,MAAM,EAAE;IACZ,MAAO,QAAS;QACd,IAAI,IAAI,CAAC;QACT,UAAU,QAAQ,IAAI;IACxB;IACA,OAAO;AACT;AAEA,CAAC;IACC,IAAI;QACF,OAAO,cAAc,CAAC,cAAc,SAAS,EAAE,UAAU;YACvD,KAAK,aAAa,SAAS,CAAC;gBAC1B,OAAO,IAAI,CAAC,SAAS;YACvB,GAAG,uEAAuE,YAAY;QACxF;IACF,EAAE,OAAO,GAAG,CAAC;AACf,CAAC;AAED,qEAAqE;AACrE,iDAAiD;AACjD,IAAI;AACJ,IAAI,OAAO,WAAW,cAAc,OAAO,WAAW,IAAI,OAAO,SAAS,SAAS,CAAC,OAAO,WAAW,CAAC,KAAK,YAAY;IACtH,kBAAkB,SAAS,SAAS,CAAC,OAAO,WAAW,CAAC;IACxD,OAAO,cAAc,CAAC,UAAU,OAAO,WAAW,EAAE;QAClD,OAAO,SAAU,MAAM;YACrB,IAAI,gBAAgB,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO;YAC/C,IAAI,IAAI,KAAK,UAAU,OAAO;YAE9B,OAAO,UAAU,OAAO,cAAc,YAAY;QACpD;IACF;AACF,OAAO;IACL,kBAAkB,SAAU,MAAM;QAChC,OAAO,kBAAkB,IAAI;IAC/B;AACF;AAEA,SAAS,SAAS,OAAO;IACvB,SAAS;IAET,6CAA6C;IAC7C,kEAAkE;IAClE,mEAAmE;IAEnE,8EAA8E;IAC9E,2EAA2E;IAC3E,0DAA0D;IAC1D,IAAI,CAAC,gBAAgB,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC,IAAI,YAAY,MAAM,GAAG;QACtE,OAAO,IAAI,SAAS;IACtB;IAEA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,SAAS,IAAI;IAErD,UAAU;IACV,IAAI,CAAC,QAAQ,GAAG;IAEhB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QAEpE,IAAI,OAAO,QAAQ,MAAM,KAAK,YAAY,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;QAEvE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;QAE1E,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IACtE;IAEA,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,mEAAmE;AACnE,SAAS,SAAS,CAAC,IAAI,GAAG;IACxB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM;AAC/B;AAEA,SAAS,cAAc,MAAM,EAAE,EAAE;IAC/B,IAAI,KAAK,IAAI,MAAM;IACnB,oEAAoE;IACpE,OAAO,IAAI,CAAC,SAAS;IACrB,IAAI,QAAQ,CAAC,IAAI;AACnB;AAEA,4EAA4E;AAC5E,4EAA4E;AAC5E,mEAAmE;AACnE,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;IAC1C,IAAI,QAAQ;IACZ,IAAI,KAAK;IAET,IAAI,UAAU,MAAM;QAClB,KAAK,IAAI,UAAU;IACrB,OAAO,IAAI,OAAO,UAAU,YAAY,UAAU,aAAa,CAAC,MAAM,UAAU,EAAE;QAChF,KAAK,IAAI,UAAU;IACrB;IACA,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,SAAS;QACrB,IAAI,QAAQ,CAAC,IAAI;QACjB,QAAQ;IACV;IACA,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,KAAK,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACtD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,MAAM;IACV,IAAI,QAAQ,CAAC,MAAM,UAAU,IAAI,cAAc;IAE/C,IAAI,SAAS,CAAC,OAAO,QAAQ,CAAC,QAAQ;QACpC,QAAQ,oBAAoB;IAC9B;IAEA,IAAI,OAAO,aAAa,YAAY;QAClC,KAAK;QACL,WAAW;IACb;IAEA,IAAI,OAAO,WAAW;SAAc,IAAI,CAAC,UAAU,WAAW,MAAM,eAAe;IAEnF,IAAI,OAAO,OAAO,YAAY,KAAK;IAEnC,IAAI,MAAM,KAAK,EAAE,cAAc,IAAI,EAAE;SAAS,IAAI,SAAS,WAAW,IAAI,EAAE,OAAO,OAAO,KAAK;QAC7F,MAAM,SAAS;QACf,MAAM,cAAc,IAAI,EAAE,OAAO,OAAO,OAAO,UAAU;IAC3D;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,IAAI,GAAG;IACxB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,MAAM,MAAM;AACd;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG;IAC1B,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM;QAEZ,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,gBAAgB,IAAI,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE;IAC7G;AACF;AAEA,SAAS,SAAS,CAAC,kBAAkB,GAAG,SAAS,mBAAmB,QAAQ;IAC1E,6CAA6C;IAC7C,IAAI,OAAO,aAAa,UAAU,WAAW,SAAS,WAAW;IACjE,IAAI,CAAC,CAAC;QAAC;QAAO;QAAQ;QAAS;QAAS;QAAU;QAAU;QAAQ;QAAS;QAAW;QAAY;KAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,WAAW,MAAM,CAAC,CAAC,GAAG,MAAM,IAAI,UAAU,uBAAuB;IACpM,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG;IACtC,OAAO,IAAI;AACb;AAEA,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,QAAQ;IACzC,IAAI,CAAC,MAAM,UAAU,IAAI,MAAM,aAAa,KAAK,SAAS,OAAO,UAAU,UAAU;QACnF,QAAQ,OAAO,IAAI,CAAC,OAAO;IAC7B;IACA,OAAO;AACT;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK;QACH,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AAEA,yDAAyD;AACzD,2DAA2D;AAC3D,oEAAoE;AACpE,SAAS,cAAc,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,IAAI,CAAC,OAAO;QACV,IAAI,WAAW,YAAY,OAAO,OAAO;QACzC,IAAI,UAAU,UAAU;YACtB,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;IACF;IACA,IAAI,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;IAE7C,MAAM,MAAM,IAAI;IAEhB,IAAI,MAAM,MAAM,MAAM,GAAG,MAAM,aAAa;IAC5C,qEAAqE;IACrE,IAAI,CAAC,KAAK,MAAM,SAAS,GAAG;IAE5B,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE;QACjC,IAAI,OAAO,MAAM,mBAAmB;QACpC,MAAM,mBAAmB,GAAG;YAC1B,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;QACR;QACA,IAAI,MAAM;YACR,KAAK,IAAI,GAAG,MAAM,mBAAmB;QACvC,OAAO;YACL,MAAM,eAAe,GAAG,MAAM,mBAAmB;QACnD;QACA,MAAM,oBAAoB,IAAI;IAChC,OAAO;QACL,QAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU;IACtD;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,MAAM,QAAQ,GAAG;IACjB,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG;IAChB,MAAM,IAAI,GAAG;IACb,IAAI,QAAQ,OAAO,OAAO,CAAC,OAAO,MAAM,OAAO;SAAO,OAAO,MAAM,CAAC,OAAO,UAAU,MAAM,OAAO;IAClG,MAAM,IAAI,GAAG;AACf;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;IAC/C,EAAE,MAAM,SAAS;IAEjB,IAAI,MAAM;QACR,0DAA0D;QAC1D,yCAAyC;QACzC,IAAI,QAAQ,CAAC,IAAI;QACjB,kDAAkD;QAClD,cAAc;QACd,IAAI,QAAQ,CAAC,aAAa,QAAQ;QAClC,OAAO,cAAc,CAAC,YAAY,GAAG;QACrC,OAAO,IAAI,CAAC,SAAS;IACvB,OAAO;QACL,6CAA6C;QAC7C,cAAc;QACd,GAAG;QACH,OAAO,cAAc,CAAC,YAAY,GAAG;QACrC,OAAO,IAAI,CAAC,SAAS;QACrB,wCAAwC;QACxC,sBAAsB;QACtB,YAAY,QAAQ;IACtB;AACF;AAEA,SAAS,mBAAmB,KAAK;IAC/B,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG;IAChB,MAAM,MAAM,IAAI,MAAM,QAAQ;IAC9B,MAAM,QAAQ,GAAG;AACnB;AAEA,SAAS,QAAQ,MAAM,EAAE,EAAE;IACzB,IAAI,QAAQ,OAAO,cAAc;IACjC,IAAI,OAAO,MAAM,IAAI;IACrB,IAAI,KAAK,MAAM,OAAO;IAEtB,mBAAmB;IAEnB,IAAI,IAAI,aAAa,QAAQ,OAAO,MAAM,IAAI;SAAS;QACrD,8DAA8D;QAC9D,IAAI,WAAW,WAAW;QAE1B,IAAI,CAAC,YAAY,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,gBAAgB,IAAI,MAAM,eAAe,EAAE;YAClF,YAAY,QAAQ;QACtB;QAEA,IAAI,MAAM;YACR,eAAe,GACf,WAAW,YAAY,QAAQ,OAAO,UAAU;QAChD,gBAAgB,GAClB,OAAO;YACL,WAAW,QAAQ,OAAO,UAAU;QACtC;IACF;AACF;AAEA,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC7C,IAAI,CAAC,UAAU,aAAa,QAAQ;IACpC,MAAM,SAAS;IACf;IACA,YAAY,QAAQ;AACtB;AAEA,iEAAiE;AACjE,mEAAmE;AACnE,wDAAwD;AACxD,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,SAAS,EAAE;QACzC,MAAM,SAAS,GAAG;QAClB,OAAO,IAAI,CAAC;IACd;AACF;AAEA,8DAA8D;AAC9D,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,MAAM,gBAAgB,GAAG;IACzB,IAAI,QAAQ,MAAM,eAAe;IAEjC,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,IAAI,EAAE;QACzC,8CAA8C;QAC9C,IAAI,IAAI,MAAM,oBAAoB;QAClC,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,SAAS,MAAM,kBAAkB;QACrC,OAAO,KAAK,GAAG;QAEf,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,MAAO,MAAO;YACZ,MAAM,CAAC,MAAM,GAAG;YAChB,IAAI,CAAC,MAAM,KAAK,EAAE,aAAa;YAC/B,QAAQ,MAAM,IAAI;YAClB,SAAS;QACX;QACA,OAAO,UAAU,GAAG;QAEpB,QAAQ,QAAQ,OAAO,MAAM,MAAM,MAAM,EAAE,QAAQ,IAAI,OAAO,MAAM;QAEpE,oEAAoE;QACpE,oCAAoC;QACpC,MAAM,SAAS;QACf,MAAM,mBAAmB,GAAG;QAC5B,IAAI,OAAO,IAAI,EAAE;YACf,MAAM,kBAAkB,GAAG,OAAO,IAAI;YACtC,OAAO,IAAI,GAAG;QAChB,OAAO;YACL,MAAM,kBAAkB,GAAG,IAAI,cAAc;QAC/C;QACA,MAAM,oBAAoB,GAAG;IAC/B,OAAO;QACL,qCAAqC;QACrC,MAAO,MAAO;YACZ,IAAI,QAAQ,MAAM,KAAK;YACvB,IAAI,WAAW,MAAM,QAAQ;YAC7B,IAAI,KAAK,MAAM,QAAQ;YACvB,IAAI,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;YAE7C,QAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU;YACpD,QAAQ,MAAM,IAAI;YAClB,MAAM,oBAAoB;YAC1B,kDAAkD;YAClD,+CAA+C;YAC/C,uDAAuD;YACvD,yDAAyD;YACzD,IAAI,MAAM,OAAO,EAAE;gBACjB;YACF;QACF;QAEA,IAAI,UAAU,MAAM,MAAM,mBAAmB,GAAG;IAClD;IAEA,MAAM,eAAe,GAAG;IACxB,MAAM,gBAAgB,GAAG;AAC3B;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACvD,GAAG,IAAI,MAAM;AACf;AAEA,SAAS,SAAS,CAAC,OAAO,GAAG;AAE7B,SAAS,SAAS,CAAC,GAAG,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACpD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,OAAO,UAAU,YAAY;QAC/B,KAAK;QACL,QAAQ;QACR,WAAW;IACb,OAAO,IAAI,OAAO,aAAa,YAAY;QACzC,KAAK;QACL,WAAW;IACb;IAEA,IAAI,UAAU,QAAQ,UAAU,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO;IAE7D,uBAAuB;IACvB,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM,GAAG;QACf,IAAI,CAAC,MAAM;IACb;IAEA,kCAAkC;IAClC,IAAI,CAAC,MAAM,MAAM,EAAE,YAAY,IAAI,EAAE,OAAO;AAC9C;AAEA,SAAS,WAAW,KAAK;IACvB,OAAO,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,eAAe,KAAK,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,OAAO;AAClH;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,OAAO,MAAM,CAAC,SAAU,GAAG;QACzB,MAAM,SAAS;QACf,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,SAAS;QACvB;QACA,MAAM,WAAW,GAAG;QACpB,OAAO,IAAI,CAAC;QACZ,YAAY,QAAQ;IACtB;AACF;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,WAAW,EAAE;QAC5C,IAAI,OAAO,OAAO,MAAM,KAAK,YAAY;YACvC,MAAM,SAAS;YACf,MAAM,WAAW,GAAG;YACpB,IAAI,QAAQ,CAAC,WAAW,QAAQ;QAClC,OAAO;YACL,MAAM,WAAW,GAAG;YACpB,OAAO,IAAI,CAAC;QACd;IACF;AACF;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,OAAO,WAAW;IACtB,IAAI,MAAM;QACR,UAAU,QAAQ;QAClB,IAAI,MAAM,SAAS,KAAK,GAAG;YACzB,MAAM,QAAQ,GAAG;YACjB,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK,EAAE,EAAE;IACpC,MAAM,MAAM,GAAG;IACf,YAAY,QAAQ;IACpB,IAAI,IAAI;QACN,IAAI,MAAM,QAAQ,EAAE,IAAI,QAAQ,CAAC;aAAS,OAAO,IAAI,CAAC,UAAU;IAClE;IACA,MAAM,KAAK,GAAG;IACd,OAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,eAAe,OAAO,EAAE,KAAK,EAAE,GAAG;IACzC,IAAI,QAAQ,QAAQ,KAAK;IACzB,QAAQ,KAAK,GAAG;IAChB,MAAO,MAAO;QACZ,IAAI,KAAK,MAAM,QAAQ;QACvB,MAAM,SAAS;QACf,GAAG;QACH,QAAQ,MAAM,IAAI;IACpB;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC,IAAI,GAAG;AAClC;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,aAAa;IACrD,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,KAAK,WAAW;YACrC,OAAO;QACT;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;IACtC;IACA,KAAK,SAAU,KAAK;QAClB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;QACF;QAEA,iDAAiD;QACjD,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AAEA,SAAS,SAAS,CAAC,OAAO,GAAG,YAAY,OAAO;AAChD,SAAS,SAAS,CAAC,UAAU,GAAG,YAAY,SAAS;AACrD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC7C,IAAI,CAAC,GAAG;IACR,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/_stream_duplex.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n\n'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\n/*<replacement>*/\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n  for (var key in obj) {\n    keys.push(key);\n  }return keys;\n};\n/*</replacement>*/\n\nmodule.exports = Duplex;\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\nvar Readable = require('./_stream_readable');\nvar Writable = require('./_stream_writable');\n\nutil.inherits(Duplex, Readable);\n\n{\n  // avoid scope creep, the keys array can then be collected\n  var keys = objectKeys(Writable.prototype);\n  for (var v = 0; v < keys.length; v++) {\n    var method = keys[v];\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n  }\n}\n\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options);\n\n  Readable.call(this, options);\n  Writable.call(this, options);\n\n  if (options && options.readable === false) this.readable = false;\n\n  if (options && options.writable === false) this.writable = false;\n\n  this.allowHalfOpen = true;\n  if (options && options.allowHalfOpen === false) this.allowHalfOpen = false;\n\n  this.once('end', onend);\n}\n\nObject.defineProperty(Duplex.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function () {\n    return this._writableState.highWaterMark;\n  }\n});\n\n// the no-half-open enforcer\nfunction onend() {\n  // if we allow half-open state, or if the writable side ended,\n  // then we're ok.\n  if (this.allowHalfOpen || this._writableState.ended) return;\n\n  // no more data can be written.\n  // But allow more writes to happen in this tick.\n  pna.nextTick(onEndNT, this);\n}\n\nfunction onEndNT(self) {\n  self.end();\n}\n\nObject.defineProperty(Duplex.prototype, 'destroyed', {\n  get: function () {\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed && this._writableState.destroyed;\n  },\n  set: function (value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n    this._writableState.destroyed = value;\n  }\n});\n\nDuplex.prototype._destroy = function (err, cb) {\n  this.push(null);\n  this.end();\n\n  pna.nextTick(cb, err);\n};"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,uEAAuE;AACvE,oEAAoE;AACpE,mEAAmE;AACnE,YAAY;AAEZ;AAEA,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GACf,IAAI,aAAa,OAAO,IAAI,IAAI,SAAU,GAAG;IAC3C,IAAI,OAAO,EAAE;IACb,IAAK,IAAI,OAAO,IAAK;QACnB,KAAK,IAAI,CAAC;IACZ;IAAC,OAAO;AACV;AACA,gBAAgB,GAEhB,OAAO,OAAO,GAAG;AAEjB,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,IAAI;AACJ,IAAI;AAEJ,KAAK,QAAQ,CAAC,QAAQ;AAEtB;IACE,0DAA0D;IAC1D,IAAI,OAAO,WAAW,SAAS,SAAS;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,SAAS,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC,OAAO,SAAS,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,SAAS,CAAC,OAAO;IACtF;AACF,CAEA,SAAS,OAAO,OAAO;IACrB,IAAI,CAAC,CAAC,IAAI,YAAY,MAAM,GAAG,OAAO,IAAI,OAAO;IAEjD,SAAS,IAAI,CAAC,IAAI,EAAE;IACpB,SAAS,IAAI,CAAC,IAAI,EAAE;IAEpB,IAAI,WAAW,QAAQ,QAAQ,KAAK,OAAO,IAAI,CAAC,QAAQ,GAAG;IAE3D,IAAI,WAAW,QAAQ,QAAQ,KAAK,OAAO,IAAI,CAAC,QAAQ,GAAG;IAE3D,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,WAAW,QAAQ,aAAa,KAAK,OAAO,IAAI,CAAC,aAAa,GAAG;IAErE,IAAI,CAAC,IAAI,CAAC,OAAO;AACnB;AAEA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,yBAAyB;IAC/D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK;QACH,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AAEA,4BAA4B;AAC5B,SAAS;IACP,8DAA8D;IAC9D,iBAAiB;IACjB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;IAErD,+BAA+B;IAC/B,gDAAgD;IAChD,IAAI,QAAQ,CAAC,SAAS,IAAI;AAC5B;AAEA,SAAS,QAAQ,IAAI;IACnB,KAAK,GAAG;AACV;AAEA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,aAAa;IACnD,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,IAAI,CAAC,cAAc,KAAK,WAAW;YAC1E,OAAO;QACT;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IACvE;IACA,KAAK,SAAU,KAAK;QAClB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,IAAI,CAAC,cAAc,KAAK,WAAW;YAC1E;QACF;QAEA,iDAAiD;QACjD,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAChC,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AAEA,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC3C,IAAI,CAAC,IAAI,CAAC;IACV,IAAI,CAAC,GAAG;IAER,IAAI,QAAQ,CAAC,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/_stream_readable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\nmodule.exports = Readable;\n\n/*<replacement>*/\nvar isArray = require('isarray');\n/*</replacement>*/\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nReadable.ReadableState = ReadableState;\n\n/*<replacement>*/\nvar EE = require('events').EventEmitter;\n\nvar EElistenerCount = function (emitter, type) {\n  return emitter.listeners(type).length;\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\n/*</replacement>*/\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\n/*<replacement>*/\nvar debugUtil = require('util');\nvar debug = void 0;\nif (debugUtil && debugUtil.debuglog) {\n  debug = debugUtil.debuglog('stream');\n} else {\n  debug = function () {};\n}\n/*</replacement>*/\n\nvar BufferList = require('./internal/streams/BufferList');\nvar destroyImpl = require('./internal/streams/destroy');\nvar StringDecoder;\n\nutil.inherits(Readable, Stream);\n\nvar kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume'];\n\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn);\n\n  // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];\n}\n\nfunction ReadableState(options, stream) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  var isDuplex = stream instanceof Duplex;\n\n  // object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away\n  this.objectMode = !!options.objectMode;\n\n  if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode;\n\n  // the point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n  var hwm = options.highWaterMark;\n  var readableHwm = options.readableHighWaterMark;\n  var defaultHwm = this.objectMode ? 16 : 16 * 1024;\n\n  if (hwm || hwm === 0) this.highWaterMark = hwm;else if (isDuplex && (readableHwm || readableHwm === 0)) this.highWaterMark = readableHwm;else this.highWaterMark = defaultHwm;\n\n  // cast to ints.\n  this.highWaterMark = Math.floor(this.highWaterMark);\n\n  // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift()\n  this.buffer = new BufferList();\n  this.length = 0;\n  this.pipes = null;\n  this.pipesCount = 0;\n  this.flowing = null;\n  this.ended = false;\n  this.endEmitted = false;\n  this.reading = false;\n\n  // a flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n  this.sync = true;\n\n  // whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n  this.needReadable = false;\n  this.emittedReadable = false;\n  this.readableListening = false;\n  this.resumeScheduled = false;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // the number of writers that are awaiting a drain event in .pipe()s\n  this.awaitDrain = 0;\n\n  // if true, a maybeReadMore has been scheduled\n  this.readingMore = false;\n\n  this.decoder = null;\n  this.encoding = null;\n  if (options.encoding) {\n    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n    this.decoder = new StringDecoder(options.encoding);\n    this.encoding = options.encoding;\n  }\n}\n\nfunction Readable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  if (!(this instanceof Readable)) return new Readable(options);\n\n  this._readableState = new ReadableState(options, this);\n\n  // legacy\n  this.readable = true;\n\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read;\n\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n  }\n\n  Stream.call(this);\n}\n\nObject.defineProperty(Readable.prototype, 'destroyed', {\n  get: function () {\n    if (this._readableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed;\n  },\n  set: function (value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._readableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n  }\n});\n\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\nReadable.prototype._destroy = function (err, cb) {\n  this.push(null);\n  cb(err);\n};\n\n// Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\nReadable.prototype.push = function (chunk, encoding) {\n  var state = this._readableState;\n  var skipChunkCheck;\n\n  if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding;\n      if (encoding !== state.encoding) {\n        chunk = Buffer.from(chunk, encoding);\n        encoding = '';\n      }\n      skipChunkCheck = true;\n    }\n  } else {\n    skipChunkCheck = true;\n  }\n\n  return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n};\n\n// Unshift should *always* be something directly out of read()\nReadable.prototype.unshift = function (chunk) {\n  return readableAddChunk(this, chunk, null, true, false);\n};\n\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n  var state = stream._readableState;\n  if (chunk === null) {\n    state.reading = false;\n    onEofChunk(stream, state);\n  } else {\n    var er;\n    if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n    if (er) {\n      stream.emit('error', er);\n    } else if (state.objectMode || chunk && chunk.length > 0) {\n      if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n        chunk = _uint8ArrayToBuffer(chunk);\n      }\n\n      if (addToFront) {\n        if (state.endEmitted) stream.emit('error', new Error('stream.unshift() after end event'));else addChunk(stream, state, chunk, true);\n      } else if (state.ended) {\n        stream.emit('error', new Error('stream.push() after EOF'));\n      } else {\n        state.reading = false;\n        if (state.decoder && !encoding) {\n          chunk = state.decoder.write(chunk);\n          if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);else maybeReadMore(stream, state);\n        } else {\n          addChunk(stream, state, chunk, false);\n        }\n      }\n    } else if (!addToFront) {\n      state.reading = false;\n    }\n  }\n\n  return needMoreData(state);\n}\n\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync) {\n    stream.emit('data', chunk);\n    stream.read(0);\n  } else {\n    // update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length;\n    if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);\n\n    if (state.needReadable) emitReadable(stream);\n  }\n  maybeReadMore(stream, state);\n}\n\nfunction chunkInvalid(state, chunk) {\n  var er;\n  if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new TypeError('Invalid non-string/buffer chunk');\n  }\n  return er;\n}\n\n// if it's past the high water mark, we can push in some more.\n// Also, if we have no data yet, we can stand some\n// more bytes.  This is to work around cases where hwm=0,\n// such as the repl.  Also, if the push() triggered a\n// readable event, and the user called read(largeNumber) such that\n// needReadable was set, then we ought to push more, so that another\n// 'readable' event will be triggered.\nfunction needMoreData(state) {\n  return !state.ended && (state.needReadable || state.length < state.highWaterMark || state.length === 0);\n}\n\nReadable.prototype.isPaused = function () {\n  return this._readableState.flowing === false;\n};\n\n// backwards compatibility.\nReadable.prototype.setEncoding = function (enc) {\n  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n  this._readableState.decoder = new StringDecoder(enc);\n  this._readableState.encoding = enc;\n  return this;\n};\n\n// Don't raise the hwm > 8MB\nvar MAX_HWM = 0x800000;\nfunction computeNewHighWaterMark(n) {\n  if (n >= MAX_HWM) {\n    n = MAX_HWM;\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts\n    n--;\n    n |= n >>> 1;\n    n |= n >>> 2;\n    n |= n >>> 4;\n    n |= n >>> 8;\n    n |= n >>> 16;\n    n++;\n  }\n  return n;\n}\n\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || state.length === 0 && state.ended) return 0;\n  if (state.objectMode) return 1;\n  if (n !== n) {\n    // Only flow one buffer at a time\n    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;\n  }\n  // If we're asking for more than the current hwm, then raise the hwm.\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n  if (n <= state.length) return n;\n  // Don't have enough\n  if (!state.ended) {\n    state.needReadable = true;\n    return 0;\n  }\n  return state.length;\n}\n\n// you can override either this method, or the async _read(n) below.\nReadable.prototype.read = function (n) {\n  debug('read', n);\n  n = parseInt(n, 10);\n  var state = this._readableState;\n  var nOrig = n;\n\n  if (n !== 0) state.emittedReadable = false;\n\n  // if we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n  if (n === 0 && state.needReadable && (state.length >= state.highWaterMark || state.ended)) {\n    debug('read: emitReadable', state.length, state.ended);\n    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);\n    return null;\n  }\n\n  n = howMuchToRead(n, state);\n\n  // if we've ended, and we're now clear, then finish it up.\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this);\n    return null;\n  }\n\n  // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n\n  // if we need a readable event, then we need to do some reading.\n  var doRead = state.needReadable;\n  debug('need readable', doRead);\n\n  // if we currently have less than the highWaterMark, then also read some\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true;\n    debug('length less than watermark', doRead);\n  }\n\n  // however, if we've ended, then there's no point, and if we're already\n  // reading, then it's unnecessary.\n  if (state.ended || state.reading) {\n    doRead = false;\n    debug('reading or ended', doRead);\n  } else if (doRead) {\n    debug('do read');\n    state.reading = true;\n    state.sync = true;\n    // if the length is currently zero, then we *need* a readable event.\n    if (state.length === 0) state.needReadable = true;\n    // call internal read method\n    this._read(state.highWaterMark);\n    state.sync = false;\n    // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n    if (!state.reading) n = howMuchToRead(nOrig, state);\n  }\n\n  var ret;\n  if (n > 0) ret = fromList(n, state);else ret = null;\n\n  if (ret === null) {\n    state.needReadable = true;\n    n = 0;\n  } else {\n    state.length -= n;\n  }\n\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true;\n\n    // If we tried to read() past the EOF, then emit end on the next tick.\n    if (nOrig !== n && state.ended) endReadable(this);\n  }\n\n  if (ret !== null) this.emit('data', ret);\n\n  return ret;\n};\n\nfunction onEofChunk(stream, state) {\n  if (state.ended) return;\n  if (state.decoder) {\n    var chunk = state.decoder.end();\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk);\n      state.length += state.objectMode ? 1 : chunk.length;\n    }\n  }\n  state.ended = true;\n\n  // emit 'readable' now to make sure it gets picked up.\n  emitReadable(stream);\n}\n\n// Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\nfunction emitReadable(stream) {\n  var state = stream._readableState;\n  state.needReadable = false;\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing);\n    state.emittedReadable = true;\n    if (state.sync) pna.nextTick(emitReadable_, stream);else emitReadable_(stream);\n  }\n}\n\nfunction emitReadable_(stream) {\n  debug('emit readable');\n  stream.emit('readable');\n  flow(stream);\n}\n\n// at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore) {\n    state.readingMore = true;\n    pna.nextTick(maybeReadMore_, stream, state);\n  }\n}\n\nfunction maybeReadMore_(stream, state) {\n  var len = state.length;\n  while (!state.reading && !state.flowing && !state.ended && state.length < state.highWaterMark) {\n    debug('maybeReadMore read 0');\n    stream.read(0);\n    if (len === state.length)\n      // didn't get any data, stop spinning.\n      break;else len = state.length;\n  }\n  state.readingMore = false;\n}\n\n// abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\nReadable.prototype._read = function (n) {\n  this.emit('error', new Error('_read() is not implemented'));\n};\n\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  var src = this;\n  var state = this._readableState;\n\n  switch (state.pipesCount) {\n    case 0:\n      state.pipes = dest;\n      break;\n    case 1:\n      state.pipes = [state.pipes, dest];\n      break;\n    default:\n      state.pipes.push(dest);\n      break;\n  }\n  state.pipesCount += 1;\n  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);\n\n  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n\n  var endFn = doEnd ? onend : unpipe;\n  if (state.endEmitted) pna.nextTick(endFn);else src.once('end', endFn);\n\n  dest.on('unpipe', onunpipe);\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe');\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true;\n        cleanup();\n      }\n    }\n  }\n\n  function onend() {\n    debug('onend');\n    dest.end();\n  }\n\n  // when the dest drains, it reduces the awaitDrain counter\n  // on the source.  This would be more elegant with a .once()\n  // handler in flow(), but adding and removing repeatedly is\n  // too slow.\n  var ondrain = pipeOnDrain(src);\n  dest.on('drain', ondrain);\n\n  var cleanedUp = false;\n  function cleanup() {\n    debug('cleanup');\n    // cleanup event handlers once the pipe is broken\n    dest.removeListener('close', onclose);\n    dest.removeListener('finish', onfinish);\n    dest.removeListener('drain', ondrain);\n    dest.removeListener('error', onerror);\n    dest.removeListener('unpipe', onunpipe);\n    src.removeListener('end', onend);\n    src.removeListener('end', unpipe);\n    src.removeListener('data', ondata);\n\n    cleanedUp = true;\n\n    // if the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n  }\n\n  // If the user pushes more data while we're writing to dest then we'll end up\n  // in ondata again. However, we only want to increase awaitDrain once because\n  // dest will only emit one 'drain' event for the multiple writes.\n  // => Introduce a guard on increasing awaitDrain.\n  var increasedAwaitDrain = false;\n  src.on('data', ondata);\n  function ondata(chunk) {\n    debug('ondata');\n    increasedAwaitDrain = false;\n    var ret = dest.write(chunk);\n    if (false === ret && !increasedAwaitDrain) {\n      // If the user unpiped during `dest.write()`, it is possible\n      // to get stuck in a permanently paused state if that write\n      // also returned false.\n      // => Check whether `dest` is still a piping destination.\n      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n        debug('false write response, pause', state.awaitDrain);\n        state.awaitDrain++;\n        increasedAwaitDrain = true;\n      }\n      src.pause();\n    }\n  }\n\n  // if the dest has an error, then stop piping into it.\n  // however, don't suppress the throwing behavior for this.\n  function onerror(er) {\n    debug('onerror', er);\n    unpipe();\n    dest.removeListener('error', onerror);\n    if (EElistenerCount(dest, 'error') === 0) dest.emit('error', er);\n  }\n\n  // Make sure our error handler is attached before userland ones.\n  prependListener(dest, 'error', onerror);\n\n  // Both close and finish should trigger unpipe, but only once.\n  function onclose() {\n    dest.removeListener('finish', onfinish);\n    unpipe();\n  }\n  dest.once('close', onclose);\n  function onfinish() {\n    debug('onfinish');\n    dest.removeListener('close', onclose);\n    unpipe();\n  }\n  dest.once('finish', onfinish);\n\n  function unpipe() {\n    debug('unpipe');\n    src.unpipe(dest);\n  }\n\n  // tell the dest that it's being piped to\n  dest.emit('pipe', src);\n\n  // start the flow if it hasn't been started already.\n  if (!state.flowing) {\n    debug('pipe resume');\n    src.resume();\n  }\n\n  return dest;\n};\n\nfunction pipeOnDrain(src) {\n  return function () {\n    var state = src._readableState;\n    debug('pipeOnDrain', state.awaitDrain);\n    if (state.awaitDrain) state.awaitDrain--;\n    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {\n      state.flowing = true;\n      flow(src);\n    }\n  };\n}\n\nReadable.prototype.unpipe = function (dest) {\n  var state = this._readableState;\n  var unpipeInfo = { hasUnpiped: false };\n\n  // if we're not piping anywhere, then do nothing.\n  if (state.pipesCount === 0) return this;\n\n  // just one destination.  most common case.\n  if (state.pipesCount === 1) {\n    // passed in one, but it's not the right one.\n    if (dest && dest !== state.pipes) return this;\n\n    if (!dest) dest = state.pipes;\n\n    // got a match.\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    if (dest) dest.emit('unpipe', this, unpipeInfo);\n    return this;\n  }\n\n  // slow case. multiple pipe destinations.\n\n  if (!dest) {\n    // remove all.\n    var dests = state.pipes;\n    var len = state.pipesCount;\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n\n    for (var i = 0; i < len; i++) {\n      dests[i].emit('unpipe', this, { hasUnpiped: false });\n    }return this;\n  }\n\n  // try to find the right one.\n  var index = indexOf(state.pipes, dest);\n  if (index === -1) return this;\n\n  state.pipes.splice(index, 1);\n  state.pipesCount -= 1;\n  if (state.pipesCount === 1) state.pipes = state.pipes[0];\n\n  dest.emit('unpipe', this, unpipeInfo);\n\n  return this;\n};\n\n// set up data events if they are asked for\n// Ensure readable listeners eventually get something\nReadable.prototype.on = function (ev, fn) {\n  var res = Stream.prototype.on.call(this, ev, fn);\n\n  if (ev === 'data') {\n    // Start flowing on next tick if stream isn't explicitly paused\n    if (this._readableState.flowing !== false) this.resume();\n  } else if (ev === 'readable') {\n    var state = this._readableState;\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true;\n      state.emittedReadable = false;\n      if (!state.reading) {\n        pna.nextTick(nReadingNextTick, this);\n      } else if (state.length) {\n        emitReadable(this);\n      }\n    }\n  }\n\n  return res;\n};\nReadable.prototype.addListener = Readable.prototype.on;\n\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0');\n  self.read(0);\n}\n\n// pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\nReadable.prototype.resume = function () {\n  var state = this._readableState;\n  if (!state.flowing) {\n    debug('resume');\n    state.flowing = true;\n    resume(this, state);\n  }\n  return this;\n};\n\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true;\n    pna.nextTick(resume_, stream, state);\n  }\n}\n\nfunction resume_(stream, state) {\n  if (!state.reading) {\n    debug('resume read 0');\n    stream.read(0);\n  }\n\n  state.resumeScheduled = false;\n  state.awaitDrain = 0;\n  stream.emit('resume');\n  flow(stream);\n  if (state.flowing && !state.reading) stream.read(0);\n}\n\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing);\n  if (false !== this._readableState.flowing) {\n    debug('pause');\n    this._readableState.flowing = false;\n    this.emit('pause');\n  }\n  return this;\n};\n\nfunction flow(stream) {\n  var state = stream._readableState;\n  debug('flow', state.flowing);\n  while (state.flowing && stream.read() !== null) {}\n}\n\n// wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\nReadable.prototype.wrap = function (stream) {\n  var _this = this;\n\n  var state = this._readableState;\n  var paused = false;\n\n  stream.on('end', function () {\n    debug('wrapped end');\n    if (state.decoder && !state.ended) {\n      var chunk = state.decoder.end();\n      if (chunk && chunk.length) _this.push(chunk);\n    }\n\n    _this.push(null);\n  });\n\n  stream.on('data', function (chunk) {\n    debug('wrapped data');\n    if (state.decoder) chunk = state.decoder.write(chunk);\n\n    // don't skip over falsy values in objectMode\n    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;\n\n    var ret = _this.push(chunk);\n    if (!ret) {\n      paused = true;\n      stream.pause();\n    }\n  });\n\n  // proxy all the other methods.\n  // important when wrapping filters and duplexes.\n  for (var i in stream) {\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = function (method) {\n        return function () {\n          return stream[method].apply(stream, arguments);\n        };\n      }(i);\n    }\n  }\n\n  // proxy certain important events.\n  for (var n = 0; n < kProxyEvents.length; n++) {\n    stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n  }\n\n  // when we try to consume some more bytes, simply unpause the\n  // underlying stream.\n  this._read = function (n) {\n    debug('wrapped _read', n);\n    if (paused) {\n      paused = false;\n      stream.resume();\n    }\n  };\n\n  return this;\n};\n\nObject.defineProperty(Readable.prototype, 'readableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function () {\n    return this._readableState.highWaterMark;\n  }\n});\n\n// exposed for testing purposes only.\nReadable._fromList = fromList;\n\n// Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromList(n, state) {\n  // nothing buffered\n  if (state.length === 0) return null;\n\n  var ret;\n  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {\n    // read it all, truncate the list\n    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.head.data;else ret = state.buffer.concat(state.length);\n    state.buffer.clear();\n  } else {\n    // read part of list\n    ret = fromListPartial(n, state.buffer, state.decoder);\n  }\n\n  return ret;\n}\n\n// Extracts only enough buffered data to satisfy the amount requested.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromListPartial(n, list, hasStrings) {\n  var ret;\n  if (n < list.head.data.length) {\n    // slice is the same for buffers and strings\n    ret = list.head.data.slice(0, n);\n    list.head.data = list.head.data.slice(n);\n  } else if (n === list.head.data.length) {\n    // first chunk is a perfect match\n    ret = list.shift();\n  } else {\n    // result spans more than one buffer\n    ret = hasStrings ? copyFromBufferString(n, list) : copyFromBuffer(n, list);\n  }\n  return ret;\n}\n\n// Copies a specified amount of characters from the list of buffered data\n// chunks.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction copyFromBufferString(n, list) {\n  var p = list.head;\n  var c = 1;\n  var ret = p.data;\n  n -= ret.length;\n  while (p = p.next) {\n    var str = p.data;\n    var nb = n > str.length ? str.length : n;\n    if (nb === str.length) ret += str;else ret += str.slice(0, n);\n    n -= nb;\n    if (n === 0) {\n      if (nb === str.length) {\n        ++c;\n        if (p.next) list.head = p.next;else list.head = list.tail = null;\n      } else {\n        list.head = p;\n        p.data = str.slice(nb);\n      }\n      break;\n    }\n    ++c;\n  }\n  list.length -= c;\n  return ret;\n}\n\n// Copies a specified amount of bytes from the list of buffered data chunks.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction copyFromBuffer(n, list) {\n  var ret = Buffer.allocUnsafe(n);\n  var p = list.head;\n  var c = 1;\n  p.data.copy(ret);\n  n -= p.data.length;\n  while (p = p.next) {\n    var buf = p.data;\n    var nb = n > buf.length ? buf.length : n;\n    buf.copy(ret, ret.length - n, 0, nb);\n    n -= nb;\n    if (n === 0) {\n      if (nb === buf.length) {\n        ++c;\n        if (p.next) list.head = p.next;else list.head = list.tail = null;\n      } else {\n        list.head = p;\n        p.data = buf.slice(nb);\n      }\n      break;\n    }\n    ++c;\n  }\n  list.length -= c;\n  return ret;\n}\n\nfunction endReadable(stream) {\n  var state = stream._readableState;\n\n  // If we get here before consuming all the bytes, then that is a\n  // bug in node.  Should never happen.\n  if (state.length > 0) throw new Error('\"endReadable()\" called on non-empty stream');\n\n  if (!state.endEmitted) {\n    state.ended = true;\n    pna.nextTick(endReadableNT, state, stream);\n  }\n}\n\nfunction endReadableNT(state, stream) {\n  // Check that we didn't get one last unshift.\n  if (!state.endEmitted && state.length === 0) {\n    state.endEmitted = true;\n    stream.readable = false;\n    stream.emit('end');\n  }\n}\n\nfunction indexOf(xs, x) {\n  for (var i = 0, l = xs.length; i < l; i++) {\n    if (xs[i] === x) return i;\n  }\n  return -1;\n}"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC;AAEA,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,OAAO,OAAO,GAAG;AAEjB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,SAAS,aAAa,GAAG;AAEzB,eAAe,GACf,IAAI,KAAK,uEAAkB,YAAY;AAEvC,IAAI,kBAAkB,SAAU,OAAO,EAAE,IAAI;IAC3C,OAAO,QAAQ,SAAS,CAAC,MAAM,MAAM;AACvC;AACA,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GAEf,IAAI,SAAS,gGAAuB,MAAM;AAC1C,IAAI,gBAAgB,CAAC,OAAO,WAAW,cAAc,SAAS,6EAAyC,OAAO,SAAS,cAAc,OAAO,CAAC,CAAC,EAAE,UAAU,IAAI,YAAa;AAC3K,SAAS,oBAAoB,KAAK;IAChC,OAAO,OAAO,IAAI,CAAC;AACrB;AACA,SAAS,cAAc,GAAG;IACxB,OAAO,OAAO,QAAQ,CAAC,QAAQ,eAAe;AAChD;AAEA,gBAAgB,GAEhB,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,IAAI,QAAQ,KAAK;AACjB,IAAI,aAAa,UAAU,QAAQ,EAAE;IACnC,QAAQ,UAAU,QAAQ,CAAC;AAC7B,OAAO;IACL,QAAQ,YAAa;AACvB;AACA,gBAAgB,GAEhB,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,KAAK,QAAQ,CAAC,UAAU;AAExB,IAAI,eAAe;IAAC;IAAS;IAAS;IAAW;IAAS;CAAS;AAEnE,SAAS,gBAAgB,OAAO,EAAE,KAAK,EAAE,EAAE;IACzC,iEAAiE;IACjE,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,eAAe,KAAK,YAAY,OAAO,QAAQ,eAAe,CAAC,OAAO;IAEzF,4EAA4E;IAC5E,2EAA2E;IAC3E,yEAAyE;IACzE,4EAA4E;IAC5E,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,OAAO;SAAS,IAAI,QAAQ,QAAQ,OAAO,CAAC,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG;QAAC;QAAI,QAAQ,OAAO,CAAC,MAAM;KAAC;AAChN;AAEA,SAAS,cAAc,OAAO,EAAE,MAAM;IACpC,SAAS;IAET,UAAU,WAAW,CAAC;IAEtB,2DAA2D;IAC3D,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,2EAA2E;IAC3E,IAAI,WAAW,kBAAkB;IAEjC,2DAA2D;IAC3D,wDAAwD;IACxD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;IAEtC,IAAI,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,QAAQ,kBAAkB;IAE/E,iEAAiE;IACjE,uEAAuE;IACvE,IAAI,MAAM,QAAQ,aAAa;IAC/B,IAAI,cAAc,QAAQ,qBAAqB;IAC/C,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG,KAAK,KAAK;IAE7C,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG;SAAS,IAAI,YAAY,CAAC,eAAe,gBAAgB,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG;SAAiB,IAAI,CAAC,aAAa,GAAG;IAEnK,gBAAgB;IAChB,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,aAAa;IAElD,6EAA6E;IAC7E,iEAAiE;IACjE,gBAAgB;IAChB,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,OAAO,GAAG;IAEf,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,yCAAyC;IACzC,IAAI,CAAC,IAAI,GAAG;IAEZ,qDAAqD;IACrD,mDAAmD;IACnD,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,iBAAiB,GAAG;IACzB,IAAI,CAAC,eAAe,GAAG;IAEvB,wBAAwB;IACxB,IAAI,CAAC,SAAS,GAAG;IAEjB,sEAAsE;IACtE,6DAA6D;IAC7D,uDAAuD;IACvD,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI;IAElD,oEAAoE;IACpE,IAAI,CAAC,UAAU,GAAG;IAElB,8CAA8C;IAC9C,IAAI,CAAC,WAAW,GAAG;IAEnB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,QAAQ,QAAQ,EAAE;QACpB,IAAI,CAAC,eAAe,gBAAgB;;;;;;;;;WAAQ,mBAAmB,aAAa;QAC5E,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,QAAQ,QAAQ;QACjD,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;IAClC;AACF;AAEA,SAAS,SAAS,OAAO;IACvB,SAAS;IAET,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,GAAG,OAAO,IAAI,SAAS;IAErD,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,SAAS,IAAI;IAErD,SAAS;IACT,IAAI,CAAC,QAAQ,GAAG;IAEhB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI;QAEjE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;IAC5E;IAEA,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,aAAa;IACrD,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,KAAK,WAAW;YACrC,OAAO;QACT;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;IACtC;IACA,KAAK,SAAU,KAAK;QAClB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;QACF;QAEA,iDAAiD;QACjD,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AAEA,SAAS,SAAS,CAAC,OAAO,GAAG,YAAY,OAAO;AAChD,SAAS,SAAS,CAAC,UAAU,GAAG,YAAY,SAAS;AACrD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC7C,IAAI,CAAC,IAAI,CAAC;IACV,GAAG;AACL;AAEA,mDAAmD;AACnD,+DAA+D;AAC/D,6DAA6D;AAC7D,qBAAqB;AACrB,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,QAAQ;IACjD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI;IAEJ,IAAI,CAAC,MAAM,UAAU,EAAE;QACrB,IAAI,OAAO,UAAU,UAAU;YAC7B,WAAW,YAAY,MAAM,eAAe;YAC5C,IAAI,aAAa,MAAM,QAAQ,EAAE;gBAC/B,QAAQ,OAAO,IAAI,CAAC,OAAO;gBAC3B,WAAW;YACb;YACA,iBAAiB;QACnB;IACF,OAAO;QACL,iBAAiB;IACnB;IAEA,OAAO,iBAAiB,IAAI,EAAE,OAAO,UAAU,OAAO;AACxD;AAEA,8DAA8D;AAC9D,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK;IAC1C,OAAO,iBAAiB,IAAI,EAAE,OAAO,MAAM,MAAM;AACnD;AAEA,SAAS,iBAAiB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc;IAC3E,IAAI,QAAQ,OAAO,cAAc;IACjC,IAAI,UAAU,MAAM;QAClB,MAAM,OAAO,GAAG;QAChB,WAAW,QAAQ;IACrB,OAAO;QACL,IAAI;QACJ,IAAI,CAAC,gBAAgB,KAAK,aAAa,OAAO;QAC9C,IAAI,IAAI;YACN,OAAO,IAAI,CAAC,SAAS;QACvB,OAAO,IAAI,MAAM,UAAU,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YACxD,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,UAAU,IAAI,OAAO,cAAc,CAAC,WAAW,OAAO,SAAS,EAAE;gBACvG,QAAQ,oBAAoB;YAC9B;YAEA,IAAI,YAAY;gBACd,IAAI,MAAM,UAAU,EAAE,OAAO,IAAI,CAAC,SAAS,IAAI,MAAM;qBAA0C,SAAS,QAAQ,OAAO,OAAO;YAChI,OAAO,IAAI,MAAM,KAAK,EAAE;gBACtB,OAAO,IAAI,CAAC,SAAS,IAAI,MAAM;YACjC,OAAO;gBACL,MAAM,OAAO,GAAG;gBAChB,IAAI,MAAM,OAAO,IAAI,CAAC,UAAU;oBAC9B,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;oBAC5B,IAAI,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG,SAAS,QAAQ,OAAO,OAAO;yBAAY,cAAc,QAAQ;gBAC/G,OAAO;oBACL,SAAS,QAAQ,OAAO,OAAO;gBACjC;YACF;QACF,OAAO,IAAI,CAAC,YAAY;YACtB,MAAM,OAAO,GAAG;QAClB;IACF;IAEA,OAAO,aAAa;AACtB;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;IAChD,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,EAAE;QACtD,OAAO,IAAI,CAAC,QAAQ;QACpB,OAAO,IAAI,CAAC;IACd,OAAO;QACL,0BAA0B;QAC1B,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACnD,IAAI,YAAY,MAAM,MAAM,CAAC,OAAO,CAAC;aAAY,MAAM,MAAM,CAAC,IAAI,CAAC;QAEnE,IAAI,MAAM,YAAY,EAAE,aAAa;IACvC;IACA,cAAc,QAAQ;AACxB;AAEA,SAAS,aAAa,KAAK,EAAE,KAAK;IAChC,IAAI;IACJ,IAAI,CAAC,cAAc,UAAU,OAAO,UAAU,YAAY,UAAU,aAAa,CAAC,MAAM,UAAU,EAAE;QAClG,KAAK,IAAI,UAAU;IACrB;IACA,OAAO;AACT;AAEA,8DAA8D;AAC9D,kDAAkD;AAClD,yDAAyD;AACzD,qDAAqD;AACrD,kEAAkE;AAClE,oEAAoE;AACpE,sCAAsC;AACtC,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,YAAY,IAAI,MAAM,MAAM,GAAG,MAAM,aAAa,IAAI,MAAM,MAAM,KAAK,CAAC;AACxG;AAEA,SAAS,SAAS,CAAC,QAAQ,GAAG;IAC5B,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK;AACzC;AAEA,2BAA2B;AAC3B,SAAS,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG;IAC5C,IAAI,CAAC,eAAe,gBAAgB;;;;;;;;;OAAQ,mBAAmB,aAAa;IAC5E,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,cAAc;IAChD,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;IAC/B,OAAO,IAAI;AACb;AAEA,4BAA4B;AAC5B,IAAI,UAAU;AACd,SAAS,wBAAwB,CAAC;IAChC,IAAI,KAAK,SAAS;QAChB,IAAI;IACN,OAAO;QACL,2EAA2E;QAC3E,eAAe;QACf;QACA,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX;IACF;IACA,OAAO;AACT;AAEA,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,cAAc,CAAC,EAAE,KAAK;IAC7B,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,OAAO;IACxD,IAAI,MAAM,UAAU,EAAE,OAAO;IAC7B,IAAI,MAAM,GAAG;QACX,iCAAiC;QACjC,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;aAAM,OAAO,MAAM,MAAM;IAClG;IACA,qEAAqE;IACrE,IAAI,IAAI,MAAM,aAAa,EAAE,MAAM,aAAa,GAAG,wBAAwB;IAC3E,IAAI,KAAK,MAAM,MAAM,EAAE,OAAO;IAC9B,oBAAoB;IACpB,IAAI,CAAC,MAAM,KAAK,EAAE;QAChB,MAAM,YAAY,GAAG;QACrB,OAAO;IACT;IACA,OAAO,MAAM,MAAM;AACrB;AAEA,oEAAoE;AACpE,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,CAAC;IACnC,MAAM,QAAQ;IACd,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,QAAQ;IAEZ,IAAI,MAAM,GAAG,MAAM,eAAe,GAAG;IAErC,6DAA6D;IAC7D,gEAAgE;IAChE,oCAAoC;IACpC,IAAI,MAAM,KAAK,MAAM,YAAY,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,aAAa,IAAI,MAAM,KAAK,GAAG;QACzF,MAAM,sBAAsB,MAAM,MAAM,EAAE,MAAM,KAAK;QACrD,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,YAAY,IAAI;aAAO,aAAa,IAAI;QAC/E,OAAO;IACT;IAEA,IAAI,cAAc,GAAG;IAErB,0DAA0D;IAC1D,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,YAAY,IAAI;QACxC,OAAO;IACT;IAEA,oDAAoD;IACpD,4DAA4D;IAC5D,6DAA6D;IAC7D,6DAA6D;IAC7D,2DAA2D;IAC3D,iCAAiC;IACjC,EAAE;IACF,qBAAqB;IACrB,6DAA6D;IAC7D,0BAA0B;IAC1B,EAAE;IACF,oEAAoE;IACpE,kEAAkE;IAClE,kEAAkE;IAClE,mEAAmE;IACnE,sCAAsC;IACtC,qEAAqE;IACrE,sEAAsE;IACtE,kBAAkB;IAClB,EAAE;IACF,sEAAsE;IAEtE,gEAAgE;IAChE,IAAI,SAAS,MAAM,YAAY;IAC/B,MAAM,iBAAiB;IAEvB,wEAAwE;IACxE,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,IAAI,MAAM,aAAa,EAAE;QAChE,SAAS;QACT,MAAM,8BAA8B;IACtC;IAEA,uEAAuE;IACvE,kCAAkC;IAClC,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,EAAE;QAChC,SAAS;QACT,MAAM,oBAAoB;IAC5B,OAAO,IAAI,QAAQ;QACjB,MAAM;QACN,MAAM,OAAO,GAAG;QAChB,MAAM,IAAI,GAAG;QACb,oEAAoE;QACpE,IAAI,MAAM,MAAM,KAAK,GAAG,MAAM,YAAY,GAAG;QAC7C,4BAA4B;QAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,aAAa;QAC9B,MAAM,IAAI,GAAG;QACb,oEAAoE;QACpE,sEAAsE;QACtE,IAAI,CAAC,MAAM,OAAO,EAAE,IAAI,cAAc,OAAO;IAC/C;IAEA,IAAI;IACJ,IAAI,IAAI,GAAG,MAAM,SAAS,GAAG;SAAY,MAAM;IAE/C,IAAI,QAAQ,MAAM;QAChB,MAAM,YAAY,GAAG;QACrB,IAAI;IACN,OAAO;QACL,MAAM,MAAM,IAAI;IAClB;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,yDAAyD;QACzD,oDAAoD;QACpD,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAY,GAAG;QAEvC,sEAAsE;QACtE,IAAI,UAAU,KAAK,MAAM,KAAK,EAAE,YAAY,IAAI;IAClD;IAEA,IAAI,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ;IAEpC,OAAO;AACT;AAEA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,IAAI,MAAM,KAAK,EAAE;IACjB,IAAI,MAAM,OAAO,EAAE;QACjB,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG;QAC7B,IAAI,SAAS,MAAM,MAAM,EAAE;YACzB,MAAM,MAAM,CAAC,IAAI,CAAC;YAClB,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACrD;IACF;IACA,MAAM,KAAK,GAAG;IAEd,sDAAsD;IACtD,aAAa;AACf;AAEA,wEAAwE;AACxE,qEAAqE;AACrE,uDAAuD;AACvD,SAAS,aAAa,MAAM;IAC1B,IAAI,QAAQ,OAAO,cAAc;IACjC,MAAM,YAAY,GAAG;IACrB,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,MAAM,gBAAgB,MAAM,OAAO;QACnC,MAAM,eAAe,GAAG;QACxB,IAAI,MAAM,IAAI,EAAE,IAAI,QAAQ,CAAC,eAAe;aAAa,cAAc;IACzE;AACF;AAEA,SAAS,cAAc,MAAM;IAC3B,MAAM;IACN,OAAO,IAAI,CAAC;IACZ,KAAK;AACP;AAEA,oEAAoE;AACpE,mEAAmE;AACnE,iEAAiE;AACjE,oBAAoB;AACpB,iEAAiE;AACjE,wDAAwD;AACxD,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,CAAC,MAAM,WAAW,EAAE;QACtB,MAAM,WAAW,GAAG;QACpB,IAAI,QAAQ,CAAC,gBAAgB,QAAQ;IACvC;AACF;AAEA,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,IAAI,MAAM,MAAM,MAAM;IACtB,MAAO,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,MAAM,GAAG,MAAM,aAAa,CAAE;QAC7F,MAAM;QACN,OAAO,IAAI,CAAC;QACZ,IAAI,QAAQ,MAAM,MAAM,EAEtB;aAAW,MAAM,MAAM,MAAM;IACjC;IACA,MAAM,WAAW,GAAG;AACtB;AAEA,yEAAyE;AACzE,kDAAkD;AAClD,qEAAqE;AACrE,8CAA8C;AAC9C,SAAS,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC;IACpC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM;AAC/B;AAEA,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,QAAQ;IAChD,IAAI,MAAM,IAAI;IACd,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,OAAQ,MAAM,UAAU;QACtB,KAAK;YACH,MAAM,KAAK,GAAG;YACd;QACF,KAAK;YACH,MAAM,KAAK,GAAG;gBAAC,MAAM,KAAK;gBAAE;aAAK;YACjC;QACF;YACE,MAAM,KAAK,CAAC,IAAI,CAAC;YACjB;IACJ;IACA,MAAM,UAAU,IAAI;IACpB,MAAM,yBAAyB,MAAM,UAAU,EAAE;IAEjD,IAAI,QAAQ,CAAC,CAAC,YAAY,SAAS,GAAG,KAAK,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,SAAS,QAAQ,MAAM;IAEvG,IAAI,QAAQ,QAAQ,QAAQ;IAC5B,IAAI,MAAM,UAAU,EAAE,IAAI,QAAQ,CAAC;SAAY,IAAI,IAAI,CAAC,OAAO;IAE/D,KAAK,EAAE,CAAC,UAAU;IAClB,SAAS,SAAS,QAAQ,EAAE,UAAU;QACpC,MAAM;QACN,IAAI,aAAa,KAAK;YACpB,IAAI,cAAc,WAAW,UAAU,KAAK,OAAO;gBACjD,WAAW,UAAU,GAAG;gBACxB;YACF;QACF;IACF;IAEA,SAAS;QACP,MAAM;QACN,KAAK,GAAG;IACV;IAEA,0DAA0D;IAC1D,4DAA4D;IAC5D,2DAA2D;IAC3D,YAAY;IACZ,IAAI,UAAU,YAAY;IAC1B,KAAK,EAAE,CAAC,SAAS;IAEjB,IAAI,YAAY;IAChB,SAAS;QACP,MAAM;QACN,iDAAiD;QACjD,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,QAAQ;QAE3B,YAAY;QAEZ,uDAAuD;QACvD,yDAAyD;QACzD,iBAAiB;QACjB,6DAA6D;QAC7D,6DAA6D;QAC7D,IAAI,MAAM,UAAU,IAAI,CAAC,CAAC,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,SAAS,GAAG;IACnF;IAEA,6EAA6E;IAC7E,6EAA6E;IAC7E,iEAAiE;IACjE,iDAAiD;IACjD,IAAI,sBAAsB;IAC1B,IAAI,EAAE,CAAC,QAAQ;IACf,SAAS,OAAO,KAAK;QACnB,MAAM;QACN,sBAAsB;QACtB,IAAI,MAAM,KAAK,KAAK,CAAC;QACrB,IAAI,UAAU,OAAO,CAAC,qBAAqB;YACzC,4DAA4D;YAC5D,2DAA2D;YAC3D,uBAAuB;YACvB,yDAAyD;YACzD,IAAI,CAAC,MAAM,UAAU,KAAK,KAAK,MAAM,KAAK,KAAK,QAAQ,MAAM,UAAU,GAAG,KAAK,QAAQ,MAAM,KAAK,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,WAAW;gBAC/H,MAAM,+BAA+B,MAAM,UAAU;gBACrD,MAAM,UAAU;gBAChB,sBAAsB;YACxB;YACA,IAAI,KAAK;QACX;IACF;IAEA,sDAAsD;IACtD,0DAA0D;IAC1D,SAAS,QAAQ,EAAE;QACjB,MAAM,WAAW;QACjB;QACA,KAAK,cAAc,CAAC,SAAS;QAC7B,IAAI,gBAAgB,MAAM,aAAa,GAAG,KAAK,IAAI,CAAC,SAAS;IAC/D;IAEA,gEAAgE;IAChE,gBAAgB,MAAM,SAAS;IAE/B,8DAA8D;IAC9D,SAAS;QACP,KAAK,cAAc,CAAC,UAAU;QAC9B;IACF;IACA,KAAK,IAAI,CAAC,SAAS;IACnB,SAAS;QACP,MAAM;QACN,KAAK,cAAc,CAAC,SAAS;QAC7B;IACF;IACA,KAAK,IAAI,CAAC,UAAU;IAEpB,SAAS;QACP,MAAM;QACN,IAAI,MAAM,CAAC;IACb;IAEA,yCAAyC;IACzC,KAAK,IAAI,CAAC,QAAQ;IAElB,oDAAoD;IACpD,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;QACN,IAAI,MAAM;IACZ;IAEA,OAAO;AACT;AAEA,SAAS,YAAY,GAAG;IACtB,OAAO;QACL,IAAI,QAAQ,IAAI,cAAc;QAC9B,MAAM,eAAe,MAAM,UAAU;QACrC,IAAI,MAAM,UAAU,EAAE,MAAM,UAAU;QACtC,IAAI,MAAM,UAAU,KAAK,KAAK,gBAAgB,KAAK,SAAS;YAC1D,MAAM,OAAO,GAAG;YAChB,KAAK;QACP;IACF;AACF;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI;IACxC,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,aAAa;QAAE,YAAY;IAAM;IAErC,iDAAiD;IACjD,IAAI,MAAM,UAAU,KAAK,GAAG,OAAO,IAAI;IAEvC,2CAA2C;IAC3C,IAAI,MAAM,UAAU,KAAK,GAAG;QAC1B,6CAA6C;QAC7C,IAAI,QAAQ,SAAS,MAAM,KAAK,EAAE,OAAO,IAAI;QAE7C,IAAI,CAAC,MAAM,OAAO,MAAM,KAAK;QAE7B,eAAe;QACf,MAAM,KAAK,GAAG;QACd,MAAM,UAAU,GAAG;QACnB,MAAM,OAAO,GAAG;QAChB,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;QACpC,OAAO,IAAI;IACb;IAEA,yCAAyC;IAEzC,IAAI,CAAC,MAAM;QACT,cAAc;QACd,IAAI,QAAQ,MAAM,KAAK;QACvB,IAAI,MAAM,MAAM,UAAU;QAC1B,MAAM,KAAK,GAAG;QACd,MAAM,UAAU,GAAG;QACnB,MAAM,OAAO,GAAG;QAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;gBAAE,YAAY;YAAM;QACpD;QAAC,OAAO,IAAI;IACd;IAEA,6BAA6B;IAC7B,IAAI,QAAQ,QAAQ,MAAM,KAAK,EAAE;IACjC,IAAI,UAAU,CAAC,GAAG,OAAO,IAAI;IAE7B,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO;IAC1B,MAAM,UAAU,IAAI;IACpB,IAAI,MAAM,UAAU,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE;IAExD,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;IAE1B,OAAO,IAAI;AACb;AAEA,2CAA2C;AAC3C,qDAAqD;AACrD,SAAS,SAAS,CAAC,EAAE,GAAG,SAAU,EAAE,EAAE,EAAE;IACtC,IAAI,MAAM,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;IAE7C,IAAI,OAAO,QAAQ;QACjB,+DAA+D;QAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,MAAM;IACxD,OAAO,IAAI,OAAO,YAAY;QAC5B,IAAI,QAAQ,IAAI,CAAC,cAAc;QAC/B,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,iBAAiB,EAAE;YACjD,MAAM,iBAAiB,GAAG,MAAM,YAAY,GAAG;YAC/C,MAAM,eAAe,GAAG;YACxB,IAAI,CAAC,MAAM,OAAO,EAAE;gBAClB,IAAI,QAAQ,CAAC,kBAAkB,IAAI;YACrC,OAAO,IAAI,MAAM,MAAM,EAAE;gBACvB,aAAa,IAAI;YACnB;QACF;IACF;IAEA,OAAO;AACT;AACA,SAAS,SAAS,CAAC,WAAW,GAAG,SAAS,SAAS,CAAC,EAAE;AAEtD,SAAS,iBAAiB,KAAI;IAC5B,MAAM;IACN,MAAK,IAAI,CAAC;AACZ;AAEA,sEAAsE;AACtE,oDAAoD;AACpD,SAAS,SAAS,CAAC,MAAM,GAAG;IAC1B,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;QACN,MAAM,OAAO,GAAG;QAChB,OAAO,IAAI,EAAE;IACf;IACA,OAAO,IAAI;AACb;AAEA,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,MAAM,eAAe,GAAG;QACxB,IAAI,QAAQ,CAAC,SAAS,QAAQ;IAChC;AACF;AAEA,SAAS,QAAQ,MAAM,EAAE,KAAK;IAC5B,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe,GAAG;IACxB,MAAM,UAAU,GAAG;IACnB,OAAO,IAAI,CAAC;IACZ,KAAK;IACL,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE,OAAO,IAAI,CAAC;AACnD;AAEA,SAAS,SAAS,CAAC,KAAK,GAAG;IACzB,MAAM,yBAAyB,IAAI,CAAC,cAAc,CAAC,OAAO;IAC1D,IAAI,UAAU,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;QACzC,MAAM;QACN,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC9B,IAAI,CAAC,IAAI,CAAC;IACZ;IACA,OAAO,IAAI;AACb;AAEA,SAAS,KAAK,MAAM;IAClB,IAAI,QAAQ,OAAO,cAAc;IACjC,MAAM,QAAQ,MAAM,OAAO;IAC3B,MAAO,MAAM,OAAO,IAAI,OAAO,IAAI,OAAO,KAAM,CAAC;AACnD;AAEA,qDAAqD;AACrD,uDAAuD;AACvD,6CAA6C;AAC7C,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM;IACxC,IAAI,QAAQ,IAAI;IAEhB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,SAAS;IAEb,OAAO,EAAE,CAAC,OAAO;QACf,MAAM;QACN,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE;YACjC,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG;YAC7B,IAAI,SAAS,MAAM,MAAM,EAAE,MAAM,IAAI,CAAC;QACxC;QAEA,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,EAAE,CAAC,QAAQ,SAAU,KAAK;QAC/B,MAAM;QACN,IAAI,MAAM,OAAO,EAAE,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;QAE/C,6CAA6C;QAC7C,IAAI,MAAM,UAAU,IAAI,CAAC,UAAU,QAAQ,UAAU,SAAS,GAAG;aAAY,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,MAAM,GAAG;QAEjI,IAAI,MAAM,MAAM,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK;YACR,SAAS;YACT,OAAO,KAAK;QACd;IACF;IAEA,+BAA+B;IAC/B,gDAAgD;IAChD,IAAK,IAAI,KAAK,OAAQ;QACpB,IAAI,IAAI,CAAC,EAAE,KAAK,aAAa,OAAO,MAAM,CAAC,EAAE,KAAK,YAAY;YAC5D,IAAI,CAAC,EAAE,GAAG,SAAU,MAAM;gBACxB,OAAO;oBACL,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ;gBACtC;YACF,EAAE;QACJ;IACF;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE;IACjE;IAEA,6DAA6D;IAC7D,qBAAqB;IACrB,IAAI,CAAC,KAAK,GAAG,SAAU,CAAC;QACtB,MAAM,iBAAiB;QACvB,IAAI,QAAQ;YACV,SAAS;YACT,OAAO,MAAM;QACf;IACF;IAEA,OAAO,IAAI;AACb;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK;QACH,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AAEA,qCAAqC;AACrC,SAAS,SAAS,GAAG;AAErB,8CAA8C;AAC9C,iEAAiE;AACjE,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,SAAS,CAAC,EAAE,KAAK;IACxB,mBAAmB;IACnB,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,IAAI;IACJ,IAAI,MAAM,UAAU,EAAE,MAAM,MAAM,MAAM,CAAC,KAAK;SAAQ,IAAI,CAAC,KAAK,KAAK,MAAM,MAAM,EAAE;QACjF,iCAAiC;QACjC,IAAI,MAAM,OAAO,EAAE,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC;aAAS,IAAI,MAAM,MAAM,CAAC,MAAM,KAAK,GAAG,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI;aAAM,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,MAAM;QAC3J,MAAM,MAAM,CAAC,KAAK;IACpB,OAAO;QACL,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,MAAM,MAAM,EAAE,MAAM,OAAO;IACtD;IAEA,OAAO;AACT;AAEA,sEAAsE;AACtE,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,gBAAgB,CAAC,EAAE,IAAI,EAAE,UAAU;IAC1C,IAAI;IACJ,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAC7B,4CAA4C;QAC5C,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IACxC,OAAO,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QACtC,iCAAiC;QACjC,MAAM,KAAK,KAAK;IAClB,OAAO;QACL,oCAAoC;QACpC,MAAM,aAAa,qBAAqB,GAAG,QAAQ,eAAe,GAAG;IACvE;IACA,OAAO;AACT;AAEA,yEAAyE;AACzE,UAAU;AACV,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,qBAAqB,CAAC,EAAE,IAAI;IACnC,IAAI,IAAI,KAAK,IAAI;IACjB,IAAI,IAAI;IACR,IAAI,MAAM,EAAE,IAAI;IAChB,KAAK,IAAI,MAAM;IACf,MAAO,IAAI,EAAE,IAAI,CAAE;QACjB,IAAI,MAAM,EAAE,IAAI;QAChB,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG;QACvC,IAAI,OAAO,IAAI,MAAM,EAAE,OAAO;aAAS,OAAO,IAAI,KAAK,CAAC,GAAG;QAC3D,KAAK;QACL,IAAI,MAAM,GAAG;YACX,IAAI,OAAO,IAAI,MAAM,EAAE;gBACrB,EAAE;gBACF,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,GAAG,EAAE,IAAI;qBAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;YAC9D,OAAO;gBACL,KAAK,IAAI,GAAG;gBACZ,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC;YACrB;YACA;QACF;QACA,EAAE;IACJ;IACA,KAAK,MAAM,IAAI;IACf,OAAO;AACT;AAEA,4EAA4E;AAC5E,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,eAAe,CAAC,EAAE,IAAI;IAC7B,IAAI,MAAM,OAAO,WAAW,CAAC;IAC7B,IAAI,IAAI,KAAK,IAAI;IACjB,IAAI,IAAI;IACR,EAAE,IAAI,CAAC,IAAI,CAAC;IACZ,KAAK,EAAE,IAAI,CAAC,MAAM;IAClB,MAAO,IAAI,EAAE,IAAI,CAAE;QACjB,IAAI,MAAM,EAAE,IAAI;QAChB,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG;QACvC,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,GAAG,GAAG,GAAG;QACjC,KAAK;QACL,IAAI,MAAM,GAAG;YACX,IAAI,OAAO,IAAI,MAAM,EAAE;gBACrB,EAAE;gBACF,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,GAAG,EAAE,IAAI;qBAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;YAC9D,OAAO;gBACL,KAAK,IAAI,GAAG;gBACZ,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC;YACrB;YACA;QACF;QACA,EAAE;IACJ;IACA,KAAK,MAAM,IAAI;IACf,OAAO;AACT;AAEA,SAAS,YAAY,MAAM;IACzB,IAAI,QAAQ,OAAO,cAAc;IAEjC,gEAAgE;IAChE,qCAAqC;IACrC,IAAI,MAAM,MAAM,GAAG,GAAG,MAAM,IAAI,MAAM;IAEtC,IAAI,CAAC,MAAM,UAAU,EAAE;QACrB,MAAM,KAAK,GAAG;QACd,IAAI,QAAQ,CAAC,eAAe,OAAO;IACrC;AACF;AAEA,SAAS,cAAc,KAAK,EAAE,MAAM;IAClC,6CAA6C;IAC7C,IAAI,CAAC,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG;QAC3C,MAAM,UAAU,GAAG;QACnB,OAAO,QAAQ,GAAG;QAClB,OAAO,IAAI,CAAC;IACd;AACF;AAEA,SAAS,QAAQ,EAAE,EAAE,CAAC;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAI,GAAG,IAAK;QACzC,IAAI,EAAE,CAAC,EAAE,KAAK,GAAG,OAAO;IAC1B;IACA,OAAO,CAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/_stream_transform.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n\n'use strict';\n\nmodule.exports = Transform;\n\nvar Duplex = require('./_stream_duplex');\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\nutil.inherits(Transform, Duplex);\n\nfunction afterTransform(er, data) {\n  var ts = this._transformState;\n  ts.transforming = false;\n\n  var cb = ts.writecb;\n\n  if (!cb) {\n    return this.emit('error', new Error('write callback called multiple times'));\n  }\n\n  ts.writechunk = null;\n  ts.writecb = null;\n\n  if (data != null) // single equals check for both `null` and `undefined`\n    this.push(data);\n\n  cb(er);\n\n  var rs = this._readableState;\n  rs.reading = false;\n  if (rs.needReadable || rs.length < rs.highWaterMark) {\n    this._read(rs.highWaterMark);\n  }\n}\n\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options);\n\n  Duplex.call(this, options);\n\n  this._transformState = {\n    afterTransform: afterTransform.bind(this),\n    needTransform: false,\n    transforming: false,\n    writecb: null,\n    writechunk: null,\n    writeencoding: null\n  };\n\n  // start out asking for a readable event once data is transformed.\n  this._readableState.needReadable = true;\n\n  // we have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n  this._readableState.sync = false;\n\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform;\n\n    if (typeof options.flush === 'function') this._flush = options.flush;\n  }\n\n  // When the writable side finishes, then flush out anything remaining.\n  this.on('prefinish', prefinish);\n}\n\nfunction prefinish() {\n  var _this = this;\n\n  if (typeof this._flush === 'function') {\n    this._flush(function (er, data) {\n      done(_this, er, data);\n    });\n  } else {\n    done(this, null, null);\n  }\n}\n\nTransform.prototype.push = function (chunk, encoding) {\n  this._transformState.needTransform = false;\n  return Duplex.prototype.push.call(this, chunk, encoding);\n};\n\n// This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\nTransform.prototype._transform = function (chunk, encoding, cb) {\n  throw new Error('_transform() is not implemented');\n};\n\nTransform.prototype._write = function (chunk, encoding, cb) {\n  var ts = this._transformState;\n  ts.writecb = cb;\n  ts.writechunk = chunk;\n  ts.writeencoding = encoding;\n  if (!ts.transforming) {\n    var rs = this._readableState;\n    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n  }\n};\n\n// Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\nTransform.prototype._read = function (n) {\n  var ts = this._transformState;\n\n  if (ts.writechunk !== null && ts.writecb && !ts.transforming) {\n    ts.transforming = true;\n    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n  } else {\n    // mark that we need a transform, so that any data that comes in\n    // will get processed, now that we've asked for it.\n    ts.needTransform = true;\n  }\n};\n\nTransform.prototype._destroy = function (err, cb) {\n  var _this2 = this;\n\n  Duplex.prototype._destroy.call(this, err, function (err2) {\n    cb(err2);\n    _this2.emit('close');\n  });\n};\n\nfunction done(stream, er, data) {\n  if (er) return stream.emit('error', er);\n\n  if (data != null) // single equals check for both `null` and `undefined`\n    stream.push(data);\n\n  // if there's nothing in the write buffer, then that means\n  // that nothing more will ever be provided\n  if (stream._writableState.length) throw new Error('Calling transform done when ws.length != 0');\n\n  if (stream._transformState.transforming) throw new Error('Calling transform done when still transforming');\n\n  return stream.push(null);\n}"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,gEAAgE;AAChE,8DAA8D;AAC9D,uEAAuE;AACvE,sEAAsE;AACtE,iDAAiD;AACjD,EAAE;AACF,gEAAgE;AAChE,qEAAqE;AACrE,kEAAkE;AAClE,0DAA0D;AAC1D,EAAE;AACF,yBAAyB;AACzB,EAAE;AACF,wEAAwE;AACxE,sEAAsE;AACtE,mEAAmE;AACnE,gEAAgE;AAChE,oDAAoD;AACpD,EAAE;AACF,uEAAuE;AACvE,oEAAoE;AACpE,qEAAqE;AACrE,uEAAuE;AACvE,qEAAqE;AACrE,sEAAsE;AACtE,EAAE;AACF,sEAAsE;AACtE,0EAA0E;AAC1E,yEAAyE;AACzE,oEAAoE;AACpE,sEAAsE;AACtE,sEAAsE;AACtE,wEAAwE;AACxE,sEAAsE;AACtE,qEAAqE;AACrE,oEAAoE;AACpE,yCAAyC;AACzC,EAAE;AACF,yEAAyE;AACzE,yEAAyE;AACzE,+DAA+D;AAE/D;AAEA,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,KAAK,QAAQ,CAAC,WAAW;AAEzB,SAAS,eAAe,EAAE,EAAE,IAAI;IAC9B,IAAI,KAAK,IAAI,CAAC,eAAe;IAC7B,GAAG,YAAY,GAAG;IAElB,IAAI,KAAK,GAAG,OAAO;IAEnB,IAAI,CAAC,IAAI;QACP,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM;IACtC;IAEA,GAAG,UAAU,GAAG;IAChB,GAAG,OAAO,GAAG;IAEb,IAAI,QAAQ,MACV,IAAI,CAAC,IAAI,CAAC;IAEZ,GAAG;IAEH,IAAI,KAAK,IAAI,CAAC,cAAc;IAC5B,GAAG,OAAO,GAAG;IACb,IAAI,GAAG,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG,aAAa,EAAE;QACnD,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa;IAC7B;AACF;AAEA,SAAS,UAAU,OAAO;IACxB,IAAI,CAAC,CAAC,IAAI,YAAY,SAAS,GAAG,OAAO,IAAI,UAAU;IAEvD,OAAO,IAAI,CAAC,IAAI,EAAE;IAElB,IAAI,CAAC,eAAe,GAAG;QACrB,gBAAgB,eAAe,IAAI,CAAC,IAAI;QACxC,eAAe;QACf,cAAc;QACd,SAAS;QACT,YAAY;QACZ,eAAe;IACjB;IAEA,kEAAkE;IAClE,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG;IAEnC,kEAAkE;IAClE,gEAAgE;IAChE,mBAAmB;IACnB,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG;IAE3B,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,SAAS,KAAK,YAAY,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;QAEhF,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IACtE;IAEA,sEAAsE;IACtE,IAAI,CAAC,EAAE,CAAC,aAAa;AACvB;AAEA,SAAS;IACP,IAAI,QAAQ,IAAI;IAEhB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY;QACrC,IAAI,CAAC,MAAM,CAAC,SAAU,EAAE,EAAE,IAAI;YAC5B,KAAK,OAAO,IAAI;QAClB;IACF,OAAO;QACL,KAAK,IAAI,EAAE,MAAM;IACnB;AACF;AAEA,UAAU,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,QAAQ;IAClD,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG;IACrC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;AACjD;AAEA,uCAAuC;AACvC,oDAAoD;AACpD,6BAA6B;AAC7B,EAAE;AACF,yDAAyD;AACzD,iEAAiE;AACjE,EAAE;AACF,iEAAiE;AACjE,sEAAsE;AACtE,wDAAwD;AACxD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC5D,MAAM,IAAI,MAAM;AAClB;AAEA,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACxD,IAAI,KAAK,IAAI,CAAC,eAAe;IAC7B,GAAG,OAAO,GAAG;IACb,GAAG,UAAU,GAAG;IAChB,GAAG,aAAa,GAAG;IACnB,IAAI,CAAC,GAAG,YAAY,EAAE;QACpB,IAAI,KAAK,IAAI,CAAC,cAAc;QAC5B,IAAI,GAAG,aAAa,IAAI,GAAG,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa;IACtG;AACF;AAEA,yCAAyC;AACzC,gCAAgC;AAChC,iEAAiE;AACjE,UAAU,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC;IACrC,IAAI,KAAK,IAAI,CAAC,eAAe;IAE7B,IAAI,GAAG,UAAU,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC,GAAG,YAAY,EAAE;QAC5D,GAAG,YAAY,GAAG;QAClB,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,EAAE,GAAG,aAAa,EAAE,GAAG,cAAc;IACpE,OAAO;QACL,gEAAgE;QAChE,mDAAmD;QACnD,GAAG,aAAa,GAAG;IACrB;AACF;AAEA,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC9C,IAAI,SAAS,IAAI;IAEjB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,SAAU,IAAI;QACtD,GAAG;QACH,OAAO,IAAI,CAAC;IACd;AACF;AAEA,SAAS,KAAK,MAAM,EAAE,EAAE,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,SAAS;IAEpC,IAAI,QAAQ,MACV,OAAO,IAAI,CAAC;IAEd,0DAA0D;IAC1D,0CAA0C;IAC1C,IAAI,OAAO,cAAc,CAAC,MAAM,EAAE,MAAM,IAAI,MAAM;IAElD,IAAI,OAAO,eAAe,CAAC,YAAY,EAAE,MAAM,IAAI,MAAM;IAEzD,OAAO,OAAO,IAAI,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/lib/_stream_passthrough.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a passthrough stream.\n// basically just the most minimal sort of Transform stream.\n// Every written chunk gets output as-is.\n\n'use strict';\n\nmodule.exports = PassThrough;\n\nvar Transform = require('./_stream_transform');\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\nutil.inherits(PassThrough, Transform);\n\nfunction PassThrough(options) {\n  if (!(this instanceof PassThrough)) return new PassThrough(options);\n\n  Transform.call(this, options);\n}\n\nPassThrough.prototype._transform = function (chunk, encoding, cb) {\n  cb(null, chunk);\n};"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,wBAAwB;AACxB,4DAA4D;AAC5D,yCAAyC;AAEzC;AAEA,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,KAAK,QAAQ,CAAC,aAAa;AAE3B,SAAS,YAAY,OAAO;IAC1B,IAAI,CAAC,CAAC,IAAI,YAAY,WAAW,GAAG,OAAO,IAAI,YAAY;IAE3D,UAAU,IAAI,CAAC,IAAI,EAAE;AACvB;AAEA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,GAAG,MAAM;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/readable-stream/readable.js"], "sourcesContent": ["var Stream = require('stream');\nif (process.env.READABLE_STREAM === 'disable' && Stream) {\n  module.exports = Stream;\n  exports = module.exports = Stream.Readable;\n  exports.Readable = Stream.Readable;\n  exports.Writable = Stream.Writable;\n  exports.Duplex = Stream.Duplex;\n  exports.Transform = Stream.Transform;\n  exports.PassThrough = Stream.PassThrough;\n  exports.Stream = Stream;\n} else {\n  exports = module.exports = require('./lib/_stream_readable.js');\n  exports.Stream = Stream || exports;\n  exports.Readable = exports;\n  exports.Writable = require('./lib/_stream_writable.js');\n  exports.Duplex = require('./lib/_stream_duplex.js');\n  exports.Transform = require('./lib/_stream_transform.js');\n  exports.PassThrough = require('./lib/_stream_passthrough.js');\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,QAAQ,GAAG,CAAC,eAAe,KAAK,aAAa,QAAQ;IACvD,OAAO,OAAO,GAAG;IACjB,UAAU,OAAO,OAAO,GAAG,OAAO,QAAQ;IAC1C,QAAQ,QAAQ,GAAG,OAAO,QAAQ;IAClC,QAAQ,QAAQ,GAAG,OAAO,QAAQ;IAClC,QAAQ,MAAM,GAAG,OAAO,MAAM;IAC9B,QAAQ,SAAS,GAAG,OAAO,SAAS;IACpC,QAAQ,WAAW,GAAG,OAAO,WAAW;IACxC,QAAQ,MAAM,GAAG;AACnB,OAAO;IACL,UAAU,OAAO,OAAO;IACxB,QAAQ,MAAM,GAAG,UAAU;IAC3B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,QAAQ;IAChB,QAAQ,MAAM;IACd,QAAQ,SAAS;IACjB,QAAQ,WAAW;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/safe-buffer/index.js"], "sourcesContent": ["/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC,IAAI;AACJ,IAAI,SAAS,OAAO,MAAM;AAE1B,oDAAoD;AACpD,SAAS,UAAW,GAAG,EAAE,GAAG;IAC1B,IAAK,IAAI,OAAO,IAAK;QACnB,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;IACrB;AACF;AACA,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,WAAW,IAAI,OAAO,eAAe,EAAE;IAC/E,OAAO,OAAO,GAAG;AACnB,OAAO;IACL,yCAAyC;IACzC,UAAU,QAAQ;IAClB,QAAQ,MAAM,GAAG;AACnB;AAEA,SAAS,WAAY,GAAG,EAAE,gBAAgB,EAAE,MAAM;IAChD,OAAO,OAAO,KAAK,kBAAkB;AACvC;AAEA,kCAAkC;AAClC,UAAU,QAAQ;AAElB,WAAW,IAAI,GAAG,SAAU,GAAG,EAAE,gBAAgB,EAAE,MAAM;IACvD,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,OAAO,KAAK,kBAAkB;AACvC;AAEA,WAAW,KAAK,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC/C,IAAI,OAAO,SAAS,UAAU;QAC5B,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,MAAM,OAAO;IACjB,IAAI,SAAS,WAAW;QACtB,IAAI,OAAO,aAAa,UAAU;YAChC,IAAI,IAAI,CAAC,MAAM;QACjB,OAAO;YACL,IAAI,IAAI,CAAC;QACX;IACF,OAAO;QACL,IAAI,IAAI,CAAC;IACX;IACA,OAAO;AACT;AAEA,WAAW,WAAW,GAAG,SAAU,IAAI;IACrC,IAAI,OAAO,SAAS,UAAU;QAC5B,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,OAAO;AAChB;AAEA,WAAW,eAAe,GAAG,SAAU,IAAI;IACzC,IAAI,OAAO,SAAS,UAAU;QAC5B,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,OAAO,UAAU,CAAC;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/core-util-is/lib/util.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// NOTE: These type checking functions intentionally don't use `instanceof`\n// because it is fragile and can be easily faked with `Object.create()`.\n\nfunction isArray(arg) {\n  if (Array.isArray) {\n    return Array.isArray(arg);\n  }\n  return objectToString(arg) === '[object Array]';\n}\nexports.isArray = isArray;\n\nfunction isBoolean(arg) {\n  return typeof arg === 'boolean';\n}\nexports.isBoolean = isBoolean;\n\nfunction isNull(arg) {\n  return arg === null;\n}\nexports.isNull = isNull;\n\nfunction isNullOrUndefined(arg) {\n  return arg == null;\n}\nexports.isNullOrUndefined = isNullOrUndefined;\n\nfunction isNumber(arg) {\n  return typeof arg === 'number';\n}\nexports.isNumber = isNumber;\n\nfunction isString(arg) {\n  return typeof arg === 'string';\n}\nexports.isString = isString;\n\nfunction isSymbol(arg) {\n  return typeof arg === 'symbol';\n}\nexports.isSymbol = isSymbol;\n\nfunction isUndefined(arg) {\n  return arg === void 0;\n}\nexports.isUndefined = isUndefined;\n\nfunction isRegExp(re) {\n  return objectToString(re) === '[object RegExp]';\n}\nexports.isRegExp = isRegExp;\n\nfunction isObject(arg) {\n  return typeof arg === 'object' && arg !== null;\n}\nexports.isObject = isObject;\n\nfunction isDate(d) {\n  return objectToString(d) === '[object Date]';\n}\nexports.isDate = isDate;\n\nfunction isError(e) {\n  return (objectToString(e) === '[object Error]' || e instanceof Error);\n}\nexports.isError = isError;\n\nfunction isFunction(arg) {\n  return typeof arg === 'function';\n}\nexports.isFunction = isFunction;\n\nfunction isPrimitive(arg) {\n  return arg === null ||\n         typeof arg === 'boolean' ||\n         typeof arg === 'number' ||\n         typeof arg === 'string' ||\n         typeof arg === 'symbol' ||  // ES6 symbol\n         typeof arg === 'undefined';\n}\nexports.isPrimitive = isPrimitive;\n\nexports.isBuffer = require('buffer').Buffer.isBuffer;\n\nfunction objectToString(o) {\n  return Object.prototype.toString.call(o);\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,2EAA2E;AAC3E,wEAAwE;AAExE,SAAS,QAAQ,GAAG;IAClB,IAAI,MAAM,OAAO,EAAE;QACjB,OAAO,MAAM,OAAO,CAAC;IACvB;IACA,OAAO,eAAe,SAAS;AACjC;AACA,QAAQ,OAAO,GAAG;AAElB,SAAS,UAAU,GAAG;IACpB,OAAO,OAAO,QAAQ;AACxB;AACA,QAAQ,SAAS,GAAG;AAEpB,SAAS,OAAO,GAAG;IACjB,OAAO,QAAQ;AACjB;AACA,QAAQ,MAAM,GAAG;AAEjB,SAAS,kBAAkB,GAAG;IAC5B,OAAO,OAAO;AAChB;AACA,QAAQ,iBAAiB,GAAG;AAE5B,SAAS,SAAS,GAAG;IACnB,OAAO,OAAO,QAAQ;AACxB;AACA,QAAQ,QAAQ,GAAG;AAEnB,SAAS,SAAS,GAAG;IACnB,OAAO,OAAO,QAAQ;AACxB;AACA,QAAQ,QAAQ,GAAG;AAEnB,SAAS,SAAS,GAAG;IACnB,OAAO,OAAO,QAAQ;AACxB;AACA,QAAQ,QAAQ,GAAG;AAEnB,SAAS,YAAY,GAAG;IACtB,OAAO,QAAQ,KAAK;AACtB;AACA,QAAQ,WAAW,GAAG;AAEtB,SAAS,SAAS,EAAE;IAClB,OAAO,eAAe,QAAQ;AAChC;AACA,QAAQ,QAAQ,GAAG;AAEnB,SAAS,SAAS,GAAG;IACnB,OAAO,OAAO,QAAQ,YAAY,QAAQ;AAC5C;AACA,QAAQ,QAAQ,GAAG;AAEnB,SAAS,OAAO,CAAC;IACf,OAAO,eAAe,OAAO;AAC/B;AACA,QAAQ,MAAM,GAAG;AAEjB,SAAS,QAAQ,CAAC;IAChB,OAAQ,eAAe,OAAO,oBAAoB,aAAa;AACjE;AACA,QAAQ,OAAO,GAAG;AAElB,SAAS,WAAW,GAAG;IACrB,OAAO,OAAO,QAAQ;AACxB;AACA,QAAQ,UAAU,GAAG;AAErB,SAAS,YAAY,GAAG;IACtB,OAAO,QAAQ,QACR,OAAO,QAAQ,aACf,OAAO,QAAQ,YACf,OAAO,QAAQ,YACf,OAAO,QAAQ,YAAa,aAAa;IACzC,OAAO,QAAQ;AACxB;AACA,QAAQ,WAAW,GAAG;AAEtB,QAAQ,QAAQ,GAAG,uEAAkB,MAAM,CAAC,QAAQ;AAEpD,SAAS,eAAe,CAAC;IACvB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/inherits/inherits_browser.js"], "sourcesContent": ["if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,IAAI,OAAO,OAAO,MAAM,KAAK,YAAY;IACvC,qDAAqD;IACrD,OAAO,OAAO,GAAG,SAAS,SAAS,IAAI,EAAE,SAAS;QAChD,IAAI,WAAW;YACb,KAAK,MAAM,GAAG;YACd,KAAK,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE;gBAClD,aAAa;oBACX,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,cAAc;gBAChB;YACF;QACF;IACF;AACF,OAAO;IACL,mCAAmC;IACnC,OAAO,OAAO,GAAG,SAAS,SAAS,IAAI,EAAE,SAAS;QAChD,IAAI,WAAW;YACb,KAAK,MAAM,GAAG;YACd,IAAI,WAAW,YAAa;YAC5B,SAAS,SAAS,GAAG,UAAU,SAAS;YACxC,KAAK,SAAS,GAAG,IAAI;YACrB,KAAK,SAAS,CAAC,WAAW,GAAG;QAC/B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/inherits/inherits.js"], "sourcesContent": ["try {\n  var util = require('util');\n  /* istanbul ignore next */\n  if (typeof util.inherits !== 'function') throw '';\n  module.exports = util.inherits;\n} catch (e) {\n  /* istanbul ignore next */\n  module.exports = require('./inherits_browser.js');\n}\n"], "names": [], "mappings": "AAAA,IAAI;IACF,IAAI;IACJ,wBAAwB,GACxB,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,MAAM;IAC/C,OAAO,OAAO,GAAG,KAAK,QAAQ;AAChC,EAAE,OAAO,GAAG;IACV,wBAAwB,GACxB,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/util-deprecate/node.js"], "sourcesContent": ["\n/**\n * For Node.js, simply re-export the core `util.deprecate` function.\n */\n\nmodule.exports = require('util').deprecate;\n"], "names": [], "mappings": "AACA;;CAEC,GAED,OAAO,OAAO,GAAG,mEAAgB,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/string_decoder/lib/string_decoder.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\n/*</replacement>*/\n\nvar isEncoding = Buffer.isEncoding || function (encoding) {\n  encoding = '' + encoding;\n  switch (encoding && encoding.toLowerCase()) {\n    case 'hex':case 'utf8':case 'utf-8':case 'ascii':case 'binary':case 'base64':case 'ucs2':case 'ucs-2':case 'utf16le':case 'utf-16le':case 'raw':\n      return true;\n    default:\n      return false;\n  }\n};\n\nfunction _normalizeEncoding(enc) {\n  if (!enc) return 'utf8';\n  var retried;\n  while (true) {\n    switch (enc) {\n      case 'utf8':\n      case 'utf-8':\n        return 'utf8';\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return 'utf16le';\n      case 'latin1':\n      case 'binary':\n        return 'latin1';\n      case 'base64':\n      case 'ascii':\n      case 'hex':\n        return enc;\n      default:\n        if (retried) return; // undefined\n        enc = ('' + enc).toLowerCase();\n        retried = true;\n    }\n  }\n};\n\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n  var nenc = _normalizeEncoding(enc);\n  if (typeof nenc !== 'string' && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error('Unknown encoding: ' + enc);\n  return nenc || enc;\n}\n\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n  this.encoding = normalizeEncoding(encoding);\n  var nb;\n  switch (this.encoding) {\n    case 'utf16le':\n      this.text = utf16Text;\n      this.end = utf16End;\n      nb = 4;\n      break;\n    case 'utf8':\n      this.fillLast = utf8FillLast;\n      nb = 4;\n      break;\n    case 'base64':\n      this.text = base64Text;\n      this.end = base64End;\n      nb = 3;\n      break;\n    default:\n      this.write = simpleWrite;\n      this.end = simpleEnd;\n      return;\n  }\n  this.lastNeed = 0;\n  this.lastTotal = 0;\n  this.lastChar = Buffer.allocUnsafe(nb);\n}\n\nStringDecoder.prototype.write = function (buf) {\n  if (buf.length === 0) return '';\n  var r;\n  var i;\n  if (this.lastNeed) {\n    r = this.fillLast(buf);\n    if (r === undefined) return '';\n    i = this.lastNeed;\n    this.lastNeed = 0;\n  } else {\n    i = 0;\n  }\n  if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n  return r || '';\n};\n\nStringDecoder.prototype.end = utf8End;\n\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function (buf) {\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n  this.lastNeed -= buf.length;\n};\n\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n  if (byte <= 0x7F) return 0;else if (byte >> 5 === 0x06) return 2;else if (byte >> 4 === 0x0E) return 3;else if (byte >> 3 === 0x1E) return 4;\n  return byte >> 6 === 0x02 ? -1 : -2;\n}\n\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n  var j = buf.length - 1;\n  if (j < i) return 0;\n  var nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 1;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 2;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) {\n      if (nb === 2) nb = 0;else self.lastNeed = nb - 3;\n    }\n    return nb;\n  }\n  return 0;\n}\n\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n  if ((buf[0] & 0xC0) !== 0x80) {\n    self.lastNeed = 0;\n    return '\\ufffd';\n  }\n  if (self.lastNeed > 1 && buf.length > 1) {\n    if ((buf[1] & 0xC0) !== 0x80) {\n      self.lastNeed = 1;\n      return '\\ufffd';\n    }\n    if (self.lastNeed > 2 && buf.length > 2) {\n      if ((buf[2] & 0xC0) !== 0x80) {\n        self.lastNeed = 2;\n        return '\\ufffd';\n      }\n    }\n  }\n}\n\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n  var p = this.lastTotal - this.lastNeed;\n  var r = utf8CheckExtraBytes(this, buf, p);\n  if (r !== undefined) return r;\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, p, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, p, 0, buf.length);\n  this.lastNeed -= buf.length;\n}\n\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n  var total = utf8CheckIncomplete(this, buf, i);\n  if (!this.lastNeed) return buf.toString('utf8', i);\n  this.lastTotal = total;\n  var end = buf.length - (total - this.lastNeed);\n  buf.copy(this.lastChar, 0, end);\n  return buf.toString('utf8', i, end);\n}\n\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + '\\ufffd';\n  return r;\n}\n\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n  if ((buf.length - i) % 2 === 0) {\n    var r = buf.toString('utf16le', i);\n    if (r) {\n      var c = r.charCodeAt(r.length - 1);\n      if (c >= 0xD800 && c <= 0xDBFF) {\n        this.lastNeed = 2;\n        this.lastTotal = 4;\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n        return r.slice(0, -1);\n      }\n    }\n    return r;\n  }\n  this.lastNeed = 1;\n  this.lastTotal = 2;\n  this.lastChar[0] = buf[buf.length - 1];\n  return buf.toString('utf16le', i, buf.length - 1);\n}\n\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) {\n    var end = this.lastTotal - this.lastNeed;\n    return r + this.lastChar.toString('utf16le', 0, end);\n  }\n  return r;\n}\n\nfunction base64Text(buf, i) {\n  var n = (buf.length - i) % 3;\n  if (n === 0) return buf.toString('base64', i);\n  this.lastNeed = 3 - n;\n  this.lastTotal = 3;\n  if (n === 1) {\n    this.lastChar[0] = buf[buf.length - 1];\n  } else {\n    this.lastChar[0] = buf[buf.length - 2];\n    this.lastChar[1] = buf[buf.length - 1];\n  }\n  return buf.toString('base64', i, buf.length - n);\n}\n\nfunction base64End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed);\n  return r;\n}\n\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n  return buf.toString(this.encoding);\n}\n\nfunction simpleEnd(buf) {\n  return buf && buf.length ? this.write(buf) : '';\n}"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC;AAEA,eAAe,GAEf,IAAI,SAAS,gGAAuB,MAAM;AAC1C,gBAAgB,GAEhB,IAAI,aAAa,OAAO,UAAU,IAAI,SAAU,QAAQ;IACtD,WAAW,KAAK;IAChB,OAAQ,YAAY,SAAS,WAAW;QACtC,KAAK;QAAM,KAAK;QAAO,KAAK;QAAQ,KAAK;QAAQ,KAAK;QAAS,KAAK;QAAS,KAAK;QAAO,KAAK;QAAQ,KAAK;QAAU,KAAK;QAAW,KAAK;YACxI,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,mBAAmB,GAAG;IAC7B,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI;IACJ,MAAO,KAAM;QACX,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,IAAI,SAAS,QAAQ,YAAY;gBACjC,MAAM,CAAC,KAAK,GAAG,EAAE,WAAW;gBAC5B,UAAU;QACd;IACF;AACF;;AAEA,wEAAwE;AACxE,0DAA0D;AAC1D,SAAS,kBAAkB,GAAG;IAC5B,IAAI,OAAO,mBAAmB;IAC9B,IAAI,OAAO,SAAS,YAAY,CAAC,OAAO,UAAU,KAAK,cAAc,CAAC,WAAW,IAAI,GAAG,MAAM,IAAI,MAAM,uBAAuB;IAC/H,OAAO,QAAQ;AACjB;AAEA,4EAA4E;AAC5E,wEAAwE;AACxE,cAAc;AACd,QAAQ,aAAa,GAAG;AACxB,SAAS,cAAc,QAAQ;IAC7B,IAAI,CAAC,QAAQ,GAAG,kBAAkB;IAClC,IAAI;IACJ,OAAQ,IAAI,CAAC,QAAQ;QACnB,KAAK;YACH,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,GAAG,GAAG;YACX,KAAK;YACL;QACF,KAAK;YACH,IAAI,CAAC,QAAQ,GAAG;YAChB,KAAK;YACL;QACF,KAAK;YACH,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,GAAG,GAAG;YACX,KAAK;YACL;QACF;YACE,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,GAAG,GAAG;YACX;IACJ;IACA,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,QAAQ,GAAG,OAAO,WAAW,CAAC;AACrC;AAEA,cAAc,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG;IAC3C,IAAI,IAAI,MAAM,KAAK,GAAG,OAAO;IAC7B,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,IAAI,CAAC,QAAQ,CAAC;QAClB,IAAI,MAAM,WAAW,OAAO;QAC5B,IAAI,IAAI,CAAC,QAAQ;QACjB,IAAI,CAAC,QAAQ,GAAG;IAClB,OAAO;QACL,IAAI;IACN;IACA,IAAI,IAAI,IAAI,MAAM,EAAE,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK;IACtE,OAAO,KAAK;AACd;AAEA,cAAc,SAAS,CAAC,GAAG,GAAG;AAE9B,+CAA+C;AAC/C,cAAc,SAAS,CAAC,IAAI,GAAG;AAE/B,+EAA+E;AAC/E,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG;IAC9C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;QAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;QACxE,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,SAAS;IAChE;IACA,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,MAAM;IACrE,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM;AAC7B;AAEA,4EAA4E;AAC5E,qEAAqE;AACrE,SAAS,cAAc,IAAI;IACzB,IAAI,QAAQ,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;IAC3I,OAAO,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC;AACpC;AAEA,sEAAsE;AACtE,gFAAgF;AAChF,uEAAuE;AACvE,SAAS,oBAAoB,IAAI,EAAE,GAAG,EAAE,CAAC;IACvC,IAAI,IAAI,IAAI,MAAM,GAAG;IACrB,IAAI,IAAI,GAAG,OAAO;IAClB,IAAI,KAAK,cAAc,GAAG,CAAC,EAAE;IAC7B,IAAI,MAAM,GAAG;QACX,IAAI,KAAK,GAAG,KAAK,QAAQ,GAAG,KAAK;QACjC,OAAO;IACT;IACA,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC,GAAG,OAAO;IACjC,KAAK,cAAc,GAAG,CAAC,EAAE;IACzB,IAAI,MAAM,GAAG;QACX,IAAI,KAAK,GAAG,KAAK,QAAQ,GAAG,KAAK;QACjC,OAAO;IACT;IACA,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC,GAAG,OAAO;IACjC,KAAK,cAAc,GAAG,CAAC,EAAE;IACzB,IAAI,MAAM,GAAG;QACX,IAAI,KAAK,GAAG;YACV,IAAI,OAAO,GAAG,KAAK;iBAAO,KAAK,QAAQ,GAAG,KAAK;QACjD;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,2EAA2E;AAC3E,6EAA6E;AAC7E,4EAA4E;AAC5E,gFAAgF;AAChF,4EAA4E;AAC5E,gFAAgF;AAChF,+EAA+E;AAC/E,QAAQ;AACR,SAAS,oBAAoB,IAAI,EAAE,GAAG,EAAE,CAAC;IACvC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;QAC5B,KAAK,QAAQ,GAAG;QAChB,OAAO;IACT;IACA,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,MAAM,GAAG,GAAG;QACvC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YAC5B,KAAK,QAAQ,GAAG;YAChB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,MAAM,GAAG,GAAG;YACvC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;gBAC5B,KAAK,QAAQ,GAAG;gBAChB,OAAO;YACT;QACF;IACF;AACF;AAEA,+EAA+E;AAC/E,SAAS,aAAa,GAAG;IACvB,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;IACtC,IAAI,IAAI,oBAAoB,IAAI,EAAE,KAAK;IACvC,IAAI,MAAM,WAAW,OAAO;IAC5B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;QAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ;QAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,SAAS;IAChE;IACA,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,IAAI,MAAM;IACxC,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM;AAC7B;AAEA,8EAA8E;AAC9E,2EAA2E;AAC3E,iCAAiC;AACjC,SAAS,SAAS,GAAG,EAAE,CAAC;IACtB,IAAI,QAAQ,oBAAoB,IAAI,EAAE,KAAK;IAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,QAAQ,CAAC,QAAQ;IAChD,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,MAAM,IAAI,MAAM,GAAG,CAAC,QAAQ,IAAI,CAAC,QAAQ;IAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG;IAC3B,OAAO,IAAI,QAAQ,CAAC,QAAQ,GAAG;AACjC;AAEA,uEAAuE;AACvE,aAAa;AACb,SAAS,QAAQ,GAAG;IAClB,IAAI,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;IAC9C,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI;IAC9B,OAAO;AACT;AAEA,gFAAgF;AAChF,0EAA0E;AAC1E,8EAA8E;AAC9E,sCAAsC;AACtC,SAAS,UAAU,GAAG,EAAE,CAAC;IACvB,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG;QAC9B,IAAI,IAAI,IAAI,QAAQ,CAAC,WAAW;QAChC,IAAI,GAAG;YACL,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG;YAChC,IAAI,KAAK,UAAU,KAAK,QAAQ;gBAC9B,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;gBACtC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;YACrB;QACF;QACA,OAAO;IACT;IACA,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;IACtC,OAAO,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,MAAM,GAAG;AACjD;AAEA,gFAAgF;AAChF,4DAA4D;AAC5D,SAAS,SAAS,GAAG;IACnB,IAAI,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;IAC9C,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;QACxC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,GAAG;IAClD;IACA,OAAO;AACT;AAEA,SAAS,WAAW,GAAG,EAAE,CAAC;IACxB,IAAI,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI;IAC3B,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,CAAC,UAAU;IAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,MAAM,GAAG;QACX,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;IACxC,OAAO;QACL,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;IACxC;IACA,OAAO,IAAI,QAAQ,CAAC,UAAU,GAAG,IAAI,MAAM,GAAG;AAChD;AAEA,SAAS,UAAU,GAAG;IACpB,IAAI,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;IAC9C,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,QAAQ;IACnF,OAAO;AACT;AAEA,4EAA4E;AAC5E,SAAS,YAAY,GAAG;IACtB,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ;AACnC;AAEA,SAAS,UAAU,GAAG;IACpB,OAAO,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/immediate/lib/index.js"], "sourcesContent": ["'use strict';\nvar Mutation = global.MutationObserver || global.WebKitMutationObserver;\n\nvar scheduleDrain;\n\nif (process.browser) {\n  if (Mutation) {\n    var called = 0;\n    var observer = new Mutation(nextTick);\n    var element = global.document.createTextNode('');\n    observer.observe(element, {\n      characterData: true\n    });\n    scheduleDrain = function () {\n      element.data = (called = ++called % 2);\n    };\n  } else if (!global.setImmediate && typeof global.MessageChannel !== 'undefined') {\n    var channel = new global.MessageChannel();\n    channel.port1.onmessage = nextTick;\n    scheduleDrain = function () {\n      channel.port2.postMessage(0);\n    };\n  } else if ('document' in global && 'onreadystatechange' in global.document.createElement('script')) {\n    scheduleDrain = function () {\n\n      // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n      // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n      var scriptEl = global.document.createElement('script');\n      scriptEl.onreadystatechange = function () {\n        nextTick();\n\n        scriptEl.onreadystatechange = null;\n        scriptEl.parentNode.removeChild(scriptEl);\n        scriptEl = null;\n      };\n      global.document.documentElement.appendChild(scriptEl);\n    };\n  } else {\n    scheduleDrain = function () {\n      setTimeout(nextTick, 0);\n    };\n  }\n} else {\n  scheduleDrain = function () {\n    process.nextTick(nextTick);\n  };\n}\n\nvar draining;\nvar queue = [];\n//named nextTick for less confusing stack traces\nfunction nextTick() {\n  draining = true;\n  var i, oldQueue;\n  var len = queue.length;\n  while (len) {\n    oldQueue = queue;\n    queue = [];\n    i = -1;\n    while (++i < len) {\n      oldQueue[i]();\n    }\n    len = queue.length;\n  }\n  draining = false;\n}\n\nmodule.exports = immediate;\nfunction immediate(task) {\n  if (queue.push(task) === 1 && !draining) {\n    scheduleDrain();\n  }\n}\n"], "names": [], "mappings": "AAAA;AACA,IAAI,WAAW,OAAO,gBAAgB,IAAI,OAAO,sBAAsB;AAEvE,IAAI;AAEJ,uCAAqB;;IAEjB,IAAI;IACJ,IAAI;IACJ,IAAI;IAQJ,IAAI;AAyBR,OAAO;IACL,gBAAgB;QACd,QAAQ,QAAQ,CAAC;IACnB;AACF;AAEA,IAAI;AACJ,IAAI,QAAQ,EAAE;AACd,gDAAgD;AAChD,SAAS;IACP,WAAW;IACX,IAAI,GAAG;IACP,IAAI,MAAM,MAAM,MAAM;IACtB,MAAO,IAAK;QACV,WAAW;QACX,QAAQ,EAAE;QACV,IAAI,CAAC;QACL,MAAO,EAAE,IAAI,IAAK;YAChB,QAAQ,CAAC,EAAE;QACb;QACA,MAAM,MAAM,MAAM;IACpB;IACA,WAAW;AACb;AAEA,OAAO,OAAO,GAAG;AACjB,SAAS,UAAU,IAAI;IACrB,IAAI,MAAM,IAAI,CAAC,UAAU,KAAK,CAAC,UAAU;QACvC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lie/lib/index.js"], "sourcesContent": ["'use strict';\nvar immediate = require('immediate');\n\n/* istanbul ignore next */\nfunction INTERNAL() {}\n\nvar handlers = {};\n\nvar REJECTED = ['REJECTED'];\nvar FULFILLED = ['FULFILLED'];\nvar PENDING = ['PENDING'];\n/* istanbul ignore else */\nif (!process.browser) {\n  // in which we actually take advantage of JS scoping\n  var UNHANDLED = ['UNHANDLED'];\n}\n\nmodule.exports = Promise;\n\nfunction Promise(resolver) {\n  if (typeof resolver !== 'function') {\n    throw new TypeError('resolver must be a function');\n  }\n  this.state = PENDING;\n  this.queue = [];\n  this.outcome = void 0;\n  /* istanbul ignore else */\n  if (!process.browser) {\n    this.handled = UNHANDLED;\n  }\n  if (resolver !== INTERNAL) {\n    safelyResolveThenable(this, resolver);\n  }\n}\n\nPromise.prototype.finally = function (callback) {\n  if (typeof callback !== 'function') {\n    return this;\n  }\n  var p = this.constructor;\n  return this.then(resolve, reject);\n\n  function resolve(value) {\n    function yes () {\n      return value;\n    }\n    return p.resolve(callback()).then(yes);\n  }\n  function reject(reason) {\n    function no () {\n      throw reason;\n    }\n    return p.resolve(callback()).then(no);\n  }\n};\nPromise.prototype.catch = function (onRejected) {\n  return this.then(null, onRejected);\n};\nPromise.prototype.then = function (onFulfilled, onRejected) {\n  if (typeof onFulfilled !== 'function' && this.state === FULFILLED ||\n    typeof onRejected !== 'function' && this.state === REJECTED) {\n    return this;\n  }\n  var promise = new this.constructor(INTERNAL);\n  /* istanbul ignore else */\n  if (!process.browser) {\n    if (this.handled === UNHANDLED) {\n      this.handled = null;\n    }\n  }\n  if (this.state !== PENDING) {\n    var resolver = this.state === FULFILLED ? onFulfilled : onRejected;\n    unwrap(promise, resolver, this.outcome);\n  } else {\n    this.queue.push(new QueueItem(promise, onFulfilled, onRejected));\n  }\n\n  return promise;\n};\nfunction QueueItem(promise, onFulfilled, onRejected) {\n  this.promise = promise;\n  if (typeof onFulfilled === 'function') {\n    this.onFulfilled = onFulfilled;\n    this.callFulfilled = this.otherCallFulfilled;\n  }\n  if (typeof onRejected === 'function') {\n    this.onRejected = onRejected;\n    this.callRejected = this.otherCallRejected;\n  }\n}\nQueueItem.prototype.callFulfilled = function (value) {\n  handlers.resolve(this.promise, value);\n};\nQueueItem.prototype.otherCallFulfilled = function (value) {\n  unwrap(this.promise, this.onFulfilled, value);\n};\nQueueItem.prototype.callRejected = function (value) {\n  handlers.reject(this.promise, value);\n};\nQueueItem.prototype.otherCallRejected = function (value) {\n  unwrap(this.promise, this.onRejected, value);\n};\n\nfunction unwrap(promise, func, value) {\n  immediate(function () {\n    var returnValue;\n    try {\n      returnValue = func(value);\n    } catch (e) {\n      return handlers.reject(promise, e);\n    }\n    if (returnValue === promise) {\n      handlers.reject(promise, new TypeError('Cannot resolve promise with itself'));\n    } else {\n      handlers.resolve(promise, returnValue);\n    }\n  });\n}\n\nhandlers.resolve = function (self, value) {\n  var result = tryCatch(getThen, value);\n  if (result.status === 'error') {\n    return handlers.reject(self, result.value);\n  }\n  var thenable = result.value;\n\n  if (thenable) {\n    safelyResolveThenable(self, thenable);\n  } else {\n    self.state = FULFILLED;\n    self.outcome = value;\n    var i = -1;\n    var len = self.queue.length;\n    while (++i < len) {\n      self.queue[i].callFulfilled(value);\n    }\n  }\n  return self;\n};\nhandlers.reject = function (self, error) {\n  self.state = REJECTED;\n  self.outcome = error;\n  /* istanbul ignore else */\n  if (!process.browser) {\n    if (self.handled === UNHANDLED) {\n      immediate(function () {\n        if (self.handled === UNHANDLED) {\n          process.emit('unhandledRejection', error, self);\n        }\n      });\n    }\n  }\n  var i = -1;\n  var len = self.queue.length;\n  while (++i < len) {\n    self.queue[i].callRejected(error);\n  }\n  return self;\n};\n\nfunction getThen(obj) {\n  // Make sure we only access the accessor once as required by the spec\n  var then = obj && obj.then;\n  if (obj && (typeof obj === 'object' || typeof obj === 'function') && typeof then === 'function') {\n    return function appyThen() {\n      then.apply(obj, arguments);\n    };\n  }\n}\n\nfunction safelyResolveThenable(self, thenable) {\n  // Either fulfill, reject or reject with error\n  var called = false;\n  function onError(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.reject(self, value);\n  }\n\n  function onSuccess(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.resolve(self, value);\n  }\n\n  function tryToUnwrap() {\n    thenable(onSuccess, onError);\n  }\n\n  var result = tryCatch(tryToUnwrap);\n  if (result.status === 'error') {\n    onError(result.value);\n  }\n}\n\nfunction tryCatch(func, value) {\n  var out = {};\n  try {\n    out.value = func(value);\n    out.status = 'success';\n  } catch (e) {\n    out.status = 'error';\n    out.value = e;\n  }\n  return out;\n}\n\nPromise.resolve = resolve;\nfunction resolve(value) {\n  if (value instanceof this) {\n    return value;\n  }\n  return handlers.resolve(new this(INTERNAL), value);\n}\n\nPromise.reject = reject;\nfunction reject(reason) {\n  var promise = new this(INTERNAL);\n  return handlers.reject(promise, reason);\n}\n\nPromise.all = all;\nfunction all(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var values = new Array(len);\n  var resolved = 0;\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    allResolver(iterable[i], i);\n  }\n  return promise;\n  function allResolver(value, i) {\n    self.resolve(value).then(resolveFromAll, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n    function resolveFromAll(outValue) {\n      values[i] = outValue;\n      if (++resolved === len && !called) {\n        called = true;\n        handlers.resolve(promise, values);\n      }\n    }\n  }\n}\n\nPromise.race = race;\nfunction race(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    resolver(iterable[i]);\n  }\n  return promise;\n  function resolver(value) {\n    self.resolve(value).then(function (response) {\n      if (!called) {\n        called = true;\n        handlers.resolve(promise, response);\n      }\n    }, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AAEJ,wBAAwB,GACxB,SAAS,YAAY;AAErB,IAAI,WAAW,CAAC;AAEhB,IAAI,WAAW;IAAC;CAAW;AAC3B,IAAI,YAAY;IAAC;CAAY;AAC7B,IAAI,UAAU;IAAC;CAAU;AACzB,wBAAwB,GACxB,IAAI,0CAAkB;IACpB,oDAAoD;IACpD,IAAI,YAAY;QAAC;KAAY;AAC/B;AAEA,OAAO,OAAO,GAAG;AAEjB,SAAS,QAAQ,QAAQ;IACvB,IAAI,OAAO,aAAa,YAAY;QAClC,MAAM,IAAI,UAAU;IACtB;IACA,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,KAAK,GAAG,EAAE;IACf,IAAI,CAAC,OAAO,GAAG,KAAK;IACpB,wBAAwB,GACxB,wCAAsB;QACpB,IAAI,CAAC,OAAO,GAAG;IACjB;IACA,IAAI,aAAa,UAAU;QACzB,sBAAsB,IAAI,EAAE;IAC9B;AACF;AAEA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAU,QAAQ;IAC5C,IAAI,OAAO,aAAa,YAAY;QAClC,OAAO,IAAI;IACb;IACA,IAAI,IAAI,IAAI,CAAC,WAAW;IACxB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;;IAE1B,SAAS,QAAQ,KAAK;QACpB,SAAS;YACP,OAAO;QACT;QACA,OAAO,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC;IACpC;IACA,SAAS,OAAO,MAAM;QACpB,SAAS;YACP,MAAM;QACR;QACA,OAAO,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC;IACpC;AACF;AACA,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAU,UAAU;IAC5C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;AACzB;AACA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,WAAW,EAAE,UAAU;IACxD,IAAI,OAAO,gBAAgB,cAAc,IAAI,CAAC,KAAK,KAAK,aACtD,OAAO,eAAe,cAAc,IAAI,CAAC,KAAK,KAAK,UAAU;QAC7D,OAAO,IAAI;IACb;IACA,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC;IACnC,wBAAwB,GACxB,wCAAsB;QACpB,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW;YAC9B,IAAI,CAAC,OAAO,GAAG;QACjB;IACF;IACA,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS;QAC1B,IAAI,WAAW,IAAI,CAAC,KAAK,KAAK,YAAY,cAAc;QACxD,OAAO,SAAS,UAAU,IAAI,CAAC,OAAO;IACxC,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,SAAS,aAAa;IACtD;IAEA,OAAO;AACT;AACA,SAAS,UAAU,OAAO,EAAE,WAAW,EAAE,UAAU;IACjD,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,OAAO,gBAAgB,YAAY;QACrC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB;IAC9C;IACA,IAAI,OAAO,eAAe,YAAY;QACpC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB;IAC5C;AACF;AACA,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;IACjD,SAAS,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE;AACjC;AACA,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAU,KAAK;IACtD,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;AACzC;AACA,UAAU,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;IAChD,SAAS,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;AAChC;AACA,UAAU,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK;IACrD,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;AACxC;AAEA,SAAS,OAAO,OAAO,EAAE,IAAI,EAAE,KAAK;IAClC,UAAU;QACR,IAAI;QACJ,IAAI;YACF,cAAc,KAAK;QACrB,EAAE,OAAO,GAAG;YACV,OAAO,SAAS,MAAM,CAAC,SAAS;QAClC;QACA,IAAI,gBAAgB,SAAS;YAC3B,SAAS,MAAM,CAAC,SAAS,IAAI,UAAU;QACzC,OAAO;YACL,SAAS,OAAO,CAAC,SAAS;QAC5B;IACF;AACF;AAEA,SAAS,OAAO,GAAG,SAAU,IAAI,EAAE,KAAK;IACtC,IAAI,SAAS,SAAS,SAAS;IAC/B,IAAI,OAAO,MAAM,KAAK,SAAS;QAC7B,OAAO,SAAS,MAAM,CAAC,MAAM,OAAO,KAAK;IAC3C;IACA,IAAI,WAAW,OAAO,KAAK;IAE3B,IAAI,UAAU;QACZ,sBAAsB,MAAM;IAC9B,OAAO;QACL,KAAK,KAAK,GAAG;QACb,KAAK,OAAO,GAAG;QACf,IAAI,IAAI,CAAC;QACT,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM;QAC3B,MAAO,EAAE,IAAI,IAAK;YAChB,KAAK,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC;QAC9B;IACF;IACA,OAAO;AACT;AACA,SAAS,MAAM,GAAG,SAAU,IAAI,EAAE,KAAK;IACrC,KAAK,KAAK,GAAG;IACb,KAAK,OAAO,GAAG;IACf,wBAAwB,GACxB,wCAAsB;QACpB,IAAI,KAAK,OAAO,KAAK,WAAW;YAC9B,UAAU;gBACR,IAAI,KAAK,OAAO,KAAK,WAAW;oBAC9B,QAAQ,IAAI,CAAC,sBAAsB,OAAO;gBAC5C;YACF;QACF;IACF;IACA,IAAI,IAAI,CAAC;IACT,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM;IAC3B,MAAO,EAAE,IAAI,IAAK;QAChB,KAAK,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC;IAC7B;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,GAAG;IAClB,qEAAqE;IACrE,IAAI,OAAO,OAAO,IAAI,IAAI;IAC1B,IAAI,OAAO,CAAC,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU,KAAK,OAAO,SAAS,YAAY;QAC/F,OAAO,SAAS;YACd,KAAK,KAAK,CAAC,KAAK;QAClB;IACF;AACF;AAEA,SAAS,sBAAsB,IAAI,EAAE,QAAQ;IAC3C,8CAA8C;IAC9C,IAAI,SAAS;IACb,SAAS,QAAQ,KAAK;QACpB,IAAI,QAAQ;YACV;QACF;QACA,SAAS;QACT,SAAS,MAAM,CAAC,MAAM;IACxB;IAEA,SAAS,UAAU,KAAK;QACtB,IAAI,QAAQ;YACV;QACF;QACA,SAAS;QACT,SAAS,OAAO,CAAC,MAAM;IACzB;IAEA,SAAS;QACP,SAAS,WAAW;IACtB;IAEA,IAAI,SAAS,SAAS;IACtB,IAAI,OAAO,MAAM,KAAK,SAAS;QAC7B,QAAQ,OAAO,KAAK;IACtB;AACF;AAEA,SAAS,SAAS,IAAI,EAAE,KAAK;IAC3B,IAAI,MAAM,CAAC;IACX,IAAI;QACF,IAAI,KAAK,GAAG,KAAK;QACjB,IAAI,MAAM,GAAG;IACf,EAAE,OAAO,GAAG;QACV,IAAI,MAAM,GAAG;QACb,IAAI,KAAK,GAAG;IACd;IACA,OAAO;AACT;AAEA,QAAQ,OAAO,GAAG;AAClB,SAAS,QAAQ,KAAK;IACpB,IAAI,iBAAiB,IAAI,EAAE;QACzB,OAAO;IACT;IACA,OAAO,SAAS,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW;AAC9C;AAEA,QAAQ,MAAM,GAAG;AACjB,SAAS,OAAO,MAAM;IACpB,IAAI,UAAU,IAAI,IAAI,CAAC;IACvB,OAAO,SAAS,MAAM,CAAC,SAAS;AAClC;AAEA,QAAQ,GAAG,GAAG;AACd,SAAS,IAAI,QAAQ;IACnB,IAAI,OAAO,IAAI;IACf,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,kBAAkB;QACjE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,UAAU;IACnC;IAEA,IAAI,MAAM,SAAS,MAAM;IACzB,IAAI,SAAS;IACb,IAAI,CAAC,KAAK;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACxB;IAEA,IAAI,SAAS,IAAI,MAAM;IACvB,IAAI,WAAW;IACf,IAAI,IAAI,CAAC;IACT,IAAI,UAAU,IAAI,IAAI,CAAC;IAEvB,MAAO,EAAE,IAAI,IAAK;QAChB,YAAY,QAAQ,CAAC,EAAE,EAAE;IAC3B;IACA,OAAO;;IACP,SAAS,YAAY,KAAK,EAAE,CAAC;QAC3B,KAAK,OAAO,CAAC,OAAO,IAAI,CAAC,gBAAgB,SAAU,KAAK;YACtD,IAAI,CAAC,QAAQ;gBACX,SAAS;gBACT,SAAS,MAAM,CAAC,SAAS;YAC3B;QACF;QACA,SAAS,eAAe,QAAQ;YAC9B,MAAM,CAAC,EAAE,GAAG;YACZ,IAAI,EAAE,aAAa,OAAO,CAAC,QAAQ;gBACjC,SAAS;gBACT,SAAS,OAAO,CAAC,SAAS;YAC5B;QACF;IACF;AACF;AAEA,QAAQ,IAAI,GAAG;AACf,SAAS,KAAK,QAAQ;IACpB,IAAI,OAAO,IAAI;IACf,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,kBAAkB;QACjE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,UAAU;IACnC;IAEA,IAAI,MAAM,SAAS,MAAM;IACzB,IAAI,SAAS;IACb,IAAI,CAAC,KAAK;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACxB;IAEA,IAAI,IAAI,CAAC;IACT,IAAI,UAAU,IAAI,IAAI,CAAC;IAEvB,MAAO,EAAE,IAAI,IAAK;QAChB,SAAS,QAAQ,CAAC,EAAE;IACtB;IACA,OAAO;;IACP,SAAS,SAAS,KAAK;QACrB,KAAK,OAAO,CAAC,OAAO,IAAI,CAAC,SAAU,QAAQ;YACzC,IAAI,CAAC,QAAQ;gBACX,SAAS;gBACT,SAAS,OAAO,CAAC,SAAS;YAC5B;QACF,GAAG,SAAU,KAAK;YAChB,IAAI,CAAC,QAAQ;gBACX,SAAS;gBACT,SAAS,MAAM,CAAC,SAAS;YAC3B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/Utility.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function() {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,QAAQ,UAAU,SAAS,SAAS,YAAY,UAAU,eAC5D,QAAQ,EAAE,CAAC,KAAK,EAChB,UAAU,CAAC,EAAE,cAAc;IAE7B,SAAS;QACP,IAAI,GAAG,KAAK,KAAK,QAAQ,SAAS;QAClC,SAAS,SAAS,CAAC,EAAE,EAAE,UAAU,KAAK,UAAU,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,KAAK,EAAE;QACtF,IAAI,WAAW,OAAO,MAAM,GAAG;YAC7B,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM;QAC5B,OAAO;YACL,IAAK,IAAI,GAAG,MAAM,QAAQ,MAAM,EAAE,IAAI,KAAK,IAAK;gBAC9C,SAAS,OAAO,CAAC,EAAE;gBACnB,IAAI,UAAU,MAAM;oBAClB,IAAK,OAAO,OAAQ;wBAClB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,MAAM;wBAChC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;oBAC3B;gBACF;YACF;QACF;QACA,OAAO;IACT;IAEA,aAAa,SAAS,GAAG;QACvB,OAAO,CAAC,CAAC,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS;IAC1D;IAEA,WAAW,SAAS,GAAG;QACrB,IAAI;QACJ,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,OAAO,GAAG,MAAM,cAAc,QAAQ,QAAQ;IACxE;IAEA,UAAU,SAAS,GAAG;QACpB,IAAI,WAAW,MAAM,OAAO,GAAG;YAC7B,OAAO,MAAM,OAAO,CAAC;QACvB,OAAO;YACL,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS;QACjD;IACF;IAEA,UAAU,SAAS,GAAG;QACpB,IAAI;QACJ,IAAI,QAAQ,MAAM;YAChB,OAAO,CAAC,IAAI,MAAM;QACpB,OAAO;YACL,IAAK,OAAO,IAAK;gBACf,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,MAAM;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,gBAAgB,SAAS,GAAG;QAC1B,IAAI,MAAM;QACV,OAAO,SAAS,QAAQ,CAAC,QAAQ,OAAO,cAAc,CAAC,IAAI,KAAK,CAAC,OAAO,MAAM,WAAW,KAAM,OAAO,SAAS,cAAgB,gBAAgB,QAAU,SAAS,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,SAAS,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;IACvO;IAEA,WAAW,SAAS,GAAG;QACrB,IAAI,WAAW,IAAI,OAAO,GAAG;YAC3B,OAAO,IAAI,OAAO;QACpB,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO,OAAO,CAAC,MAAM,GAAG;IAExB,OAAO,OAAO,CAAC,UAAU,GAAG;IAE5B,OAAO,OAAO,CAAC,QAAQ,GAAG;IAE1B,OAAO,OAAO,CAAC,OAAO,GAAG;IAEzB,OAAO,OAAO,CAAC,OAAO,GAAG;IAEzB,OAAO,OAAO,CAAC,aAAa,GAAG;IAE/B,OAAO,OAAO,CAAC,QAAQ,GAAG;AAE5B,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLAttribute.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute;\n\n  module.exports = XMLAttribute = (function() {\n    function XMLAttribute(parent, name, value) {\n      this.options = parent.options;\n      this.stringify = parent.stringify;\n      this.parent = parent;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing attribute value. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.attName(name);\n      this.value = this.stringify.attValue(value);\n    }\n\n    XMLAttribute.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLAttribute.prototype.toString = function(options) {\n      return this.options.writer.set(options).attribute(this);\n    };\n\n    XMLAttribute.prototype.debugInfo = function(name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    return XMLAttribute;\n\n  })();\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI;IAEJ,OAAO,OAAO,GAAG,eAAe,AAAC;QAC/B,SAAS,aAAa,MAAM,EAAE,IAAI,EAAE,KAAK;YACvC,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;YAC7B,IAAI,CAAC,SAAS,GAAG,OAAO,SAAS;YACjC,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,6BAA6B,IAAI,CAAC,SAAS,CAAC;YAC9D;YACA,IAAI,SAAS,MAAM;gBACjB,MAAM,IAAI,MAAM,8BAA8B,IAAI,CAAC,SAAS,CAAC;YAC/D;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACvC;QAEA,aAAa,SAAS,CAAC,KAAK,GAAG;YAC7B,OAAO,OAAO,MAAM,CAAC,IAAI;QAC3B;QAEA,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAChD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,IAAI;QACxD;QAEA,aAAa,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI;YAC9C,OAAO,QAAQ,IAAI,CAAC,IAAI;YACxB,IAAI,QAAQ,MAAM;gBAChB,OAAO,cAAc,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;YAC1C,OAAO;gBACL,OAAO,iBAAiB,OAAO,iBAAiB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;YACrE;QACF;QAEA,OAAO;IAET;AAEF,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLElement.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute, XMLElement, XMLNode, getValue, isFunction, isObject, ref,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  ref = require('./Utility'), isObject = ref.isObject, isFunction = ref.isFunction, getValue = ref.getValue;\n\n  XMLNode = require('./XMLNode');\n\n  XMLAttribute = require('./XMLAttribute');\n\n  module.exports = XMLElement = (function(superClass) {\n    extend(XMLElement, superClass);\n\n    function XMLElement(parent, name, attributes) {\n      XMLElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing element name. \" + this.debugInfo());\n      }\n      this.name = this.stringify.eleName(name);\n      this.attributes = {};\n      if (attributes != null) {\n        this.attribute(attributes);\n      }\n      if (parent.isDocument) {\n        this.isRoot = true;\n        this.documentObject = parent;\n        parent.rootObject = this;\n      }\n    }\n\n    XMLElement.prototype.clone = function() {\n      var att, attName, clonedSelf, ref1;\n      clonedSelf = Object.create(this);\n      if (clonedSelf.isRoot) {\n        clonedSelf.documentObject = null;\n      }\n      clonedSelf.attributes = {};\n      ref1 = this.attributes;\n      for (attName in ref1) {\n        if (!hasProp.call(ref1, attName)) continue;\n        att = ref1[attName];\n        clonedSelf.attributes[attName] = att.clone();\n      }\n      clonedSelf.children = [];\n      this.children.forEach(function(child) {\n        var clonedChild;\n        clonedChild = child.clone();\n        clonedChild.parent = clonedSelf;\n        return clonedSelf.children.push(clonedChild);\n      });\n      return clonedSelf;\n    };\n\n    XMLElement.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (!this.options.skipNullAttributes || (value != null)) {\n          this.attributes[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLElement.prototype.removeAttribute = function(name) {\n      var attName, i, len;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo());\n      }\n      name = getValue(name);\n      if (Array.isArray(name)) {\n        for (i = 0, len = name.length; i < len; i++) {\n          attName = name[i];\n          delete this.attributes[attName];\n        }\n      } else {\n        delete this.attributes[name];\n      }\n      return this;\n    };\n\n    XMLElement.prototype.toString = function(options) {\n      return this.options.writer.set(options).element(this);\n    };\n\n    XMLElement.prototype.att = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.a = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    return XMLElement;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,cAAc,YAAY,SAAS,UAAU,YAAY,UAAU,KACrE,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B,4GAA4B,WAAW,IAAI,QAAQ,EAAE,aAAa,IAAI,UAAU,EAAE,WAAW,IAAI,QAAQ;IAEzG;IAEA;IAEA,OAAO,OAAO,GAAG,aAAa,AAAC,SAAS,UAAU;QAChD,OAAO,YAAY;QAEnB,SAAS,WAAW,MAAM,EAAE,IAAI,EAAE,UAAU;YAC1C,WAAW,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC5C,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,2BAA2B,IAAI,CAAC,SAAS;YAC3D;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,UAAU,GAAG,CAAC;YACnB,IAAI,cAAc,MAAM;gBACtB,IAAI,CAAC,SAAS,CAAC;YACjB;YACA,IAAI,OAAO,UAAU,EAAE;gBACrB,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,cAAc,GAAG;gBACtB,OAAO,UAAU,GAAG,IAAI;YAC1B;QACF;QAEA,WAAW,SAAS,CAAC,KAAK,GAAG;YAC3B,IAAI,KAAK,SAAS,YAAY;YAC9B,aAAa,OAAO,MAAM,CAAC,IAAI;YAC/B,IAAI,WAAW,MAAM,EAAE;gBACrB,WAAW,cAAc,GAAG;YAC9B;YACA,WAAW,UAAU,GAAG,CAAC;YACzB,OAAO,IAAI,CAAC,UAAU;YACtB,IAAK,WAAW,KAAM;gBACpB,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,UAAU;gBAClC,MAAM,IAAI,CAAC,QAAQ;gBACnB,WAAW,UAAU,CAAC,QAAQ,GAAG,IAAI,KAAK;YAC5C;YACA,WAAW,QAAQ,GAAG,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;gBAClC,IAAI;gBACJ,cAAc,MAAM,KAAK;gBACzB,YAAY,MAAM,GAAG;gBACrB,OAAO,WAAW,QAAQ,CAAC,IAAI,CAAC;YAClC;YACA,OAAO;QACT;QAEA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;YACnD,IAAI,SAAS;YACb,IAAI,QAAQ,MAAM;gBAChB,OAAO,SAAS;YAClB;YACA,IAAI,SAAS,OAAO;gBAClB,IAAK,WAAW,KAAM;oBACpB,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,UAAU;oBAClC,WAAW,IAAI,CAAC,QAAQ;oBACxB,IAAI,CAAC,SAAS,CAAC,SAAS;gBAC1B;YACF,OAAO;gBACL,IAAI,WAAW,QAAQ;oBACrB,QAAQ,MAAM,KAAK;gBACrB;gBACA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAK,SAAS,MAAO;oBACvD,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,aAAa,IAAI,EAAE,MAAM;gBACvD;YACF;YACA,OAAO,IAAI;QACb;QAEA,WAAW,SAAS,CAAC,eAAe,GAAG,SAAS,IAAI;YAClD,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,6BAA6B,IAAI,CAAC,SAAS;YAC7D;YACA,OAAO,SAAS;YAChB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,IAAK,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;oBAC3C,UAAU,IAAI,CAAC,EAAE;oBACjB,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ;gBACjC;YACF,OAAO;gBACL,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK;YAC9B;YACA,OAAO,IAAI;QACb;QAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,IAAI;QACtD;QAEA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;QAC9B;QAEA,WAAW,SAAS,CAAC,CAAC,GAAG,SAAS,IAAI,EAAE,KAAK;YAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;QAC9B;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLCData.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLCData = (function(superClass) {\n    extend(XMLCData, superClass);\n\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.text = this.stringify.cdata(text);\n    }\n\n    XMLCData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCData.prototype.toString = function(options) {\n      return this.options.writer.set(options).cdata(this);\n    };\n\n    return XMLCData;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,UAAU,SACZ,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,WAAW,AAAC,SAAS,UAAU;QAC9C,OAAO,UAAU;QAEjB,SAAS,SAAS,MAAM,EAAE,IAAI;YAC5B,SAAS,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC1C,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,yBAAyB,IAAI,CAAC,SAAS;YACzD;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QACnC;QAEA,SAAS,SAAS,CAAC,KAAK,GAAG;YACzB,OAAO,OAAO,MAAM,CAAC,IAAI;QAC3B;QAEA,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,KAAK,CAAC,IAAI;QACpD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLComment.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLComment, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLComment = (function(superClass) {\n    extend(XMLComment, superClass);\n\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.text = this.stringify.comment(text);\n    }\n\n    XMLComment.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLComment.prototype.toString = function(options) {\n      return this.options.writer.set(options).comment(this);\n    };\n\n    return XMLComment;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,YAAY,SACd,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,aAAa,AAAC,SAAS,UAAU;QAChD,OAAO,YAAY;QAEnB,SAAS,WAAW,MAAM,EAAE,IAAI;YAC9B,WAAW,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC5C,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,2BAA2B,IAAI,CAAC,SAAS;YAC3D;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QACrC;QAEA,WAAW,SAAS,CAAC,KAAK,GAAG;YAC3B,OAAO,OAAO,MAAM,CAAC,IAAI;QAC3B;QAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,IAAI;QACtD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDeclaration.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDeclaration, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLDeclaration = (function(superClass) {\n    extend(XMLDeclaration, superClass);\n\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    XMLDeclaration.prototype.toString = function(options) {\n      return this.options.writer.set(options).declaration(this);\n    };\n\n    return XMLDeclaration;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,gBAAgB,SAAS,UAC3B,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B,WAAW,qGAAqB,QAAQ;IAExC;IAEA,OAAO,OAAO,GAAG,iBAAiB,AAAC,SAAS,UAAU;QACpD,OAAO,gBAAgB;QAEvB,SAAS,eAAe,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU;YAC3D,IAAI;YACJ,eAAe,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAChD,IAAI,SAAS,UAAU;gBACrB,MAAM,SAAS,UAAU,IAAI,OAAO,EAAE,WAAW,IAAI,QAAQ,EAAE,aAAa,IAAI,UAAU;YAC5F;YACA,IAAI,CAAC,SAAS;gBACZ,UAAU;YACZ;YACA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;YACzC,IAAI,YAAY,MAAM;gBACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;YAC7C;YACA,IAAI,cAAc,MAAM;gBACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;YACjD;QACF;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAClD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;QAC1D;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDTDAttList.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDAttList, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLDTDAttList = (function(superClass) {\n    extend(XMLDTDAttList, superClass);\n\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.eleName(elementName);\n      this.attributeName = this.stringify.attName(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      this.defaultValueType = defaultValueType;\n    }\n\n    XMLDTDAttList.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdAttList(this);\n    };\n\n    return XMLDTDAttList;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,eAAe,SACjB,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,gBAAgB,AAAC,SAAS,UAAU;QACnD,OAAO,eAAe;QAEtB,SAAS,cAAc,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;YACtG,cAAc,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC/C,IAAI,eAAe,MAAM;gBACvB,MAAM,IAAI,MAAM,+BAA+B,IAAI,CAAC,SAAS;YAC/D;YACA,IAAI,iBAAiB,MAAM;gBACzB,MAAM,IAAI,MAAM,iCAAiC,IAAI,CAAC,SAAS,CAAC;YAClE;YACA,IAAI,CAAC,eAAe;gBAClB,MAAM,IAAI,MAAM,iCAAiC,IAAI,CAAC,SAAS,CAAC;YAClE;YACA,IAAI,CAAC,kBAAkB;gBACrB,MAAM,IAAI,MAAM,oCAAoC,IAAI,CAAC,SAAS,CAAC;YACrE;YACA,IAAI,iBAAiB,OAAO,CAAC,SAAS,GAAG;gBACvC,mBAAmB,MAAM;YAC3B;YACA,IAAI,CAAC,iBAAiB,KAAK,CAAC,2CAA2C;gBACrE,MAAM,IAAI,MAAM,oFAAoF,IAAI,CAAC,SAAS,CAAC;YACrH;YACA,IAAI,gBAAgB,CAAC,iBAAiB,KAAK,CAAC,wBAAwB;gBAClE,MAAM,IAAI,MAAM,uDAAuD,IAAI,CAAC,SAAS,CAAC;YACxF;YACA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC1C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;YAC/C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;YACjD,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QAEA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,UAAU,CAAC,IAAI;QACzD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDTDEntity.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDEntity, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLDTDEntity = (function(superClass) {\n    extend(XMLDTDEntity, superClass);\n\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.eleName(name);\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n\n    XMLDTDEntity.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdEntity(this);\n    };\n\n    return XMLDTDEntity;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,cAAc,SAAS,UACzB,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B,WAAW,qGAAqB,QAAQ;IAExC;IAEA,OAAO,OAAO,GAAG,eAAe,AAAC,SAAS,UAAU;QAClD,OAAO,cAAc;QAErB,SAAS,aAAa,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK;YAC3C,aAAa,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC9C,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,8BAA8B,IAAI,CAAC,SAAS,CAAC;YAC/D;YACA,IAAI,SAAS,MAAM;gBACjB,MAAM,IAAI,MAAM,+BAA+B,IAAI,CAAC,SAAS,CAAC;YAChE;YACA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,SAAS,QAAQ;gBACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;YAC7C,OAAO;gBACL,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,EAAE;oBAChC,MAAM,IAAI,MAAM,2EAA2E,IAAI,CAAC,SAAS,CAAC;gBAC5G;gBACA,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,EAAE;oBAC/B,MAAM,IAAI,MAAM,iEAAiE,IAAI,CAAC,SAAS,CAAC;gBAClG;gBACA,IAAI,MAAM,KAAK,IAAI,MAAM;oBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK;gBAClD;gBACA,IAAI,MAAM,KAAK,IAAI,MAAM;oBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK;gBAClD;gBACA,IAAI,MAAM,KAAK,IAAI,MAAM;oBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK;gBAClD;gBACA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;oBACzB,MAAM,IAAI,MAAM,gEAAgE,IAAI,CAAC,SAAS,CAAC;gBACjG;YACF;QACF;QAEA,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAChD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,SAAS,CAAC,IAAI;QACxD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDTDElement.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDElement, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLDTDElement = (function(superClass) {\n    extend(XMLDTDElement, superClass);\n\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.eleName(name);\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    XMLDTDElement.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdElement(this);\n    };\n\n    return XMLDTDElement;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,eAAe,SACjB,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,gBAAgB,AAAC,SAAS,UAAU;QACnD,OAAO,eAAe;QAEtB,SAAS,cAAc,MAAM,EAAE,IAAI,EAAE,KAAK;YACxC,cAAc,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC/C,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,+BAA+B,IAAI,CAAC,SAAS;YAC/D;YACA,IAAI,CAAC,OAAO;gBACV,QAAQ;YACV;YACA,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,QAAQ,MAAM,MAAM,IAAI,CAAC,OAAO;YAClC;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QAC9C;QAEA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,UAAU,CAAC,IAAI;QACzD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDTDNotation.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDNotation, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLDTDNotation = (function(superClass) {\n    extend(XMLDTDNotation, superClass);\n\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.eleName(name);\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n\n    XMLDTDNotation.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdNotation(this);\n    };\n\n    return XMLDTDNotation;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,gBAAgB,SAClB,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,iBAAiB,AAAC,SAAS,UAAU;QACpD,OAAO,gBAAgB;QAEvB,SAAS,eAAe,MAAM,EAAE,IAAI,EAAE,KAAK;YACzC,eAAe,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAChD,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,gCAAgC,IAAI,CAAC,SAAS,CAAC;YACjE;YACA,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,EAAE;gBAChC,MAAM,IAAI,MAAM,uEAAuE,IAAI,CAAC,SAAS,CAAC;YACxG;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YACnC,IAAI,MAAM,KAAK,IAAI,MAAM;gBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK;YAClD;YACA,IAAI,MAAM,KAAK,IAAI,MAAM;gBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK;YAClD;QACF;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAClD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,IAAI;QAC1D;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDocType.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDocType, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  module.exports = XMLDocType = (function(superClass) {\n    extend(XMLDocType, superClass);\n\n    function XMLDocType(parent, pubID, sysID) {\n      var ref, ref1;\n      XMLDocType.__super__.constructor.call(this, parent);\n      this.name = \"!DOCTYPE\";\n      this.documentObject = parent;\n      if (isObject(pubID)) {\n        ref = pubID, pubID = ref.pubID, sysID = ref.sysID;\n      }\n      if (sysID == null) {\n        ref1 = [pubID, sysID], sysID = ref1[0], pubID = ref1[1];\n      }\n      if (pubID != null) {\n        this.pubID = this.stringify.dtdPubID(pubID);\n      }\n      if (sysID != null) {\n        this.sysID = this.stringify.dtdSysID(sysID);\n      }\n    }\n\n    XMLDocType.prototype.element = function(name, value) {\n      var child;\n      child = new XMLDTDElement(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var child;\n      child = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.entity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, false, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.pEntity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, true, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.notation = function(name, value) {\n      var child;\n      child = new XMLDTDNotation(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.toString = function(options) {\n      return this.options.writer.set(options).docType(this);\n    };\n\n    XMLDocType.prototype.ele = function(name, value) {\n      return this.element(name, value);\n    };\n\n    XMLDocType.prototype.att = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      return this.attList(elementName, attributeName, attributeType, defaultValueType, defaultValue);\n    };\n\n    XMLDocType.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocType.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocType.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    XMLDocType.prototype.up = function() {\n      return this.root() || this.documentObject;\n    };\n\n    return XMLDocType;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,eAAe,eAAe,cAAc,gBAAgB,YAAY,SAAS,UACnF,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B,WAAW,qGAAqB,QAAQ;IAExC;IAEA;IAEA;IAEA;IAEA;IAEA,OAAO,OAAO,GAAG,aAAa,AAAC,SAAS,UAAU;QAChD,OAAO,YAAY;QAEnB,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,KAAK;YACtC,IAAI,KAAK;YACT,WAAW,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC5C,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,cAAc,GAAG;YACtB,IAAI,SAAS,QAAQ;gBACnB,MAAM,OAAO,QAAQ,IAAI,KAAK,EAAE,QAAQ,IAAI,KAAK;YACnD;YACA,IAAI,SAAS,MAAM;gBACjB,OAAO;oBAAC;oBAAO;iBAAM,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,EAAE;YACzD;YACA,IAAI,SAAS,MAAM;gBACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACvC;YACA,IAAI,SAAS,MAAM;gBACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACvC;QACF;QAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACjD,IAAI;YACJ,QAAQ,IAAI,cAAc,IAAI,EAAE,MAAM;YACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;YAC/G,IAAI;YACJ,QAAQ,IAAI,cAAc,IAAI,EAAE,aAAa,eAAe,eAAe,kBAAkB;YAC7F,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAS,IAAI,EAAE,KAAK;YAChD,IAAI;YACJ,QAAQ,IAAI,aAAa,IAAI,EAAE,OAAO,MAAM;YAC5C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACjD,IAAI;YACJ,QAAQ,IAAI,aAAa,IAAI,EAAE,MAAM,MAAM;YAC3C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,KAAK;YAClD,IAAI;YACJ,QAAQ,IAAI,eAAe,IAAI,EAAE,MAAM;YACvC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,IAAI;QACtD;QAEA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC5B;QAEA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;YAC3G,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,eAAe,eAAe,kBAAkB;QACnF;QAEA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM;QAC3B;QAEA,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,KAAK;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC5B;QAEA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;QAC7B;QAEA,WAAW,SAAS,CAAC,EAAE,GAAG;YACxB,OAAO,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,cAAc;QAC3C;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLRaw.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLRaw,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLRaw = (function(superClass) {\n    extend(XMLRaw, superClass);\n\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.value = this.stringify.raw(text);\n    }\n\n    XMLRaw.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLRaw.prototype.toString = function(options) {\n      return this.options.writer.set(options).raw(this);\n    };\n\n    return XMLRaw;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,SAAS,QACX,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,SAAS,AAAC,SAAS,UAAU;QAC5C,OAAO,QAAQ;QAEf,SAAS,OAAO,MAAM,EAAE,IAAI;YAC1B,OAAO,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YACxC,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,uBAAuB,IAAI,CAAC,SAAS;YACvD;YACA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAClC;QAEA,OAAO,SAAS,CAAC,KAAK,GAAG;YACvB,OAAO,OAAO,MAAM,CAAC,IAAI;QAC3B;QAEA,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC1C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI;QAClD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLText.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLText,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLText = (function(superClass) {\n    extend(XMLText, superClass);\n\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.value = this.stringify.eleText(text);\n    }\n\n    XMLText.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLText.prototype.toString = function(options) {\n      return this.options.writer.set(options).text(this);\n    };\n\n    return XMLText;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,SAAS,SACX,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,UAAU,AAAC,SAAS,UAAU;QAC7C,OAAO,SAAS;QAEhB,SAAS,QAAQ,MAAM,EAAE,IAAI;YAC3B,QAAQ,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YACzC,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM,2BAA2B,IAAI,CAAC,SAAS;YAC3D;YACA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QACtC;QAEA,QAAQ,SAAS,CAAC,KAAK,GAAG;YACxB,OAAO,OAAO,MAAM,CAAC,IAAI;QAC3B;QAEA,QAAQ,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI;QACnD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLProcessingInstruction,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLProcessingInstruction = (function(superClass) {\n    extend(XMLProcessingInstruction, superClass);\n\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.target = this.stringify.insTarget(target);\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    XMLProcessingInstruction.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLProcessingInstruction.prototype.toString = function(options) {\n      return this.options.writer.set(options).processingInstruction(this);\n    };\n\n    return XMLProcessingInstruction;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,SAAS,0BACX,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,2BAA2B,AAAC,SAAS,UAAU;QAC9D,OAAO,0BAA0B;QAEjC,SAAS,yBAAyB,MAAM,EAAE,MAAM,EAAE,KAAK;YACrD,yBAAyB,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC1D,IAAI,UAAU,MAAM;gBAClB,MAAM,IAAI,MAAM,iCAAiC,IAAI,CAAC,SAAS;YACjE;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;YACvC,IAAI,OAAO;gBACT,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACvC;QACF;QAEA,yBAAyB,SAAS,CAAC,KAAK,GAAG;YACzC,OAAO,OAAO,MAAM,CAAC,IAAI;QAC3B;QAEA,yBAAyB,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC5D,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,qBAAqB,CAAC,IAAI;QACpE;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDummy.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDummy, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLDummy = (function(superClass) {\n    extend(XMLDummy, superClass);\n\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.isDummy = true;\n    }\n\n    XMLDummy.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLDummy.prototype.toString = function(options) {\n      return '';\n    };\n\n    return XMLDummy;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,UAAU,SACZ,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA,OAAO,OAAO,GAAG,WAAW,AAAC,SAAS,UAAU;QAC9C,OAAO,UAAU;QAEjB,SAAS,SAAS,MAAM;YACtB,SAAS,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC1C,IAAI,CAAC,OAAO,GAAG;QACjB;QAEA,SAAS,SAAS,CAAC,KAAK,GAAG;YACzB,OAAO,OAAO,MAAM,CAAC,IAAI;QAC3B;QAEA,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC5C,OAAO;QACT;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLNode.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNode, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = require('./Utility'), isObject = ref.isObject, isFunction = ref.isFunction, isEmpty = ref.isEmpty, getValue = ref.getValue;\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  module.exports = XMLNode = (function() {\n    function XMLNode(parent) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.children = [];\n      if (!XMLElement) {\n        XMLElement = require('./XMLElement');\n        XMLCData = require('./XMLCData');\n        XMLComment = require('./XMLComment');\n        XMLDeclaration = require('./XMLDeclaration');\n        XMLDocType = require('./XMLDocType');\n        XMLRaw = require('./XMLRaw');\n        XMLText = require('./XMLText');\n        XMLProcessingInstruction = require('./XMLProcessingInstruction');\n        XMLDummy = require('./XMLDummy');\n      }\n    }\n\n    XMLNode.prototype.element = function(name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref1, ref2, val;\n      lastChild = null;\n      if (attributes === null && (text == null)) {\n        ref1 = [{}, null], attributes = ref1[0], text = ref1[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if ((isObject(val)) && (isEmpty(val))) {\n            val = null;\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            lastChild = this.element(key);\n            lastChild.element(val);\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (this.options.skipNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n\n    XMLNode.prototype.insertBefore = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.insertAfter = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.remove = function() {\n      var i, ref1;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref1 = [])), ref1;\n      return this.parent;\n    };\n\n    XMLNode.prototype.node = function(name, attributes, text) {\n      var child, ref1;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.text = function(value) {\n      var child;\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.cdata = function(value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.comment = function(value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.commentBefore = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.commentAfter = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.raw = function(value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.dummy = function() {\n      var child;\n      child = new XMLDummy(this);\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.instruction = function(target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n\n    XMLNode.prototype.instructionBefore = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.instructionAfter = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.declaration = function(version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children[0] instanceof XMLDeclaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n\n    XMLNode.prototype.doctype = function(pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref1, ref2;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref1 = doc.children;\n      for (i = j = 0, len = ref1.length; j < len; i = ++j) {\n        child = ref1[i];\n        if (child instanceof XMLDocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref2 = doc.children;\n      for (i = k = 0, len1 = ref2.length; k < len1; i = ++k) {\n        child = ref2[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n\n    XMLNode.prototype.up = function() {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n\n    XMLNode.prototype.root = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.isDocument) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.document = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.isDocument) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.end = function(options) {\n      return this.document().end(options);\n    };\n\n    XMLNode.prototype.prev = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      while (i > 0 && this.parent.children[i - 1].isDummy) {\n        i = i - 1;\n      }\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n\n    XMLNode.prototype.next = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      while (i < this.parent.children.length - 1 && this.parent.children[i + 1].isDummy) {\n        i = i + 1;\n      }\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n\n    XMLNode.prototype.importDocument = function(doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n\n    XMLNode.prototype.debugInfo = function(name) {\n      var ref1, ref2;\n      name = name || this.name;\n      if ((name == null) && !((ref1 = this.parent) != null ? ref1.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLNode.prototype.ele = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.doc = function() {\n      return this.document();\n    };\n\n    XMLNode.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLNode.prototype.dtd = function(pubID, sysID) {\n      return this.doctype(pubID, sysID);\n    };\n\n    XMLNode.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLNode.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.u = function() {\n      return this.up();\n    };\n\n    XMLNode.prototype.importXMLBuilder = function(doc) {\n      return this.importDocument(doc);\n    };\n\n    return XMLNode;\n\n  })();\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,UAAU,YAAY,gBAAgB,YAAY,UAAU,YAAY,SAAS,0BAA0B,QAAQ,SAAS,UAAU,SAAS,YAAY,UAAU,KACvK,UAAU,CAAC,EAAE,cAAc;IAE7B,4GAA4B,WAAW,IAAI,QAAQ,EAAE,aAAa,IAAI,UAAU,EAAE,UAAU,IAAI,OAAO,EAAE,WAAW,IAAI,QAAQ;IAEhI,aAAa;IAEb,WAAW;IAEX,aAAa;IAEb,iBAAiB;IAEjB,aAAa;IAEb,SAAS;IAET,UAAU;IAEV,2BAA2B;IAE3B,WAAW;IAEX,OAAO,OAAO,GAAG,UAAU,AAAC;QAC1B,SAAS,QAAQ,MAAM;YACrB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;gBAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS;YACxC;YACA,IAAI,CAAC,QAAQ,GAAG,EAAE;YAClB,IAAI,CAAC,YAAY;gBACf;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACzD,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,WAAW,KAAK,MAAM,MAAM,MAAM;YAClE,YAAY;YACZ,IAAI,eAAe,QAAS,QAAQ,MAAO;gBACzC,OAAO;oBAAC,CAAC;oBAAG;iBAAK,EAAE,aAAa,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,EAAE;YACzD;YACA,IAAI,cAAc,MAAM;gBACtB,aAAa,CAAC;YAChB;YACA,aAAa,SAAS;YACtB,IAAI,CAAC,SAAS,aAAa;gBACzB,OAAO;oBAAC;oBAAY;iBAAK,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,aAAa,IAAI,CAAC,EAAE;YACjE;YACA,IAAI,QAAQ,MAAM;gBAChB,OAAO,SAAS;YAClB;YACA,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,IAAK,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;oBAC3C,OAAO,IAAI,CAAC,EAAE;oBACd,YAAY,IAAI,CAAC,OAAO,CAAC;gBAC3B;YACF,OAAO,IAAI,WAAW,OAAO;gBAC3B,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK;YACrC,OAAO,IAAI,SAAS,OAAO;gBACzB,IAAK,OAAO,KAAM;oBAChB,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,MAAM;oBAC9B,MAAM,IAAI,CAAC,IAAI;oBACf,IAAI,WAAW,MAAM;wBACnB,MAAM,IAAI,KAAK;oBACjB;oBACA,IAAI,AAAC,SAAS,QAAU,QAAQ,MAAO;wBACrC,MAAM;oBACR;oBACA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,MAAM,GAAG;wBACrH,YAAY,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,GAAG;oBAC9E,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,MAAM,OAAO,CAAC,MAAM;wBACjE,IAAK,IAAI,GAAG,OAAO,IAAI,MAAM,EAAE,IAAI,MAAM,IAAK;4BAC5C,OAAO,GAAG,CAAC,EAAE;4BACb,YAAY,CAAC;4BACb,SAAS,CAAC,IAAI,GAAG;4BACjB,YAAY,IAAI,CAAC,OAAO,CAAC;wBAC3B;oBACF,OAAO,IAAI,SAAS,MAAM;wBACxB,YAAY,IAAI,CAAC,OAAO,CAAC;wBACzB,UAAU,OAAO,CAAC;oBACpB,OAAO;wBACL,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK;oBAChC;gBACF;YACF,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM;gBACtD,YAAY,IAAI,CAAC,KAAK;YACxB,OAAO;gBACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,MAAM,GAAG;oBACxH,YAAY,IAAI,CAAC,IAAI,CAAC;gBACxB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,MAAM,GAAG;oBACjI,YAAY,IAAI,CAAC,KAAK,CAAC;gBACzB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,MAAM,GAAG;oBACrI,YAAY,IAAI,CAAC,OAAO,CAAC;gBAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,MAAM,GAAG;oBAC7H,YAAY,IAAI,CAAC,GAAG,CAAC;gBACvB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,MAAM,GAAG;oBAC3H,YAAY,IAAI,CAAC,WAAW,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,GAAG;gBAChF,OAAO;oBACL,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;gBAC1C;YACF;YACA,IAAI,aAAa,MAAM;gBACrB,MAAM,IAAI,MAAM,yCAAyC,OAAO,OAAO,IAAI,CAAC,SAAS;YACvF;YACA,OAAO;QACT;QAEA,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YAC9D,IAAI,OAAO,GAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,MAAM,2CAA2C,IAAI,CAAC,SAAS,CAAC;YAC5E;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,YAAY;YAC9C,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACjD,OAAO;QACT;QAEA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YAC7D,IAAI,OAAO,GAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,MAAM,2CAA2C,IAAI,CAAC,SAAS,CAAC;YAC5E;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC1C,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,YAAY;YAC9C,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACjD,OAAO;QACT;QAEA,QAAQ,SAAS,CAAC,MAAM,GAAG;YACzB,IAAI,GAAG;YACP,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,MAAM,qCAAqC,IAAI,CAAC,SAAS;YACrE;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAAC;gBAAG,IAAI,IAAI;aAAE,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI;YACzE,OAAO,IAAI,CAAC,MAAM;QACpB;QAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACtD,IAAI,OAAO;YACX,IAAI,QAAQ,MAAM;gBAChB,OAAO,SAAS;YAClB;YACA,cAAc,CAAC,aAAa,CAAC,CAAC;YAC9B,aAAa,SAAS;YACtB,IAAI,CAAC,SAAS,aAAa;gBACzB,OAAO;oBAAC;oBAAY;iBAAK,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,aAAa,IAAI,CAAC,EAAE;YACjE;YACA,QAAQ,IAAI,WAAW,IAAI,EAAE,MAAM;YACnC,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,CAAC;YACb;YACA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO;QACT;QAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK;YACrC,IAAI;YACJ,QAAQ,IAAI,QAAQ,IAAI,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK;YACtC,IAAI;YACJ,QAAQ,IAAI,SAAS,IAAI,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAS,KAAK;YACxC,IAAI;YACJ,QAAQ,IAAI,WAAW,IAAI,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,aAAa,GAAG,SAAS,KAAK;YAC9C,IAAI,OAAO,GAAG;YACd,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAC5B,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACjD,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,KAAK;YAC7C,IAAI,OAAO,GAAG;YACd,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC1C,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAC5B,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACjD,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YACpC,IAAI;YACJ,QAAQ,IAAI,OAAO,IAAI,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,KAAK,GAAG;YACxB,IAAI;YACJ,QAAQ,IAAI,SAAS,IAAI;YACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO;QACT;QAEA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAS,MAAM,EAAE,KAAK;YACpD,IAAI,WAAW,UAAU,aAAa,GAAG;YACzC,IAAI,UAAU,MAAM;gBAClB,SAAS,SAAS;YACpB;YACA,IAAI,SAAS,MAAM;gBACjB,QAAQ,SAAS;YACnB;YACA,IAAI,MAAM,OAAO,CAAC,SAAS;gBACzB,IAAK,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;oBAC7C,YAAY,MAAM,CAAC,EAAE;oBACrB,IAAI,CAAC,WAAW,CAAC;gBACnB;YACF,OAAO,IAAI,SAAS,SAAS;gBAC3B,IAAK,aAAa,OAAQ;oBACxB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,YAAY;oBACtC,WAAW,MAAM,CAAC,UAAU;oBAC5B,IAAI,CAAC,WAAW,CAAC,WAAW;gBAC9B;YACF,OAAO;gBACL,IAAI,WAAW,QAAQ;oBACrB,QAAQ,MAAM,KAAK;gBACrB;gBACA,cAAc,IAAI,yBAAyB,IAAI,EAAE,QAAQ;gBACzD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACrB;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAS,MAAM,EAAE,KAAK;YAC1D,IAAI,OAAO,GAAG;YACd,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;YACxC,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACjD,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAS,MAAM,EAAE,KAAK;YACzD,IAAI,OAAO,GAAG;YACd,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC1C,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;YACxC,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACjD,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU;YACpE,IAAI,KAAK;YACT,MAAM,IAAI,CAAC,QAAQ;YACnB,SAAS,IAAI,eAAe,KAAK,SAAS,UAAU;YACpD,IAAI,IAAI,QAAQ,CAAC,EAAE,YAAY,gBAAgB;gBAC7C,IAAI,QAAQ,CAAC,EAAE,GAAG;YACpB,OAAO;gBACL,IAAI,QAAQ,CAAC,OAAO,CAAC;YACvB;YACA,OAAO,IAAI,IAAI,MAAM;QACvB;QAEA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAS,KAAK,EAAE,KAAK;YAC/C,IAAI,OAAO,KAAK,SAAS,GAAG,GAAG,GAAG,KAAK,MAAM,MAAM;YACnD,MAAM,IAAI,CAAC,QAAQ;YACnB,UAAU,IAAI,WAAW,KAAK,OAAO;YACrC,OAAO,IAAI,QAAQ;YACnB,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAI,EAAE,EAAG;gBACnD,QAAQ,IAAI,CAAC,EAAE;gBACf,IAAI,iBAAiB,YAAY;oBAC/B,IAAI,QAAQ,CAAC,EAAE,GAAG;oBAClB,OAAO;gBACT;YACF;YACA,OAAO,IAAI,QAAQ;YACnB,IAAK,IAAI,IAAI,GAAG,OAAO,KAAK,MAAM,EAAE,IAAI,MAAM,IAAI,EAAE,EAAG;gBACrD,QAAQ,IAAI,CAAC,EAAE;gBACf,IAAI,MAAM,MAAM,EAAE;oBAChB,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG;oBAC1B,OAAO;gBACT;YACF;YACA,IAAI,QAAQ,CAAC,IAAI,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,SAAS,CAAC,EAAE,GAAG;YACrB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,IAAI,CAAC,MAAM;QACpB;QAEA,QAAQ,SAAS,CAAC,IAAI,GAAG;YACvB,IAAI;YACJ,OAAO,IAAI;YACX,MAAO,KAAM;gBACX,IAAI,KAAK,UAAU,EAAE;oBACnB,OAAO,KAAK,UAAU;gBACxB,OAAO,IAAI,KAAK,MAAM,EAAE;oBACtB,OAAO;gBACT,OAAO;oBACL,OAAO,KAAK,MAAM;gBACpB;YACF;QACF;QAEA,QAAQ,SAAS,CAAC,QAAQ,GAAG;YAC3B,IAAI;YACJ,OAAO,IAAI;YACX,MAAO,KAAM;gBACX,IAAI,KAAK,UAAU,EAAE;oBACnB,OAAO;gBACT,OAAO;oBACL,OAAO,KAAK,MAAM;gBACpB;YACF;QACF;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,OAAO;YACtC,OAAO,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QAC7B;QAEA,QAAQ,SAAS,CAAC,IAAI,GAAG;YACvB,IAAI;YACJ,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,MAAO,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,CAAE;gBACnD,IAAI,IAAI;YACV;YACA,IAAI,IAAI,GAAG;gBACT,MAAM,IAAI,MAAM,gCAAgC,IAAI,CAAC,SAAS;YAChE;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;QACpC;QAEA,QAAQ,SAAS,CAAC,IAAI,GAAG;YACvB,IAAI;YACJ,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;YACrC,MAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,CAAE;gBACjF,IAAI,IAAI;YACV;YACA,IAAI,MAAM,CAAC,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACrD,MAAM,IAAI,MAAM,+BAA+B,IAAI,CAAC,SAAS;YAC/D;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;QACpC;QAEA,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAS,GAAG;YAC7C,IAAI;YACJ,aAAa,IAAI,IAAI,GAAG,KAAK;YAC7B,WAAW,MAAM,GAAG,IAAI;YACxB,WAAW,MAAM,GAAG;YACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI;QACb;QAEA,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI;YACzC,IAAI,MAAM;YACV,OAAO,QAAQ,IAAI,CAAC,IAAI;YACxB,IAAI,AAAC,QAAQ,QAAS,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG;gBAC1E,OAAO;YACT,OAAO,IAAI,QAAQ,MAAM;gBACvB,OAAO,cAAc,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;YAC1C,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG;gBAC/D,OAAO,YAAY,OAAO;YAC5B,OAAO;gBACL,OAAO,YAAY,OAAO,iBAAiB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;YAChE;QACF;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACrD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,YAAY;QACxC;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACrD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;QACrC;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YACpC,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YACpC,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YACpC,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,MAAM,EAAE,KAAK;YAC5C,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;QAClC;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG;YACtB,OAAO,IAAI,CAAC,QAAQ;QACtB;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU;YAC5D,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,UAAU;QAC7C;QAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK,EAAE,KAAK;YAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;QAC7B;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACnD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,YAAY;QACxC;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACnD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;QACrC;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YAClC,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YAClC,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YAClC,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YAClC,OAAO,IAAI,CAAC,GAAG,CAAC;QAClB;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAS,MAAM,EAAE,KAAK;YAC1C,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;QAClC;QAEA,QAAQ,SAAS,CAAC,CAAC,GAAG;YACpB,OAAO,IAAI,CAAC,EAAE;QAChB;QAEA,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAS,GAAG;YAC/C,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B;QAEA,OAAO;IAET;AAEF,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLStringifier.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringifier,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLStringifier = (function() {\n    function XMLStringifier(options) {\n      this.assertLegalChar = bind(this.assertLegalChar, this);\n      var key, ref, value;\n      options || (options = {});\n      this.noDoubleEncoding = options.noDoubleEncoding;\n      ref = options.stringify || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n    }\n\n    XMLStringifier.prototype.eleName = function(val) {\n      val = '' + val || '';\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.eleText = function(val) {\n      val = '' + val || '';\n      return this.assertLegalChar(this.elEscape(val));\n    };\n\n    XMLStringifier.prototype.cdata = function(val) {\n      val = '' + val || '';\n      val = val.replace(']]>', ']]]]><![CDATA[>');\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.comment = function(val) {\n      val = '' + val || '';\n      if (val.match(/--/)) {\n        throw new Error(\"Comment text cannot contain double-hypen: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.raw = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.attName = function(val) {\n      return val = '' + val || '';\n    };\n\n    XMLStringifier.prototype.attValue = function(val) {\n      val = '' + val || '';\n      return this.attEscape(val);\n    };\n\n    XMLStringifier.prototype.insTarget = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.insValue = function(val) {\n      val = '' + val || '';\n      if (val.match(/\\?>/)) {\n        throw new Error(\"Invalid processing instruction value: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlVersion = function(val) {\n      val = '' + val || '';\n      if (!val.match(/1\\.[0-9]+/)) {\n        throw new Error(\"Invalid version number: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlEncoding = function(val) {\n      val = '' + val || '';\n      if (!val.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/)) {\n        throw new Error(\"Invalid encoding: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlStandalone = function(val) {\n      if (val) {\n        return \"yes\";\n      } else {\n        return \"no\";\n      }\n    };\n\n    XMLStringifier.prototype.dtdPubID = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdSysID = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdElementValue = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdAttType = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdAttDefault = function(val) {\n      if (val != null) {\n        return '' + val || '';\n      } else {\n        return val;\n      }\n    };\n\n    XMLStringifier.prototype.dtdEntityValue = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdNData = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.convertAttKey = '@';\n\n    XMLStringifier.prototype.convertPIKey = '?';\n\n    XMLStringifier.prototype.convertTextKey = '#text';\n\n    XMLStringifier.prototype.convertCDataKey = '#cdata';\n\n    XMLStringifier.prototype.convertCommentKey = '#comment';\n\n    XMLStringifier.prototype.convertRawKey = '#raw';\n\n    XMLStringifier.prototype.assertLegalChar = function(str) {\n      var res;\n      res = str.match(/[\\0\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/);\n      if (res) {\n        throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.elEscape = function(str) {\n      var ampregex;\n      ampregex = this.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\r/g, '&#xD;');\n    };\n\n    XMLStringifier.prototype.attEscape = function(str) {\n      var ampregex;\n      ampregex = this.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/\"/g, '&quot;').replace(/\\t/g, '&#x9;').replace(/\\n/g, '&#xA;').replace(/\\r/g, '&#xD;');\n    };\n\n    return XMLStringifier;\n\n  })();\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,gBACF,OAAO,SAAS,EAAE,EAAE,EAAE;QAAG,OAAO;YAAY,OAAO,GAAG,KAAK,CAAC,IAAI;QAAY;IAAG,GAC/E,UAAU,CAAC,EAAE,cAAc;IAE7B,OAAO,OAAO,GAAG,iBAAiB,AAAC;QACjC,SAAS,eAAe,OAAO;YAC7B,IAAI,CAAC,eAAe,GAAG,KAAK,IAAI,CAAC,eAAe,EAAE,IAAI;YACtD,IAAI,KAAK,KAAK;YACd,WAAW,CAAC,UAAU,CAAC,CAAC;YACxB,IAAI,CAAC,gBAAgB,GAAG,QAAQ,gBAAgB;YAChD,MAAM,QAAQ,SAAS,IAAI,CAAC;YAC5B,IAAK,OAAO,IAAK;gBACf,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,MAAM;gBAC7B,QAAQ,GAAG,CAAC,IAAI;gBAChB,IAAI,CAAC,IAAI,GAAG;YACd;QACF;QAEA,eAAe,SAAS,CAAC,OAAO,GAAG,SAAS,GAAG;YAC7C,MAAM,KAAK,OAAO;YAClB,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B;QAEA,eAAe,SAAS,CAAC,OAAO,GAAG,SAAS,GAAG;YAC7C,MAAM,KAAK,OAAO;YAClB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC5C;QAEA,eAAe,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;YAC3C,MAAM,KAAK,OAAO;YAClB,MAAM,IAAI,OAAO,CAAC,OAAO;YACzB,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B;QAEA,eAAe,SAAS,CAAC,OAAO,GAAG,SAAS,GAAG;YAC7C,MAAM,KAAK,OAAO;YAClB,IAAI,IAAI,KAAK,CAAC,OAAO;gBACnB,MAAM,IAAI,MAAM,+CAA+C;YACjE;YACA,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B;QAEA,eAAe,SAAS,CAAC,GAAG,GAAG,SAAS,GAAG;YACzC,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,OAAO,GAAG,SAAS,GAAG;YAC7C,OAAO,MAAM,KAAK,OAAO;QAC3B;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC9C,MAAM,KAAK,OAAO;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB;QAEA,eAAe,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG;YAC/C,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC9C,MAAM,KAAK,OAAO;YAClB,IAAI,IAAI,KAAK,CAAC,QAAQ;gBACpB,MAAM,IAAI,MAAM,2CAA2C;YAC7D;YACA,OAAO;QACT;QAEA,eAAe,SAAS,CAAC,UAAU,GAAG,SAAS,GAAG;YAChD,MAAM,KAAK,OAAO;YAClB,IAAI,CAAC,IAAI,KAAK,CAAC,cAAc;gBAC3B,MAAM,IAAI,MAAM,6BAA6B;YAC/C;YACA,OAAO;QACT;QAEA,eAAe,SAAS,CAAC,WAAW,GAAG,SAAS,GAAG;YACjD,MAAM,KAAK,OAAO;YAClB,IAAI,CAAC,IAAI,KAAK,CAAC,kCAAkC;gBAC/C,MAAM,IAAI,MAAM,uBAAuB;YACzC;YACA,OAAO;QACT;QAEA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAS,GAAG;YACnD,IAAI,KAAK;gBACP,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC9C,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC9C,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,eAAe,GAAG,SAAS,GAAG;YACrD,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,UAAU,GAAG,SAAS,GAAG;YAChD,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAS,GAAG;YACnD,IAAI,OAAO,MAAM;gBACf,OAAO,KAAK,OAAO;YACrB,OAAO;gBACL,OAAO;YACT;QACF;QAEA,eAAe,SAAS,CAAC,cAAc,GAAG,SAAS,GAAG;YACpD,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC9C,OAAO,KAAK,OAAO;QACrB;QAEA,eAAe,SAAS,CAAC,aAAa,GAAG;QAEzC,eAAe,SAAS,CAAC,YAAY,GAAG;QAExC,eAAe,SAAS,CAAC,cAAc,GAAG;QAE1C,eAAe,SAAS,CAAC,eAAe,GAAG;QAE3C,eAAe,SAAS,CAAC,iBAAiB,GAAG;QAE7C,eAAe,SAAS,CAAC,aAAa,GAAG;QAEzC,eAAe,SAAS,CAAC,eAAe,GAAG,SAAS,GAAG;YACrD,IAAI;YACJ,MAAM,IAAI,KAAK,CAAC;YAChB,IAAI,KAAK;gBACP,MAAM,IAAI,MAAM,kCAAkC,MAAM,eAAe,IAAI,KAAK;YAClF;YACA,OAAO;QACT;QAEA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC9C,IAAI;YACJ,WAAW,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;YACnD,OAAO,IAAI,OAAO,CAAC,UAAU,SAAS,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,OAAO;QACnG;QAEA,eAAe,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG;YAC/C,IAAI;YACJ,WAAW,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;YACnD,OAAO,IAAI,OAAO,CAAC,UAAU,SAAS,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM,UAAU,OAAO,CAAC,OAAO,SAAS,OAAO,CAAC,OAAO,SAAS,OAAO,CAAC,OAAO;QACrJ;QAEA,OAAO;IAET;AAEF,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLWriterBase.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLWriterBase,\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLWriterBase = (function() {\n    function XMLWriterBase(options) {\n      var key, ref, ref1, ref2, ref3, ref4, ref5, ref6, value;\n      options || (options = {});\n      this.pretty = options.pretty || false;\n      this.allowEmpty = (ref = options.allowEmpty) != null ? ref : false;\n      if (this.pretty) {\n        this.indent = (ref1 = options.indent) != null ? ref1 : '  ';\n        this.newline = (ref2 = options.newline) != null ? ref2 : '\\n';\n        this.offset = (ref3 = options.offset) != null ? ref3 : 0;\n        this.dontprettytextnodes = (ref4 = options.dontprettytextnodes) != null ? ref4 : 0;\n      } else {\n        this.indent = '';\n        this.newline = '';\n        this.offset = 0;\n        this.dontprettytextnodes = 0;\n      }\n      this.spacebeforeslash = (ref5 = options.spacebeforeslash) != null ? ref5 : '';\n      if (this.spacebeforeslash === true) {\n        this.spacebeforeslash = ' ';\n      }\n      this.newlinedefault = this.newline;\n      this.prettydefault = this.pretty;\n      ref6 = options.writer || {};\n      for (key in ref6) {\n        if (!hasProp.call(ref6, key)) continue;\n        value = ref6[key];\n        this[key] = value;\n      }\n    }\n\n    XMLWriterBase.prototype.set = function(options) {\n      var key, ref, value;\n      options || (options = {});\n      if (\"pretty\" in options) {\n        this.pretty = options.pretty;\n      }\n      if (\"allowEmpty\" in options) {\n        this.allowEmpty = options.allowEmpty;\n      }\n      if (this.pretty) {\n        this.indent = \"indent\" in options ? options.indent : '  ';\n        this.newline = \"newline\" in options ? options.newline : '\\n';\n        this.offset = \"offset\" in options ? options.offset : 0;\n        this.dontprettytextnodes = \"dontprettytextnodes\" in options ? options.dontprettytextnodes : 0;\n      } else {\n        this.indent = '';\n        this.newline = '';\n        this.offset = 0;\n        this.dontprettytextnodes = 0;\n      }\n      this.spacebeforeslash = \"spacebeforeslash\" in options ? options.spacebeforeslash : '';\n      if (this.spacebeforeslash === true) {\n        this.spacebeforeslash = ' ';\n      }\n      this.newlinedefault = this.newline;\n      this.prettydefault = this.pretty;\n      ref = options.writer || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n      return this;\n    };\n\n    XMLWriterBase.prototype.space = function(level) {\n      var indent;\n      if (this.pretty) {\n        indent = (level || 0) + this.offset + 1;\n        if (indent > 0) {\n          return new Array(indent).join(this.indent);\n        } else {\n          return '';\n        }\n      } else {\n        return '';\n      }\n    };\n\n    return XMLWriterBase;\n\n  })();\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,eACF,UAAU,CAAC,EAAE,cAAc;IAE7B,OAAO,OAAO,GAAG,gBAAgB,AAAC;QAChC,SAAS,cAAc,OAAO;YAC5B,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;YAClD,WAAW,CAAC,UAAU,CAAC,CAAC;YACxB,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM,IAAI;YAChC,IAAI,CAAC,UAAU,GAAG,CAAC,MAAM,QAAQ,UAAU,KAAK,OAAO,MAAM;YAC7D,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,QAAQ,MAAM,KAAK,OAAO,OAAO;gBACvD,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO,QAAQ,OAAO,KAAK,OAAO,OAAO;gBACzD,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,QAAQ,MAAM,KAAK,OAAO,OAAO;gBACvD,IAAI,CAAC,mBAAmB,GAAG,CAAC,OAAO,QAAQ,mBAAmB,KAAK,OAAO,OAAO;YACnF,OAAO;gBACL,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,mBAAmB,GAAG;YAC7B;YACA,IAAI,CAAC,gBAAgB,GAAG,CAAC,OAAO,QAAQ,gBAAgB,KAAK,OAAO,OAAO;YAC3E,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM;gBAClC,IAAI,CAAC,gBAAgB,GAAG;YAC1B;YACA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM;YAChC,OAAO,QAAQ,MAAM,IAAI,CAAC;YAC1B,IAAK,OAAO,KAAM;gBAChB,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,MAAM;gBAC9B,QAAQ,IAAI,CAAC,IAAI;gBACjB,IAAI,CAAC,IAAI,GAAG;YACd;QACF;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,OAAO;YAC5C,IAAI,KAAK,KAAK;YACd,WAAW,CAAC,UAAU,CAAC,CAAC;YACxB,IAAI,YAAY,SAAS;gBACvB,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;YAC9B;YACA,IAAI,gBAAgB,SAAS;gBAC3B,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;YACtC;YACA,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,GAAG,YAAY,UAAU,QAAQ,MAAM,GAAG;gBACrD,IAAI,CAAC,OAAO,GAAG,aAAa,UAAU,QAAQ,OAAO,GAAG;gBACxD,IAAI,CAAC,MAAM,GAAG,YAAY,UAAU,QAAQ,MAAM,GAAG;gBACrD,IAAI,CAAC,mBAAmB,GAAG,yBAAyB,UAAU,QAAQ,mBAAmB,GAAG;YAC9F,OAAO;gBACL,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,mBAAmB,GAAG;YAC7B;YACA,IAAI,CAAC,gBAAgB,GAAG,sBAAsB,UAAU,QAAQ,gBAAgB,GAAG;YACnF,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM;gBAClC,IAAI,CAAC,gBAAgB,GAAG;YAC1B;YACA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM;YAChC,MAAM,QAAQ,MAAM,IAAI,CAAC;YACzB,IAAK,OAAO,IAAK;gBACf,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,MAAM;gBAC7B,QAAQ,GAAG,CAAC,IAAI;gBAChB,IAAI,CAAC,IAAI,GAAG;YACd;YACA,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK;YAC5C,IAAI;YACJ,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,SAAS,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG;gBACtC,IAAI,SAAS,GAAG;oBACd,OAAO,IAAI,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM;gBAC3C,OAAO;oBACL,OAAO;gBACT;YACF,OAAO;gBACL,OAAO;YACT;QACF;QAEA,OAAO;IAET;AAEF,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLStringWriter.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLText, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLDeclaration = require('./XMLDeclaration');\n\n  XMLDocType = require('./XMLDocType');\n\n  XMLCData = require('./XMLCData');\n\n  XMLComment = require('./XMLComment');\n\n  XMLElement = require('./XMLElement');\n\n  XMLRaw = require('./XMLRaw');\n\n  XMLText = require('./XMLText');\n\n  XMLProcessingInstruction = require('./XMLProcessingInstruction');\n\n  XMLDummy = require('./XMLDummy');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  XMLWriterBase = require('./XMLWriterBase');\n\n  module.exports = XMLStringWriter = (function(superClass) {\n    extend(XMLStringWriter, superClass);\n\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStringWriter.prototype.document = function(doc) {\n      var child, i, len, r, ref;\n      this.textispresent = false;\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        if (child instanceof XMLDummy) {\n          continue;\n        }\n        r += (function() {\n          switch (false) {\n            case !(child instanceof XMLDeclaration):\n              return this.declaration(child);\n            case !(child instanceof XMLDocType):\n              return this.docType(child);\n            case !(child instanceof XMLComment):\n              return this.comment(child);\n            case !(child instanceof XMLProcessingInstruction):\n              return this.processingInstruction(child);\n            default:\n              return this.element(child, 0);\n          }\n        }).call(this);\n      }\n      if (this.pretty && r.slice(-this.newline.length) === this.newline) {\n        r = r.slice(0, -this.newline.length);\n      }\n      return r;\n    };\n\n    XMLStringWriter.prototype.attribute = function(att) {\n      return ' ' + att.name + '=\"' + att.value + '\"';\n    };\n\n    XMLStringWriter.prototype.cdata = function(node, level) {\n      return this.space(level) + '<![CDATA[' + node.text + ']]>' + this.newline;\n    };\n\n    XMLStringWriter.prototype.comment = function(node, level) {\n      return this.space(level) + '<!-- ' + node.text + ' -->' + this.newline;\n    };\n\n    XMLStringWriter.prototype.declaration = function(node, level) {\n      var r;\n      r = this.space(level);\n      r += '<?xml version=\"' + node.version + '\"';\n      if (node.encoding != null) {\n        r += ' encoding=\"' + node.encoding + '\"';\n      }\n      if (node.standalone != null) {\n        r += ' standalone=\"' + node.standalone + '\"';\n      }\n      r += this.spacebeforeslash + '?>';\n      r += this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.docType = function(node, level) {\n      var child, i, len, r, ref;\n      level || (level = 0);\n      r = this.space(level);\n      r += '<!DOCTYPE ' + node.root().name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      if (node.children.length > 0) {\n        r += ' [';\n        r += this.newline;\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          r += (function() {\n            switch (false) {\n              case !(child instanceof XMLDTDAttList):\n                return this.dtdAttList(child, level + 1);\n              case !(child instanceof XMLDTDElement):\n                return this.dtdElement(child, level + 1);\n              case !(child instanceof XMLDTDEntity):\n                return this.dtdEntity(child, level + 1);\n              case !(child instanceof XMLDTDNotation):\n                return this.dtdNotation(child, level + 1);\n              case !(child instanceof XMLCData):\n                return this.cdata(child, level + 1);\n              case !(child instanceof XMLComment):\n                return this.comment(child, level + 1);\n              case !(child instanceof XMLProcessingInstruction):\n                return this.processingInstruction(child, level + 1);\n              default:\n                throw new Error(\"Unknown DTD node type: \" + child.constructor.name);\n            }\n          }).call(this);\n        }\n        r += ']';\n      }\n      r += this.spacebeforeslash + '>';\n      r += this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.element = function(node, level) {\n      var att, child, i, j, len, len1, name, r, ref, ref1, ref2, space, textispresentwasset;\n      level || (level = 0);\n      textispresentwasset = false;\n      if (this.textispresent) {\n        this.newline = '';\n        this.pretty = false;\n      } else {\n        this.newline = this.newlinedefault;\n        this.pretty = this.prettydefault;\n      }\n      space = this.space(level);\n      r = '';\n      r += space + '<' + node.name;\n      ref = node.attributes;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        r += this.attribute(att);\n      }\n      if (node.children.length === 0 || node.children.every(function(e) {\n        return e.value === '';\n      })) {\n        if (this.allowEmpty) {\n          r += '></' + node.name + '>' + this.newline;\n        } else {\n          r += this.spacebeforeslash + '/>' + this.newline;\n        }\n      } else if (this.pretty && node.children.length === 1 && (node.children[0].value != null)) {\n        r += '>';\n        r += node.children[0].value;\n        r += '</' + node.name + '>' + this.newline;\n      } else {\n        if (this.dontprettytextnodes) {\n          ref1 = node.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            if (child.value != null) {\n              this.textispresent++;\n              textispresentwasset = true;\n              break;\n            }\n          }\n        }\n        if (this.textispresent) {\n          this.newline = '';\n          this.pretty = false;\n          space = this.space(level);\n        }\n        r += '>' + this.newline;\n        ref2 = node.children;\n        for (j = 0, len1 = ref2.length; j < len1; j++) {\n          child = ref2[j];\n          r += (function() {\n            switch (false) {\n              case !(child instanceof XMLCData):\n                return this.cdata(child, level + 1);\n              case !(child instanceof XMLComment):\n                return this.comment(child, level + 1);\n              case !(child instanceof XMLElement):\n                return this.element(child, level + 1);\n              case !(child instanceof XMLRaw):\n                return this.raw(child, level + 1);\n              case !(child instanceof XMLText):\n                return this.text(child, level + 1);\n              case !(child instanceof XMLProcessingInstruction):\n                return this.processingInstruction(child, level + 1);\n              case !(child instanceof XMLDummy):\n                return '';\n              default:\n                throw new Error(\"Unknown XML node type: \" + child.constructor.name);\n            }\n          }).call(this);\n        }\n        if (textispresentwasset) {\n          this.textispresent--;\n        }\n        if (!this.textispresent) {\n          this.newline = this.newlinedefault;\n          this.pretty = this.prettydefault;\n        }\n        r += space + '</' + node.name + '>' + this.newline;\n      }\n      return r;\n    };\n\n    XMLStringWriter.prototype.processingInstruction = function(node, level) {\n      var r;\n      r = this.space(level) + '<?' + node.target;\n      if (node.value) {\n        r += ' ' + node.value;\n      }\n      r += this.spacebeforeslash + '?>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.raw = function(node, level) {\n      return this.space(level) + node.value + this.newline;\n    };\n\n    XMLStringWriter.prototype.text = function(node, level) {\n      return this.space(level) + node.value + this.newline;\n    };\n\n    XMLStringWriter.prototype.dtdAttList = function(node, level) {\n      var r;\n      r = this.space(level) + '<!ATTLIST ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType;\n      if (node.defaultValueType !== '#DEFAULT') {\n        r += ' ' + node.defaultValueType;\n      }\n      if (node.defaultValue) {\n        r += ' \"' + node.defaultValue + '\"';\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.dtdElement = function(node, level) {\n      return this.space(level) + '<!ELEMENT ' + node.name + ' ' + node.value + this.spacebeforeslash + '>' + this.newline;\n    };\n\n    XMLStringWriter.prototype.dtdEntity = function(node, level) {\n      var r;\n      r = this.space(level) + '<!ENTITY';\n      if (node.pe) {\n        r += ' %';\n      }\n      r += ' ' + node.name;\n      if (node.value) {\n        r += ' \"' + node.value + '\"';\n      } else {\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        if (node.nData) {\n          r += ' NDATA ' + node.nData;\n        }\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.dtdNotation = function(node, level) {\n      var r;\n      r = this.space(level) + '<!NOTATION ' + node.name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.pubID) {\n        r += ' PUBLIC \"' + node.pubID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.openNode = function(node, level) {\n      var att, name, r, ref;\n      level || (level = 0);\n      if (node instanceof XMLElement) {\n        r = this.space(level) + '<' + node.name;\n        ref = node.attributes;\n        for (name in ref) {\n          if (!hasProp.call(ref, name)) continue;\n          att = ref[name];\n          r += this.attribute(att);\n        }\n        r += (node.children ? '>' : '/>') + this.newline;\n        return r;\n      } else {\n        r = this.space(level) + '<!DOCTYPE ' + node.rootNodeName;\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        r += (node.children ? ' [' : '>') + this.newline;\n        return r;\n      }\n    };\n\n    XMLStringWriter.prototype.closeNode = function(node, level) {\n      level || (level = 0);\n      switch (false) {\n        case !(node instanceof XMLElement):\n          return this.space(level) + '</' + node.name + '>' + this.newline;\n        case !(node instanceof XMLDocType):\n          return this.space(level) + ']>' + this.newline;\n      }\n    };\n\n    return XMLStringWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,UAAU,YAAY,eAAe,eAAe,cAAc,gBAAgB,gBAAgB,YAAY,UAAU,YAAY,0BAA0B,QAAQ,iBAAiB,SAAS,eAClM,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA,OAAO,OAAO,GAAG,kBAAkB,AAAC,SAAS,UAAU;QACrD,OAAO,iBAAiB;QAExB,SAAS,gBAAgB,OAAO;YAC9B,gBAAgB,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;QACnD;QAEA,gBAAgB,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC/C,IAAI,OAAO,GAAG,KAAK,GAAG;YACtB,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI;YACJ,MAAM,IAAI,QAAQ;YAClB,IAAK,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;gBAC1C,QAAQ,GAAG,CAAC,EAAE;gBACd,IAAI,iBAAiB,UAAU;oBAC7B;gBACF;gBACA,KAAK,CAAC;oBACJ,OAAQ;wBACN,KAAK,CAAC,CAAC,iBAAiB,cAAc;4BACpC,OAAO,IAAI,CAAC,WAAW,CAAC;wBAC1B,KAAK,CAAC,CAAC,iBAAiB,UAAU;4BAChC,OAAO,IAAI,CAAC,OAAO,CAAC;wBACtB,KAAK,CAAC,CAAC,iBAAiB,UAAU;4BAChC,OAAO,IAAI,CAAC,OAAO,CAAC;wBACtB,KAAK,CAAC,CAAC,iBAAiB,wBAAwB;4BAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;wBACpC;4BACE,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;oBAC/B;gBACF,CAAC,EAAE,IAAI,CAAC,IAAI;YACd;YACA,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACjE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;YACrC;YACA,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG;YAChD,OAAO,MAAM,IAAI,IAAI,GAAG,OAAO,IAAI,KAAK,GAAG;QAC7C;QAEA,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAS,IAAI,EAAE,KAAK;YACpD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,cAAc,KAAK,IAAI,GAAG,QAAQ,IAAI,CAAC,OAAO;QAC3E;QAEA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACtD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,UAAU,KAAK,IAAI,GAAG,SAAS,IAAI,CAAC,OAAO;QACxE;QAEA,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,KAAK;YAC1D,IAAI;YACJ,IAAI,IAAI,CAAC,KAAK,CAAC;YACf,KAAK,oBAAoB,KAAK,OAAO,GAAG;YACxC,IAAI,KAAK,QAAQ,IAAI,MAAM;gBACzB,KAAK,gBAAgB,KAAK,QAAQ,GAAG;YACvC;YACA,IAAI,KAAK,UAAU,IAAI,MAAM;gBAC3B,KAAK,kBAAkB,KAAK,UAAU,GAAG;YAC3C;YACA,KAAK,IAAI,CAAC,gBAAgB,GAAG;YAC7B,KAAK,IAAI,CAAC,OAAO;YACjB,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACtD,IAAI,OAAO,GAAG,KAAK,GAAG;YACtB,SAAS,CAAC,QAAQ,CAAC;YACnB,IAAI,IAAI,CAAC,KAAK,CAAC;YACf,KAAK,eAAe,KAAK,IAAI,GAAG,IAAI;YACpC,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;gBAC5B,KAAK,cAAc,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,GAAG;YACvD,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,KAAK,cAAc,KAAK,KAAK,GAAG;YAClC;YACA,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC5B,KAAK;gBACL,KAAK,IAAI,CAAC,OAAO;gBACjB,MAAM,KAAK,QAAQ;gBACnB,IAAK,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;oBAC1C,QAAQ,GAAG,CAAC,EAAE;oBACd,KAAK,CAAC;wBACJ,OAAQ;4BACN,KAAK,CAAC,CAAC,iBAAiB,aAAa;gCACnC,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;4BACxC,KAAK,CAAC,CAAC,iBAAiB,aAAa;gCACnC,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;4BACxC,KAAK,CAAC,CAAC,iBAAiB,YAAY;gCAClC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,QAAQ;4BACvC,KAAK,CAAC,CAAC,iBAAiB,cAAc;gCACpC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,QAAQ;4BACzC,KAAK,CAAC,CAAC,iBAAiB,QAAQ;gCAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ;4BACnC,KAAK,CAAC,CAAC,iBAAiB,UAAU;gCAChC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ;4BACrC,KAAK,CAAC,CAAC,iBAAiB,wBAAwB;gCAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,QAAQ;4BACnD;gCACE,MAAM,IAAI,MAAM,4BAA4B,MAAM,WAAW,CAAC,IAAI;wBACtE;oBACF,CAAC,EAAE,IAAI,CAAC,IAAI;gBACd;gBACA,KAAK;YACP;YACA,KAAK,IAAI,CAAC,gBAAgB,GAAG;YAC7B,KAAK,IAAI,CAAC,OAAO;YACjB,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACtD,IAAI,KAAK,OAAO,GAAG,GAAG,KAAK,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,OAAO;YAClE,SAAS,CAAC,QAAQ,CAAC;YACnB,sBAAsB;YACtB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,MAAM,GAAG;YAChB,OAAO;gBACL,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc;gBAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa;YAClC;YACA,QAAQ,IAAI,CAAC,KAAK,CAAC;YACnB,IAAI;YACJ,KAAK,QAAQ,MAAM,KAAK,IAAI;YAC5B,MAAM,KAAK,UAAU;YACrB,IAAK,QAAQ,IAAK;gBAChB,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,OAAO;gBAC9B,MAAM,GAAG,CAAC,KAAK;gBACf,KAAK,IAAI,CAAC,SAAS,CAAC;YACtB;YACA,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC9D,OAAO,EAAE,KAAK,KAAK;YACrB,IAAI;gBACF,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB,KAAK,QAAQ,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO;gBAC7C,OAAO;oBACL,KAAK,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,OAAO;gBAClD;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAM,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,MAAO;gBACxF,KAAK;gBACL,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK;gBAC3B,KAAK,OAAO,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO;YAC5C,OAAO;gBACL,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBAC5B,OAAO,KAAK,QAAQ;oBACpB,IAAK,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;wBAC3C,QAAQ,IAAI,CAAC,EAAE;wBACf,IAAI,MAAM,KAAK,IAAI,MAAM;4BACvB,IAAI,CAAC,aAAa;4BAClB,sBAAsB;4BACtB;wBACF;oBACF;gBACF;gBACA,IAAI,IAAI,CAAC,aAAa,EAAE;oBACtB,IAAI,CAAC,OAAO,GAAG;oBACf,IAAI,CAAC,MAAM,GAAG;oBACd,QAAQ,IAAI,CAAC,KAAK,CAAC;gBACrB;gBACA,KAAK,MAAM,IAAI,CAAC,OAAO;gBACvB,OAAO,KAAK,QAAQ;gBACpB,IAAK,IAAI,GAAG,OAAO,KAAK,MAAM,EAAE,IAAI,MAAM,IAAK;oBAC7C,QAAQ,IAAI,CAAC,EAAE;oBACf,KAAK,CAAC;wBACJ,OAAQ;4BACN,KAAK,CAAC,CAAC,iBAAiB,QAAQ;gCAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ;4BACnC,KAAK,CAAC,CAAC,iBAAiB,UAAU;gCAChC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ;4BACrC,KAAK,CAAC,CAAC,iBAAiB,UAAU;gCAChC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ;4BACrC,KAAK,CAAC,CAAC,iBAAiB,MAAM;gCAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,QAAQ;4BACjC,KAAK,CAAC,CAAC,iBAAiB,OAAO;gCAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ;4BAClC,KAAK,CAAC,CAAC,iBAAiB,wBAAwB;gCAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,QAAQ;4BACnD,KAAK,CAAC,CAAC,iBAAiB,QAAQ;gCAC9B,OAAO;4BACT;gCACE,MAAM,IAAI,MAAM,4BAA4B,MAAM,WAAW,CAAC,IAAI;wBACtE;oBACF,CAAC,EAAE,IAAI,CAAC,IAAI;gBACd;gBACA,IAAI,qBAAqB;oBACvB,IAAI,CAAC,aAAa;gBACpB;gBACA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;oBACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc;oBAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa;gBAClC;gBACA,KAAK,QAAQ,OAAO,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO;YACpD;YACA,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,qBAAqB,GAAG,SAAS,IAAI,EAAE,KAAK;YACpE,IAAI;YACJ,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,OAAO,KAAK,MAAM;YAC1C,IAAI,KAAK,KAAK,EAAE;gBACd,KAAK,MAAM,KAAK,KAAK;YACvB;YACA,KAAK,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,OAAO;YAChD,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAClD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,KAAK,GAAG,IAAI,CAAC,OAAO;QACtD;QAEA,gBAAgB,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,KAAK;YACnD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,KAAK,GAAG,IAAI,CAAC,OAAO;QACtD;QAEA,gBAAgB,SAAS,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,KAAK;YACzD,IAAI;YACJ,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,eAAe,KAAK,WAAW,GAAG,MAAM,KAAK,aAAa,GAAG,MAAM,KAAK,aAAa;YAC7G,IAAI,KAAK,gBAAgB,KAAK,YAAY;gBACxC,KAAK,MAAM,KAAK,gBAAgB;YAClC;YACA,IAAI,KAAK,YAAY,EAAE;gBACrB,KAAK,OAAO,KAAK,YAAY,GAAG;YAClC;YACA,KAAK,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO;YAC/C,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,KAAK;YACzD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,eAAe,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO;QACrH;QAEA,gBAAgB,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;YACxD,IAAI;YACJ,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS;YACxB,IAAI,KAAK,EAAE,EAAE;gBACX,KAAK;YACP;YACA,KAAK,MAAM,KAAK,IAAI;YACpB,IAAI,KAAK,KAAK,EAAE;gBACd,KAAK,OAAO,KAAK,KAAK,GAAG;YAC3B,OAAO;gBACL,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;oBAC5B,KAAK,cAAc,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,GAAG;gBACvD,OAAO,IAAI,KAAK,KAAK,EAAE;oBACrB,KAAK,cAAc,KAAK,KAAK,GAAG;gBAClC;gBACA,IAAI,KAAK,KAAK,EAAE;oBACd,KAAK,YAAY,KAAK,KAAK;gBAC7B;YACF;YACA,KAAK,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO;YAC/C,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,KAAK;YAC1D,IAAI;YACJ,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,gBAAgB,KAAK,IAAI;YACjD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;gBAC5B,KAAK,cAAc,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,GAAG;YACvD,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,KAAK,cAAc,KAAK,KAAK,GAAG;YAClC,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,KAAK,cAAc,KAAK,KAAK,GAAG;YAClC;YACA,KAAK,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO;YAC/C,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,KAAK;YACvD,IAAI,KAAK,MAAM,GAAG;YAClB,SAAS,CAAC,QAAQ,CAAC;YACnB,IAAI,gBAAgB,YAAY;gBAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,KAAK,IAAI;gBACvC,MAAM,KAAK,UAAU;gBACrB,IAAK,QAAQ,IAAK;oBAChB,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,OAAO;oBAC9B,MAAM,GAAG,CAAC,KAAK;oBACf,KAAK,IAAI,CAAC,SAAS,CAAC;gBACtB;gBACA,KAAK,CAAC,KAAK,QAAQ,GAAG,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO;gBAChD,OAAO;YACT,OAAO;gBACL,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,eAAe,KAAK,YAAY;gBACxD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;oBAC5B,KAAK,cAAc,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,GAAG;gBACvD,OAAO,IAAI,KAAK,KAAK,EAAE;oBACrB,KAAK,cAAc,KAAK,KAAK,GAAG;gBAClC;gBACA,KAAK,CAAC,KAAK,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO;gBAChD,OAAO;YACT;QACF;QAEA,gBAAgB,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;YACxD,SAAS,CAAC,QAAQ,CAAC;YACnB,OAAQ;gBACN,KAAK,CAAC,CAAC,gBAAgB,UAAU;oBAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,OAAO,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO;gBAClE,KAAK,CAAC,CAAC,gBAAgB,UAAU;oBAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI,CAAC,OAAO;YAClD;QACF;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDocument.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDocument, XMLNode, XMLStringWriter, XMLStringifier, isPlainObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isPlainObject = require('./Utility').isPlainObject;\n\n  XMLNode = require('./XMLNode');\n\n  XMLStringifier = require('./XMLStringifier');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  module.exports = XMLDocument = (function(superClass) {\n    extend(XMLDocument, superClass);\n\n    function XMLDocument(options) {\n      XMLDocument.__super__.constructor.call(this, null);\n      this.name = \"?xml\";\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.stringify = new XMLStringifier(options);\n      this.isDocument = true;\n    }\n\n    XMLDocument.prototype.end = function(writer) {\n      var writerOptions;\n      if (!writer) {\n        writer = this.options.writer;\n      } else if (isPlainObject(writer)) {\n        writerOptions = writer;\n        writer = this.options.writer.set(writerOptions);\n      }\n      return writer.document(this);\n    };\n\n    XMLDocument.prototype.toString = function(options) {\n      return this.options.writer.set(options).document(this);\n    };\n\n    return XMLDocument;\n\n  })(XMLNode);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,aAAa,SAAS,iBAAiB,gBAAgB,eACzD,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B,gBAAgB,qGAAqB,aAAa;IAElD;IAEA;IAEA;IAEA,OAAO,OAAO,GAAG,cAAc,AAAC,SAAS,UAAU;QACjD,OAAO,aAAa;QAEpB,SAAS,YAAY,OAAO;YAC1B,YAAY,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAC7C,IAAI,CAAC,IAAI,GAAG;YACZ,WAAW,CAAC,UAAU,CAAC,CAAC;YACxB,IAAI,CAAC,QAAQ,MAAM,EAAE;gBACnB,QAAQ,MAAM,GAAG,IAAI;YACvB;YACA,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe;YACpC,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,YAAY,SAAS,CAAC,GAAG,GAAG,SAAS,MAAM;YACzC,IAAI;YACJ,IAAI,CAAC,QAAQ;gBACX,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM;YAC9B,OAAO,IAAI,cAAc,SAAS;gBAChC,gBAAgB;gBAChB,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC;YACnC;YACA,OAAO,OAAO,QAAQ,CAAC,IAAI;QAC7B;QAEA,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAS,OAAO;YAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,QAAQ,CAAC,IAAI;QACvD;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLDocumentCB.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = require('./Utility'), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n\n  XMLElement = require('./XMLElement');\n\n  XMLCData = require('./XMLCData');\n\n  XMLComment = require('./XMLComment');\n\n  XMLRaw = require('./XMLRaw');\n\n  XMLText = require('./XMLText');\n\n  XMLProcessingInstruction = require('./XMLProcessingInstruction');\n\n  XMLDeclaration = require('./XMLDeclaration');\n\n  XMLDocType = require('./XMLDocType');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  XMLAttribute = require('./XMLAttribute');\n\n  XMLStringifier = require('./XMLStringifier');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  module.exports = XMLDocumentCB = (function() {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter(options);\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter(writerOptions);\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    XMLDocumentCB.prototype.node = function(name, attributes, text) {\n      var ref1, ref2;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes === null && (text == null)) {\n        ref1 = [{}, null], attributes = ref1[0], text = ref1[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.element = function(name, attributes, text) {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.dtdElement.apply(this, arguments);\n      } else {\n        return this.node(name, attributes, text);\n      }\n    };\n\n    XMLDocumentCB.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (!this.options.skipNullAttributes || (value != null)) {\n          this.currentNode.attributes[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.text = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.cdata = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.comment = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.raw = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.instruction = function(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.declaration = function(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.doctype = function(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dtdElement = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.entity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.pEntity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.notation = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.up = function() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.end = function() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n\n    XMLDocumentCB.prototype.openCurrent = function() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n\n    XMLDocumentCB.prototype.openNode = function(node) {\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node instanceof XMLElement) {\n          this.root = node;\n        }\n        this.onData(this.writer.openNode(node, this.currentLevel), this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.closeNode = function(node) {\n      if (!node.isClosed) {\n        this.onData(this.writer.closeNode(node, this.currentLevel), this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.onData = function(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n\n    XMLDocumentCB.prototype.onEnd = function() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n\n    XMLDocumentCB.prototype.debugInfo = function(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n\n    XMLDocumentCB.prototype.ele = function() {\n      return this.element.apply(this, arguments);\n    };\n\n    XMLDocumentCB.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLDocumentCB.prototype.dtd = function(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n\n    XMLDocumentCB.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLDocumentCB.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.att = function() {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.a = function() {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocumentCB.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocumentCB.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    return XMLDocumentCB;\n\n  })();\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,cAAc,UAAU,YAAY,eAAe,eAAe,cAAc,gBAAgB,gBAAgB,YAAY,eAAe,YAAY,0BAA0B,QAAQ,iBAAiB,gBAAgB,SAAS,UAAU,YAAY,UAAU,eAAe,KACpR,UAAU,CAAC,EAAE,cAAc;IAE7B,4GAA4B,WAAW,IAAI,QAAQ,EAAE,aAAa,IAAI,UAAU,EAAE,gBAAgB,IAAI,aAAa,EAAE,WAAW,IAAI,QAAQ;IAE5I;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA,OAAO,OAAO,GAAG,gBAAgB,AAAC;QAChC,SAAS,cAAc,OAAO,EAAE,MAAM,EAAE,KAAK;YAC3C,IAAI;YACJ,IAAI,CAAC,IAAI,GAAG;YACZ,WAAW,CAAC,UAAU,CAAC,CAAC;YACxB,IAAI,CAAC,QAAQ,MAAM,EAAE;gBACnB,QAAQ,MAAM,GAAG,IAAI,gBAAgB;YACvC,OAAO,IAAI,cAAc,QAAQ,MAAM,GAAG;gBACxC,gBAAgB,QAAQ,MAAM;gBAC9B,QAAQ,MAAM,GAAG,IAAI,gBAAgB;YACvC;YACA,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;YAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe;YACpC,IAAI,CAAC,cAAc,GAAG,UAAU,YAAY;YAC5C,IAAI,CAAC,aAAa,GAAG,SAAS,YAAY;YAC1C,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,YAAY,GAAG,CAAC;YACrB,IAAI,CAAC,QAAQ,GAAG,CAAC;YACjB,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI,CAAC,IAAI,GAAG;QACd;QAEA,cAAc,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YAC5D,IAAI,MAAM;YACV,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG;gBACzC,MAAM,IAAI,MAAM,2CAA2C,IAAI,CAAC,SAAS,CAAC;YAC5E;YACA,IAAI,CAAC,WAAW;YAChB,OAAO,SAAS;YAChB,IAAI,eAAe,QAAS,QAAQ,MAAO;gBACzC,OAAO;oBAAC,CAAC;oBAAG;iBAAK,EAAE,aAAa,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,EAAE;YACzD;YACA,IAAI,cAAc,MAAM;gBACtB,aAAa,CAAC;YAChB;YACA,aAAa,SAAS;YACtB,IAAI,CAAC,SAAS,aAAa;gBACzB,OAAO;oBAAC;oBAAY;iBAAK,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,aAAa,IAAI,CAAC,EAAE;YACjE;YACA,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,IAAI,EAAE,MAAM;YAC9C,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG;YAC5B,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,WAAW;YACnD,IAAI,QAAQ,MAAM;gBAChB,IAAI,CAAC,IAAI,CAAC;YACZ;YACA,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YAC/D,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,YAAY,YAAY;gBAC9D,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE;YACrC,OAAO;gBACL,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;YACrC;QACF;QAEA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;YACtD,IAAI,SAAS;YACb,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;gBAClD,MAAM,IAAI,MAAM,8EAA8E,IAAI,CAAC,SAAS,CAAC;YAC/G;YACA,IAAI,QAAQ,MAAM;gBAChB,OAAO,SAAS;YAClB;YACA,IAAI,SAAS,OAAO;gBAClB,IAAK,WAAW,KAAM;oBACpB,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,UAAU;oBAClC,WAAW,IAAI,CAAC,QAAQ;oBACxB,IAAI,CAAC,SAAS,CAAC,SAAS;gBAC1B;YACF,OAAO;gBACL,IAAI,WAAW,QAAQ;oBACrB,QAAQ,MAAM,KAAK;gBACrB;gBACA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAK,SAAS,MAAO;oBACvD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,aAAa,IAAI,EAAE,MAAM;gBACnE;YACF;YACA,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK;YAC3C,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,QAAQ,IAAI,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YAC/E,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK;YAC5C,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,SAAS,IAAI,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YAChF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,OAAO,GAAG,SAAS,KAAK;YAC9C,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,WAAW,IAAI,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YAClF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YAC1C,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,OAAO,IAAI,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YAC9E,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAS,MAAM,EAAE,KAAK;YAC1D,IAAI,GAAG,WAAW,UAAU,KAAK;YACjC,IAAI,CAAC,WAAW;YAChB,IAAI,UAAU,MAAM;gBAClB,SAAS,SAAS;YACpB;YACA,IAAI,SAAS,MAAM;gBACjB,QAAQ,SAAS;YACnB;YACA,IAAI,MAAM,OAAO,CAAC,SAAS;gBACzB,IAAK,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;oBAC7C,YAAY,MAAM,CAAC,EAAE;oBACrB,IAAI,CAAC,WAAW,CAAC;gBACnB;YACF,OAAO,IAAI,SAAS,SAAS;gBAC3B,IAAK,aAAa,OAAQ;oBACxB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,YAAY;oBACtC,WAAW,MAAM,CAAC,UAAU;oBAC5B,IAAI,CAAC,WAAW,CAAC,WAAW;gBAC9B;YACF,OAAO;gBACL,IAAI,WAAW,QAAQ;oBACrB,QAAQ,MAAM,KAAK;gBACrB;gBACA,OAAO,IAAI,yBAAyB,IAAI,EAAE,QAAQ;gBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YAClG;YACA,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU;YAC1E,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,IAAI,IAAI,CAAC,eAAe,EAAE;gBACxB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,IAAI,eAAe,IAAI,EAAE,SAAS,UAAU;YACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YACtF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,KAAK;YAC3D,IAAI,CAAC,WAAW;YAChB,IAAI,QAAQ,MAAM;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,IAAI,EAAE,OAAO;YAC/C,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG;YAChC,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG;YAC5B,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,WAAW;YACnD,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,KAAK;YACvD,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,cAAc,IAAI,EAAE,MAAM;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YACrF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,OAAO,GAAG,SAAS,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;YAClH,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,cAAc,IAAI,EAAE,aAAa,eAAe,eAAe,kBAAkB;YAC5F,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YACrF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAS,IAAI,EAAE,KAAK;YACnD,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,aAAa,IAAI,EAAE,OAAO,MAAM;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YACpF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACpD,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,aAAa,IAAI,EAAE,MAAM,MAAM;YAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YACpF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,KAAK;YACrD,IAAI;YACJ,IAAI,CAAC,WAAW;YAChB,OAAO,IAAI,eAAe,IAAI,EAAE,MAAM;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG;YACtF,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,EAAE,GAAG;YAC3B,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG;gBACzB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;oBAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW;gBACjC,OAAO;oBACL,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;gBAChC;gBACA,IAAI,CAAC,WAAW,GAAG;YACrB,OAAO;gBACL,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;YACjD;YACA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;YACvC,IAAI,CAAC,YAAY;YACjB,OAAO,IAAI;QACb;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG;YAC5B,MAAO,IAAI,CAAC,YAAY,IAAI,EAAG;gBAC7B,IAAI,CAAC,EAAE;YACT;YACA,OAAO,IAAI,CAAC,KAAK;QACnB;QAEA,cAAc,SAAS,CAAC,WAAW,GAAG;YACpC,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG;gBAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YACvC;QACF;QAEA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI;YAC9C,IAAI,CAAC,KAAK,MAAM,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,gBAAgB,YAAY;oBACvE,IAAI,CAAC,IAAI,GAAG;gBACd;gBACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;gBAC5E,OAAO,KAAK,MAAM,GAAG;YACvB;QACF;QAEA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI;YAC/C,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;gBAC7E,OAAO,KAAK,QAAQ,GAAG;YACzB;QACF;QAEA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAS,KAAK,EAAE,KAAK;YACpD,IAAI,CAAC,eAAe,GAAG;YACvB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,QAAQ;QAC5C;QAEA,cAAc,SAAS,CAAC,KAAK,GAAG;YAC9B,IAAI,CAAC,iBAAiB,GAAG;YACzB,OAAO,IAAI,CAAC,aAAa;QAC3B;QAEA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI;YAC/C,IAAI,QAAQ,MAAM;gBAChB,OAAO;YACT,OAAO;gBACL,OAAO,YAAY,OAAO;YAC5B;QACF;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG;YAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;QAClC;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YAC3D,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;QACrC;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YAC1C,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YAC1C,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,KAAK;YAC1C,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,MAAM,EAAE,KAAK;YAClD,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;QAClC;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU;YAClE,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,UAAU;QAC7C;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,KAAK;YACvD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO;QACnC;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACzD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,YAAY;QACxC;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI;YACzD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;QACrC;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YACxC,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YACxC,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YACxC,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG,SAAS,KAAK;YACxC,OAAO,IAAI,CAAC,GAAG,CAAC;QAClB;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG,SAAS,MAAM,EAAE,KAAK;YAChD,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;QAClC;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG;YAC5B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,YAAY,YAAY;gBAC9D,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;YAClC,OAAO;gBACL,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE;YACpC;QACF;QAEA,cAAc,SAAS,CAAC,CAAC,GAAG;YAC1B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,YAAY,YAAY;gBAC9D,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;YAClC,OAAO;gBACL,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE;YACpC;QACF;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAChD,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM;QAC3B;QAEA,cAAc,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,KAAK;YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC5B;QAEA,cAAc,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAChD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;QAC7B;QAEA,OAAO;IAET;AAEF,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/XMLStreamWriter.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStreamWriter, XMLText, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLDeclaration = require('./XMLDeclaration');\n\n  XMLDocType = require('./XMLDocType');\n\n  XMLCData = require('./XMLCData');\n\n  XMLComment = require('./XMLComment');\n\n  XMLElement = require('./XMLElement');\n\n  XMLRaw = require('./XMLRaw');\n\n  XMLText = require('./XMLText');\n\n  XMLProcessingInstruction = require('./XMLProcessingInstruction');\n\n  XMLDummy = require('./XMLDummy');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  XMLWriterBase = require('./XMLWriterBase');\n\n  module.exports = XMLStreamWriter = (function(superClass) {\n    extend(XMLStreamWriter, superClass);\n\n    function XMLStreamWriter(stream, options) {\n      XMLStreamWriter.__super__.constructor.call(this, options);\n      this.stream = stream;\n    }\n\n    XMLStreamWriter.prototype.document = function(doc) {\n      var child, i, j, len, len1, ref, ref1, results;\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        child.isLastRootNode = false;\n      }\n      doc.children[doc.children.length - 1].isLastRootNode = true;\n      ref1 = doc.children;\n      results = [];\n      for (j = 0, len1 = ref1.length; j < len1; j++) {\n        child = ref1[j];\n        if (child instanceof XMLDummy) {\n          continue;\n        }\n        switch (false) {\n          case !(child instanceof XMLDeclaration):\n            results.push(this.declaration(child));\n            break;\n          case !(child instanceof XMLDocType):\n            results.push(this.docType(child));\n            break;\n          case !(child instanceof XMLComment):\n            results.push(this.comment(child));\n            break;\n          case !(child instanceof XMLProcessingInstruction):\n            results.push(this.processingInstruction(child));\n            break;\n          default:\n            results.push(this.element(child));\n        }\n      }\n      return results;\n    };\n\n    XMLStreamWriter.prototype.attribute = function(att) {\n      return this.stream.write(' ' + att.name + '=\"' + att.value + '\"');\n    };\n\n    XMLStreamWriter.prototype.cdata = function(node, level) {\n      return this.stream.write(this.space(level) + '<![CDATA[' + node.text + ']]>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.comment = function(node, level) {\n      return this.stream.write(this.space(level) + '<!-- ' + node.text + ' -->' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.declaration = function(node, level) {\n      this.stream.write(this.space(level));\n      this.stream.write('<?xml version=\"' + node.version + '\"');\n      if (node.encoding != null) {\n        this.stream.write(' encoding=\"' + node.encoding + '\"');\n      }\n      if (node.standalone != null) {\n        this.stream.write(' standalone=\"' + node.standalone + '\"');\n      }\n      this.stream.write(this.spacebeforeslash + '?>');\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.docType = function(node, level) {\n      var child, i, len, ref;\n      level || (level = 0);\n      this.stream.write(this.space(level));\n      this.stream.write('<!DOCTYPE ' + node.root().name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      if (node.children.length > 0) {\n        this.stream.write(' [');\n        this.stream.write(this.endline(node));\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          switch (false) {\n            case !(child instanceof XMLDTDAttList):\n              this.dtdAttList(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDElement):\n              this.dtdElement(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDEntity):\n              this.dtdEntity(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDNotation):\n              this.dtdNotation(child, level + 1);\n              break;\n            case !(child instanceof XMLCData):\n              this.cdata(child, level + 1);\n              break;\n            case !(child instanceof XMLComment):\n              this.comment(child, level + 1);\n              break;\n            case !(child instanceof XMLProcessingInstruction):\n              this.processingInstruction(child, level + 1);\n              break;\n            default:\n              throw new Error(\"Unknown DTD node type: \" + child.constructor.name);\n          }\n        }\n        this.stream.write(']');\n      }\n      this.stream.write(this.spacebeforeslash + '>');\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.element = function(node, level) {\n      var att, child, i, len, name, ref, ref1, space;\n      level || (level = 0);\n      space = this.space(level);\n      this.stream.write(space + '<' + node.name);\n      ref = node.attributes;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        this.attribute(att);\n      }\n      if (node.children.length === 0 || node.children.every(function(e) {\n        return e.value === '';\n      })) {\n        if (this.allowEmpty) {\n          this.stream.write('></' + node.name + '>');\n        } else {\n          this.stream.write(this.spacebeforeslash + '/>');\n        }\n      } else if (this.pretty && node.children.length === 1 && (node.children[0].value != null)) {\n        this.stream.write('>');\n        this.stream.write(node.children[0].value);\n        this.stream.write('</' + node.name + '>');\n      } else {\n        this.stream.write('>' + this.newline);\n        ref1 = node.children;\n        for (i = 0, len = ref1.length; i < len; i++) {\n          child = ref1[i];\n          switch (false) {\n            case !(child instanceof XMLCData):\n              this.cdata(child, level + 1);\n              break;\n            case !(child instanceof XMLComment):\n              this.comment(child, level + 1);\n              break;\n            case !(child instanceof XMLElement):\n              this.element(child, level + 1);\n              break;\n            case !(child instanceof XMLRaw):\n              this.raw(child, level + 1);\n              break;\n            case !(child instanceof XMLText):\n              this.text(child, level + 1);\n              break;\n            case !(child instanceof XMLProcessingInstruction):\n              this.processingInstruction(child, level + 1);\n              break;\n            case !(child instanceof XMLDummy):\n              '';\n              break;\n            default:\n              throw new Error(\"Unknown XML node type: \" + child.constructor.name);\n          }\n        }\n        this.stream.write(space + '</' + node.name + '>');\n      }\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.processingInstruction = function(node, level) {\n      this.stream.write(this.space(level) + '<?' + node.target);\n      if (node.value) {\n        this.stream.write(' ' + node.value);\n      }\n      return this.stream.write(this.spacebeforeslash + '?>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.raw = function(node, level) {\n      return this.stream.write(this.space(level) + node.value + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.text = function(node, level) {\n      return this.stream.write(this.space(level) + node.value + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdAttList = function(node, level) {\n      this.stream.write(this.space(level) + '<!ATTLIST ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType);\n      if (node.defaultValueType !== '#DEFAULT') {\n        this.stream.write(' ' + node.defaultValueType);\n      }\n      if (node.defaultValue) {\n        this.stream.write(' \"' + node.defaultValue + '\"');\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdElement = function(node, level) {\n      this.stream.write(this.space(level) + '<!ELEMENT ' + node.name + ' ' + node.value);\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdEntity = function(node, level) {\n      this.stream.write(this.space(level) + '<!ENTITY');\n      if (node.pe) {\n        this.stream.write(' %');\n      }\n      this.stream.write(' ' + node.name);\n      if (node.value) {\n        this.stream.write(' \"' + node.value + '\"');\n      } else {\n        if (node.pubID && node.sysID) {\n          this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n        } else if (node.sysID) {\n          this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n        }\n        if (node.nData) {\n          this.stream.write(' NDATA ' + node.nData);\n        }\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdNotation = function(node, level) {\n      this.stream.write(this.space(level) + '<!NOTATION ' + node.name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.pubID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.endline = function(node) {\n      if (!node.isLastRootNode) {\n        return this.newline;\n      } else {\n        return '';\n      }\n    };\n\n    return XMLStreamWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,UAAU,YAAY,eAAe,eAAe,cAAc,gBAAgB,gBAAgB,YAAY,UAAU,YAAY,0BAA0B,QAAQ,iBAAiB,SAAS,eAClM,SAAS,SAAS,KAAK,EAAE,MAAM;QAAI,IAAK,IAAI,OAAO,OAAQ;YAAE,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;QAAE,SAAS;YAAS,IAAI,CAAC,WAAW,GAAG;QAAO;QAAE,KAAK,SAAS,GAAG,OAAO,SAAS;QAAE,MAAM,SAAS,GAAG,IAAI;QAAQ,MAAM,SAAS,GAAG,OAAO,SAAS;QAAE,OAAO;IAAO,GACzR,UAAU,CAAC,EAAE,cAAc;IAE7B;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA;IAEA,OAAO,OAAO,GAAG,kBAAkB,AAAC,SAAS,UAAU;QACrD,OAAO,iBAAiB;QAExB,SAAS,gBAAgB,MAAM,EAAE,OAAO;YACtC,gBAAgB,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YACjD,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,gBAAgB,SAAS,CAAC,QAAQ,GAAG,SAAS,GAAG;YAC/C,IAAI,OAAO,GAAG,GAAG,KAAK,MAAM,KAAK,MAAM;YACvC,MAAM,IAAI,QAAQ;YAClB,IAAK,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;gBAC1C,QAAQ,GAAG,CAAC,EAAE;gBACd,MAAM,cAAc,GAAG;YACzB;YACA,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,cAAc,GAAG;YACvD,OAAO,IAAI,QAAQ;YACnB,UAAU,EAAE;YACZ,IAAK,IAAI,GAAG,OAAO,KAAK,MAAM,EAAE,IAAI,MAAM,IAAK;gBAC7C,QAAQ,IAAI,CAAC,EAAE;gBACf,IAAI,iBAAiB,UAAU;oBAC7B;gBACF;gBACA,OAAQ;oBACN,KAAK,CAAC,CAAC,iBAAiB,cAAc;wBACpC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;wBAC9B;oBACF,KAAK,CAAC,CAAC,iBAAiB,UAAU;wBAChC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;wBAC1B;oBACF,KAAK,CAAC,CAAC,iBAAiB,UAAU;wBAChC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;wBAC1B;oBACF,KAAK,CAAC,CAAC,iBAAiB,wBAAwB;wBAC9C,QAAQ,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;wBACxC;oBACF;wBACE,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;gBAC9B;YACF;YACA,OAAO;QACT;QAEA,gBAAgB,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG;YAChD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,GAAG,OAAO,IAAI,KAAK,GAAG;QAC/D;QAEA,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAS,IAAI,EAAE,KAAK;YACpD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,cAAc,KAAK,IAAI,GAAG,QAAQ,IAAI,CAAC,OAAO,CAAC;QAC9F;QAEA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACtD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,UAAU,KAAK,IAAI,GAAG,SAAS,IAAI,CAAC,OAAO,CAAC;QAC3F;QAEA,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,KAAK;YAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,OAAO,GAAG;YACrD,IAAI,KAAK,QAAQ,IAAI,MAAM;gBACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,KAAK,QAAQ,GAAG;YACpD;YACA,IAAI,KAAK,UAAU,IAAI,MAAM;gBAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,UAAU,GAAG;YACxD;YACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG;YAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;QACxC;QAEA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACtD,IAAI,OAAO,GAAG,KAAK;YACnB,SAAS,CAAC,QAAQ,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,IAAI,GAAG,IAAI;YACjD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,GAAG;YACpE,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG;YAC/C;YACA,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;gBAC/B,MAAM,KAAK,QAAQ;gBACnB,IAAK,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;oBAC1C,QAAQ,GAAG,CAAC,EAAE;oBACd,OAAQ;wBACN,KAAK,CAAC,CAAC,iBAAiB,aAAa;4BACnC,IAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;4BAC/B;wBACF,KAAK,CAAC,CAAC,iBAAiB,aAAa;4BACnC,IAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;4BAC/B;wBACF,KAAK,CAAC,CAAC,iBAAiB,YAAY;4BAClC,IAAI,CAAC,SAAS,CAAC,OAAO,QAAQ;4BAC9B;wBACF,KAAK,CAAC,CAAC,iBAAiB,cAAc;4BACpC,IAAI,CAAC,WAAW,CAAC,OAAO,QAAQ;4BAChC;wBACF,KAAK,CAAC,CAAC,iBAAiB,QAAQ;4BAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ;4BAC1B;wBACF,KAAK,CAAC,CAAC,iBAAiB,UAAU;4BAChC,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ;4BAC5B;wBACF,KAAK,CAAC,CAAC,iBAAiB,wBAAwB;4BAC9C,IAAI,CAAC,qBAAqB,CAAC,OAAO,QAAQ;4BAC1C;wBACF;4BACE,MAAM,IAAI,MAAM,4BAA4B,MAAM,WAAW,CAAC,IAAI;oBACtE;gBACF;gBACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YACpB;YACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG;YAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;QACxC;QAEA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK;YACtD,IAAI,KAAK,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM;YACzC,SAAS,CAAC,QAAQ,CAAC;YACnB,QAAQ,IAAI,CAAC,KAAK,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,KAAK,IAAI;YACzC,MAAM,KAAK,UAAU;YACrB,IAAK,QAAQ,IAAK;gBAChB,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,OAAO;gBAC9B,MAAM,GAAG,CAAC,KAAK;gBACf,IAAI,CAAC,SAAS,CAAC;YACjB;YACA,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC9D,OAAO,EAAE,KAAK,KAAK;YACrB,IAAI;gBACF,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,GAAG;gBACxC,OAAO;oBACL,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG;gBAC5C;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAM,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,MAAO;gBACxF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG;YACvC,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,OAAO;gBACpC,OAAO,KAAK,QAAQ;gBACpB,IAAK,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,IAAK;oBAC3C,QAAQ,IAAI,CAAC,EAAE;oBACf,OAAQ;wBACN,KAAK,CAAC,CAAC,iBAAiB,QAAQ;4BAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ;4BAC1B;wBACF,KAAK,CAAC,CAAC,iBAAiB,UAAU;4BAChC,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ;4BAC5B;wBACF,KAAK,CAAC,CAAC,iBAAiB,UAAU;4BAChC,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ;4BAC5B;wBACF,KAAK,CAAC,CAAC,iBAAiB,MAAM;4BAC5B,IAAI,CAAC,GAAG,CAAC,OAAO,QAAQ;4BACxB;wBACF,KAAK,CAAC,CAAC,iBAAiB,OAAO;4BAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ;4BACzB;wBACF,KAAK,CAAC,CAAC,iBAAiB,wBAAwB;4BAC9C,IAAI,CAAC,qBAAqB,CAAC,OAAO,QAAQ;4BAC1C;wBACF,KAAK,CAAC,CAAC,iBAAiB,QAAQ;4BAC9B;4BACA;wBACF;4BACE,MAAM,IAAI,MAAM,4BAA4B,MAAM,WAAW,CAAC,IAAI;oBACtE;gBACF;gBACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,GAAG;YAC/C;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;QACxC;QAEA,gBAAgB,SAAS,CAAC,qBAAqB,GAAG,SAAS,IAAI,EAAE,KAAK;YACpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,OAAO,KAAK,MAAM;YACxD,IAAI,KAAK,KAAK,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK;YACpC;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC;QACvE;QAEA,gBAAgB,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,EAAE,KAAK;YAClD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QACzE;QAEA,gBAAgB,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE,KAAK;YACnD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QACzE;QAEA,gBAAgB,SAAS,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,KAAK;YACzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,eAAe,KAAK,WAAW,GAAG,MAAM,KAAK,aAAa,GAAG,MAAM,KAAK,aAAa;YAC3H,IAAI,KAAK,gBAAgB,KAAK,YAAY;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,gBAAgB;YAC/C;YACA,IAAI,KAAK,YAAY,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,KAAK,YAAY,GAAG;YAC/C;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;QACtE;QAEA,gBAAgB,SAAS,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,KAAK;YACzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,eAAe,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK;YACjF,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;QACtE;QAEA,gBAAgB,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;YACxD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;YACtC,IAAI,KAAK,EAAE,EAAE;gBACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YACpB;YACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI;YACjC,IAAI,KAAK,KAAK,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,GAAG;YACxC,OAAO;gBACL,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;oBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,GAAG;gBACpE,OAAO,IAAI,KAAK,KAAK,EAAE;oBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG;gBAC/C;gBACA,IAAI,KAAK,KAAK,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,KAAK;gBAC1C;YACF;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;QACtE;QAEA,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,KAAK;YAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,gBAAgB,KAAK,IAAI;YAC/D,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,GAAG;YACpE,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG;YAC/C,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,KAAK,GAAG;YAC/C;YACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;QACtE;QAEA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI;YAC/C,IAAI,CAAC,KAAK,cAAc,EAAE;gBACxB,OAAO,IAAI,CAAC,OAAO;YACrB,OAAO;gBACL,OAAO;YACT;QACF;QAEA,OAAO;IAET,EAAG;AAEL,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/xmlbuilder/lib/index.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n\n  ref = require('./Utility'), assign = ref.assign, isFunction = ref.isFunction;\n\n  XMLDocument = require('./XMLDocument');\n\n  XMLDocumentCB = require('./XMLDocumentCB');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  XMLStreamWriter = require('./XMLStreamWriter');\n\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.doctype(options);\n      }\n    }\n    return root;\n  };\n\n  module.exports.begin = function(options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n}).call(this);\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,CAAC;IACC,IAAI,aAAa,eAAe,iBAAiB,iBAAiB,QAAQ,YAAY;IAEtF,4GAA4B,SAAS,IAAI,MAAM,EAAE,aAAa,IAAI,UAAU;IAE5E;IAEA;IAEA;IAEA;IAEA,OAAO,OAAO,CAAC,MAAM,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;QAC7D,IAAI,KAAK;QACT,IAAI,QAAQ,MAAM;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,UAAU,OAAO,CAAC,GAAG,QAAQ,SAAS;QACtC,MAAM,IAAI,YAAY;QACtB,OAAO,IAAI,OAAO,CAAC;QACnB,IAAI,CAAC,QAAQ,QAAQ,EAAE;YACrB,IAAI,WAAW,CAAC;YAChB,IAAI,AAAC,QAAQ,KAAK,IAAI,QAAU,QAAQ,KAAK,IAAI,MAAO;gBACtD,IAAI,OAAO,CAAC;YACd;QACF;QACA,OAAO;IACT;IAEA,OAAO,OAAO,CAAC,KAAK,GAAG,SAAS,OAAO,EAAE,MAAM,EAAE,KAAK;QACpD,IAAI;QACJ,IAAI,WAAW,UAAU;YACvB,OAAO;gBAAC;gBAAS;aAAO,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,EAAE;YAC3D,UAAU,CAAC;QACb;QACA,IAAI,QAAQ;YACV,OAAO,IAAI,cAAc,SAAS,QAAQ;QAC5C,OAAO;YACL,OAAO,IAAI,YAAY;QACzB;IACF;IAEA,OAAO,OAAO,CAAC,YAAY,GAAG,SAAS,OAAO;QAC5C,OAAO,IAAI,gBAAgB;IAC7B;IAEA,OAAO,OAAO,CAAC,YAAY,GAAG,SAAS,MAAM,EAAE,OAAO;QACpD,OAAO,IAAI,gBAAgB,QAAQ;IACrC;AAEF,CAAC,EAAE,IAAI,CAAC,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/path-is-absolute/index.js"], "sourcesContent": ["'use strict';\n\nfunction posix(path) {\n\treturn path.charAt(0) === '/';\n}\n\nfunction win32(path) {\n\t// https://github.com/nodejs/node/blob/b3fcc245fb25539909ef1d5eaa01dbf92e168633/lib/path.js#L56\n\tvar splitDeviceRe = /^([a-zA-Z]:|[\\\\\\/]{2}[^\\\\\\/]+[\\\\\\/]+[^\\\\\\/]+)?([\\\\\\/])?([\\s\\S]*?)$/;\n\tvar result = splitDeviceRe.exec(path);\n\tvar device = result[1] || '';\n\tvar isUnc = Boolean(device && device.charAt(1) !== ':');\n\n\t// UNC paths are always absolute\n\treturn Boolean(result[2] || isUnc);\n}\n\nmodule.exports = process.platform === 'win32' ? win32 : posix;\nmodule.exports.posix = posix;\nmodule.exports.win32 = win32;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,MAAM,IAAI;IAClB,OAAO,KAAK,MAAM,CAAC,OAAO;AAC3B;AAEA,SAAS,MAAM,IAAI;IAClB,+FAA+F;IAC/F,IAAI,gBAAgB;IACpB,IAAI,SAAS,cAAc,IAAI,CAAC;IAChC,IAAI,SAAS,MAAM,CAAC,EAAE,IAAI;IAC1B,IAAI,QAAQ,QAAQ,UAAU,OAAO,MAAM,CAAC,OAAO;IAEnD,gCAAgC;IAChC,OAAO,QAAQ,MAAM,CAAC,EAAE,IAAI;AAC7B;AAEA,OAAO,OAAO,GAAG,uCAA+B;AAChD,OAAO,OAAO,CAAC,KAAK,GAAG;AACvB,OAAO,OAAO,CAAC,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/TokenIterator.js"], "sourcesContent": ["var TokenIterator = module.exports = function(tokens, startIndex) {\n    this._tokens = tokens;\n    this._startIndex = startIndex || 0;\n};\n\nTokenIterator.prototype.head = function() {\n    return this._tokens[this._startIndex];\n};\n\nTokenIterator.prototype.tail = function(startIndex) {\n    return new TokenIterator(this._tokens, this._startIndex + 1);\n};\n\nTokenIterator.prototype.toArray = function() {\n    return this._tokens.slice(this._startIndex);\n};\n\nTokenIterator.prototype.end = function() {\n    return this._tokens[this._tokens.length - 1];\n};\n\n// TODO: doesn't need to be a method, can be a separate function,\n// which simplifies implementation of the TokenIterator interface\nTokenIterator.prototype.to = function(end) {\n    var start = this.head().source;\n    var endToken = end.head() || end.end();\n    return start.to(endToken.source);\n};\n"], "names": [], "mappings": "AAAA,IAAI,gBAAgB,OAAO,OAAO,GAAG,SAAS,MAAM,EAAE,UAAU;IAC5D,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,WAAW,GAAG,cAAc;AACrC;AAEA,cAAc,SAAS,CAAC,IAAI,GAAG;IAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AACzC;AAEA,cAAc,SAAS,CAAC,IAAI,GAAG,SAAS,UAAU;IAC9C,OAAO,IAAI,cAAc,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,GAAG;AAC9D;AAEA,cAAc,SAAS,CAAC,OAAO,GAAG;IAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;AAC9C;AAEA,cAAc,SAAS,CAAC,GAAG,GAAG;IAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE;AAChD;AAEA,iEAAiE;AACjE,iEAAiE;AACjE,cAAc,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG;IACrC,IAAI,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM;IAC9B,IAAI,WAAW,IAAI,IAAI,MAAM,IAAI,GAAG;IACpC,OAAO,MAAM,EAAE,CAAC,SAAS,MAAM;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/parser.js"], "sourcesContent": ["var TokenIterator = require(\"./TokenIterator\");\n\nexports.Parser = function(options) {\n    var parseTokens = function(parser, tokens) {\n        return parser(new TokenIterator(tokens));\n    };\n    \n    return {\n        parseTokens: parseTokens\n    };\n};\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,QAAQ,MAAM,GAAG,SAAS,OAAO;IAC7B,IAAI,cAAc,SAAS,MAAM,EAAE,MAAM;QACrC,OAAO,OAAO,IAAI,cAAc;IACpC;IAEA,OAAO;QACH,aAAa;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/parsing-results.js"], "sourcesContent": ["module.exports = {\n    failure: function(errors, remaining) {\n        if (errors.length < 1) {\n            throw new Error(\"Failure must have errors\");\n        }\n        return new Result({\n            status: \"failure\",\n            remaining: remaining,\n            errors: errors\n        });\n    },\n    error: function(errors, remaining) {\n        if (errors.length < 1) {\n            throw new Error(\"Failure must have errors\");\n        }\n        return new Result({\n            status: \"error\",\n            remaining: remaining,\n            errors: errors\n        });\n    },\n    success: function(value, remaining, source) {\n        return new Result({\n            status: \"success\",\n            value: value,\n            source: source,\n            remaining: remaining,\n            errors: []\n        });\n    },\n    cut: function(remaining) {\n        return new Result({\n            status: \"cut\",\n            remaining: remaining,\n            errors: []\n        });\n    }\n};\n\nvar Result = function(options) {\n    this._value = options.value;\n    this._status = options.status;\n    this._hasValue = options.value !== undefined;\n    this._remaining = options.remaining;\n    this._source = options.source;\n    this._errors = options.errors;\n};\n\nResult.prototype.map = function(func) {\n    if (this._hasValue) {\n        return new Result({\n            value: func(this._value, this._source),\n            status: this._status,\n            remaining: this._remaining,\n            source: this._source,\n            errors: this._errors\n        });\n    } else {\n        return this;\n    }\n};\n\nResult.prototype.changeRemaining = function(remaining) {\n    return new Result({\n        value: this._value,\n        status: this._status,\n        remaining: remaining,\n        source: this._source,\n        errors: this._errors\n    });\n};\n\nResult.prototype.isSuccess = function() {\n    return this._status === \"success\" || this._status === \"cut\";\n};\n\nResult.prototype.isFailure = function() {\n    return this._status === \"failure\";\n};\n\nResult.prototype.isError = function() {\n    return this._status === \"error\";\n};\n\nResult.prototype.isCut = function() {\n    return this._status === \"cut\";\n};\n\nResult.prototype.value = function() {\n    return this._value;\n};\n\nResult.prototype.remaining = function() {\n    return this._remaining;\n};\n\nResult.prototype.source = function() {\n    return this._source;\n};\n\nResult.prototype.errors = function() {\n    return this._errors;\n};\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG;IACb,SAAS,SAAS,MAAM,EAAE,SAAS;QAC/B,IAAI,OAAO,MAAM,GAAG,GAAG;YACnB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,OAAO;YACd,QAAQ;YACR,WAAW;YACX,QAAQ;QACZ;IACJ;IACA,OAAO,SAAS,MAAM,EAAE,SAAS;QAC7B,IAAI,OAAO,MAAM,GAAG,GAAG;YACnB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,OAAO;YACd,QAAQ;YACR,WAAW;YACX,QAAQ;QACZ;IACJ;IACA,SAAS,SAAS,KAAK,EAAE,SAAS,EAAE,MAAM;QACtC,OAAO,IAAI,OAAO;YACd,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ,EAAE;QACd;IACJ;IACA,KAAK,SAAS,SAAS;QACnB,OAAO,IAAI,OAAO;YACd,QAAQ;YACR,WAAW;YACX,QAAQ,EAAE;QACd;IACJ;AACJ;AAEA,IAAI,SAAS,SAAS,OAAO;IACzB,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IAC3B,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;IAC7B,IAAI,CAAC,SAAS,GAAG,QAAQ,KAAK,KAAK;IACnC,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;IACnC,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;IAC7B,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;AACjC;AAEA,OAAO,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI;IAChC,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,OAAO,IAAI,OAAO;YACd,OAAO,KAAK,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;YACrC,QAAQ,IAAI,CAAC,OAAO;YACpB,WAAW,IAAI,CAAC,UAAU;YAC1B,QAAQ,IAAI,CAAC,OAAO;YACpB,QAAQ,IAAI,CAAC,OAAO;QACxB;IACJ,OAAO;QACH,OAAO,IAAI;IACf;AACJ;AAEA,OAAO,SAAS,CAAC,eAAe,GAAG,SAAS,SAAS;IACjD,OAAO,IAAI,OAAO;QACd,OAAO,IAAI,CAAC,MAAM;QAClB,QAAQ,IAAI,CAAC,OAAO;QACpB,WAAW;QACX,QAAQ,IAAI,CAAC,OAAO;QACpB,QAAQ,IAAI,CAAC,OAAO;IACxB;AACJ;AAEA,OAAO,SAAS,CAAC,SAAS,GAAG;IACzB,OAAO,IAAI,CAAC,OAAO,KAAK,aAAa,IAAI,CAAC,OAAO,KAAK;AAC1D;AAEA,OAAO,SAAS,CAAC,SAAS,GAAG;IACzB,OAAO,IAAI,CAAC,OAAO,KAAK;AAC5B;AAEA,OAAO,SAAS,CAAC,OAAO,GAAG;IACvB,OAAO,IAAI,CAAC,OAAO,KAAK;AAC5B;AAEA,OAAO,SAAS,CAAC,KAAK,GAAG;IACrB,OAAO,IAAI,CAAC,OAAO,KAAK;AAC5B;AAEA,OAAO,SAAS,CAAC,KAAK,GAAG;IACrB,OAAO,IAAI,CAAC,MAAM;AACtB;AAEA,OAAO,SAAS,CAAC,SAAS,GAAG;IACzB,OAAO,IAAI,CAAC,UAAU;AAC1B;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG;IACtB,OAAO,IAAI,CAAC,OAAO;AACvB;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG;IACtB,OAAO,IAAI,CAAC,OAAO;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/errors.js"], "sourcesContent": ["exports.error = function(options) {\n    return new Error(options);\n};\n\nvar Error = function(options) {\n    this.expected = options.expected;\n    this.actual = options.actual;\n    this._location = options.location;\n};\n\nError.prototype.describe = function() {\n    var locationDescription = this._location ? this._location.describe() + \":\\n\" : \"\";\n    return locationDescription + \"Expected \" + this.expected + \"\\nbut got \" + this.actual;\n};\n\nError.prototype.lineNumber = function() {\n    return this._location.lineNumber();\n};\n\nError.prototype.characterNumber = function() {\n    return this._location.characterNumber();\n};\n"], "names": [], "mappings": "AAAA,QAAQ,KAAK,GAAG,SAAS,OAAO;IAC5B,OAAO,IAAI,MAAM;AACrB;AAEA,IAAI,QAAQ,SAAS,OAAO;IACxB,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;IAChC,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;IAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;AACrC;AAEA,MAAM,SAAS,CAAC,QAAQ,GAAG;IACvB,IAAI,sBAAsB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,QAAQ;IAC/E,OAAO,sBAAsB,cAAc,IAAI,CAAC,QAAQ,GAAG,eAAe,IAAI,CAAC,MAAM;AACzF;AAEA,MAAM,SAAS,CAAC,UAAU,GAAG;IACzB,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU;AACpC;AAEA,MAAM,SAAS,CAAC,eAAe,GAAG;IAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/lazy-iterators.js"], "sourcesContent": ["var fromArray = exports.fromArray = function(array) {\n    var index = 0;\n    var hasNext = function() {\n        return index < array.length;\n    };\n    return new LazyIterator({\n        hasNext: hasNext,\n        next: function() {\n            if (!hasNext()) {\n                throw new Error(\"No more elements\");\n            } else {\n                return array[index++];\n            }\n        }\n    });\n};\n\nvar LazyIterator = function(iterator) {\n    this._iterator = iterator;\n};\n\nLazyIterator.prototype.map = function(func) {\n    var iterator = this._iterator;\n    return new LazyIterator({\n        hasNext: function() {\n            return iterator.hasNext();\n        },\n        next: function() {\n            return func(iterator.next());\n        }\n    });\n};\n\nLazyIterator.prototype.filter = function(condition) {\n    var iterator = this._iterator;\n    \n    var moved = false;\n    var hasNext = false;\n    var next;\n    var moveIfNecessary = function() {\n        if (moved) {\n            return;\n        }\n        moved = true;\n        hasNext = false;\n        while (iterator.hasNext() && !hasNext) {\n            next = iterator.next();\n            hasNext = condition(next);\n        }\n    };\n    \n    return new LazyIterator({\n        hasNext: function() {\n            moveIfNecessary();\n            return hasNext;\n        },\n        next: function() {\n            moveIfNecessary();\n            var toReturn = next;\n            moved = false;\n            return toReturn;\n        }\n    });\n};\n\nLazyIterator.prototype.first = function() {\n    var iterator = this._iterator;\n    if (this._iterator.hasNext()) {\n        return iterator.next();\n    } else {\n        return null;\n    }\n};\n\nLazyIterator.prototype.toArray = function() {\n    var result = [];\n    while (this._iterator.hasNext()) {\n        result.push(this._iterator.next());\n    }\n    return result;\n};\n"], "names": [], "mappings": "AAAA,IAAI,YAAY,QAAQ,SAAS,GAAG,SAAS,KAAK;IAC9C,IAAI,QAAQ;IACZ,IAAI,UAAU;QACV,OAAO,QAAQ,MAAM,MAAM;IAC/B;IACA,OAAO,IAAI,aAAa;QACpB,SAAS;QACT,MAAM;YACF,IAAI,CAAC,WAAW;gBACZ,MAAM,IAAI,MAAM;YACpB,OAAO;gBACH,OAAO,KAAK,CAAC,QAAQ;YACzB;QACJ;IACJ;AACJ;AAEA,IAAI,eAAe,SAAS,QAAQ;IAChC,IAAI,CAAC,SAAS,GAAG;AACrB;AAEA,aAAa,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI;IACtC,IAAI,WAAW,IAAI,CAAC,SAAS;IAC7B,OAAO,IAAI,aAAa;QACpB,SAAS;YACL,OAAO,SAAS,OAAO;QAC3B;QACA,MAAM;YACF,OAAO,KAAK,SAAS,IAAI;QAC7B;IACJ;AACJ;AAEA,aAAa,SAAS,CAAC,MAAM,GAAG,SAAS,SAAS;IAC9C,IAAI,WAAW,IAAI,CAAC,SAAS;IAE7B,IAAI,QAAQ;IACZ,IAAI,UAAU;IACd,IAAI;IACJ,IAAI,kBAAkB;QAClB,IAAI,OAAO;YACP;QACJ;QACA,QAAQ;QACR,UAAU;QACV,MAAO,SAAS,OAAO,MAAM,CAAC,QAAS;YACnC,OAAO,SAAS,IAAI;YACpB,UAAU,UAAU;QACxB;IACJ;IAEA,OAAO,IAAI,aAAa;QACpB,SAAS;YACL;YACA,OAAO;QACX;QACA,MAAM;YACF;YACA,IAAI,WAAW;YACf,QAAQ;YACR,OAAO;QACX;IACJ;AACJ;AAEA,aAAa,SAAS,CAAC,KAAK,GAAG;IAC3B,IAAI,WAAW,IAAI,CAAC,SAAS;IAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI;QAC1B,OAAO,SAAS,IAAI;IACxB,OAAO;QACH,OAAO;IACX;AACJ;AAEA,aAAa,SAAS,CAAC,OAAO,GAAG;IAC7B,IAAI,SAAS,EAAE;IACf,MAAO,IAAI,CAAC,SAAS,CAAC,OAAO,GAAI;QAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;IACnC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/rules.js"], "sourcesContent": ["var _ = require(\"underscore\");\nvar options = require(\"option\");\nvar results = require(\"./parsing-results\");\nvar errors = require(\"./errors\");\nvar lazyIterators = require(\"./lazy-iterators\");\n\nexports.token = function(tokenType, value) {\n    var matchValue = value !== undefined;\n    return function(input) {\n        var token = input.head();\n        if (token && token.name === tokenType && (!matchValue || token.value === value)) {\n            return results.success(token.value, input.tail(), token.source);\n        } else {\n            var expected = describeToken({name: tokenType, value: value});\n            return describeTokenMismatch(input, expected);\n        }\n    };\n};\n\nexports.tokenOfType = function(tokenType) {\n    return exports.token(tokenType);\n};\n\nexports.firstOf = function(name, parsers) {\n    if (!_.isArray(parsers)) {\n        parsers = Array.prototype.slice.call(arguments, 1);\n    }\n    return function(input) {\n        return lazyIterators\n            .fromArray(parsers)\n            .map(function(parser) {\n                return parser(input);\n            })\n            .filter(function(result) {\n                return result.isSuccess() || result.isError();\n            })\n            .first() || describeTokenMismatch(input, name);\n    };\n};\n\nexports.then = function(parser, func) {\n    return function(input) {\n        var result = parser(input);\n        if (!result.map) {\n            console.log(result);\n        }\n        return result.map(func);\n    };\n};\n\nexports.sequence = function() {\n    var parsers = Array.prototype.slice.call(arguments, 0);\n    var rule = function(input) {\n        var result = _.foldl(parsers, function(memo, parser) {\n            var result = memo.result;\n            var hasCut = memo.hasCut;\n            if (!result.isSuccess()) {\n                return {result: result, hasCut: hasCut};\n            }\n            var subResult = parser(result.remaining());\n            if (subResult.isCut()) {\n                return {result: result, hasCut: true};\n            } else if (subResult.isSuccess()) {\n                var values;\n                if (parser.isCaptured) {\n                    values = result.value().withValue(parser, subResult.value());\n                } else {\n                    values = result.value();\n                }\n                var remaining = subResult.remaining();\n                var source = input.to(remaining);\n                return {\n                    result: results.success(values, remaining, source),\n                    hasCut: hasCut\n                };\n            } else if (hasCut) {\n                return {result: results.error(subResult.errors(), subResult.remaining()), hasCut: hasCut};\n            } else {\n                return {result: subResult, hasCut: hasCut};\n            }\n        }, {result: results.success(new SequenceValues(), input), hasCut: false}).result;\n        var source = input.to(result.remaining());\n        return result.map(function(values) {\n            return values.withValue(exports.sequence.source, source);\n        });\n    };\n    rule.head = function() {\n        var firstCapture = _.find(parsers, isCapturedRule);\n        return exports.then(\n            rule,\n            exports.sequence.extract(firstCapture)\n        );\n    };\n    rule.map = function(func) {\n        return exports.then(\n            rule,\n            function(result) {\n                return func.apply(this, result.toArray());\n            }\n        );\n    };\n    \n    function isCapturedRule(subRule) {\n        return subRule.isCaptured;\n    }\n    \n    return rule;\n};\n\nvar SequenceValues = function(values, valuesArray) {\n    this._values = values || {};\n    this._valuesArray = valuesArray || [];\n};\n\nSequenceValues.prototype.withValue = function(rule, value) {\n    if (rule.captureName && rule.captureName in this._values) {\n        throw new Error(\"Cannot add second value for capture \\\"\" + rule.captureName + \"\\\"\");\n    } else {\n        var newValues = _.clone(this._values);\n        newValues[rule.captureName] = value;\n        var newValuesArray = this._valuesArray.concat([value]);\n        return new SequenceValues(newValues, newValuesArray);\n    }\n};\n\nSequenceValues.prototype.get = function(rule) {\n    if (rule.captureName in this._values) {\n        return this._values[rule.captureName];\n    } else {\n        throw new Error(\"No value for capture \\\"\" + rule.captureName + \"\\\"\");\n    }\n};\n\nSequenceValues.prototype.toArray = function() {\n    return this._valuesArray;\n};\n\nexports.sequence.capture = function(rule, name) {\n    var captureRule = function() {\n        return rule.apply(this, arguments);\n    };\n    captureRule.captureName = name;\n    captureRule.isCaptured = true;\n    return captureRule;\n};\n\nexports.sequence.extract = function(rule) {\n    return function(result) {\n        return result.get(rule);\n    };\n};\n\nexports.sequence.applyValues = function(func) {\n    // TODO: check captureName doesn't conflict with source or other captures\n    var rules = Array.prototype.slice.call(arguments, 1);\n    return function(result) {\n        var values = rules.map(function(rule) {\n            return result.get(rule);\n        });\n        return func.apply(this, values);\n    };\n};\n\nexports.sequence.source = {\n    captureName: \"☃source☃\"\n};\n\nexports.sequence.cut = function() {\n    return function(input) {\n        return results.cut(input);\n    };\n};\n\nexports.optional = function(rule) {\n    return function(input) {\n        var result = rule(input);\n        if (result.isSuccess()) {\n            return result.map(options.some);\n        } else if (result.isFailure()) {\n            return results.success(options.none, input);\n        } else {\n            return result;\n        }\n    };\n};\n\nexports.zeroOrMoreWithSeparator = function(rule, separator) {\n    return repeatedWithSeparator(rule, separator, false);\n};\n\nexports.oneOrMoreWithSeparator = function(rule, separator) {\n    return repeatedWithSeparator(rule, separator, true);\n};\n\nvar zeroOrMore = exports.zeroOrMore = function(rule) {\n    return function(input) {\n        var values = [];\n        var result;\n        while ((result = rule(input)) && result.isSuccess()) {\n            input = result.remaining();\n            values.push(result.value());\n        }\n        if (result.isError()) {\n            return result;\n        } else {\n            return results.success(values, input);\n        }\n    };\n};\n\nexports.oneOrMore = function(rule) {\n    return exports.oneOrMoreWithSeparator(rule, noOpRule);\n};\n\nfunction noOpRule(input) {\n    return results.success(null, input);\n}\n\nvar repeatedWithSeparator = function(rule, separator, isOneOrMore) {\n    return function(input) {\n        var result = rule(input);\n        if (result.isSuccess()) {\n            var mainRule = exports.sequence.capture(rule, \"main\");\n            var remainingRule = zeroOrMore(exports.then(\n                exports.sequence(separator, mainRule),\n                exports.sequence.extract(mainRule)\n            ));\n            var remainingResult = remainingRule(result.remaining());\n            return results.success([result.value()].concat(remainingResult.value()), remainingResult.remaining());\n        } else if (isOneOrMore || result.isError()) {\n            return result;\n        } else {\n            return results.success([], input);\n        }\n    };\n};\n\nexports.leftAssociative = function(leftRule, rightRule, func) {\n    var rights;\n    if (func) {\n        rights = [{func: func, rule: rightRule}];\n    } else {\n        rights = rightRule;\n    }\n    rights = rights.map(function(right) {\n        return exports.then(right.rule, function(rightValue) {\n            return function(leftValue, source) {\n                return right.func(leftValue, rightValue, source);\n            };\n        });\n    });\n    var repeatedRule = exports.firstOf.apply(null, [\"rules\"].concat(rights));\n    \n    return function(input) {\n        var start = input;\n        var leftResult = leftRule(input);\n        if (!leftResult.isSuccess()) {\n            return leftResult;\n        }\n        var repeatedResult = repeatedRule(leftResult.remaining());\n        while (repeatedResult.isSuccess()) {\n            var remaining = repeatedResult.remaining();\n            var source = start.to(repeatedResult.remaining());\n            var right = repeatedResult.value();\n            leftResult = results.success(\n                right(leftResult.value(), source),\n                remaining,\n                source\n            );\n            repeatedResult = repeatedRule(leftResult.remaining());\n        }\n        if (repeatedResult.isError()) {\n            return repeatedResult;\n        }\n        return leftResult;\n    };\n};\n\nexports.leftAssociative.firstOf = function() {\n    return Array.prototype.slice.call(arguments, 0);\n};\n\nexports.nonConsuming = function(rule) {\n    return function(input) {\n        return rule(input).changeRemaining(input);\n    };\n};\n\nvar describeToken = function(token) {\n    if (token.value) {\n        return token.name + \" \\\"\" + token.value + \"\\\"\";\n    } else {\n        return token.name;\n    }\n};\n\nfunction describeTokenMismatch(input, expected) {\n    var error;\n    var token = input.head();\n    if (token) {\n        error = errors.error({\n            expected: expected,\n            actual: describeToken(token),\n            location: token.source\n        });\n    } else {\n        error = errors.error({\n            expected: expected,\n            actual: \"end of tokens\"\n        });\n    }\n    return results.failure([error], input);\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,QAAQ,KAAK,GAAG,SAAS,SAAS,EAAE,KAAK;IACrC,IAAI,aAAa,UAAU;IAC3B,OAAO,SAAS,KAAK;QACjB,IAAI,QAAQ,MAAM,IAAI;QACtB,IAAI,SAAS,MAAM,IAAI,KAAK,aAAa,CAAC,CAAC,cAAc,MAAM,KAAK,KAAK,KAAK,GAAG;YAC7E,OAAO,QAAQ,OAAO,CAAC,MAAM,KAAK,EAAE,MAAM,IAAI,IAAI,MAAM,MAAM;QAClE,OAAO;YACH,IAAI,WAAW,cAAc;gBAAC,MAAM;gBAAW,OAAO;YAAK;YAC3D,OAAO,sBAAsB,OAAO;QACxC;IACJ;AACJ;AAEA,QAAQ,WAAW,GAAG,SAAS,SAAS;IACpC,OAAO,QAAQ,KAAK,CAAC;AACzB;AAEA,QAAQ,OAAO,GAAG,SAAS,IAAI,EAAE,OAAO;IACpC,IAAI,CAAC,EAAE,OAAO,CAAC,UAAU;QACrB,UAAU,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACpD;IACA,OAAO,SAAS,KAAK;QACjB,OAAO,cACF,SAAS,CAAC,SACV,GAAG,CAAC,SAAS,MAAM;YAChB,OAAO,OAAO;QAClB,GACC,MAAM,CAAC,SAAS,MAAM;YACnB,OAAO,OAAO,SAAS,MAAM,OAAO,OAAO;QAC/C,GACC,KAAK,MAAM,sBAAsB,OAAO;IACjD;AACJ;AAEA,QAAQ,IAAI,GAAG,SAAS,MAAM,EAAE,IAAI;IAChC,OAAO,SAAS,KAAK;QACjB,IAAI,SAAS,OAAO;QACpB,IAAI,CAAC,OAAO,GAAG,EAAE;YACb,QAAQ,GAAG,CAAC;QAChB;QACA,OAAO,OAAO,GAAG,CAAC;IACtB;AACJ;AAEA,QAAQ,QAAQ,GAAG;IACf,IAAI,UAAU,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACpD,IAAI,OAAO,SAAS,KAAK;QACrB,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS,SAAS,IAAI,EAAE,MAAM;YAC/C,IAAI,SAAS,KAAK,MAAM;YACxB,IAAI,SAAS,KAAK,MAAM;YACxB,IAAI,CAAC,OAAO,SAAS,IAAI;gBACrB,OAAO;oBAAC,QAAQ;oBAAQ,QAAQ;gBAAM;YAC1C;YACA,IAAI,YAAY,OAAO,OAAO,SAAS;YACvC,IAAI,UAAU,KAAK,IAAI;gBACnB,OAAO;oBAAC,QAAQ;oBAAQ,QAAQ;gBAAI;YACxC,OAAO,IAAI,UAAU,SAAS,IAAI;gBAC9B,IAAI;gBACJ,IAAI,OAAO,UAAU,EAAE;oBACnB,SAAS,OAAO,KAAK,GAAG,SAAS,CAAC,QAAQ,UAAU,KAAK;gBAC7D,OAAO;oBACH,SAAS,OAAO,KAAK;gBACzB;gBACA,IAAI,YAAY,UAAU,SAAS;gBACnC,IAAI,SAAS,MAAM,EAAE,CAAC;gBACtB,OAAO;oBACH,QAAQ,QAAQ,OAAO,CAAC,QAAQ,WAAW;oBAC3C,QAAQ;gBACZ;YACJ,OAAO,IAAI,QAAQ;gBACf,OAAO;oBAAC,QAAQ,QAAQ,KAAK,CAAC,UAAU,MAAM,IAAI,UAAU,SAAS;oBAAK,QAAQ;gBAAM;YAC5F,OAAO;gBACH,OAAO;oBAAC,QAAQ;oBAAW,QAAQ;gBAAM;YAC7C;QACJ,GAAG;YAAC,QAAQ,QAAQ,OAAO,CAAC,IAAI,kBAAkB;YAAQ,QAAQ;QAAK,GAAG,MAAM;QAChF,IAAI,SAAS,MAAM,EAAE,CAAC,OAAO,SAAS;QACtC,OAAO,OAAO,GAAG,CAAC,SAAS,MAAM;YAC7B,OAAO,OAAO,SAAS,CAAC,QAAQ,QAAQ,CAAC,MAAM,EAAE;QACrD;IACJ;IACA,KAAK,IAAI,GAAG;QACR,IAAI,eAAe,EAAE,IAAI,CAAC,SAAS;QACnC,OAAO,QAAQ,IAAI,CACf,MACA,QAAQ,QAAQ,CAAC,OAAO,CAAC;IAEjC;IACA,KAAK,GAAG,GAAG,SAAS,IAAI;QACpB,OAAO,QAAQ,IAAI,CACf,MACA,SAAS,MAAM;YACX,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE,OAAO,OAAO;QAC1C;IAER;IAEA,SAAS,eAAe,OAAO;QAC3B,OAAO,QAAQ,UAAU;IAC7B;IAEA,OAAO;AACX;AAEA,IAAI,iBAAiB,SAAS,MAAM,EAAE,WAAW;IAC7C,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;IAC1B,IAAI,CAAC,YAAY,GAAG,eAAe,EAAE;AACzC;AAEA,eAAe,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;IACrD,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE;QACtD,MAAM,IAAI,MAAM,2CAA2C,KAAK,WAAW,GAAG;IAClF,OAAO;QACH,IAAI,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;QACpC,SAAS,CAAC,KAAK,WAAW,CAAC,GAAG;QAC9B,IAAI,iBAAiB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAAC;SAAM;QACrD,OAAO,IAAI,eAAe,WAAW;IACzC;AACJ;AAEA,eAAe,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI;IACxC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,WAAW,CAAC;IACzC,OAAO;QACH,MAAM,IAAI,MAAM,4BAA4B,KAAK,WAAW,GAAG;IACnE;AACJ;AAEA,eAAe,SAAS,CAAC,OAAO,GAAG;IAC/B,OAAO,IAAI,CAAC,YAAY;AAC5B;AAEA,QAAQ,QAAQ,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE,IAAI;IAC1C,IAAI,cAAc;QACd,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;IAC5B;IACA,YAAY,WAAW,GAAG;IAC1B,YAAY,UAAU,GAAG;IACzB,OAAO;AACX;AAEA,QAAQ,QAAQ,CAAC,OAAO,GAAG,SAAS,IAAI;IACpC,OAAO,SAAS,MAAM;QAClB,OAAO,OAAO,GAAG,CAAC;IACtB;AACJ;AAEA,QAAQ,QAAQ,CAAC,WAAW,GAAG,SAAS,IAAI;IACxC,yEAAyE;IACzE,IAAI,QAAQ,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IAClD,OAAO,SAAS,MAAM;QAClB,IAAI,SAAS,MAAM,GAAG,CAAC,SAAS,IAAI;YAChC,OAAO,OAAO,GAAG,CAAC;QACtB;QACA,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;IAC5B;AACJ;AAEA,QAAQ,QAAQ,CAAC,MAAM,GAAG;IACtB,aAAa;AACjB;AAEA,QAAQ,QAAQ,CAAC,GAAG,GAAG;IACnB,OAAO,SAAS,KAAK;QACjB,OAAO,QAAQ,GAAG,CAAC;IACvB;AACJ;AAEA,QAAQ,QAAQ,GAAG,SAAS,IAAI;IAC5B,OAAO,SAAS,KAAK;QACjB,IAAI,SAAS,KAAK;QAClB,IAAI,OAAO,SAAS,IAAI;YACpB,OAAO,OAAO,GAAG,CAAC,QAAQ,IAAI;QAClC,OAAO,IAAI,OAAO,SAAS,IAAI;YAC3B,OAAO,QAAQ,OAAO,CAAC,QAAQ,IAAI,EAAE;QACzC,OAAO;YACH,OAAO;QACX;IACJ;AACJ;AAEA,QAAQ,uBAAuB,GAAG,SAAS,IAAI,EAAE,SAAS;IACtD,OAAO,sBAAsB,MAAM,WAAW;AAClD;AAEA,QAAQ,sBAAsB,GAAG,SAAS,IAAI,EAAE,SAAS;IACrD,OAAO,sBAAsB,MAAM,WAAW;AAClD;AAEA,IAAI,aAAa,QAAQ,UAAU,GAAG,SAAS,IAAI;IAC/C,OAAO,SAAS,KAAK;QACjB,IAAI,SAAS,EAAE;QACf,IAAI;QACJ,MAAO,CAAC,SAAS,KAAK,MAAM,KAAK,OAAO,SAAS,GAAI;YACjD,QAAQ,OAAO,SAAS;YACxB,OAAO,IAAI,CAAC,OAAO,KAAK;QAC5B;QACA,IAAI,OAAO,OAAO,IAAI;YAClB,OAAO;QACX,OAAO;YACH,OAAO,QAAQ,OAAO,CAAC,QAAQ;QACnC;IACJ;AACJ;AAEA,QAAQ,SAAS,GAAG,SAAS,IAAI;IAC7B,OAAO,QAAQ,sBAAsB,CAAC,MAAM;AAChD;AAEA,SAAS,SAAS,KAAK;IACnB,OAAO,QAAQ,OAAO,CAAC,MAAM;AACjC;AAEA,IAAI,wBAAwB,SAAS,IAAI,EAAE,SAAS,EAAE,WAAW;IAC7D,OAAO,SAAS,KAAK;QACjB,IAAI,SAAS,KAAK;QAClB,IAAI,OAAO,SAAS,IAAI;YACpB,IAAI,WAAW,QAAQ,QAAQ,CAAC,OAAO,CAAC,MAAM;YAC9C,IAAI,gBAAgB,WAAW,QAAQ,IAAI,CACvC,QAAQ,QAAQ,CAAC,WAAW,WAC5B,QAAQ,QAAQ,CAAC,OAAO,CAAC;YAE7B,IAAI,kBAAkB,cAAc,OAAO,SAAS;YACpD,OAAO,QAAQ,OAAO,CAAC;gBAAC,OAAO,KAAK;aAAG,CAAC,MAAM,CAAC,gBAAgB,KAAK,KAAK,gBAAgB,SAAS;QACtG,OAAO,IAAI,eAAe,OAAO,OAAO,IAAI;YACxC,OAAO;QACX,OAAO;YACH,OAAO,QAAQ,OAAO,CAAC,EAAE,EAAE;QAC/B;IACJ;AACJ;AAEA,QAAQ,eAAe,GAAG,SAAS,QAAQ,EAAE,SAAS,EAAE,IAAI;IACxD,IAAI;IACJ,IAAI,MAAM;QACN,SAAS;YAAC;gBAAC,MAAM;gBAAM,MAAM;YAAS;SAAE;IAC5C,OAAO;QACH,SAAS;IACb;IACA,SAAS,OAAO,GAAG,CAAC,SAAS,KAAK;QAC9B,OAAO,QAAQ,IAAI,CAAC,MAAM,IAAI,EAAE,SAAS,UAAU;YAC/C,OAAO,SAAS,SAAS,EAAE,MAAM;gBAC7B,OAAO,MAAM,IAAI,CAAC,WAAW,YAAY;YAC7C;QACJ;IACJ;IACA,IAAI,eAAe,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM;QAAC;KAAQ,CAAC,MAAM,CAAC;IAEhE,OAAO,SAAS,KAAK;QACjB,IAAI,QAAQ;QACZ,IAAI,aAAa,SAAS;QAC1B,IAAI,CAAC,WAAW,SAAS,IAAI;YACzB,OAAO;QACX;QACA,IAAI,iBAAiB,aAAa,WAAW,SAAS;QACtD,MAAO,eAAe,SAAS,GAAI;YAC/B,IAAI,YAAY,eAAe,SAAS;YACxC,IAAI,SAAS,MAAM,EAAE,CAAC,eAAe,SAAS;YAC9C,IAAI,QAAQ,eAAe,KAAK;YAChC,aAAa,QAAQ,OAAO,CACxB,MAAM,WAAW,KAAK,IAAI,SAC1B,WACA;YAEJ,iBAAiB,aAAa,WAAW,SAAS;QACtD;QACA,IAAI,eAAe,OAAO,IAAI;YAC1B,OAAO;QACX;QACA,OAAO;IACX;AACJ;AAEA,QAAQ,eAAe,CAAC,OAAO,GAAG;IAC9B,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;AACjD;AAEA,QAAQ,YAAY,GAAG,SAAS,IAAI;IAChC,OAAO,SAAS,KAAK;QACjB,OAAO,KAAK,OAAO,eAAe,CAAC;IACvC;AACJ;AAEA,IAAI,gBAAgB,SAAS,KAAK;IAC9B,IAAI,MAAM,KAAK,EAAE;QACb,OAAO,MAAM,IAAI,GAAG,QAAQ,MAAM,KAAK,GAAG;IAC9C,OAAO;QACH,OAAO,MAAM,IAAI;IACrB;AACJ;AAEA,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IAC1C,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI;IACtB,IAAI,OAAO;QACP,QAAQ,OAAO,KAAK,CAAC;YACjB,UAAU;YACV,QAAQ,cAAc;YACtB,UAAU,MAAM,MAAM;QAC1B;IACJ,OAAO;QACH,QAAQ,OAAO,KAAK,CAAC;YACjB,UAAU;YACV,QAAQ;QACZ;IACJ;IACA,OAAO,QAAQ,OAAO,CAAC;QAAC;KAAM,EAAE;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/StringSource.js"], "sourcesContent": ["var StringSource = module.exports = function(string, description) {\n    var self = {\n        asString: function() {\n            return string;\n        },\n        range: function(startIndex, endIndex) {\n            return new StringSourceRange(string, description, startIndex, endIndex);\n        }\n    };\n    return self;\n};\n\nvar StringSourceRange = function(string, description, startIndex, endIndex) {\n    this._string = string;\n    this._description = description;\n    this._startIndex = startIndex;\n    this._endIndex = endIndex;\n};\n\nStringSourceRange.prototype.to = function(otherRange) {\n    // TODO: Assert that tokens are the same across both iterators\n    return new StringSourceRange(this._string, this._description, this._startIndex, otherRange._endIndex);\n};\n\nStringSourceRange.prototype.describe = function() {\n    var position = this._position();\n    var description = this._description ? this._description + \"\\n\" : \"\";\n    return description + \"Line number: \" + position.lineNumber + \"\\nCharacter number: \" + position.characterNumber;\n};\n\nStringSourceRange.prototype.lineNumber = function() {\n    return this._position().lineNumber;\n};\n\nStringSourceRange.prototype.characterNumber = function() {\n    return this._position().characterNumber;\n};\n\nStringSourceRange.prototype._position = function() {\n    var self = this;\n    var index = 0;\n    var nextNewLine = function() {\n        return self._string.indexOf(\"\\n\", index);\n    };\n\n    var lineNumber = 1;\n    while (nextNewLine() !== -1 && nextNewLine() < this._startIndex) {\n        index = nextNewLine() + 1;\n        lineNumber += 1;\n    }\n    var characterNumber = this._startIndex - index + 1;\n    return {lineNumber: lineNumber, characterNumber: characterNumber};\n};\n"], "names": [], "mappings": "AAAA,IAAI,eAAe,OAAO,OAAO,GAAG,SAAS,MAAM,EAAE,WAAW;IAC5D,IAAI,OAAO;QACP,UAAU;YACN,OAAO;QACX;QACA,OAAO,SAAS,UAAU,EAAE,QAAQ;YAChC,OAAO,IAAI,kBAAkB,QAAQ,aAAa,YAAY;QAClE;IACJ;IACA,OAAO;AACX;AAEA,IAAI,oBAAoB,SAAS,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ;IACtE,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,SAAS,GAAG;AACrB;AAEA,kBAAkB,SAAS,CAAC,EAAE,GAAG,SAAS,UAAU;IAChD,8DAA8D;IAC9D,OAAO,IAAI,kBAAkB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,WAAW,SAAS;AACxG;AAEA,kBAAkB,SAAS,CAAC,QAAQ,GAAG;IACnC,IAAI,WAAW,IAAI,CAAC,SAAS;IAC7B,IAAI,cAAc,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO;IACjE,OAAO,cAAc,kBAAkB,SAAS,UAAU,GAAG,yBAAyB,SAAS,eAAe;AAClH;AAEA,kBAAkB,SAAS,CAAC,UAAU,GAAG;IACrC,OAAO,IAAI,CAAC,SAAS,GAAG,UAAU;AACtC;AAEA,kBAAkB,SAAS,CAAC,eAAe,GAAG;IAC1C,OAAO,IAAI,CAAC,SAAS,GAAG,eAAe;AAC3C;AAEA,kBAAkB,SAAS,CAAC,SAAS,GAAG;IACpC,IAAI,OAAO,IAAI;IACf,IAAI,QAAQ;IACZ,IAAI,cAAc;QACd,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,MAAM;IACtC;IAEA,IAAI,aAAa;IACjB,MAAO,kBAAkB,CAAC,KAAK,gBAAgB,IAAI,CAAC,WAAW,CAAE;QAC7D,QAAQ,gBAAgB;QACxB,cAAc;IAClB;IACA,IAAI,kBAAkB,IAAI,CAAC,WAAW,GAAG,QAAQ;IACjD,OAAO;QAAC,YAAY;QAAY,iBAAiB;IAAe;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/Token.js"], "sourcesContent": ["module.exports = function(name, value, source) {\n    this.name = name;\n    this.value = value;\n    if (source) {\n        this.source = source;\n    }\n};\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;IACzC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,QAAQ;QACR,IAAI,CAAC,MAAM,GAAG;IAClB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/bottom-up.js"], "sourcesContent": ["var rules = require(\"./rules\");\nvar results = require(\"./parsing-results\");\n\nexports.parser = function(name, prefixRules, infixRuleBuilders) {\n    var self = {\n        rule: rule,\n        leftAssociative: leftAssociative,\n        rightAssociative: rightAssociative\n    };\n    \n    var infixRules = new InfixRules(infixRuleBuilders.map(createInfixRule));\n    var prefixRule = rules.firstOf(name, prefixRules);\n    \n    function createInfixRule(infixRuleBuilder) {\n        return {\n            name: infixRuleBuilder.name,\n            rule: lazyRule(infixRuleBuilder.ruleBuilder.bind(null, self))\n        };\n    }\n    \n    function rule() {\n        return createRule(infixRules);\n    }\n    \n    function leftAssociative(name) {\n        return createRule(infixRules.untilExclusive(name));\n    }\n    \n    function rightAssociative(name) {\n        return createRule(infixRules.untilInclusive(name));\n    }\n    \n    function createRule(infixRules) {\n        return apply.bind(null, infixRules);\n    }\n    \n    function apply(infixRules, tokens) {\n        var leftResult = prefixRule(tokens);\n        if (leftResult.isSuccess()) {\n            return infixRules.apply(leftResult);\n        } else {\n            return leftResult;\n        }\n    }\n    \n    return self;\n};\n\nfunction InfixRules(infixRules) {\n    function untilExclusive(name) {\n        return new InfixRules(infixRules.slice(0, ruleNames().indexOf(name)));\n    }\n    \n    function untilInclusive(name) {\n        return new InfixRules(infixRules.slice(0, ruleNames().indexOf(name) + 1));\n    }\n    \n    function ruleNames() {\n        return infixRules.map(function(rule) {\n            return rule.name;\n        });\n    }\n    \n    function apply(leftResult) {\n        var currentResult;\n        var source;\n        while (true) {\n            currentResult = applyToTokens(leftResult.remaining());\n            if (currentResult.isSuccess()) {\n                source = leftResult.source().to(currentResult.source());\n                leftResult = results.success(\n                    currentResult.value()(leftResult.value(), source),\n                    currentResult.remaining(),\n                    source\n                )\n            } else if (currentResult.isFailure()) {\n                return leftResult;\n            } else {\n                return currentResult;\n            }\n        }\n    }\n    \n    function applyToTokens(tokens) {\n        return rules.firstOf(\"infix\", infixRules.map(function(infix) {\n            return infix.rule;\n        }))(tokens);\n    }\n    \n    return {\n        apply: apply,\n        untilExclusive: untilExclusive,\n        untilInclusive: untilInclusive\n    }\n}\n\nexports.infix = function(name, ruleBuilder) {\n    function map(func) {\n        return exports.infix(name, function(parser) {\n            var rule = ruleBuilder(parser);\n            return function(tokens) {\n                var result = rule(tokens);\n                return result.map(function(right) {\n                    return function(left, source) {\n                        return func(left, right, source);\n                    };\n                });\n            };\n        });\n    }\n    \n    return {\n        name: name,\n        ruleBuilder: ruleBuilder,\n        map: map\n    };\n}\n\n// TODO: move into a sensible place and remove duplication\nvar lazyRule = function(ruleBuilder) {\n    var rule;\n    return function(input) {\n        if (!rule) {\n            rule = ruleBuilder();\n        }\n        return rule(input);\n    };\n};\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,QAAQ,MAAM,GAAG,SAAS,IAAI,EAAE,WAAW,EAAE,iBAAiB;IAC1D,IAAI,OAAO;QACP,MAAM;QACN,iBAAiB;QACjB,kBAAkB;IACtB;IAEA,IAAI,aAAa,IAAI,WAAW,kBAAkB,GAAG,CAAC;IACtD,IAAI,aAAa,MAAM,OAAO,CAAC,MAAM;IAErC,SAAS,gBAAgB,gBAAgB;QACrC,OAAO;YACH,MAAM,iBAAiB,IAAI;YAC3B,MAAM,SAAS,iBAAiB,WAAW,CAAC,IAAI,CAAC,MAAM;QAC3D;IACJ;IAEA,SAAS;QACL,OAAO,WAAW;IACtB;IAEA,SAAS,gBAAgB,IAAI;QACzB,OAAO,WAAW,WAAW,cAAc,CAAC;IAChD;IAEA,SAAS,iBAAiB,IAAI;QAC1B,OAAO,WAAW,WAAW,cAAc,CAAC;IAChD;IAEA,SAAS,WAAW,UAAU;QAC1B,OAAO,MAAM,IAAI,CAAC,MAAM;IAC5B;IAEA,SAAS,MAAM,UAAU,EAAE,MAAM;QAC7B,IAAI,aAAa,WAAW;QAC5B,IAAI,WAAW,SAAS,IAAI;YACxB,OAAO,WAAW,KAAK,CAAC;QAC5B,OAAO;YACH,OAAO;QACX;IACJ;IAEA,OAAO;AACX;AAEA,SAAS,WAAW,UAAU;IAC1B,SAAS,eAAe,IAAI;QACxB,OAAO,IAAI,WAAW,WAAW,KAAK,CAAC,GAAG,YAAY,OAAO,CAAC;IAClE;IAEA,SAAS,eAAe,IAAI;QACxB,OAAO,IAAI,WAAW,WAAW,KAAK,CAAC,GAAG,YAAY,OAAO,CAAC,QAAQ;IAC1E;IAEA,SAAS;QACL,OAAO,WAAW,GAAG,CAAC,SAAS,IAAI;YAC/B,OAAO,KAAK,IAAI;QACpB;IACJ;IAEA,SAAS,MAAM,UAAU;QACrB,IAAI;QACJ,IAAI;QACJ,MAAO,KAAM;YACT,gBAAgB,cAAc,WAAW,SAAS;YAClD,IAAI,cAAc,SAAS,IAAI;gBAC3B,SAAS,WAAW,MAAM,GAAG,EAAE,CAAC,cAAc,MAAM;gBACpD,aAAa,QAAQ,OAAO,CACxB,cAAc,KAAK,GAAG,WAAW,KAAK,IAAI,SAC1C,cAAc,SAAS,IACvB;YAER,OAAO,IAAI,cAAc,SAAS,IAAI;gBAClC,OAAO;YACX,OAAO;gBACH,OAAO;YACX;QACJ;IACJ;IAEA,SAAS,cAAc,MAAM;QACzB,OAAO,MAAM,OAAO,CAAC,SAAS,WAAW,GAAG,CAAC,SAAS,KAAK;YACvD,OAAO,MAAM,IAAI;QACrB,IAAI;IACR;IAEA,OAAO;QACH,OAAO;QACP,gBAAgB;QAChB,gBAAgB;IACpB;AACJ;AAEA,QAAQ,KAAK,GAAG,SAAS,IAAI,EAAE,WAAW;IACtC,SAAS,IAAI,IAAI;QACb,OAAO,QAAQ,KAAK,CAAC,MAAM,SAAS,MAAM;YACtC,IAAI,OAAO,YAAY;YACvB,OAAO,SAAS,MAAM;gBAClB,IAAI,SAAS,KAAK;gBAClB,OAAO,OAAO,GAAG,CAAC,SAAS,KAAK;oBAC5B,OAAO,SAAS,IAAI,EAAE,MAAM;wBACxB,OAAO,KAAK,MAAM,OAAO;oBAC7B;gBACJ;YACJ;QACJ;IACJ;IAEA,OAAO;QACH,MAAM;QACN,aAAa;QACb,KAAK;IACT;AACJ;AAEA,0DAA0D;AAC1D,IAAI,WAAW,SAAS,WAAW;IAC/B,IAAI;IACJ,OAAO,SAAS,KAAK;QACjB,IAAI,CAAC,MAAM;YACP,OAAO;QACX;QACA,OAAO,KAAK;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/lib/regex-tokeniser.js"], "sourcesContent": ["var Token = require(\"./Token\");\nvar StringSource = require(\"./StringSource\");\n\nexports.RegexTokeniser = RegexTokeniser;\n\nfunction RegexTokeniser(rules) {\n    rules = rules.map(function(rule) {\n        return {\n            name: rule.name,\n            regex: new RegExp(rule.regex.source, \"g\")\n        };\n    });\n    \n    function tokenise(input, description) {\n        var source = new StringSource(input, description);\n        var index = 0;\n        var tokens = [];\n    \n        while (index < input.length) {\n            var result = readNextToken(input, index, source);\n            index = result.endIndex;\n            tokens.push(result.token);\n        }\n        \n        tokens.push(endToken(input, source));\n        return tokens;\n    }\n\n    function readNextToken(string, startIndex, source) {\n        for (var i = 0; i < rules.length; i++) {\n            var regex = rules[i].regex;\n            regex.lastIndex = startIndex;\n            var result = regex.exec(string);\n            \n            if (result) {\n                var endIndex = startIndex + result[0].length;\n                if (result.index === startIndex && endIndex > startIndex) {\n                    var value = result[1];\n                    var token = new Token(\n                        rules[i].name,\n                        value,\n                        source.range(startIndex, endIndex)\n                    );\n                    return {token: token, endIndex: endIndex};\n                }\n            }\n        }\n        var endIndex = startIndex + 1;\n        var token = new Token(\n            \"unrecognisedCharacter\",\n            string.substring(startIndex, endIndex),\n            source.range(startIndex, endIndex)\n        );\n        return {token: token, endIndex: endIndex};\n    }\n    \n    function endToken(input, source) {\n        return new Token(\n            \"end\",\n            null,\n            source.range(input.length, input.length)\n        );\n    }\n    \n    return {\n        tokenise: tokenise\n    }\n}\n\n\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,QAAQ,cAAc,GAAG;AAEzB,SAAS,eAAe,KAAK;IACzB,QAAQ,MAAM,GAAG,CAAC,SAAS,IAAI;QAC3B,OAAO;YACH,MAAM,KAAK,IAAI;YACf,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE;QACzC;IACJ;IAEA,SAAS,SAAS,KAAK,EAAE,WAAW;QAChC,IAAI,SAAS,IAAI,aAAa,OAAO;QACrC,IAAI,QAAQ;QACZ,IAAI,SAAS,EAAE;QAEf,MAAO,QAAQ,MAAM,MAAM,CAAE;YACzB,IAAI,SAAS,cAAc,OAAO,OAAO;YACzC,QAAQ,OAAO,QAAQ;YACvB,OAAO,IAAI,CAAC,OAAO,KAAK;QAC5B;QAEA,OAAO,IAAI,CAAC,SAAS,OAAO;QAC5B,OAAO;IACX;IAEA,SAAS,cAAc,MAAM,EAAE,UAAU,EAAE,MAAM;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC,KAAK;YAC1B,MAAM,SAAS,GAAG;YAClB,IAAI,SAAS,MAAM,IAAI,CAAC;YAExB,IAAI,QAAQ;gBACR,IAAI,WAAW,aAAa,MAAM,CAAC,EAAE,CAAC,MAAM;gBAC5C,IAAI,OAAO,KAAK,KAAK,cAAc,WAAW,YAAY;oBACtD,IAAI,QAAQ,MAAM,CAAC,EAAE;oBACrB,IAAI,QAAQ,IAAI,MACZ,KAAK,CAAC,EAAE,CAAC,IAAI,EACb,OACA,OAAO,KAAK,CAAC,YAAY;oBAE7B,OAAO;wBAAC,OAAO;wBAAO,UAAU;oBAAQ;gBAC5C;YACJ;QACJ;QACA,IAAI,WAAW,aAAa;QAC5B,IAAI,QAAQ,IAAI,MACZ,yBACA,OAAO,SAAS,CAAC,YAAY,WAC7B,OAAO,KAAK,CAAC,YAAY;QAE7B,OAAO;YAAC,OAAO;YAAO,UAAU;QAAQ;IAC5C;IAEA,SAAS,SAAS,KAAK,EAAE,MAAM;QAC3B,OAAO,IAAI,MACP,OACA,MACA,OAAO,KAAK,CAAC,MAAM,MAAM,EAAE,MAAM,MAAM;IAE/C;IAEA,OAAO;QACH,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/lop/index.js"], "sourcesContent": ["exports.Parser = require(\"./lib/parser\").Parser;\nexports.rules = require(\"./lib/rules\");\nexports.errors = require(\"./lib/errors\");\nexports.results = require(\"./lib/parsing-results\");\nexports.StringSource = require(\"./lib/StringSource\");\nexports.Token = require(\"./lib/Token\");\nexports.bottomUp = require(\"./lib/bottom-up\");\nexports.RegexTokeniser = require(\"./lib/regex-tokeniser\").RegexTokeniser;\n\nexports.rule = function(ruleBuilder) {\n    var rule;\n    return function(input) {\n        if (!rule) {\n            rule = ruleBuilder();\n        }\n        return rule(input);\n    };\n};\n"], "names": [], "mappings": "AAAA,QAAQ,MAAM,GAAG,6FAAwB,MAAM;AAC/C,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,cAAc,GAAG,sGAAiC,cAAc;AAExE,QAAQ,IAAI,GAAG,SAAS,WAAW;IAC/B,IAAI;IACJ,OAAO,SAAS,KAAK;QACjB,IAAI,CAAC,MAAM;YACP,OAAO;QACX;QACA,OAAO,KAAK;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/RapidPrototyping/rapid-prototyping-automation/node_modules/option/index.js"], "sourcesContent": ["exports.none = Object.create({\n    value: function() {\n        throw new Error('Called value on none');\n    },\n    isNone: function() {\n        return true;\n    },\n    isSome: function() {\n        return false;\n    },\n    map: function() {\n        return exports.none;\n    },\n    flatMap: function() {\n        return exports.none;\n    },\n    filter: function() {\n        return exports.none;\n    },\n    toArray: function() {\n        return [];\n    },\n    orElse: callOrReturn,\n    valueOrElse: callOrReturn\n});\n\nfunction callOrReturn(value) {\n    if (typeof(value) == \"function\") {\n        return value();\n    } else {\n        return value;\n    }\n}\n\nexports.some = function(value) {\n    return new Some(value);\n};\n\nvar Some = function(value) {\n    this._value = value;\n};\n\nSome.prototype.value = function() {\n    return this._value;\n};\n\nSome.prototype.isNone = function() {\n    return false;\n};\n\nSome.prototype.isSome = function() {\n    return true;\n};\n\nSome.prototype.map = function(func) {\n    return new Some(func(this._value));\n};\n\nSome.prototype.flatMap = function(func) {\n    return func(this._value);\n};\n\nSome.prototype.filter = function(predicate) {\n    return predicate(this._value) ? this : exports.none;\n};\n\nSome.prototype.toArray = function() {\n    return [this._value];\n};\n\nSome.prototype.orElse = function(value) {\n    return this;\n};\n\nSome.prototype.valueOrElse = function(value) {\n    return this._value;\n};\n\nexports.isOption = function(value) {\n    return value === exports.none || value instanceof Some;\n};\n\nexports.fromNullable = function(value) {\n    if (value == null) {\n        return exports.none;\n    }\n    return new Some(value);\n}\n"], "names": [], "mappings": "AAAA,QAAQ,IAAI,GAAG,OAAO,MAAM,CAAC;IACzB,OAAO;QACH,MAAM,IAAI,MAAM;IACpB;IACA,QAAQ;QACJ,OAAO;IACX;IACA,QAAQ;QACJ,OAAO;IACX;IACA,KAAK;QACD,OAAO,QAAQ,IAAI;IACvB;IACA,SAAS;QACL,OAAO,QAAQ,IAAI;IACvB;IACA,QAAQ;QACJ,OAAO,QAAQ,IAAI;IACvB;IACA,SAAS;QACL,OAAO,EAAE;IACb;IACA,QAAQ;IACR,aAAa;AACjB;AAEA,SAAS,aAAa,KAAK;IACvB,IAAI,OAAO,SAAU,YAAY;QAC7B,OAAO;IACX,OAAO;QACH,OAAO;IACX;AACJ;AAEA,QAAQ,IAAI,GAAG,SAAS,KAAK;IACzB,OAAO,IAAI,KAAK;AACpB;AAEA,IAAI,OAAO,SAAS,KAAK;IACrB,IAAI,CAAC,MAAM,GAAG;AAClB;AAEA,KAAK,SAAS,CAAC,KAAK,GAAG;IACnB,OAAO,IAAI,CAAC,MAAM;AACtB;AAEA,KAAK,SAAS,CAAC,MAAM,GAAG;IACpB,OAAO;AACX;AAEA,KAAK,SAAS,CAAC,MAAM,GAAG;IACpB,OAAO;AACX;AAEA,KAAK,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI;IAC9B,OAAO,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM;AACpC;AAEA,KAAK,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI;IAClC,OAAO,KAAK,IAAI,CAAC,MAAM;AAC3B;AAEA,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,SAAS;IACtC,OAAO,UAAU,IAAI,CAAC,MAAM,IAAI,IAAI,GAAG,QAAQ,IAAI;AACvD;AAEA,KAAK,SAAS,CAAC,OAAO,GAAG;IACrB,OAAO;QAAC,IAAI,CAAC,MAAM;KAAC;AACxB;AAEA,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,KAAK;IAClC,OAAO,IAAI;AACf;AAEA,KAAK,SAAS,CAAC,WAAW,GAAG,SAAS,KAAK;IACvC,OAAO,IAAI,CAAC,MAAM;AACtB;AAEA,QAAQ,QAAQ,GAAG,SAAS,KAAK;IAC7B,OAAO,UAAU,QAAQ,IAAI,IAAI,iBAAiB;AACtD;AAEA,QAAQ,YAAY,GAAG,SAAS,KAAK;IACjC,IAAI,SAAS,MAAM;QACf,OAAO,QAAQ,IAAI;IACvB;IACA,OAAO,IAAI,KAAK;AACpB", "ignoreList": [0], "debugId": null}}]}