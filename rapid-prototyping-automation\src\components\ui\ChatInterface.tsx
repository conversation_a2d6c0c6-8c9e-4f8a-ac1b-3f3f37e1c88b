import React, { useState } from 'react';
import { Button } from './Button';
import { Send, Bot, User } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

interface ChatInterfaceProps {
  onFeedback: (feedback: string) => void;
  context?: string;
  diagramCode?: string;
  onDiagramUpdate?: (updatedCode: any) => void;
  className?: string;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  onFeedback,
  context = '',
  diagramCode = '',
  onDiagramUpdate,
  className
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'I\'ve generated the architecture diagram based on your technical requirements. What do you think about the current design? You can ask me to modify components, add security layers, improve performance, or make any other architectural changes.',
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    onFeedback(inputValue);

    const currentInput = inputValue;
    setInputValue('');
    setIsLoading(true);

    try {
      // Call the chat API
      const response = await fetch('/api/chat-architecture', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput,
          context: context,
          diagramCode: diagramCode
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get chat response');
      }

      const data = await response.json();

      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: data.response || 'I understand your feedback. Let me help you improve the architecture.',
        sender: 'bot',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botResponse]);

      // If there's an updated diagram code, notify the parent component
      if (data.updatedDiagramCode && onDiagramUpdate) {
        onDiagramUpdate(data.updatedDiagramCode);
      }

    } catch (error) {
      console.error('Chat error:', error);
      const errorResponse: Message = {
        id: (Date.now() + 2).toString(),
        content: 'I apologize, but I\'m having trouble processing your request right now. Please try again or check if the Claude API is properly configured.',
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorResponse]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className={cn('bg-white border border-gray-200 rounded-lg', className)}>
      <div className="p-4 border-b border-gray-200">
        <h4 className="font-semibold text-gray-900">Architecture Feedback</h4>
        <p className="text-sm text-gray-600">Share your thoughts on the generated architecture</p>
      </div>
      
      <div className="h-64 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex items-start space-x-3',
              message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''
            )}
          >
            <div className={cn(
              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
              message.sender === 'user' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-600'
            )}>
              {message.sender === 'user' ? (
                <User className="w-4 h-4" />
              ) : (
                <Bot className="w-4 h-4" />
              )}
            </div>
            <div className={cn(
              'flex-1 max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
              message.sender === 'user'
                ? 'bg-blue-500 text-white ml-auto'
                : 'bg-gray-100 text-gray-900'
            )}>
              <p className="text-sm">{message.content}</p>
              <p className={cn(
                'text-xs mt-1',
                message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
              )}>
                {message.timestamp.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Share your feedback on the architecture..."
            className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={2}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            loading={isLoading}
            size="sm"
            className="self-end"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
